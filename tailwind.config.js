/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/utils/**/*.{js,ts,jsx,tsx,mdx}"
  ],
  theme: {
    colors: {
      transparent: "transparent",
      background: "rgb(var(--color-background) / <alpha-value>)",
      cards: "rgb(var(--color-cards) / <alpha-value>)",
      v2: {
        background: {
          primary: "rgb(var(--base-white))",
          secondary: "rgb(var(--neutral-neutral-lighter))"
        },
        border: {
          active: "rgb(var(--brand-brand))",
          error: "rgb(var(--error-error))",
          info: "rgb(var(--info-info))",
          primary: "rgb(var(--neutral-neutral-light))",
          secondary: "rgb(var(--neutral-neutral-lighter))",
          success: "rgb(var(--success-success))",
          tertiary: "rgb(var(--neutral-neutral-lightest))",
          warning: "rgb(var(--warning-warning))"
        },
        content: {
          disable: "rgb(var(--neutral-neutral-lighter-2))",
          "on-action-1": "rgb(var(--base-white))",
          "on-action-2": "rgb(var(--brand-brand))",
          "on-action-disable": "rgb(var(--brand-brand-light))",
          "on-action-hover-1": "rgb(var(--neutral-neutral-lightest))",
          "on-action-hover-2": "rgb(var(--brand-brand-dark))",
          "on-error": "rgb(var(--error-error-darkest))",
          "on-error-2": "rgb(var(--error-error))",
          "on-info": "rgb(var(--info-info-darkest))",
          "on-info-2": "rgb(var(--info-info))",
          "on-success-1": "rgb(var(--success-success-darkest))",
          "on-success-2": "rgb(var(--success-success))",
          "on-warning-1": "rgb(var(--warning-warning-darkest))",
          "on-warning-2": "rgb(var(--warning-warning-dark))",
          "on-warning-disable": "rgb(var(--warning-warning-light))",
          primary: "rgb(var(--neutral-neutral-darkest))",
          secondary: "rgb(var(--neutral-neutral-dark))",
          subtle: "rgb(var(--neutral-neutral-light-2))",
          tertiary: "rgb(var(--neutral-neutral))"
        },
        surface: {
          action: "rgb(var(--brand-brand))",
          "action-disable": "rgb(var(--brand-brand-lighter))",
          "action-hover": "rgb(var(--brand-brand-dark))",
          "action-light": "rgb(var(--brand-brand-lightest))",
          disable: "rgb(var(--neutral-neutral-lightest-2))",
          error: "rgb(var(--error-error-lighter))",
          info: "rgb(var(--info-info-lightest))",
          inverse: "rgb(var(--base-black))",
          primary: "rgb(var(--base-white))",
          secondary: "rgb(var(--neutral-neutral-lightest))",
          success: "rgb(var(--success-success-lightest))",
          "success-2": "rgb(var(--success-success-lighter))",
          thertiary: "rgb(var(--neutral-neutral-lighter))",
          "warining-1": "rgb(var(--warning-warning-lighter))",
          "warining-2": "rgb(var(--warning-warning))",
          "warining-hover": "rgb(var(--warning-warning-dark))"
        }
      },
      gray: {
        20: "rgb(var(--color-gray-20) / <alpha-value>)",
        40: "rgb(var(--color-gray-40) / <alpha-value>)",
        50: "rgb(var(--color-gray-50) / <alpha-value>)",
        100: "rgb(var(--color-gray-100) / <alpha-value>)",
        200: "rgb(var(--color-gray-200) / <alpha-value>)",
        400: "rgb(var(--color-gray-400) / <alpha-value>)",
        500: "rgb(var(--color-gray-500) / <alpha-value>)",
        600: "rgb(var(--color-gray-600) / <alpha-value>)",
        999: "rgb(var(--color-gray-999) / <alpha-value>)"
      },
      purple: {
        50: "rgb(var(--color-purple-40) / <alpha-value>)",
        50: "rgb(var(--color-purple-50) / <alpha-value>)",
        500: "rgb(var(--color-purple-500) / <alpha-value>)",
        700: "rgb(var(--color-purple-700) / <alpha-value>)"
      },
      cyan: {
        50: "rgb(var(--color-cyan-50) / <alpha-value>)",
        500: "rgb(var(--color-cyan-500) / <alpha-value>)"
      },
      info: {
        50: "rgb(var(--color-info-50) / <alpha-value>)"
      },
      error: {
        50: "rgb(var(--color-error-50) / <alpha-value>)",
        500: "rgb(var(--color-error-500) / <alpha-value>)",
        600: "rgb(var(--color-error-600) / <alpha-value>)",
        800: "rgb(var(--color-error-800) / <alpha-value>)"
      },
      warning: {
        50: "rgb(var(--color-warning-50) / <alpha-value>)",
        75: "rgb(var(--color-warning-75) / <alpha-value>)",
        500: "rgb(var(--color-warning-500) / <alpha-value>)",
        800: "rgb(var(--color-warning-800) / <alpha-value>)"
      },
      success: {
        50: "rgb(var(--color-success-50) / <alpha-value>)",
        500: "rgb(var(--color-success-500) / <alpha-value>)"
      },
      foreground: {
        neutral: {
          tertiary: "rgb(var(--color-foreground-neutral-tertiary) / <alpha-value>)",
          secondary: "rgb(var(--color-foreground-neutral-secondary) / <alpha-value>)"
        }
      }
    },
    extend: {
      fontFamily: {
        mainFont: ["mainFont", "sans-serif"],
        iranYekanThin: ["Iran yekanthin", "sans-serif"],
        iranYekanLight: ["Iran yekanLight", "sans-serif"],
        iranYekan: ["Iran yekan", "sans-serif"],
        iranYekanSemiBold: ["Iran yekanSemiBold", "sans-serif"],
        iranYekanBold: ["Iran yekanBold", "sans-serif"],
        iranYekanExtraBold: ["Iran yekanExtraBold", "sans-serif"],
        iranYekanExtraBlack: ["Iran yekanExtraBlack", "sans-serif"]
      },
      height: {
        fullMinesHeader: "calc(100vh - 131px)"
      },
      maxHeight: {
        fullMinesHeader: "calc(100vh - 131px)"
      },
      screens: {
        xs: "320px",
        "2xs": "400px", // this is slightly bigger than xs
        "2xmd": "500px",
        xmd: "768px"
      },
      boxShadow: {
        "product-card": "var(--box-shadow-product-card)",
        header: "var(--box-shadow-header)",
        category: "0px 7px 14px 0px rgba(0, 0, 0, 0.06)"
      }
    }
  },

  plugins: [
    function ({ addUtilities, theme }) {
      const newUtilities = {
        "@screen xs": {
          ".text-h1-bold": {
            fontSize: "28px",
            fontWeight: "700"
          },
          ".text-h1-heavy": {
            fontSize: "28px",
            fontWeight: "800"
          },
          ".text-h2-bold": {
            fontSize: "24px",
            fontWeight: "700"
          },
          ".text-h2-heavy": {
            fontSize: "24px",
            fontWeight: "800"
          },
          ".text-h3-bold": {
            fontSize: "22px",
            fontWeight: "700"
          },
          ".text-h3-medium": {
            fontSize: "22px",
            fontWeight: "500"
          },
          ".text-h4-bold": {
            fontSize: "20px",
            fontWeight: "700"
          },
          ".text-h4-medium": {
            fontSize: "20px",
            fontWeight: "500"
          },
          ".text-h5-bold": {
            fontSize: "18px",
            fontWeight: "700"
          },
          ".text-h5-medium": {
            fontSize: "18px",
            fontWeight: "500"
          },
          ".text-subtitle-bold": {
            fontSize: "16px",
            fontWeight: "700"
          },
          ".text-subtitle-medium": {
            fontSize: "16px",
            fontWeight: "500"
          },
          ".text-body1-bold": {
            fontSize: "15px",
            fontWeight: "700"
          },
          ".text-body1-regular": {
            fontSize: "15px",
            fontWeight: "400"
          },
          ".text-body1-medium": {
            fontSize: "15px",
            fontWeight: "500"
          },
          ".text-body2-bold": {
            fontSize: "14px",
            fontWeight: "700"
          },
          ".text-body2-regular": {
            fontSize: "14px",
            fontWeight: "400"
          },
          ".text-body2-medium": {
            fontSize: "14px",
            fontWeight: "500"
          },
          ".text-body3-regular": {
            fontSize: "13px",
            fontWeight: "400"
          },
          ".text-body3-medium": {
            fontSize: "13px",
            fontWeight: "500"
          },
          ".text-body4-bold": {
            fontSize: "12px",
            fontWeight: "700"
          },
          ".text-body4-regular": {
            fontSize: "12px",
            fontWeight: "400"
          },
          ".text-body4-medium": {
            fontSize: "12px",
            fontWeight: "500"
          },
          ".text-caption-regular": {
            fontSize: "10px",
            fontWeight: "400"
          },
          ".text-caption-bold": {
            fontSize: "10px",
            fontWeight: "700"
          }
        },
        "@screen xmd": {
          ".text-h1-bold": {
            fontSize: "48px",
            fontWeight: "700"
          },
          ".text-h1-heavy": {
            fontSize: "48px",
            fontWeight: "800"
          },
          ".text-h2-bold": {
            fontSize: "40px",
            fontWeight: "700"
          },
          ".text-h2-heavy": {
            fontSize: "40px",
            fontWeight: "800"
          },
          ".text-h3-bold": {
            fontSize: "32px",
            fontWeight: "700"
          },
          ".text-h3-heavy": {
            fontSize: "32px",
            fontWeight: "800"
          },
          ".text-h4-bold": {
            fontSize: "24px",
            fontWeight: "700"
          },
          ".text-h4-medium": {
            fontSize: "24px",
            fontWeight: "500"
          },
          ".text-h5-bold": {
            fontSize: "18px",
            fontWeight: "700"
          },
          ".text-h5-medium": {
            fontSize: "18px",
            fontWeight: "500"
          },
          ".text-subtitle-bold": {
            fontSize: "16px",
            fontWeight: "700"
          },
          ".text-subtitle-medium": {
            fontSize: "16px",
            fontWeight: "500"
          },
          ".text-body1-regular": {
            fontSize: "16px",
            fontWeight: "400"
          },
          ".text-body1-medium": {
            fontSize: "16px",
            fontWeight: "500"
          },
          ".text-body2-regular": {
            fontSize: "15px",
            fontWeight: "400"
          },
          ".text-body2-bold": {
            fontSize: "15px",
            fontWeight: "700"
          },
          ".text-body3-regular": {
            fontSize: "14px",
            fontWeight: "400"
          },
          ".text-body3-bold": {
            fontSize: "14px",
            fontWeight: "700"
          },
          ".text-body4-regular": {
            fontSize: "13px",
            fontWeight: "400"
          },
          ".text-body4-medium": {
            fontSize: "13px",
            fontWeight: "500"
          },
          ".text-caption-regular": {
            fontSize: "12px",
            fontWeight: "400"
          },
          ".text-caption-medium": {
            fontSize: "12px",
            fontWeight: "500"
          }
        },

        "@screen lg": {
          ".text-h1-bold": {
            fontSize: "56px",
            fontWeight: "700"
          },
          ".text-h1-heavy": {
            fontSize: "56px",
            fontWeight: "800"
          },
          ".text-h2-bold": {
            fontSize: "36px",
            fontWeight: "700"
          },
          ".text-h2-heavy": {
            fontSize: "36px",
            fontWeight: "800"
          },
          ".text-h3-bold": {
            fontSize: "28px",
            fontWeight: "700"
          },
          ".text-h3-heavy": {
            fontSize: "28px",
            fontWeight: "800"
          },
          ".text-h4-bold": {
            fontSize: "24px",
            fontWeight: "700"
          },
          ".text-h4-medium": {
            fontSize: "24px",
            fontWeight: "500"
          },
          ".text-h5-bold": {
            fontSize: "18px",
            fontWeight: "700"
          },
          ".text-h5-medium": {
            fontSize: "18px",
            fontWeight: "500"
          },
          ".text-subtitle-bold": {
            fontSize: "16px",
            fontWeight: "700"
          },
          ".text-subtitle-medium": {
            fontSize: "16px",
            fontWeight: "500"
          },
          ".text-body1-regular": {
            fontSize: "16px",
            fontWeight: "400"
          },
          ".text-body1-medium": {
            fontSize: "16px",
            fontWeight: "500"
          },
          ".text-body2-regular": {
            fontSize: "15px",
            fontWeight: "400"
          },
          ".text-body2-medium": {
            fontSize: "15px",
            fontWeight: "500"
          },
          ".text-body3-regular": {
            fontSize: "14px",
            fontWeight: "400"
          },
          ".text-body3-medium": {
            fontSize: "14px",
            fontWeight: "500"
          },
          ".text-body4-regular": {
            fontSize: "13px",
            fontWeight: "400"
          },
          ".text-body4-medium": {
            fontSize: "13px",
            fontWeight: "500"
          },
          ".text-caption-regular": {
            fontSize: "12px",
            fontWeight: "400"
          },
          ".text-caption-medium": {
            fontSize: "12px",
            fontWeight: "500"
          }
        }
      };
      addUtilities(newUtilities, ["responsive", "hover"]);
    }
  ]
};
