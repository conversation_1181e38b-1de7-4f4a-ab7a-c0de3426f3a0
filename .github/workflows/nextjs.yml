name: Drophub Dashboards

on:
  push:
    branches:
      - stage
      - main
  workflow_dispatch:
jobs:
  build_stage:
    environment: staging
    name: run staging build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/stage'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Login to Docker Registry
        run: echo "${{ secrets.ACCESS_TOKEN }}" | docker login ${{ vars.DOCKER_REGISTRY }} -u ${{ github.actor }} --password-stdin
      - name: Build Images
        run: |
          DOCKER_REGISTRY=${{ vars.DOCKER_REGISTRY }} \
          REPOSITORY_OWNER=${{ github.repository_owner }} \
          PROJECT_NAME=${{ vars.PROJECT_NAME }} \
          PORT=${{ vars.PORT }} \
          NEXT_PUBLIC_DATA_BASE_URL=${{ vars.NEXT_PUBLIC_DATA_BASE_URL }} \
          NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }} \
          NEXTAUTH_URL=${{ vars.NEXTAUTH_URL }} \
          NEXT_PUBLIC_LANG_LOCALE=${{ vars.NEXT_PUBLIC_LANG_LOCALE }} \
          NEXT_PUBLIC_SAMPLE_EXCEL_DOWNLOAD_LINK=${{ vars.NEXT_PUBLIC_SAMPLE_EXCEL_DOWNLOAD_LINK }} \
          NEXT_PUBLIC_SAMPLE_EXCEL_UPLOAD_LINK=${{ vars.NEXT_PUBLIC_SAMPLE_EXCEL_UPLOAD_LINK }} \
          NEXT_PUBLIC_DASHBOARD_LANG_LOCALES=\"${{ vars.NEXT_PUBLIC_DASHBOARD_LANG_LOCALES }}\" \
          NEXT_PUBLIC_DASHBOARD_DEFAULT_CURRENCY=${{ vars.NEXT_PUBLIC_DASHBOARD_DEFAULT_CURRENCY }} \
          NEXT_PUBLIC_IRAN_SERVED_ONLY=${{ vars.NEXT_PUBLIC_IRAN_SERVED_ONLY }} \
          NEXT_PUBLIC_SUPPLIER_USER_CONTEXT=${{ vars.NEXT_PUBLIC_SUPPLIER_USER_CONTEXT }} \
          NEXT_PUBLIC_RETAILER_USER_CONTEXT=${{ vars.NEXT_PUBLIC_RETAILER_USER_CONTEXT }} \
          NEXT_TELEMETRY_DISABLED=1 \
          NEXT_PUBLIC_DOC_PUBLIC=${{ vars.NEXT_PUBLIC_DOC_PUBLIC }} \
          NEXT_PUBLIC_SUPPLIER_SUBDOMAIN=${{ vars.NEXT_PUBLIC_SUPPLIER_SUBDOMAIN }} \
          NEXT_PUBLIC_RETAILER_SUBDOMAIN=${{ vars.NEXT_PUBLIC_RETAILER_SUBDOMAIN }} \
          PROFILES_MAX_UPLOAD_SIZE_MB=${{ vars.PROFILES_MAX_UPLOAD_SIZE_MB }} \
          PRODUCTS_IMG_MAX_SIZE_MB=${{ vars.PRODUCTS_IMG_MAX_SIZE_MB }} \
          SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }} \
          docker compose --project-name ${{ vars.PROJECT_NAME }} build --push
  deploy_stage:
    needs: build_stage
    environment: staging
    name: run staging deploy
    if: github.ref == 'refs/heads/stage'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Copy Docker Compose file
        uses: appleboy/scp-action@v0.1.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          proxy_host: ${{ secrets.SSH_PROXY_HOST }}
          proxy_username: ${{ secrets.SSH_PROXY_USER }}
          proxy_key: ${{ secrets.SSH_PROXY_PRIVATE_KEY }}
          proxy_passphrase: ${{ secrets.SSH_PROXY_PASSPHRASE }}
          port: 22
          timeout: 120s
          proxy_timeout: 120s
          source: "./docker-compose.yml"
          target: "~/${{ vars.PROJECT_NAME }}"
      - name: Deploy
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          proxy_host: ${{ secrets.SSH_PROXY_HOST }}
          proxy_username: ${{ secrets.SSH_PROXY_USER }}
          proxy_key: ${{ secrets.SSH_PROXY_PRIVATE_KEY }}
          proxy_passphrase: ${{ secrets.SSH_PROXY_PASSPHRASE }}
          port: 22
          timeout: 120s
          proxy_timeout: 120s
          script: |
            echo "${{ secrets.ACCESS_TOKEN }}" | docker login ${{ vars.DOCKER_REGISTRY }} -u ${{ github.actor }} --password-stdin

            cd ~/${{ vars.PROJECT_NAME }}
            rm .env
            touch .env
            echo DOCKER_REGISTRY=${{ vars.DOCKER_REGISTRY }} >> .env
            echo REPOSITORY_OWNER=${{ github.repository_owner }} >> .env
            echo PROJECT_NAME=${{ vars.PROJECT_NAME }} >> .env
            echo PORT=${{ vars.PORT }} >> .env
            echo NEXT_PUBLIC_DATA_BASE_URL=${{ vars.NEXT_PUBLIC_DATA_BASE_URL }} >> .env
            echo NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }} >> .env
            echo NEXTAUTH_URL=${{ vars.NEXTAUTH_URL }} >> .env
            echo NEXT_PUBLIC_LANG_LOCALE=${{ vars.NEXT_PUBLIC_LANG_LOCALE }} >> .env
            echo NEXT_PUBLIC_SAMPLE_EXCEL_DOWNLOAD_LINK=${{ vars.NEXT_PUBLIC_SAMPLE_EXCEL_DOWNLOAD_LINK }} >> .env
            echo NEXT_PUBLIC_SAMPLE_EXCEL_UPLOAD_LINK=${{ vars.NEXT_PUBLIC_SAMPLE_EXCEL_UPLOAD_LINK }} >> .env
            echo NEXT_PUBLIC_DASHBOARD_LANG_LOCALES=\"${{ vars.NEXT_PUBLIC_DASHBOARD_LANG_LOCALES }}\" >> .env
            echo NEXT_PUBLIC_DASHBOARD_DEFAULT_CURRENCY=${{ vars.NEXT_PUBLIC_DASHBOARD_DEFAULT_CURRENCY }} >> .env
            echo NEXT_PUBLIC_IRAN_SERVED_ONLY=${{ vars.NEXT_PUBLIC_IRAN_SERVED_ONLY }} >> .env
            echo NEXT_PUBLIC_SUPPLIER_USER_CONTEXT=${{ vars.NEXT_PUBLIC_SUPPLIER_USER_CONTEXT }} >> .env
            echo NEXT_PUBLIC_RETAILER_USER_CONTEXT=${{ vars.NEXT_PUBLIC_RETAILER_USER_CONTEXT }} >> .env
            echo NEXT_TELEMETRY_DISABLED=1 >> .env
            echo NEXT_PUBLIC_DOC_PUBLIC=${{ vars.NEXT_PUBLIC_DOC_PUBLIC }} >> .env
            echo NEXT_PUBLIC_SUPPLIER_SUBDOMAIN=${{ vars.NEXT_PUBLIC_SUPPLIER_SUBDOMAIN }} >> .env
            echo NEXT_PUBLIC_RETAILER_SUBDOMAIN=${{ vars.NEXT_PUBLIC_RETAILER_SUBDOMAIN }} >> .env
            echo PROFILES_MAX_UPLOAD_SIZE_MB=${{ vars.PROFILES_MAX_UPLOAD_SIZE_MB }} >> .env
            echo PRODUCTS_IMG_MAX_SIZE_MB=${{ vars.PRODUCTS_IMG_MAX_SIZE_MB }} >> .env
            echo SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }} >> .env
            docker compose pull
            docker compose --project-name ${{ vars.PROJECT_NAME }} up -d
            docker system prune -f
  build_production:
    environment: production
    name: run production build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Login to Docker Registry
        run: echo "${{ secrets.ACCESS_TOKEN }}" | docker login ${{ vars.DOCKER_REGISTRY }} -u ${{ github.actor }} --password-stdin
      - name: Build Images
        run: |
          DOCKER_REGISTRY=${{ vars.DOCKER_REGISTRY }} \
          REPOSITORY_OWNER=${{ github.repository_owner }} \
          PROJECT_NAME=${{ vars.PROJECT_NAME }} \
          PORT=${{ vars.PORT }} \
          NEXT_PUBLIC_DATA_BASE_URL=${{ vars.NEXT_PUBLIC_DATA_BASE_URL }} \
          NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }} \
          NEXTAUTH_URL=${{ vars.NEXTAUTH_URL }} \
          NEXT_PUBLIC_LANG_LOCALE=${{ vars.NEXT_PUBLIC_LANG_LOCALE }} \
          NEXT_PUBLIC_SAMPLE_EXCEL_DOWNLOAD_LINK=${{ vars.NEXT_PUBLIC_SAMPLE_EXCEL_DOWNLOAD_LINK }} \
          NEXT_PUBLIC_SAMPLE_EXCEL_UPLOAD_LINK=${{ vars.NEXT_PUBLIC_SAMPLE_EXCEL_UPLOAD_LINK }} \
          NEXT_PUBLIC_DASHBOARD_LANG_LOCALES=\"${{ vars.NEXT_PUBLIC_DASHBOARD_LANG_LOCALES }}\" \
          NEXT_PUBLIC_DASHBOARD_DEFAULT_CURRENCY=${{ vars.NEXT_PUBLIC_DASHBOARD_DEFAULT_CURRENCY }} \
          NEXT_PUBLIC_IRAN_SERVED_ONLY=${{ vars.NEXT_PUBLIC_IRAN_SERVED_ONLY }} \
          NEXT_PUBLIC_SUPPLIER_USER_CONTEXT=${{ vars.NEXT_PUBLIC_SUPPLIER_USER_CONTEXT }} \
          NEXT_PUBLIC_RETAILER_USER_CONTEXT=${{ vars.NEXT_PUBLIC_RETAILER_USER_CONTEXT }} \
          NEXT_TELEMETRY_DISABLED=1 \
          NEXT_PUBLIC_DOC_PUBLIC=${{ vars.NEXT_PUBLIC_DOC_PUBLIC }} \
          NEXT_PUBLIC_SUPPLIER_SUBDOMAIN=${{ vars.NEXT_PUBLIC_SUPPLIER_SUBDOMAIN }} \
          NEXT_PUBLIC_RETAILER_SUBDOMAIN=${{ vars.NEXT_PUBLIC_RETAILER_SUBDOMAIN }} \
          PROFILES_MAX_UPLOAD_SIZE_MB=${{ vars.PROFILES_MAX_UPLOAD_SIZE_MB }} \
          PRODUCTS_IMG_MAX_SIZE_MB=${{ vars.PRODUCTS_IMG_MAX_SIZE_MB }} \
          SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }} \
          docker compose --project-name ${{ vars.PROJECT_NAME }} build --push
  deploy_production:
    needs: build_production
    environment: production
    name: run production deploy
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Copy Docker Compose file
        uses: appleboy/scp-action@v0.1.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          proxy_host: ${{ secrets.SSH_PROXY_HOST }}
          proxy_username: ${{ secrets.SSH_PROXY_USER }}
          proxy_key: ${{ secrets.SSH_PROXY_PRIVATE_KEY }}
          proxy_passphrase: ${{ secrets.SSH_PROXY_PASSPHRASE }}
          port: 22
          timeout: 120s
          proxy_timeout: 120s
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "./docker-compose.yml"
          target: "~/${{ vars.PROJECT_NAME }}"
      - name: Deploy
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          proxy_host: ${{ secrets.SSH_PROXY_HOST }}
          proxy_username: ${{ secrets.SSH_PROXY_USER }}
          proxy_key: ${{ secrets.SSH_PROXY_PRIVATE_KEY }}
          proxy_passphrase: ${{ secrets.SSH_PROXY_PASSPHRASE }}
          port: 22
          timeout: 120s
          proxy_timeout: 120s
          script: |
            echo "${{ secrets.ACCESS_TOKEN }}" | docker login ${{ vars.DOCKER_REGISTRY }} -u ${{ github.actor }} --password-stdin

            cd ~/${{ vars.PROJECT_NAME }}
            rm .env
            touch .env
            echo DOCKER_REGISTRY=${{ vars.DOCKER_REGISTRY }} >> .env
            echo REPOSITORY_OWNER=${{ github.repository_owner }} >> .env
            echo PROJECT_NAME=${{ vars.PROJECT_NAME }} >> .env
            echo PORT=${{ vars.PORT }} >> .env
            echo NEXT_PUBLIC_DATA_BASE_URL=${{ vars.NEXT_PUBLIC_DATA_BASE_URL }} >> .env
            echo NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }} >> .env
            echo NEXTAUTH_URL=${{ vars.NEXTAUTH_URL }} >> .env
            echo NEXT_PUBLIC_LANG_LOCALE=${{ vars.NEXT_PUBLIC_LANG_LOCALE }} >> .env
            echo NEXT_PUBLIC_SAMPLE_EXCEL_DOWNLOAD_LINK=${{ vars.NEXT_PUBLIC_SAMPLE_EXCEL_DOWNLOAD_LINK }} >> .env
            echo NEXT_PUBLIC_SAMPLE_EXCEL_UPLOAD_LINK=${{ vars.NEXT_PUBLIC_SAMPLE_EXCEL_UPLOAD_LINK }} >> .env
            echo NEXT_PUBLIC_DASHBOARD_LANG_LOCALES=\"${{ vars.NEXT_PUBLIC_DASHBOARD_LANG_LOCALES }}\" >> .env
            echo NEXT_PUBLIC_DASHBOARD_DEFAULT_CURRENCY=${{ vars.NEXT_PUBLIC_DASHBOARD_DEFAULT_CURRENCY }} >> .env
            echo NEXT_PUBLIC_IRAN_SERVED_ONLY=${{ vars.NEXT_PUBLIC_IRAN_SERVED_ONLY }} >> .env
            echo NEXT_PUBLIC_SUPPLIER_USER_CONTEXT=${{ vars.NEXT_PUBLIC_SUPPLIER_USER_CONTEXT }} >> .env
            echo NEXT_PUBLIC_RETAILER_USER_CONTEXT=${{ vars.NEXT_PUBLIC_RETAILER_USER_CONTEXT }} >> .env
            echo NEXT_TELEMETRY_DISABLED=1 >> .env
            echo NEXT_PUBLIC_DOC_PUBLIC=${{ vars.NEXT_PUBLIC_DOC_PUBLIC }} >> .env
            echo NEXT_PUBLIC_SUPPLIER_SUBDOMAIN=${{ vars.NEXT_PUBLIC_SUPPLIER_SUBDOMAIN }} >> .env
            echo NEXT_PUBLIC_RETAILER_SUBDOMAIN=${{ vars.NEXT_PUBLIC_RETAILER_SUBDOMAIN }} >> .env
            echo PROFILES_MAX_UPLOAD_SIZE_MB=${{ vars.PROFILES_MAX_UPLOAD_SIZE_MB }} >> .env
            echo PRODUCTS_IMG_MAX_SIZE_MB=${{ vars.PRODUCTS_IMG_MAX_SIZE_MB }} >> .env
            echo SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }} >> .env
            docker compose pull
            docker compose --project-name ${{ vars.PROJECT_NAME }} up -d
            docker system prune -f
