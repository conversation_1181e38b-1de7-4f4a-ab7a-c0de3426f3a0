const dir = "src\\app\\(DashboardLayout)\\forms\\product-form";
const fs = require("fs");
const pathModule = require("path");
const { parse } = require("@babel/parser");
const generate = require("@babel/generator").default;
const traverse = require("@babel/traverse").default;

const mockTheme = {
  palette: {
    primary: {
      main: "var(--mui-palette-primary-main)",
      light: "var(--mui-palette-primary-light)",
      dark: "var(--mui-palette-primary-dark)",
      contrastText: "var(--mui-palette-primary-contrastText)"
    },
    secondary: {
      main: "var(--mui-palette-secondary-main)",
      light: "var(--mui-palette-secondary-light)",
      dark: "var(--mui-palette-secondary-dark)",
      contrastText: "var(--mui-palette-secondary-contrastText)"
    },
    error: {
      main: "var(--mui-palette-error-main)",
      light: "var(--mui-palette-error-light)",
      dark: "var(--mui-palette-error-dark)",
      contrastText: "var(--mui-palette-error-contrastText)"
    },
    warning: {
      main: "var(--mui-palette-warning-main)",
      light: "var(--mui-palette-warning-light)",
      dark: "var(--mui-palette-warning-dark)",
      contrastText: "var(--mui-palette-warning-contrastText)"
    },
    info: {
      main: "var(--mui-palette-info-main)",
      light: "var(--mui-palette-info-light)",
      dark: "var(--mui-palette-info-dark)",
      contrastText: "var(--mui-palette-info-contrastText)"
    },
    success: {
      main: "var(--mui-palette-success-main)",
      light: "var(--mui-palette-success-light)",
      dark: "var(--mui-palette-success-dark)",
      contrastText: "var(--mui-palette-success-contrastText)"
    },
    grey: {
      50: "var(--mui-palette-grey-50)",
      100: "var(--mui-palette-grey-100)",
      200: "var(--mui-palette-grey-200)",
      300: "var(--mui-palette-grey-300)",
      400: "var(--mui-palette-grey-400)",
      500: "var(--mui-palette-grey-500)",
      600: "var(--mui-palette-grey-600)",
      700: "var(--mui-palette-grey-700)",
      800: "var(--mui-palette-grey-800)",
      900: "var(--mui-palette-grey-900)",
      A100: "var(--mui-palette-grey-A100)",
      A200: "var(--mui-palette-grey-A200)",
      A400: "var(--mui-palette-grey-A400)",
      A700: "var(--mui-palette-grey-A700)"
    },
    text: {
      primary: "var(--mui-palette-text-primary)",
      secondary: "var(--mui-palette-text-secondary)",
      disabled: "var(--mui-palette-text-disabled)",
      hint: "var(--mui-palette-text-hint)"
    },
    background: {
      paper: "var(--mui-palette-background-paper)",
      default: "var(--mui-palette-background-default)"
    },
    divider: "var(--mui-palette-divider)"
  },
  spacing: value => `var(--mui-theme-spacing-${value})`,
  typography: {
    fontFamily: "var(--mui-typography-fontFamily)",
    fontSize: "var(--mui-typography-fontSize)",
    fontWeightLight: "var(--mui-typography-fontWeightLight)",
    fontWeightRegular: "var(--mui-typography-fontWeightRegular)",
    fontWeightMedium: "var(--mui-typography-fontWeightMedium)",
    fontWeightBold: "var(--mui-typography-fontWeightBold)",
    h1: {
      fontSize: "var(--mui-typography-h1-fontSize)",
      fontWeight: "var(--mui-typography-h1-fontWeight)",
      lineHeight: "var(--mui-typography-h1-lineHeight)",
      letterSpacing: "var(--mui-typography-h1-letterSpacing)"
    },
    h2: {
      fontSize: "var(--mui-typography-h2-fontSize)",
      fontWeight: "var(--mui-typography-h2-fontWeight)",
      lineHeight: "var(--mui-typography-h2-lineHeight)",
      letterSpacing: "var(--mui-typography-h2-letterSpacing)"
    },
    h3: {
      fontSize: "var(--mui-typography-h3-fontSize)",
      fontWeight: "var(--mui-typography-h3-fontWeight)",
      lineHeight: "var(--mui-typography-h3-lineHeight)",
      letterSpacing: "var(--mui-typography-h3-letterSpacing)"
    },
    h4: {
      fontSize: "var(--mui-typography-h4-fontSize)",
      fontWeight: "var(--mui-typography-h4-fontWeight)",
      lineHeight: "var(--mui-typography-h4-lineHeight)",
      letterSpacing: "var(--mui-typography-h4-letterSpacing)"
    },
    h5: {
      fontSize: "var(--mui-typography-h5-fontSize)",
      fontWeight: "var(--mui-typography-h5-fontWeight)",
      lineHeight: "var(--mui-typography-h5-lineHeight)",
      letterSpacing: "var(--mui-typography-h5-letterSpacing)"
    },
    h6: {
      fontSize: "var(--mui-typography-h6-fontSize)",
      fontWeight: "var(--mui-typography-h6-fontWeight)",
      lineHeight: "var(--mui-typography-h6-lineHeight)",
      letterSpacing: "var(--mui-typography-h6-letterSpacing)"
    },
    subtitle1: {
      fontSize: "var(--mui-typography-subtitle1-fontSize)",
      fontWeight: "var(--mui-typography-subtitle1-fontWeight)",
      lineHeight: "var(--mui-typography-subtitle1-lineHeight)",
      letterSpacing: "var(--mui-typography-subtitle1-letterSpacing)"
    },
    subtitle2: {
      fontSize: "var(--mui-typography-subtitle2-fontSize)",
      fontWeight: "var(--mui-typography-subtitle2-fontWeight)",
      lineHeight: "var(--mui-typography-subtitle2-lineHeight)",
      letterSpacing: "var(--mui-typography-subtitle2-letterSpacing)"
    },
    body1: {
      fontSize: "var(--mui-typography-body1-fontSize)",
      fontWeight: "var(--mui-typography-body1-fontWeight)",
      lineHeight: "var(--mui-typography-body1-lineHeight)",
      letterSpacing: "var(--mui-typography-body1-letterSpacing)"
    },
    body2: {
      fontSize: "var(--mui-typography-body2-fontSize)",
      fontWeight: "var(--mui-typography-body2-fontWeight)",
      lineHeight: "var(--mui-typography-body2-lineHeight)",
      letterSpacing: "var(--mui-typography-body2-letterSpacing)"
    },
    button: {
      fontSize: "var(--mui-typography-button-fontSize)",
      fontWeight: "var(--mui-typography-button-fontWeight)",
      lineHeight: "var(--mui-typography-button-lineHeight)",
      letterSpacing: "var(--mui-typography-button-letterSpacing)"
    },
    caption: {
      fontSize: "var(--mui-typography-caption-fontSize)",
      fontWeight: "var(--mui-typography-caption-fontWeight)",
      lineHeight: "var(--mui-typography-caption-lineHeight)",
      letterSpacing: "var(--mui-typography-caption-letterSpacing)"
    },
    overline: {
      fontSize: "var(--mui-typography-overline-fontSize)",
      fontWeight: "var(--mui-typography-overline-fontWeight)",
      lineHeight: "var(--mui-typography-overline-lineHeight)",
      letterSpacing: "var(--mui-typography-overline-letterSpacing)"
    }
  },
  zIndex: {
    mobileStepper: "var(--mui-zIndex-mobileStepper)",
    speedDial: "var(--mui-zIndex-speedDial)",
    appBar: "var(--mui-zIndex-appBar)",
    drawer: "var(--mui-zIndex-drawer)",
    modal: "var(--mui-zIndex-modal)",
    snackbar: "var(--mui-zIndex-snackbar)",
    tooltip: "var(--mui-zIndex-tooltip)"
  },
  shape: {
    borderRadius: "var(--mui-shape-borderRadius)"
  },
  transitions: {
    duration: {
      shortest: "var(--mui-transitions-duration-shortest)",
      shorter: "var(--mui-transitions-duration-shorter)",
      short: "var(--mui-transitions-duration-short)",
      standard: "var(--mui-transitions-duration-standard)",
      complex: "var(--mui-transitions-duration-complex)",
      enteringScreen: "var(--mui-transitions-duration-enteringScreen)",
      leavingScreen: "var(--mui-transitions-duration-leavingScreen)"
    },
    easing: {
      easeInOut: "var(--mui-transitions-easing-easeInOut)",
      easeOut: "var(--mui-transitions-easing-easeOut)",
      easeIn: "var(--mui-transitions-easing-easeIn)",
      sharp: "var(--mui-transitions-easing-sharp)"
    }
  }
};

const shorthandToCss = {
  px: ["padding-inline-end", "padding-inline-start"],
  py: ["padding-top", "padding-bottom"],
  pl: ["padding-inline-end"],
  pr: ["padding-inline-start"],
  pt: ["padding-top"],
  pb: ["padding-bottom"],
  mx: ["margin-inline-end", "margin-inline-start"],
  my: ["margin-top", "margin-bottom"],
  ml: ["margin-inline-end"],
  mr: ["margin-inline-start"],
  mt: ["margin-top"],
  mb: ["margin-bottom"],
  p: ["padding"],
  m: ["margin"]
};

const relativeProperties = ["px", "py", "pl", "pr", "pt", "pb", "mx", "my", "ml", "mr", "mt", "mb", "p", "m"];

const evaluateFunction = fn => {
  try {
    return fn(mockTheme);
  } catch (error) {
    console.error("Error evaluating function:", error);
    return null;
  }
};

const sxToCss = (sx, selector = "", styles = {}) => {
  const processObject = (obj, currentSelector) => {
    if (!styles[currentSelector]) {
      styles[currentSelector] = {};
    }

    for (const key in obj) {
      if (typeof obj[key] === "object" && !Array.isArray(obj[key])) {
        if (typeof key === "function") {
          const value = evaluateFunction(key);
          if (value !== null && value !== undefined) {
            const mediaQuery = `@media screen and (max-width: var(--mui-theme-breakpoints-${value}))`;
            if (!styles[mediaQuery]) {
              styles[mediaQuery] = {};
            }
            processObject(obj[key], currentSelector);
          }
        } else if (key.startsWith("@media")) {
          const mediaQuery = key.replace("@media", "").trim();
          if (!styles[mediaQuery]) {
            styles[mediaQuery] = {};
          }
          processObject(obj[key], currentSelector);
        } else {
          const nestedSelector = key.startsWith("&")
            ? `${currentSelector}${key.slice(1)}`
            : `${currentSelector} ${key}`;
          processObject(obj[key], nestedSelector);
        }
      } else if (typeof obj[key] === "function") {
        const value = evaluateFunction(obj[key]);
        if (value !== null && value !== undefined && value !== "") {
          // Exclude empty values
          const cssKeys = shorthandToCss[key] || [key.replace(/[A-Z]/g, match => `-${match.toLowerCase()}`)];
          cssKeys.forEach(cssKey => {
            styles[currentSelector][cssKey] = value;
          });
        }
      } else {
        const cssKeys = shorthandToCss[key] || [key.replace(/[A-Z]/g, match => `-${match.toLowerCase()}`)];
        let value = obj[key];
        if (relativeProperties.includes(key) && !isNaN(value)) {
          value = `var(--mui-theme-spacing-${value})`;
        }
        if (value !== null && value !== undefined && value !== "") {
          // Exclude empty values
          cssKeys.forEach(cssKey => {
            styles[currentSelector][cssKey] = value;
          });
        }
      }
    }
  };

  processObject(sx, selector);
  return styles;
};

const processFile = filePath => {
  const fileContent = fs.readFileSync(filePath, "utf8");

  const ast = parse(fileContent, {
    sourceType: "module",
    plugins: ["jsx", "typescript"]
  });

  let styles = {};

  traverse(ast, {
    JSXAttribute(path) {
      if (path.node.name.name === "sx") {
        const sxNode = path.node.value.expression;
        let sxObject = {};

        if (sxNode.type === "ObjectExpression") {
          sxObject = sxNode.properties.reduce((acc, prop) => {
            const key = prop.key?.name || prop.key?.value;
            let value = "";
            if (!key) return acc;
            if (prop.value?.type === "StringLiteral" || prop.value?.type === "NumericLiteral") {
              value = prop.value.value;
            } else if (prop.value.type === "ObjectExpression") {
              value = prop.value.properties.reduce((nestedAcc, nestedProp) => {
                const nestedKey = nestedProp.key.name || nestedProp.key.value;
                let nestedValue = "";
                if (nestedProp.value.type === "StringLiteral" || nestedProp.value.type === "NumericLiteral") {
                  nestedValue = nestedProp.value.value;
                } else if (nestedProp.value.type === "Identifier") {
                  nestedValue = `var(--${nestedProp.value.name})`;
                } else if (nestedProp.value.type === "ArrowFunctionExpression") {
                  const { code } = generate(nestedProp.value);
                  nestedValue = new Function("theme", `return ${code}`)(mockTheme);
                }
                nestedAcc[nestedKey] = nestedValue;
                return nestedAcc;
              }, {});
            } else if (prop.value.type === "ArrowFunctionExpression") {
              const { code } = generate(prop.value);
              value = new Function("theme", `return ${code}`)(mockTheme);
            } else if (prop.value.type === "Identifier") {
              value = `var(--${prop.value.name})`;
            }

            acc[key] = value;
            return acc;
          }, {});
        }

        const tsxDir = pathModule.dirname(filePath);
        const cssFilePath = pathModule.join(tsxDir, pathModule.basename(filePath, ".tsx") + ".css");

        // Generate import statement for CSS file
        const importStatement = `import './${pathModule.relative(tsxDir, cssFilePath)}';`;

        // Add import statement to the beginning of the file
        const newFileContent = importStatement + "\n\n" + fileContent;
        fs.writeFileSync(filePath, newFileContent, "utf8");

        const idAttr = path.parentPath.parentPath.node.openingElement.attributes.find(attr => attr.name?.name === "id");
        if (!idAttr) return;
        const selector = `#${idAttr.value.value}`;
        styles = sxToCss(sxObject, selector, styles);
      }
    }
  });

  return styles;
};

const scanDirectory = dirPath => {
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });

  entries.forEach(entry => {
    const fullPath = pathModule.join(dirPath, entry.name);
    console.log(`parseing ${fullPath}`);
    if (entry.isDirectory() && !entry.path.includes("node_modules")) {
      scanDirectory(fullPath);
    } else if (entry.isFile() && fullPath.endsWith(".tsx")) {
      const styles = processFile(fullPath);
      let mergedStyles = "";
      for (const selector in styles) {
        mergedStyles += `${selector} {\n`;
        for (const key in styles[selector]) {
          mergedStyles += `  ${key}: ${styles[selector][key]};\n`;
        }
        mergedStyles += `}\n`;
      }
      const cssFilePath = fullPath.replace(".tsx", ".css");
      fs.writeFileSync(cssFilePath, mergedStyles);
      console.log(`Styles extracted to ${cssFilePath}`);
    }
  });
};

const main = () => {
  const directoryPath = pathModule.resolve(__dirname, "src"); // Change this to your project's source directory
  scanDirectory(directoryPath);
};

main();
