"use client";

import { useEffect } from "react";
import useRole from "@/utils/hooks/useRole";
import { useRouter } from "next/navigation";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";

const DashboardPage: React.FC = () => {
  const router = useRouter();
  const userType = useRole();

  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      if (userType === "SUPPLIER") router.replace("/supplier");
      if (userType === "RETAILER") router.replace("/retailer");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userType]);

  return null;
};

export default WithBottomBar(DashboardPage);
