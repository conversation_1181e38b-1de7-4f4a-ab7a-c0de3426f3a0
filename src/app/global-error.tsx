"use client";

import * as Sen<PERSON> from "@sentry/nextjs";
import { useEffect } from "react";
import ErrorPage from "@/components/containers/ErrorPage/ErrorPage";

export default function GlobalError({ error }: { error: Error & { digest?: string } }) {
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  return (
    <html>
      <body>
        <ErrorPage />
      </body>
    </html>
  );
}
