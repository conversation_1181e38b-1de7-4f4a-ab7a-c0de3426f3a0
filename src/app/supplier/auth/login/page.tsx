"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { USER_TYPES } from "@/constants/userTypes";
import AuthEmailLogin from "@/app/auth/authForms/AuthEmaiLogin";
import AuthOtpLogin from "@/app/auth/authForms/AuthOtpLogin";
import AuthCheckUserName from "@/app/auth/authForms/AuthCheckUserName";

import "./page.css";
import { routes } from "@/constants/routes/index";
import { useTranslation } from "react-i18next";
import { Divider, Stack, Typography } from "@mui/material";
import Link from "next/link";

function Login() {
  const { t } = useTranslation();
  const router = useRouter();
  const [username, setUsername] = useState("");
  const [error, setError] = useState("");
  const [mode, setMode] = useState<"otp" | "password">("password");

  if (username && !error) {
    if (mode === "otp") {
      return (
        <AuthOtpLogin
          userType={USER_TYPES.SUPPLIER}
          username={username}
          setError={setError}
          onBack={() => setUsername("")}
          title={t("otpLogin.enterCode")}
          onSubmitted={() => router.replace(routes.home)}
          footer={
            <div className="flex justify-center mt-8 gap-2">
              {" "}
              <Link
                className="text-body3-medium text-v2-content-on-action-2"
                href={process.env.NODE_ENV === "development" ? routes.supplierChangePassword : routes.changePassword}
              >
                {t("emailLogin.forgetPassword")}
              </Link>
              <Divider orientation="vertical" variant="middle" flexItem className="email-login-divider" />
              <span className="text-body3-medium text-v2-content-tertiary" onClick={() => setMode("password")}>
                {t("emailLogin.loginWithPassword")}
              </span>
            </div>
          }
        />
      );
    } else if (mode === "password") {
      return (
        <AuthEmailLogin
          userType={USER_TYPES.SUPPLIER}
          username={username}
          onBack={() => setUsername("")}
          onChangeMode={() => setMode("otp")}
        />
      );
    }
  }
  return (
    <AuthCheckUserName
      userType={USER_TYPES.SUPPLIER}
      error={error}
      setError={setError}
      onChangeUsername={setUsername}
      title={t("checkUsername.title")}
      subtitle={t("checkUsername.subTitle")}
      footer={
        <div className="flex justify-center mt-8 gap-2">
          <span className="text-body3-medium text-v2-content-tertiary">{t("checkUsername.loginTitle")}</span>
          <Link
            className="text-body3-medium text-v2-content-on-action-2"
            href={process.env.NODE_ENV === "development" ? routes.supplierRegister : routes.register}
          >
            {t("checkUsername.subTitle2")}
          </Link>
        </div>
      }
    />
  );
}

Login.layout = "Blank";

export default Login;
