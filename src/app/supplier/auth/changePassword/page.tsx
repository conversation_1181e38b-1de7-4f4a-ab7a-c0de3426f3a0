"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { USER_TYPES } from "@/constants/userTypes";
import AuthOtpLogin from "@/app/auth/authForms/AuthOtpLogin";
import AuthCheckUserName from "@/app/auth/authForms/AuthCheckUserName";
import AuthForgetPassword from "@/app/auth/authForms/AuthForgetPassword";

import "./page.css";
import { useRouter } from "next/navigation";

function ChangePassword() {
  const router = useRouter();
  const { t } = useTranslation();
  const [otpCode, setOtpCode] = useState("");
  const [username, setUsername] = useState("");
  const [error, setError] = useState("");
  const [hasCode, setHasCode] = useState(false);

  const ChangePasswordForm = () => {
    if (username && !error) {
      if (hasCode) {
        return (
          <AuthForgetPassword
            otpCode={otpCode}
            userType={USER_TYPES.SUPPLIER}
            username={username}
            onBack={() => setUsername("")}
            title={t("changePassword.form.title")}
            subtitle={t("changePassword.form.subtitle")}
          />
        );
      }
      return (
        <AuthOtpLogin
          username={username}
          setError={setError}
          handleLogin={code => {
            setOtpCode(code);
            setHasCode(true);
          }}
          title={t("changePassword.otpCode.title")}
          userType={USER_TYPES.SUPPLIER}
          onSubmitted={() => setHasCode(true)}
          onBack={() => setUsername("")}
        />
      );
    }
    return (
      <AuthCheckUserName
        error={error}
        setError={setError}
        title={t("changePassword.title")}
        subtitle={t("changePassword.subtitle")}
        onBack={() => router.back()}
        buttonText={t("otpLogin.confirm")}
        userType={USER_TYPES.SUPPLIER}
        onChangeUsername={value => {
          setUsername(value);
        }}
      />
    );
  };

  return <ChangePasswordForm />;
}

ChangePassword.layout = "Blank";

export default ChangePassword;
