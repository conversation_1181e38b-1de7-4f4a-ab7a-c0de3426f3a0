#sx-page-18392 {
  text-decoration: none;
  color: rgb(var(--color-purple-500));
}
#sx-page-18429 {
  text-decoration: none;
  color: rgb(var(--color-purple-500));
}
#sx-page-18449 {
  max-width: 1600px;
  height: calc(100vh - 20px);
  margin: 10px auto;
  background: var(--mui-palette-grey-200);
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
#sx-page-18462 {
  position: relative;
}
#sx-page-18462:before {
  content: "''";
  position: absolute;
  left: -125px;
  bottom: -50px;
  width: 300px;
  height: 300px;
  border-radius: 100%;
  background-color: rgb(251, 151, 125);
}
#sx-page-18462:after {
  content: "";
  position: absolute;
  top: -65px;
  right: -60px;
  width: 304px;
  height: 315px;
  background-repeat: no-repeat;
  background: url("/images/backgrounds/shap-login.png");
}
#sx-page-18488 {
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 30px 15px rgba(37, 83, 185, 0.1);
  background-color: white;
  border-radius: 12px;
  max-width: 340px;
  margin: 0 15px;
}

@media (min-width: 768px) {
  #sx-page-18488 {
    margin: 0 20px;
    max-width: 500px;
  }
}
@media (min-width: 1200px) {
  #sx-page-18488 {
    margin: 50px auto;
    max-width: 1320px;
  }

  #sx-page-18510 {
    padding-inline: 32px;
  }

  #sx-page-18544 {
    margin-block-end: 54px;
  }
}

#sx-page-18519 {
  margin-bottom: var(--mui-theme-spacing-4);
}
#sx-page-18530 {
  width: 500px;
  height: 500px;
  max-width: 100%;
  border-radius: 0;
  margin: 0 auto;
}

#sx-page-image-18530 {
  object-fit: scale-down;
}
