"use client";

import Header from "@/components/containers/header/Header";
import SupplierOnboarding from "./onboarding/SupplierOnboarding";
import { useTranslation } from "react-i18next";

export default function HomePage() {
  const { t } = useTranslation();
  return (
    <>
      <Header title={t("dashboard")} isMobile isSticky />
      <div className="xmd:p-0 p-4">
        <SupplierOnboarding />
      </div>
    </>
  );
}
