export type Order = "asc" | "desc";

export interface HeadCell {
  disablePadding: boolean;
  id: string;
  label: string;
  hasSort?: boolean;
  numeric: boolean;
}

export interface EnhancedTableProps {
  numSelected: number;
  onRequestSort: (event: React.MouseEvent<unknown>, property: any) => void;
  onSelectAllClick: (event: React.ChangeEvent<HTMLInputElement>) => void;
  order: Order;
  orderBy: string;
  rowCount: number;
}

export type TOrdersFilters = {
  filters: {
    id?: string;
    order_number?: string;
    shipping_location?: string;
    first_name?: string;
    last_name?: string;
    phone_number?: string;
    email?: string;
    created_from?: string;
    created_to?: string;
    updated_from?: string;
    updated_to?: string;
    state?: "Pending" | "InProgress" | "Done" | "Refunded" | "Cancelled";
  };
  sort: {
    created_at?: "asc" | "desc";
    updated_at?: "asc" | "desc";
  };
};
