import React, { ReactNode } from "react";
import { useTranslation } from "react-i18next";
import { useFilters } from "./useFilters";
import InputFilter from "@/components/containers/Filters/InputFilter";
import LocationSelectFilter from "@/components/containers/Filters/LocationSelectFilter";
import RangeDatepickerFilter from "@/components/containers/Filters/RangeDatepickerFilter";
import DropdownFilter from "@/components/containers/Filters/DropdownFilter";
import { Icon } from "@iconify/react";
import Input from "@/components/ui/inputs/Input";
import Button from "@/components/ui/Button";
import { handleSetFilter, omitEmptyValues } from "@/utils/helpers";

interface ISupplierOrderFiltersProps {
  RenderStartAdornment?: ReactNode;
  RenderEndAdornment?: ReactNode;
}

const SupplierOrderFilters = ({ RenderEndAdornment, RenderStartAdornment }: ISupplierOrderFiltersProps) => {
  const { t } = useTranslation();
  const { setFilters, filters, sorts: sortsStates } = useFilters();
  const { created_at, updated_at } = sortsStates || {};
  const hasFilters = !!Object.values(omitEmptyValues(filters))?.filter(Boolean)?.length;

  const handleResetAll = () => {
    const keys = !!filters ? Object.keys(filters) : [];

    keys?.forEach(item => setFilters({ [item]: null }));
  };

  const onReset = (key: string | string[]) => {
    if (Array.isArray(key) && key?.length) {
      key?.forEach(item => {
        setFilters({ [item]: null }, { history: "push" });
      });
    } else setFilters({ [key as any]: null }, { history: "push" });
  };

  return (
    <div className="flex flex-col gap-2">
      <Input
        startAdornment={
          <Icon icon="solar:magnifer-linear" width="1.1rem" color="#ADADAD" height="1.1rem" className="ml-1.5" />
        }
        inputParentClassName="bg-cards xmd:border-v2-border-primary border-transparent"
        rootClassName="shrink-0 flex-1"
        value={filters?.order_number || undefined}
        placeholder={`${t("chats.searchQuery")} ...`}
        onChange={e => handleSetFilter({ key: "order_number", value: e.target.value as any, setFilters })}
        // setFilters({ order_number: e.target.value as any }, { history: "push" })}
        requiredStar={false}
      />

      {RenderStartAdornment && RenderStartAdornment}

      <div className="flex justify-between items-start gap-4">
        <div className="flex items-center gap-2 flex-wrap">
          <InputFilter
            title={t("order.placeholder.id")}
            filterKey="id"
            initialValue={filters?.id}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />
          <LocationSelectFilter
            title={t("order.shippingLocation")}
            placeholder={t("order.placeholder.shippingLocation")}
            filterKey="shipping_location"
            initialValue={filters?.shipping_location}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />
          <InputFilter
            title={t("order.placeholder.firstName")}
            filterKey="first_name"
            initialValue={filters?.first_name}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />
          <InputFilter
            title={t("order.lastName")}
            filterKey="last_name"
            initialValue={filters?.last_name}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />
          <InputFilter
            title={t("order.phoneNumber")}
            filterKey="phone_number"
            initialValue={filters?.phone_number}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />
          <InputFilter
            title={t("order.email")}
            filterKey="email"
            initialValue={filters?.email}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />
          <RangeDatepickerFilter
            title={t("product.filterItems.createdDate")}
            filterKey={["created_from", "created_to"]}
            initialValue={[filters?.created_from || "", filters?.created_to || ""]}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />
          <RangeDatepickerFilter
            title={t("product.filterItems.updatedDate")}
            filterKey={["updated_from", "updated_to"]}
            initialValue={[filters?.updated_from || "", filters?.updated_to || ""]}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />

          <DropdownFilter
            title={t("order.sortByCreatedAt")}
            filterKey="created_at"
            icon={<Icon icon="solar:sort-outline" width={18} height={18} />}
            initialValue={created_at}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
            options={[
              { id: "desc", label: t(`retailerProduct.filters.newest`) },
              { id: "asc", label: t(`retailerProduct.filters.oldest`) }
            ]}
          />
          <DropdownFilter
            title={t("order.sortByUpdatedAt")}
            filterKey="updated_at"
            icon={<Icon icon="solar:sort-outline" width={18} height={18} />}
            initialValue={updated_at}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
            options={[
              { id: "asc", label: t(`retailerProduct.filters.newest`) },
              { id: "desc", label: t(`retailerProduct.filters.oldest`) }
            ]}
          />

          {hasFilters && (
            <>
              <div className="w-px h-4 bg-v2-border-primary" />

              <div className="text-v2-content-on-action-2 text-xs cursor-pointer" onClick={handleResetAll}>
                {t("removeFilters")}
              </div>
            </>
          )}
        </div>

        {RenderEndAdornment && RenderEndAdornment}
      </div>
    </div>
  );
};

export default SupplierOrderFilters;
