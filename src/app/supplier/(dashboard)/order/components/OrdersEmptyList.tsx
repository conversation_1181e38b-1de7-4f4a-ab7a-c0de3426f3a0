import { Theme } from "@mui/material";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useMediaQuery } from "@mui/system";
import Image from "next/image";
import { useTranslation } from "react-i18next";

import "./supplierOrder.css";

function OrdersEmptyList() {
  const { t } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  return (
    <div className="flex items-center flex-col">
      <Image
        // className="order-list-empty-image"
        src="/images/svgs/orderEmpty.svg"
        width={isMobile ? 100 : 200}
        height={isMobile ? 100 : 200}
        alt="order-empty-list"
      />
      <div className="flex flex-col gap-2 xmd:mt-6 mt-3 items-center">
        <span className="text-h5-bold text-v2-content-primary">{t("supplierOrder.empty.title")}</span>
        <span className="text-v2-content-tertiary text-body3-medium">{t("supplierOrder.empty.subtitle")}</span>
      </div>
    </div>
  );
}

export default OrdersEmptyList;
