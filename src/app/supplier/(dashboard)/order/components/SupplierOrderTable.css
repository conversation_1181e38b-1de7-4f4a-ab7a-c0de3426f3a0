#sx-supplierordertable-16559 {
  min-width: 750px;
}

.supplier-order-table-header-item {
  font-size: 13px;
  font-weight: 500;
  color: var(--mui-palette-grey-700);
}

.supplier-order-table-header-item-value {
  font-size: 14px;
  font-weight: 500;
  color: rgb(var(--color-gray-600));
}

.supplier-order-table-header-item-table-cell {
  padding: 16px 0;
}

.supplier-order-table-header-item-value-table-cell {
  padding: 16px 0;
}

.supplier-order-table-header-item-table-row {
  padding-inline: 12px;
}

.supplier-order-table-title-mobile {
  font-size: 14px;
  font-weight: 700;
}

.supplier-order-table-subtitle-mobile {
  font-size: 12px;
  font-weight: 500;
  color: var(--mui-palette-grey-400);
}

.supplier-order-table-wrapper-mobile {
  display: none;
  padding-top: 50px;
  padding-inline: 16px;
}

.supplier-order-tablecontainer1 {
  height: calc(100vh - 360px);
  overflow: auto;
  /* padding-inline: 10px; */
  margin-bottom: 24px;
}

.supplier-order-table-container-mobile {
  background-color: rgb(var(--color-cards));
  border-radius: 8px;
  padding: 16px;
}

@media (max-width: 768px) {
  .supplier-order-table-container {
    display: none;
  }

  .supplier-order-table-wrapper-mobile {
    display: block;
  }
}

.supplier-order-table-title-wrapper-mobile {
  border-bottom: 1px solid var(--mui-palette-grey-200);
  padding-block-end: 10px;
}

.supplier-order-table-destination-mobile {
  border-bottom: 1px solid var(--mui-palette-grey-200);
  /* padding-block-end: 10px; */
}

.supplier-order-table-status-mobile {
  border: 1px solid var(--mui-palette-grey-200);
  padding: 16px;
  border-radius: 8px;
}

.supplier-order-table-action-mobile {
  display: flex;
  align-items: center;
  gap: 4px;
  padding-block-start: 12px;
  padding-inline-start: 12px;
}

.supplier-order-table-action-title-mobile {
  font-size: 12px;
  font-weight: 700;
  color: rgb(var(--color-purple-500));
}
