import "./utils.css";

import { CircularProgress } from "@mui/material";
import { Typography } from "@mui/material";
import { TableCell } from "@mui/material";
import { TableRow } from "@mui/material";
import { IRenderTableRowEndProps } from "./types";
import i18n from "@/utils/i18n";
import { useTranslation } from "react-i18next";

export const a11yProps = (index: number) => {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`
  };
};

export const headerItems = [
  {
    id: 1,
    title: i18n.t("supplierOrder.orderSummary.product")
  },
  {
    id: 2,
    title: i18n.t("supplierOrder.orderSummary.state")
  },
  // {
  //   id: 3,
  //   title: i18n.t("supplierOrder.orderSummary.sku")
  // },
  {
    id: 4,
    title: i18n.t("supplierOrder.orderSummary.qty")
  },
  {
    id: 5,
    title: i18n.t("supplierOrder.orderSummary.unitPrice")
  },
  {
    id: 6,
    title: i18n.t("supplierOrder.orderSummary.total")
  }
];

export const RenderTableRowEnd = ({ children }: IRenderTableRowEndProps) => {
  const lineItemsCount = headerItems?.length;

  return (
    <TableRow id="sx-utils-15465">
      {Array.from(Array(lineItemsCount - 2).keys()).map(item => (
        <TableCell key={item} />
      ))}
      {children}
    </TableRow>
  );
};

export const RenderOrderStateValue = ({ value, isLoading }: { value: string; isLoading: boolean }) => {
  const { t } = useTranslation();
  if (isLoading) return <CircularProgress size={16} id="sx-utils-15480" />;
  return (
    <Typography variant="caption">{t(`supplierOrder.lineItemsOrderStateItems.${value.toLowerCase()}`)}</Typography>
  );
};

export const orderStatusVariants = {
  Pending: {
    bgColor: "#FFEBD8",
    color: "#D26500",
    icon: "hugeicons:loading-03"
  },
  Authorized: {
    bgColor: "#EDFDF4",
    color: "#18B466",
    icon: "solar:bill-check-outline"
  },
  Captured: {
    bgColor: "#EDFDF4",
    color: "#18B466",
    icon: "solar:bill-check-outline"
  },
  InProgress: {
    bgColor: "#FFEBD8",
    color: "#D26500",
    icon: "hugeicons:loading-03"
  },
  ShippedPartially: {
    bgColor: "#FFEBD8",
    color: "#D26500",
    icon: "hugeicons:loading-03"
  },
  Paid: {
    bgColor: "#EDFDF4",
    color: "#18B466",
    icon: "solar:bill-check-outline"
  },
  Done: {
    bgColor: "#EDFDF4",
    color: "#18B466",
    icon: "solar:check-circle-outline"
  },
  Shipped: {
    bgColor: "#E6FBF4",
    color: "#04D294",
    icon: "solar:delivery-outline"
  },
  Refunded: {
    bgColor: "rgb(var(--color-gray-20))",
    color: "rgb(var(--color-gray-400))",
    icon: "solar:revote-outline"
  },
  RefundedPartially: {
    bgColor: "#FFEBD8",
    color: "#D26500",
    icon: "solar:revote-outline"
  },
  Cancelled: {
    bgColor: "#FDE8E6",
    color: "#A31304",
    icon: "iconamoon:close-light"
  },
  Voided: {
    bgColor: "#FDE8E6",
    color: "#A31304",
    icon: "solar:forbidden-circle-outline"
  },
  Failed: {
    bgColor: "#FDE8E6",
    color: "#A31304",
    icon: "solar:danger-outline"
  }
};
