"use client";
import AclWrapper from "@/components/containers/AclWrapper";
import SupplierOrderDetail from "./SupplierOrderDetail";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import { useTranslation } from "react-i18next";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import React from "react";

const OrderDetailPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <AclWrapper for="SUPPLIER">
      <MobileAppBar title={t("orderDetail")} hasBack />
      <SupplierOrderDetail />
    </AclWrapper>
  );
};

export default WithBottomBar(OrderDetailPage);
