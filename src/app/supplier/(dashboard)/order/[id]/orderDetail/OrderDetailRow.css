#sx-ordersummaryrow-15907 > * {
  border-bottom: 1px solid rgb(var(--color-gray-50));
}
#sx-ordersummaryrow-15923 {
  width: 46px;
  height: 46px;
  border-radius: 6px;
}
#sx-ordersummaryrow-15940 {
  margin-inline: var(--mui-theme-spacing-1);
}
#sx-ordersummaryrow-15966 {
  width: 150px;
}

.supplier-order-detail-table-line-item-title {
  font-size: 14px;
  font-weight: 500;
}

.supplier-order-detail-table-line-items-select .MuiOutlinedInput-root {
  width: 170px !important;
  /* max-height: 32px; */
  font-size: 12px;
}

.supplier-order-detail-table-line-items-select .MuiFormHelperText-root {
  display: none;
}

.supplier-order-detail-row-mobile {
  display: none;
}

.supplier-order-detail-row-mobile-qty-title {
  font-size: 12px;
  font-weight: 400;
  color: rgb(var(--color-gray-400));
}

.supplier-order-detail-row-mobile-qty-value {
  font-size: 12px;
  font-weight: 400;
}

.supplier-order-detail-row-line-items-price {
  font-size: 14px;
  font-weight: 700;
}

@media (max-width: 768px) {
  #sx-ordersummaryrow-15907 {
    display: none;
  }

  .supplier-order-detail-row-mobile {
    display: block;
  }
}

/* .supplier-order-detail-table-line-items-select .MuiOutlinedInput-root.MuiInputBase-sizeSmall{

} */
