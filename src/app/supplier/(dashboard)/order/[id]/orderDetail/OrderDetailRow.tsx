"use client";

import "./OrderDetailRow.css";

import { useTranslation } from "react-i18next";
import useCurrency from "@/utils/hooks/useCurrency";
import { Box, CircularProgress, Theme, useMediaQuery } from "@mui/material";
import { Divider } from "@mui/material";
import { Stack } from "@mui/material";
import { TableCell } from "@mui/material";
import { TableRow } from "@mui/material";
import { Typography } from "@mui/material";
import ImageIcon from "@mui/icons-material/Image";
import Image from "next/image";
import Link from "next/link";
import { routes } from "@/constants/routes";
import useLanguage from "@/utils/hooks/useLanguage";
import FulfillmentDialog from "./modals/FulfillmentDialog";
import { ShipmentDetail, TOrderLineItem, TOrderState } from "@/store/apps/order/types";
import { usePutOrderStateMutation } from "@/store/apps/order";
import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import useModal from "@/utils/hooks/useModal";
import RejectDialog from "./modals/RejectDialog";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { useEffect } from "react";

interface IOrderSummaryRowProps {
  item: TOrderLineItem;
  isLastItem?: boolean;
  shipmentDetail?: ShipmentDetail;
  lineItems?: TOrderLineItem[];
}
const OrderDetailRow = ({ item, isLastItem, shipmentDetail, lineItems }: IOrderSummaryRowProps) => {
  const { t } = useTranslation();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };
  const [{ renderDate }] = useLanguage();
  const { showModal } = useModal();
  const makePath = useRoleBasePath();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const otherLineItemStates = lineItems?.filter(val => val?.id !== item?.id)?.map(val => val?.state === "InProgress");

  const formattedOptions = Object.keys(item?.options)?.length
    ? Object.entries(item?.options)
        .map(([_, value]) => value)
        .join(", ")
    : "";

  const orderStateItemValue = item.state
    ? { id: item.state, label: t(`supplierOrder.lineItemsOrderStateItems.${item?.state?.toLowerCase()}`) }
    : {};

  const orderStateOptions = !!item?.availableStates?.length
    ? item?.availableStates?.map(item => ({
        id: item,
        label: t(`supplierOrder.lineItemsOrderStateItems.${item?.toLowerCase()}`)
      }))
    : [];

  const orderStateItems = [...orderStateOptions, orderStateItemValue];

  const [updateOrderState, { isLoading }] = usePutOrderStateMutation();

  const onUpdateOrderState = async (value: string) => {
    const body = {
      state: value as TOrderState
    };
    if (value === "Canceled") {
      showModal({
        width: isMobile ? undefined : 450,
        body: <RejectDialog orderState="Canceled" orderData={item} />,
        modalProps: {
          showCloseIcon: false
        }
      });
      return;
    }

    if (value === "Fulfilled" && !otherLineItemStates?.length) {
      showModal({
        width: isMobile ? undefined : 450,
        body: <FulfillmentDialog orderState="Fulfilled" orderId={item?.orderId} shipmentDetail={shipmentDetail} />,
        modalProps: {
          showCloseIcon: false
        }
      });
    }

    try {
      const res = await updateOrderState({ oid: item?.orderId, vid: item?.variantId, body });

      if ((res as any)?.error) {
        clientDefaultErrorHandler({ error: (res as any)?.error });
      }

      if ("data" in res && res.data) {
      }
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  return (
    <>
      {/* mobile */}
      <Box
        className="supplier-order-detail-row-mobile"
        borderBottom={isLastItem ? "" : "1px solid rgb(var(--color-gray-50))"}
        pb={isLastItem ? 0 : 2}
        mb={isLastItem ? 0 : 2}
        mt={2}
      >
        <Stack flexDirection="row" alignItems="center" flexWrap="wrap" justifyContent="space-between" rowGap={2}>
          <Stack direction="row" gap={1} alignItems="center">
            {item?.product?.cover?.url ? (
              <Image
                src={item?.product?.cover?.url}
                alt={item?.product?.cover?.alt}
                width={46}
                height={46}
                style={{
                  borderRadius: "6px"
                }}
              />
            ) : (
              <ImageIcon color="secondary" id="sx-ordersummaryrow-15923" />
            )}
            <Box>
              <Link href={`${makePath(routes.product)}/edit/${item.product?.id}`} target="_blank">
                <Typography className="supplier-order-detail-table-line-item-title">
                  {item?.title}{" "}
                  {formattedOptions ? (
                    <Typography color="textSecondary" variant="caption">
                      ({formattedOptions})
                    </Typography>
                  ) : (
                    ""
                  )}
                </Typography>
              </Link>

              {/* <Stack flexDirection="row" alignItems="center" mb={0.5}>
                <Stack flexDirection="row" alignItems="center" gap={0.5}>
                  <Typography whiteSpace="nowrap" color="textSecondary" variant="caption" textAlign="center">
                    {t("supplierOrder.orderSummary.supplier")}:
                  </Typography>
                  <Typography
                    component={Link}
                    target="_blank"
                    href={`${makePath(routes.product)}?supplier_id=${item?.product.supplier.id}`}
                    whiteSpace="nowrap"
                    color="textSecondary"
                    variant="caption"
                  >
                    {item?.product?.supplier?.name ?? "-"}
                  </Typography>
                </Stack>

                <Icon icon="mage:message-dots" width={16} height={16} cursor="pointer" onClick={onRedirectToChat} />
              </Stack> */}

              <Stack direction="row" alignItems="center">
                <Typography whiteSpace="nowrap" color="textSecondary" variant="caption">
                  {item?.shippingTime?.min} {t("supplierOrder.orderSummary.to")} {item?.shippingTime?.max}{" "}
                  {t("supplierOrder.orderSummary.dayWorking")}
                </Typography>

                {!!item?.shippingCarrier && (
                  <>
                    <Divider orientation="vertical" flexItem id="sx-ordersummaryrow-15940" />
                    <Typography whiteSpace="nowrap" color="textSecondary" variant="caption">
                      {t("supplierOrder.orderSummary.deliveredBy")} {item?.shippingCarrier}
                    </Typography>
                  </>
                )}

                {/* <Divider orientation="vertical" flexItem id="sx-ordersummaryrow-15940" /> */}
              </Stack>
            </Box>
          </Stack>

          <Typography className="supplier-order-detail-row-line-items-price" whiteSpace="nowrap">
            {renderPrice(item?.listPrice)}
          </Typography>
        </Stack>

        <div className="flex flex-col mt-6">
          {/* <Stack flexDirection="row" alignItems="center" gap={0.5}>
            <Typography className="supplier-order-detail-row-mobile-qty-title">
              {t("supplierOrder.orderSummary.qty")}:
            </Typography>

            <Typography className="supplier-order-detail-row-mobile-qty-value">{item?.quantity || "0"}</Typography>
          </Stack> */}

          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center py-1 px-3 pl-6 border border-v2-border-primary text-body4-medium text-v2-content-primary rounded-md gap-0.5">
              <span>{item?.listPrice}</span> <span>x</span> <span>{item?.quantity || "0"}</span>
            </div>

            <div>
              <span className="text-body4-medium text-v2-content-primary">
                {renderPrice(item?.listPrice * item?.quantity)}
              </span>
            </div>
          </div>

          <div>
            {isLoading ? (
              <Stack alignItems="center">
                <CircularProgress size={16} />
              </Stack>
            ) : (
              <CustomAutocomplete
                readOnlyInput
                startAdornment={
                  <div className="flex h-[36px] items-center ml-2">
                    <p className=" text-body4-medium text-v2-content-tertiary items-center pe-2">{t("status")}</p>
                    <Divider orientation="vertical" flexItem variant="fullWidth" />
                  </div>
                }
                className="supplier-order-detail-table-line-items-select"
                inputParentClassName="!h-[36px] "
                options={orderStateItems}
                size="small"
                value={orderStateItems?.find(val => val.id === item.state)}
                onChange={(e, value) => onUpdateOrderState(value?.id as string)}
              />
              // <CustomAutocomplete
              //   readOnlyInput
              //   className="supplier-order-detail-table-line-items-select"
              //   options={orderStateItems}
              //   size="small"
              //   value={orderStateItems?.find(val => val.id === item.state)}
              //   onChange={(e, value) => onUpdateOrderState(value?.id as string)}
              // />
            )}
          </div>
        </div>
      </Box>

      {/* desktop */}
      <div className="xmd:flex hidden items-center justify-between gap-4 pb-3 border-b border-b-v2-border-secondary last-of-type:border-b-transparent">
        <div>
          <div className="flex items-center gap-4">
            {item?.product?.cover?.url ? (
              <Image
                src={item?.product?.cover?.url}
                alt={item?.product?.cover?.alt}
                width={80}
                height={80}
                style={{
                  borderRadius: "8px"
                }}
              />
            ) : (
              <ImageIcon color="secondary" className="rounded-lg size-20" />
            )}
            <div>
              <Link href={`${makePath(routes.product)}/edit/${item.product?.id}`} target="_blank">
                <span className="text-body3-medium text-v2-content-primary">
                  {item?.title}{" "}
                  {formattedOptions ? (
                    <span className="text-body4-medium text-v2-content-tertiary">({formattedOptions})</span>
                  ) : (
                    ""
                  )}
                </span>
              </Link>

              {/* <Stack flexDirection="row" alignItems="center" mb={0.5}>
                <Stack flexDirection="row" alignItems="center" gap={0.5}>
                  <Typography whiteSpace="nowrap" color="textSecondary" variant="caption" textAlign="center">
                    {t("supplierOrder.orderSummary.supplier")}:
                  </Typography>
                  <Typography
                    component={Link}
                    target="_blank"
                    href={`${makePath(routes.product)}?supplier_id=${item?.product.supplier.id}`}
                    whiteSpace="nowrap"
                    color="textSecondary"
                    variant="caption"
                  >
                    {item?.product?.supplier?.name ?? "-"}
                  </Typography>
                </Stack>

                <Icon icon="mage:message-dots" width={16} height={16} cursor="pinter" onClick={onRedirectToChat} />
              </Stack> */}

              <div className="flex items-center mt-4">
                <span className="whitespace-nowrap text-v2-content-secondary text-caption-regular">
                  {item?.shippingTime?.min} {t("supplierOrder.orderSummary.to")} {item?.shippingTime?.max}{" "}
                  {t("supplierOrder.orderSummary.dayWorking")}
                </span>

                {!!item?.shippingCarrier && (
                  <>
                    <Divider orientation="vertical" flexItem id="sx-ordersummaryrow-15940" />
                    <span className="whitespace-nowrap text-v2-content-secondary text-caption-regular">
                      {t("supplierOrder.orderSummary.deliveredBy")} {item?.shippingCarrier}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-[210px]">
          {/* <Typography color="textSecondary" variant="caption">
                      {supplierOrder.orderStateItems.${item?.state.toLocaleLowerCase()}`)}
                    </Typography> */}
          {/* <CustomSelect
            id="orderStateItems"
            MenuProps={{
              style: {
                maxHeight: "400px"
              }
            }}
            renderValue={value => {
              const inputValue = value as string;
              return <RenderOrderStateValue value={inputValue} isLoading={isSupplierOrderFetching} />;
            }}
            name="orderStateItems"
            value={item?.state}
            onChange={({ target: { value } }) => onUpdateOrderState(value as string)}
          >
            {orderStates?.data?.map(item => (
              <MenuItem key={item} value={item}>
                {supplierOrder.lineItemsOrderStateItems.${item.toLocaleLowerCase()}`)}
              </MenuItem>
            ))}
          </CustomSelect> */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center py-1 px-3 pl-6 border border-v2-border-primary text-body4-medium text-v2-content-primary rounded-md gap-0.5">
              <span>{item?.listPrice}</span> <span>x</span> <span>{item?.quantity || "0"}</span>
            </div>

            <div>
              <span className="text-body4-medium text-v2-content-primary">
                {renderPrice(item?.listPrice * item?.quantity)}
              </span>
            </div>
          </div>

          <div>
            {isLoading ? (
              <Stack alignItems="center" maxWidth={120}>
                <CircularProgress size={16} />
              </Stack>
            ) : (
              <CustomAutocomplete
                readOnlyInput
                startAdornment={
                  <div className="flex h-[36px] items-center ml-2">
                    <p className=" text-body4-medium text-v2-content-tertiary items-center pe-2">{t("status")}</p>
                    <Divider orientation="vertical" flexItem variant="fullWidth" />
                  </div>
                }
                className="supplier-order-detail-table-line-items-select"
                inputParentClassName="!h-[36px] "
                options={orderStateItems}
                size="small"
                value={orderStateItems?.find(val => val.id === item.state)}
                onChange={(e, value) => onUpdateOrderState(value?.id as string)}
              />
            )}
          </div>
        </div>

        {/* <TableCell>
          <Typography color="textSecondary" variant="caption">
            {numberTransformer(item?.product?.sku ?? "")}
          </Typography>
        </TableCell> */}
      </div>
    </>
  );
};

export default OrderDetailRow;
