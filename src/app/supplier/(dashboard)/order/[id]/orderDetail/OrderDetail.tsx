"use client";

import "./OrderDetail.css";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Box, CircularProgress, Divider, Stack, Theme, Typography, useMediaQuery } from "@mui/material";
import OrderDetailTable from "./OrderDetailTable";
import useLanguage from "@/utils/hooks/useLanguage";

import { useGetOrderQuery } from "@/store/apps/order";
import useLocations from "@/utils/hooks/useLocations";
import { Icon } from "@iconify/react";
import OrderStatus, { IOrderStatusItems } from "./OrderStatus";
import clsx from "clsx";
import OrderDetailRow from "./OrderDetailRow";
import useCurrency from "@/utils/hooks/useCurrency";
import isNumber from "lodash/isNumber";
import FulfillmentDialog from "./modals/FulfillmentDialog";
import useModal from "@/utils/hooks/useModal";
import { ensureUrlScheme } from "@/utils/helpers";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import DownloadInvoice from "@/components/containers/orders/DownloadInvoice";
import Link from "next/link";
import useClipboard from "@/utils/hooks/useClipboard";
import { twMerge } from "tailwind-merge";
import { mockOrderData } from "../../components/data";
import MobileOrderDetail from "./MobileOrderDetail";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";

const OrderDetail = () => {
  const [{ renderDate }] = useLanguage();
  const { t } = useTranslation();
  const params = useParams();
  const id = params?.id as string;
  const [curr] = useCurrency();
  const { showModal } = useModal();
  const { render: renderPrice } = curr ?? { render: v => v };
  const { getLocation } = useLocations();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const { isCopied, copyToClipboard } = useClipboard();
  const makePath = useRoleBasePath();
  const router = useRouter();

  const {
    data: supplierOrder,
    isLoading: isSupplierOrderLoading,
    isFetching: isSupplierOrderFetching
  } = useGetOrderQuery({ id }, { skip: !id });

  const subTotal = supplierOrder?.data?.lineItems?.reduce((acc, item) => acc + item.quantity * item.listPrice, 0) ?? 0;
  const vat = 0;
  const shippingCost =
    supplierOrder?.data?.lineItems?.reduce(
      (acc, item) => acc + (isNumber(item.shippingCost) ? item.shippingCost : +item.shippingCost),
      0
    ) ?? 0;

  const handleTrackingCode = () => {
    showModal({
      width: isMobile ? undefined : 450,
      body: (
        <FulfillmentDialog
          orderState="Fulfilled"
          orderId={supplierOrder?.data?.id}
          shipmentDetail={supplierOrder?.data?.shipmentDetails?.[0]}
        />
      ),
      modalProps: {
        showCloseIcon: false
      }
    });
  };

  const onBack = () => {
    const hasHistory = typeof window !== "undefined" && window.history.length > 1;

    if (hasHistory) {
      router.back();
    } else {
      router.push(makePath(routes.order));
    }
  };

  if (isSupplierOrderLoading) {
    return (
      <Box id="sx-ordersummary-15641">
        <CircularProgress />
      </Box>
    );
  }
  if (isMobile) {
    return (
      <MobileOrderDetail
        handleTrackingCode={handleTrackingCode}
        shippingCost={shippingCost}
        subTotal={subTotal}
        supplierOrder={supplierOrder}
        vat={vat}
        isSupplierOrderFetching={isSupplierOrderFetching}
      />
    );
  }

  return (
    <>
      <div className="xmd:flex hidden items-center justify-between border-b border-b-gray-40 pb-7">
        <div onClick={onBack} className="items-center gap-2 flex cursor-pointer">
          <Icon icon="solar:arrow-right-outline" className=" size-6" />
          <span className="text-subtitle-bold text-v2-content-primary">
            {t("supplierOrder.orderSummary.orderDetails")}
          </span>
        </div>
        {/* <div className="flex items-center rounded-lg bg-gray-40 gap-2 p-2">
          <Icon
            icon="solar:alt-arrow-right-outline"
            className={twMerge("size-4", isDisabledNext ? "text-gray-600" : "text-gray-999")}
          />
          <Icon
            icon="solar:alt-arrow-left-outline"
            className={twMerge("size-4", isDisabledPrev ? "text-gray-600" : "text-gray-999")}
          />
        </div> */}
      </div>

      <div className="flex items-center justify-between mt-4 flex-wrap">
        <div className="flex items-center text-h4-bold text-v2-content-primary flex-wrap gap-4">
          <div className="flex items-center">
            <span className="whitespace-nowrap">{t("supplierOrder.orderSummary.orderNumber")} : </span>
            <span className="mr-1">{supplierOrder?.data?.orderNumber ?? ""}</span>#
          </div>
          <div>
            {!!supplierOrder?.data?.state && (
              <OrderStatus
                id={supplierOrder?.data?.state as IOrderStatusItems["id"]}
                title={t(`supplierOrder.orderStateItems.${supplierOrder?.data?.state?.toLowerCase()}`)}
              />
            )}
          </div>
        </div>

        <div className="flex items-center gap-1">
          {supplierOrder?.data?.state === "Done" && (
            <div
              onClick={handleTrackingCode}
              className="flex items-center gap-1.5 px-5 py-2.5 text-v2-content-primary cursor-pointer"
            >
              <Icon icon="solar:scanner-outline" className="size-3.5 text-v2-content-primary" />
              <span className="text-body4-medium text-v2-content-primary">{t("order.trackingCode")}</span>
            </div>
          )}

          {supplierOrder?.data?.state === "Done" &&
            (supplierOrder?.data?.paymentState === "Paid" || supplierOrder?.data?.paymentState === "Captured") && (
              <Divider orientation="vertical" variant="middle" flexItem className="bg-gray-50" />
            )}

          <div className="flex items-center gap-1.5 xmd:px-5 xmd:py-2.5 text-purple-500">
            <Icon icon="solar:printer-outline" className="size-4 text-purple-500" />
            <span className="text-body4-medium text-v2-content-on-info">{t("downloadLabel")}</span>
          </div>

          <Divider orientation="vertical" variant="middle" flexItem className="bg-gray-50" />

          {(supplierOrder?.data?.paymentState === "Paid" || supplierOrder?.data?.paymentState === "Captured") && (
            <DownloadInvoice
              orderId={id}
              orderNumber={supplierOrder?.data?.orderNumber}
              printCustomerTitle={t("supplierOrder.orderSummary.printCustomerInvoice")}
              printSellerTitle={t("supplierOrder.orderSummary.printSellerInvoice")}
            />
          )}
        </div>
      </div>

      <div className="flex items-center gap-2 mt-1">
        <Icon icon="solar:calendar-outline" className="size-4" />
        {!!supplierOrder?.data?.createdAt && (
          <div className="flex items-center gap-2 ">
            <span className="text-body3-medium text-v2-content-tertiary">
              {t("supplierOrder.orderSummary.orderAt")} :{" "}
            </span>
            <span className="text-body3-medium text-v2-content-tertiary">
              {!!supplierOrder?.data?.createdAt && renderDate(supplierOrder?.data?.createdAt, "HH:mm - YYYY/MM/DD")}
            </span>
          </div>
        )}
      </div>

      <div className="mt-4 flex gap-4 flex-wrap">
        <div className="flex-1">
          <div className="border border-v2-border-primary rounded-lg p-6">
            <div className="flex items-center gap-2 pb-6 border-b border-b-v2-border-secondary">
              <Icon icon="solar:cart-3-outline" className="size-6" />
              <span className="text-v2-content-primary text-body2-bold font-semibold">
                {t("supplierOrder.orderSummary.addedOrder")}
              </span>
            </div>

            <div className="pt-3">
              {supplierOrder?.data?.lineItems?.map((item, index) => (
                <OrderDetailRow
                  item={item}
                  shipmentDetail={supplierOrder?.data?.shipmentDetails?.find(
                    sItem => sItem?.supplierId === item?.supplierId
                  )}
                  key={item.orderId}
                  isLastItem={(supplierOrder?.data?.lineItems?.length || 0) - 1 === index}
                  lineItems={supplierOrder?.data?.lineItems}
                />
              ))}
            </div>
          </div>

          <div className="border border-v2-border-primary rounded-lg p-6 mt-2.5">
            <div className="flex items-center gap-2 ">
              <Icon icon="solar:card-outline" className="size-6" />
              <span className="text-v2-content-primary text-body2-bold font-semibold">
                {t("supplierOrder.orderSummary.payInfo.title")}
              </span>
              <div className="mr-2">
                {!!supplierOrder?.data?.paymentState && (
                  <OrderStatus
                    id={supplierOrder?.data?.paymentState}
                    title={t(`supplierOrder.paymentStateItems.${supplierOrder?.data?.paymentState?.toLowerCase()}`)}
                  />
                )}
              </div>
            </div>
            <p className="text-body4-regular text-v2-content-primary mt-2">
              {t("supplierOrder.orderSummary.payInfo.subtitle")}
            </p>

            <div className="mt-6">
              <div className="flex items-center justify-between">
                <div className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("supplierOrder.orderSummary.grandTotal")}
                </div>
                <div className="flex-1 text-body4-medium text-v2-content-tertiary">
                  {supplierOrder?.data?.lineItems?.reduce((sum, item) => sum + item?.quantity, 0)}
                </div>
                <div className="flex-1 text-body3-medium text-v2-content-primary text-center">
                  {renderPrice(subTotal)}
                </div>
              </div>

              <div className="flex items-center justify-between mt-6">
                <div className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("supplierOrder.orderSummary.discount")}
                </div>
                <div className="flex-1 text-body4-medium text-v2-content-tertiary">-</div>
                <div className="flex-1 text-body3-medium text-v2-content-primary text-center">-</div>
              </div>

              <div className="flex items-center justify-between mt-6">
                <div className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("supplierOrder.orderSummary.shippingCost")}
                </div>
                <div className="flex-1 text-body4-medium text-v2-content-tertiary">
                  {/* {t("supplierOrder.orderSummary.freeIfBiggerThan2mil")} */}
                </div>
                <div className="flex-1 text-body3-medium text-v2-content-primary text-center">
                  {renderPrice(shippingCost)}
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between mt-6 bg-v2-surface-info rounded-lg px-3 py-2">
              <p className="flex-1 text-body3-medium font-bold text-v2-content-primary">
                {t("supplierOrder.orderSummary.finalTotalPrice")}
              </p>
              <p className="flex-1" />
              <p className="flex-1 text-center text-body3-medium font-bold text-v2-content-primary">
                {renderPrice(supplierOrder?.data?.totalPrice)}
              </p>
            </div>
          </div>
        </div>
        <div className="lg:w-[284px] w-full ">
          <div className="border border-v2-border-primary rounded-lg p-6">
            <div>
              <span className="text-v2-content-primary text-body2-medium font-semibold">
                {t("supplierOrder.orderSummary.statuses")}
              </span>

              <div className="flex items-center justify-between pb-3 border-b border-b-v2-border-secondary mt-6">
                <span className="text-body4-regular text-v2-content-primary">
                  {t("supplierOrder.orderSummary.orderState")}
                </span>

                {!!supplierOrder?.data?.state && (
                  <OrderStatus
                    id={supplierOrder?.data?.state as IOrderStatusItems["id"]}
                    title={t(`supplierOrder.orderStateItems.${supplierOrder?.data?.state?.toLowerCase()}`)}
                  />
                )}
              </div>

              <div className="flex items-center justify-between py-3 border-b border-b-v2-border-secondary">
                <span className="text-body4-regular text-v2-content-primary">
                  {t("supplierOrder.orderSummary.paymentState")}
                </span>

                {!!supplierOrder?.data?.paymentState && (
                  <OrderStatus
                    id={supplierOrder?.data?.paymentState}
                    title={t(`supplierOrder.paymentStateItems.${supplierOrder?.data?.paymentState?.toLowerCase()}`)}
                  />
                )}
              </div>

              <div className="flex items-center justify-between pt-3 ">
                <span className="text-body4-regular text-v2-content-primary">
                  {t("supplierOrder.orderSummary.deliveryState")}
                </span>

                {!!supplierOrder?.data?.shippingState && (
                  <OrderStatus
                    id={supplierOrder?.data?.shippingState}
                    title={t(`supplierOrder.deliveryStateItems.${supplierOrder?.data?.shippingState?.toLowerCase()}`)}
                  />
                )}
              </div>
            </div>
          </div>

          {/* <div className="border border-v2-border-primary rounded-lg p-6 mt-2.5">
            <div>
              <span className="text-v2-content-primary text-body2-medium font-semibold">
                {t("supplierOrder.orderSummary.customerInfo")}
              </span>
            </div>

            <div className="flex items-center justify-between mt-6 pb-3 border-b border-b-v2-border-secondary">
              <div className="flex items-center gap-1">
                <Icon icon="carbon:user-avatar" width={15} height={15} />
                <span className="text-body4-regular text-v2-content-primary">
                  {t("supplierOrder.orderSummary.customer")}
                </span>
              </div>

              <span className="text-body4-regular text-v2-content-primary">
                {" "}
                {supplierOrder?.data?.customer?.firstName} {supplierOrder?.data?.customer?.lastName}
              </span>
            </div>

            <div className="flex items-center justify-between  py-3 border-b border-b-v2-border-secondary">
              <div className="flex items-center gap-1">
                <Icon icon="mage:email" width={15} height={15} />
                <span className="text-body4-regular text-v2-content-primary">
                  {t("supplierOrder.orderSummary.email")}
                </span>
              </div>
              <span className="text-body4-regular text-v2-content-primary">
                {" "}
                {supplierOrder?.data?.customer?.email ?? "-"}{" "}
              </span>
            </div>

            <div className="flex items-center justify-between pt-3">
              <div className="flex items-center gap-1">
                <Icon icon="solar:outgoing-call-outline" width={15} height={15} />{" "}
                <span className="text-body4-regular text-v2-content-primary">
                  {t("supplierOrder.orderSummary.phone")}
                </span>
              </div>
              <span className="text-body4-regular text-v2-content-primary">
                {supplierOrder?.data?.customer?.phoneNumber ? supplierOrder?.data?.customer?.phoneNumber : "-"}{" "}
              </span>
            </div>
          </div> */}

          <div className="border border-v2-border-primary rounded-lg p-6 mt-2.5">
            <div className="flex items-center justify-between">
              <span className="text-v2-content-primary text-body2-medium font-semibold">
                {t("supplierOrder.orderSummary.customerPostAddress")}
              </span>
            </div>

            <div className="flex items-center gap-1 pb-3 border-b border-b-v2-border-secondary mt-6">
              <Icon icon="carbon:user-avatar" width={15} height={15} />
              <span className="text-body4-regular text-v2-content-primary">
                {supplierOrder?.data?.customer?.firstName || ""} {supplierOrder?.data?.customer?.lastName || ""}{" "}
              </span>
            </div>

            {/* <div className="flex items-center ">
              <Icon icon="carbon:user-avatar" width={15} height={15} />
              <span>
                {t("supplierOrder.orderSummary.cityState") + supplierOrder?.data?.shippingAddress?.state ||
                  getLocation(supplierOrder?.data?.shippingAddress?.locationId ?? "")?.parent?.name ||
                  "-"}
              </span>
            </div> */}

            <div className="flex items-center gap-1 py-3 ">
              <Icon icon="solar:map-arrow-square-outline" width={15} height={15} />
              <span className="text-body4-regular text-v2-content-primary">
                {t("supplierOrder.orderSummary.cityState")}{" "}
                {getLocation(supplierOrder?.data?.shippingAddress?.locationId ?? "")?.parent?.name || "-"}
              </span>
            </div>

            <div className="mb-2 mt-3">
              <span className="text-body4-regular text-v2-content-primary">
                {t("supplierOrder.orderSummary.city")}:{" "}
                {getLocation(supplierOrder?.data?.shippingAddress?.locationId ?? "")?.name || "-"}
              </span>
            </div>

            <div>
              <span className="text-body4-regular text-v2-content-primary">
                {supplierOrder?.data?.shippingAddress?.address1 || ""}
              </span>
            </div>

            <div className="flex items-center justify-between mt-2">
              <span className="text-body4-regular text-gray-600">{t("order.phoneNumber")}</span>
              <span className="text-body4-regular text-v2-content-primary">
                {supplierOrder?.data?.customer?.phoneNumber ? supplierOrder?.data?.customer?.phoneNumber : "-"}
              </span>
            </div>

            <div className="flex items-center justify-between mt-1">
              <span className="text-body4-regular text-gray-600">{t("supplier.profile.zip")}</span>
              <span className="text-body4-regular text-v2-content-primary">
                {supplierOrder?.data?.shippingAddress?.zip ? supplierOrder?.data?.shippingAddress?.zip : "-"}
              </span>
            </div>
          </div>

          <div className="border border-v2-border-primary rounded-lg p-6 mt-2.5">
            <div className="flex items-center justify-between">
              <span className="text-v2-content-primary text-body2-medium font-semibold">{t("order.shippingInfo")}</span>
              {supplierOrder?.data?.state === "Done" && (
                <Icon
                  onClick={handleTrackingCode}
                  icon="solar:pen-2-outline"
                  className="size-4 text-gray-400 cursor-pointer"
                />
              )}
            </div>

            <div className="mt-6">
              {supplierOrder?.data?.shipmentDetails?.map(item => (
                <div key={item?.orderId}>
                  <div className="flex items-center justify-between pb-3 border-b border-b-v2-border-secondary">
                    <div className="flex items-center gap-1">
                      <Icon icon="solar:square-alt-arrow-left-outline" width={15} height={15} />
                      <span className="text-body4-regular text-v2-content-primary"> {t("order.shippingType")}</span>
                    </div>
                    <span className="text-body4-medium text-v2-content-primary"> {t("order.shippingType")}</span>
                  </div>

                  <div className="flex items-center justify-between pt-3 ">
                    <div className="flex items-center gap-1">
                      <Icon icon="solar:scanner-outline" width={15} height={15} />
                      <span className="text-body4-regular text-v2-content-primary"> {t("order.trackingCode")}</span>
                    </div>
                    <Link
                      target="_blank"
                      rel="noopener noreferrer"
                      href={ensureUrlScheme(item?.trackingUrl)}
                      className="py-0.5 px-1 rounded-full flex items-center gap-2 text-v2-content-on-info bg-v2-surface-info"
                    >
                      <span className="text-caption-medium">{t("supplierOrder.orderSummary.tracking")}</span>
                      <Icon
                        icon="solar:round-alt-arrow-left-bold"
                        width={14}
                        height={14}
                        className="text-v2-content-on-info"
                      />
                    </Link>
                  </div>

                  <div className="flex items-center gap-1 mt-4 mr-auto w-fit">
                    <span className="text-body4-medium text-v2-content-primary">{item?.trackingCode}</span>
                    <Icon
                      icon={isCopied ? "flat-color-icons:checkmark" : "icon-park-outline:copy"}
                      className={twMerge(isCopied ? "size-4 mb-1" : "size-3.5 ", "text-gray-600 cursor-pointer")}
                      onClick={e => {
                        e.stopPropagation();
                        copyToClipboard(item?.trackingCode);
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default OrderDetail;
