"use client";
import "./OrderDetailTable.css";
import { useTranslation } from "react-i18next";
import useCurrency from "@/utils/hooks/useCurrency";
import { Box, Stack, Table } from "@mui/material";
import { TableBody } from "@mui/material";
import { TableCell } from "@mui/material";
import { TableContainer } from "@mui/material";
import { TableHead } from "@mui/material";
import { TableRow } from "@mui/material";
import { Typography } from "@mui/material";

import { headerItems } from "../utils";
import OrderDetailRow from "./OrderDetailRow";
import { TOrderData } from "@/store/apps/order/types";

interface IOrderSummaryTableProps {
  supplierOrder?: TOrderData;
  isSupplierOrderFetching: boolean;
  subTotal: number;
  vat: number;
  shippingCost: number;
}

const OrderDetailTable = ({ supplierOrder, vat, shippingCost }: IOrderSummaryTableProps) => {
  const { t } = useTranslation();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };

  return (
    <>
      <TableContainer>
        <Table aria-label="product" id="sx-ordersummarytable-16050">
          <TableHead>
            <TableRow>
              {headerItems?.map(item => (
                <TableCell key={item.id}>
                  <Typography whiteSpace="nowrap" className="supplier-order-detail-table-header-title">
                    {item.title}
                  </Typography>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          {!!supplierOrder?.lineItems?.length && (
            <>
              <TableBody>
                {supplierOrder?.lineItems?.map(item => (
                  <OrderDetailRow
                    item={item}
                    key={item?.id}
                    shipmentDetail={supplierOrder?.shipmentDetails?.find(
                      sItem => sItem?.supplierId === item?.supplierId
                    )}
                    lineItems={supplierOrder?.lineItems}
                  />
                ))}
              </TableBody>
            </>
          )}
        </Table>
        <Stack flexDirection="row" justifyContent="end" marginBlockStart={1}>
          <Box bgcolor="rgb(var(--color-purple-50))" padding={2} display="flex" flexDirection="column" minWidth={260}>
            <Stack direction="row" mb={2} gap={5} alignItems="center" justifyContent="space-between">
              <Typography className="supplier-order-detail-table-line-items-total-title" whiteSpace="nowrap">
                {t("supplierOrder.orderSummary.vat")}
              </Typography>
              <Typography className="supplier-order-detail-table-line-items-total-title" whiteSpace="nowrap">
                {renderPrice(vat)}{" "}
              </Typography>
            </Stack>

            <Stack direction="row" mb={2} gap={5} alignItems="center" justifyContent="space-between">
              <Typography className="supplier-order-detail-table-line-items-total-title" whiteSpace="nowrap">
                {t("supplierOrder.orderSummary.shippingCost")}
              </Typography>
              <Typography className="supplier-order-detail-table-line-items-total-title" whiteSpace="nowrap">
                {renderPrice(shippingCost)}{" "}
              </Typography>
            </Stack>

            <Stack direction="row" mb={2} gap={5} alignItems="center" justifyContent="space-between">
              <Typography className="supplier-order-detail-table-line-items-total-value" whiteSpace="nowrap">
                {t("supplierOrder.orderSummary.grandTotal")}
              </Typography>
              <Typography className="supplier-order-detail-table-line-items-total-value" whiteSpace="nowrap">
                {supplierOrder ? renderPrice(supplierOrder?.totalPrice) : "-"}
              </Typography>
            </Stack>
          </Box>
        </Stack>
      </TableContainer>
    </>
  );
};

export default OrderDetailTable;
