import React from "react";
import { Box, CircularProgress, Stack, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { TOrderLineItem, TOrderState, TOrderStateBody } from "@/store/apps/order/types";
import { usePutOrderStateMutation } from "@/store/apps/order";
import { Formik } from "formik";
import { supplierOrderRejectedValidationSchema } from "@/utils/validations/profile/supplier";
import useModal from "@/utils/hooks/useModal";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import Textarea from "@/components/ui/inputs/Textarea/Textarea";
import { Icon } from "@iconify/react";

interface IRejectDialogProps {
  orderState: string;
  orderData: TOrderLineItem;
}

const RejectDialog = ({ orderData, orderState }: IRejectDialogProps) => {
  const { t } = useTranslation();
  const { hideModal } = useModal();

  const [updateOrderState, { isLoading }] = usePutOrderStateMutation();

  const initialValues = {
    comment: undefined
  };

  const onUpdateOrderState = async (value: Omit<TOrderStateBody["body"], "state">) => {
    const body = {
      state: orderState as TOrderState,
      comment: value?.comment
    };

    try {
      const res = await updateOrderState({ oid: orderData?.orderId, vid: orderData?.variantId, body });

      if ((res as any)?.error) {
        clientDefaultErrorHandler({ error: (res as any)?.error });
      }

      if ("data" in res && res.data) {
        hideModal();
      }
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  return (
    <div className="">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Icon icon="solar:bag-cross-bold" className="text-purple-500 size-6" />
          <span className="text-v2-content-primary text-body4-medium">
            {t("supplierOrder.orderSummary.rejectReason")}
          </span>
        </div>
        <Icon icon="iconamoon:close-light" className="size-5 cursor-pointer" onClick={hideModal} />
      </div>
      <div className="mt-4">
        {/* <Typography className="text-sm text-center text-gray-999">{t("order.rejectedTitle")}</Typography> */}
        <Formik
          initialValues={initialValues}
          validationSchema={supplierOrderRejectedValidationSchema}
          onSubmit={onUpdateOrderState}
        >
          {({ values, handleChange, handleBlur, touched, errors, handleSubmit }) => (
            <form onSubmit={handleSubmit} className="mt-4">
              <p className="mb-1 text-body4-medium text-v2-content-tertiary">{t("order.rejectedTitle")}</p>
              <Textarea
                // size="medium"
                rows={5}
                placeholder={t("order.comment")}
                id="comment"
                name="comment"
                value={values?.comment}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched?.comment && Boolean(errors?.comment)}
                helperText={touched?.comment && errors?.comment}
              />

              <Stack
                flexDirection="row"
                gap={1}
                mt={2}
                alignItems="center"
                className="order-detail-fullfillment-state-button-wrapper"
              >
                <CustomButton fullWidth color="secondary" onClick={() => hideModal()}>
                  {t("order.cancel")}
                </CustomButton>
                <CustomButton type="submit" fullWidth disabled={isLoading} size="small">
                  {isLoading ? <CircularProgress color="info" size={20} /> : t("save")}
                </CustomButton>
              </Stack>
            </form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default RejectDialog;
