#sx-ordersummary-15641 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50dvh;
}
#sx-ordersummary-15663 {
  padding: 24px 16px;
}
#sx-ordersummary-15674 {
  font-size: 16px;
}
#sx-ordersummary-15684 {
  font-size: 16px;
}
#sx-ordersummary-15692 {
  font-size: 16px;
}
#sx-ordersummary-15705 {
  padding: 12px;
}
#sx-ordersummary-15715 {
  font-size: 16px;
}
#sx-ordersummary-15723 {
  font-size: 16px;
}
#sx-ordersummary-15731 {
  font-size: 16px;
}
#sx-ordersummary-15744 {
  padding: 12px;
}
#sx-ordersummary-15754 {
  font-size: 16px;
}
#sx-ordersummary-15764 {
  font-size: 16px;
}
#sx-ordersummary-15774 {
  font-size: 16px;
}
#sx-ordersummary-15791 {
  padding: 12px;
}
#sx-ordersummary-15821 {
  color: rgb(var(--color-gray-50));
  font-size: 100px;
}
#sx-ordersummary-15832 {
  padding: 12px;
}

.order-detail-box-wrapper {
  width: 100%;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid rgb(var(--color-gray-50));
}

.order-detail-table-box-wrapper-mobile {
  display: none;
}

.supplier-order-detail-line-items-address-wrapper {
  margin-block: 24px;
}

@media (max-width: 768px) {
  .order-detail-box-wrapper {
    border: 1px solid transparent;
    background-color: rgb(var(--color-cards));
  }

  .order-detail-table-box-wrapper {
    display: none;
  }

  .order-detail-table-box-wrapper-mobile {
    display: block;
  }

  .supplier-order-detail-line-items-address-wrapper {
    margin-block: 8px;
  }
}

.supplier-order-detail-title {
  font-size: 16px;
  font-weight: 700;
}

.supplier-order-detail-box-subtitle {
  font-size: 13px;
  font-weight: 400;
}

.supplier-order-detail-info-line {
  width: 4px;
  height: 14px;
  margin-block-start: 2px;
  background-color: #67dfef;
}

.supplier-order-detail-address-title {
  font-size: 12px;
  font-weight: 400;
  color: rgb(var(--color-gray-400));
}

.supplier-order-detail-address-value {
  font-size: 16px;
  font-weight: 500;
}
