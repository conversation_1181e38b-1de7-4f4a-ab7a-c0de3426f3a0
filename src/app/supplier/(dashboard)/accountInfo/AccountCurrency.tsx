import "./AccountInfo.css";

import { usePutSupplierProfileMutation } from "@/store/apps/supplier";
import { TSupplierProfileData } from "@/store/apps/supplier/types";
import useCurrency from "@/utils/hooks/useCurrency";
import { Box, CircularProgress, FormControl, Stack, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { shallowEqual, useSelector } from "react-redux";
import { Icon } from "@iconify/react";
import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import { isValidUUID } from "@/utils/helpers";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { ICurrency } from "@/app/retailer/(dashboard)/accountInfo/AccountCurrency";

function AccountCurrency() {
  const { t } = useTranslation();
  const [selected, selectCurrency, currencyOptions] = useCurrency();

  const [putSupplierProfile] = usePutSupplierProfileMutation();

  const supplierData = useSelector(
    (state: any) => state?.Supplier?.queries[`getSupplierProfile(undefined)`]?.data,
    shallowEqual
  ) as { data: TSupplierProfileData };

  const statusCurrency = useSelector(
    (state: any) => state?.Meta?.queries[`getMetaCurrencies(undefined)`],
    shallowEqual
  ) as { status: string };

  const loadingCurrency = statusCurrency?.status === "pending";

  const profileData = {
    address: {
      address1: supplierData?.data?.address?.address1,
      locationId:
        supplierData?.data?.address?.locationId && isValidUUID(supplierData?.data?.address?.locationId)
          ? supplierData?.data?.address?.locationId
          : undefined,
      zip: supplierData?.data?.address?.zip
    },
    warehouseAddress: {
      address1: supplierData?.data?.warehouseAddress?.address1,
      locationId:
        supplierData?.data?.warehouseAddress?.locationId &&
        isValidUUID(supplierData?.data?.warehouseAddress?.locationId)
          ? supplierData?.data?.warehouseAddress?.locationId
          : undefined,
      zip: supplierData?.data?.warehouseAddress?.zip,
      phoneNumber: supplierData?.data?.warehouseAddress?.phoneNumber
    },
    identity: supplierData?.data?.identity,
    bankAccount: supplierData?.data?.bankAccount,
    contactEmail: supplierData?.data?.contactEmail,
    contactNumber: supplierData?.data?.contactNumber,
    biography: supplierData?.data?.biography,
    logo: supplierData?.data?.logo,
    name: supplierData?.data?.name,
    processingTime: supplierData?.data?.processingTime,
    website: supplierData?.data?.website
  };

  const handleChangeCurrency = async (checked: boolean, currency: ICurrency) => {
    if (checked) selectCurrency(currency?.id as any);

    const body = { ...profileData, currencyId: currency?.id } as TSupplierProfileData;

    try {
      const supplierProfileApi = putSupplierProfile({ body });

      await supplierProfileApi.then(res => {
        if ((res as any)?.error) {
          clientDefaultErrorHandler({ error: (res as any)?.error });
        }

        if ("data" in res && res?.data) {
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };
  return (
    <Stack>
      <Box className="account-info-overallInfo-icon-wrapper">
        <Icon icon="solar:dollar-outline" color="rgb(var(--color-gray-999))" width={24} height={24} />
      </Box>
      <Typography className="account-info-action-title">{t("currency")}</Typography>
      <FormControl className="account-info-action-radio-wrapper">
        <Box>
          {loadingCurrency ? (
            <Box mt={2}>
              <CircularProgress size={20} />
            </Box>
          ) : (
            currencyOptions.map((currency: ICurrency) => (
              <CustomRadio
                color="secondary"
                key={currency?.id}
                label={currency?.name}
                checked={selected?.id === currency?.id}
                onChange={(e, checked) => {
                  handleChangeCurrency(checked, currency);
                }}
              />
            ))
          )}
        </Box>
      </FormControl>
    </Stack>
  );
}

export default AccountCurrency;
