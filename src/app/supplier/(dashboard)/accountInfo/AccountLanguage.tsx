import "./AccountInfo.css";

import { LANGUAGES, LanguageType } from "@/constants/localization";
import useLanguage from "@/utils/hooks/useLanguage";
import { Box, FormControl, Stack, Typography } from "@mui/material";
import i18next from "i18next";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";

function AccountLanguage() {
  const { t } = useTranslation();
  const [currentLang, getPathLang, pathname] = useLanguage();
  const router = useRouter();

  const changeLang = (lang: LanguageType) => {
    const pathLocale = getPathLang();
    let toPath = pathname;
    if (lang.value === pathLocale) return;
    if (pathLocale) {
      toPath = toPath.replace("/" + pathLocale, "/" + lang.value);
    } else {
      toPath = "/" + lang.value + pathname;
    }
    i18next.changeLanguage(lang?.value);
    router.replace(toPath);
  };

  return (
    <Stack>
      <Box className="account-info-overallInfo-icon-wrapper">
        <Icon icon="solar:accessibility-outline" color="rgb(var(--color-gray-999))" width={24} height={24} />
      </Box>
      <Typography className="account-info-action-title">{t("language")}</Typography>
      <FormControl className="account-info-action-radio-wrapper">
        <Box>
          {LANGUAGES.map(item => (
            <CustomRadio
              color="secondary"
              disabled={item?.disabled}
              key={item.value}
              checked={item.value === currentLang?.value}
              onChange={() => changeLang(item)}
              value={item.value}
              label={item.flagname}
            />
          ))}
        </Box>
      </FormControl>
    </Stack>
  );
}

export default AccountLanguage;
