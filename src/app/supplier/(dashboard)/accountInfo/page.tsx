"use client";

import Header from "@/components/containers/header/Header";
import { useTranslation } from "react-i18next";
import AccountInfo from "./AccountInfo";
import AclWrapper from "@/components/containers/AclWrapper";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import React from "react";

const AccountInfoPage: React.FC = () => {
  const { t } = useTranslation();
  return (
    <AclWrapper for="SUPPLIER">
      <Header title={t("accountInfo")} isMobile isSticky />
      <div className="xmd:p-0 p-4">
        <AccountInfo />
      </div>
    </AclWrapper>
  );
};

export default WithBottomBar(AccountInfoPage);
