.account-info-wrapper {
  background-color: rgb(var(--color-cards));
  border-radius: 10px;
}

.account-info-action-radio-wrapper label {
  font-size: 14px;
  font-weight: 400;
  color: rgb(var(--color-gray-600));
}

.account-info-container {
  padding: 24px;
}

.account-info-overallInfo-wrapper {
  border: 1px solid rgb(var(--color-gray-50));
  border-radius: 8px;
  padding: 24px;
}

.account-info-overallInfo-wrapper-col2 {
  flex: 1 1 60%;
}

.account-info-action-title {
  font-size: 14px;
  font-weight: 400;
  color: rgb(var(--color-gray-600));
  margin-top: 12px;
  margin-bottom: 6px;
}

.account-info-overallInfo-wrapper-col1 {
  flex: 1 1 30%;
}

.account-info-overallInfo-button-wrapper {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-top: 24px;
}

.account-info-overallInfo-wrapper-col {
  flex: 1 1 40%;
}

@media (max-width: 800px) {
  .account-info-overallInfo-wrapper-col2 {
    flex: 1 1 100%;
    border: unset;
    border-radius: 0;
    padding: 0 8px;
    margin-bottom: 8px;
  }

  .account-info-overallInfo-wrapper-col1 {
    flex: 1 1 100%;
  }

  .account-info-container {
    padding: 16px;
  }

  .account-info-overallInfo-button-wrapper {
    justify-content: flex-start;
  }

  .account-info-overallInfo-wrapper-col {
    flex: 1 1 100%;
  }
}

.account-info-overallInfo-logo {
  border-radius: 50%;
}

.account-info-overallInfo-text-title {
  font-size: 12px;
  font-weight: 400;
  color: rgb(var(--color-gray-500));
}

.account-info-overallInfo-text-value {
  font-size: 14px;
  font-weight: 700;
  color: var(--mui-palette-grey-900);
}

.account-info-overallInfo-loading-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.account-info-overallInfo-change-password-button {
  margin-top: 47px;
}

.account-info-overallInfo-button-icon {
  transform: rotate(-45deg);
}

.account-info-stack-wrapper {
  margin-bottom: 8px;
}

@media (max-width: 768px) {
  /* .profilestepper-container {
    padding-inline: 24px;
  } */

  .account-info-overallInfo-change-password-button {
    margin-top: 16px;
  }

  .account-info-stack-wrapper {
    margin-bottom: 16px;
  }

  .account-info-change-password {
    margin-top: 16px;
  }
}

.account-info-overallInfo-icon-wrapper {
  width: 48px;
  height: 48px;
  background-color: rgb(var(--color-gray-20));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.account-info-overallInfo-change-password-text {
  font-size: 14px;
  font-weight: 700;
}

.account-info-overallInfo-change-password-subtitle {
  font-size: 13px;
  font-weight: 400;
  color: var(--mui-palette-grey-400);
}
