"use client";

import Header from "@/components/containers/header/Header";
import RootLayout, { RootLayoutRef } from "@/components/layouts/RootLayout/RootLayout";
import { routes } from "@/constants/routes";
import { useGetSupplierProfileQuery } from "@/store/apps/supplier";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Box, useMediaQuery } from "@mui/material";
import { Theme } from "@mui/system";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef } from "react";

interface Props {
  children: React.ReactNode;
}

export default function Layout({ children }: Props) {
  const router = useRouter();
  const pathname = usePathname();
  const isInProfile = pathname.includes(routes.profile);
  const makeRoute = useRoleBasePath();
  const layoutRef = useRef<RootLayoutRef>(null);
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const { error: supplierError } = useGetSupplierProfileQuery();

  const whiteList = ["/supplier", "/", ""];

  useEffect(() => {
    const isWhiteListRoute = whiteList?.some(item => item === pathname);
    if (isWhiteListRoute) return;

    if (supplierError?.status === 428) {
      if (isInProfile) return;

      router.replace(`${makeRoute(routes.profile)}?step=0&status=incomplete`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [supplierError?.status, pathname, whiteList]);

  return (
    <RootLayout ref={layoutRef}>
      {!isMobile && <Header onToggleSidebar={() => layoutRef.current?.onToggleSidebar()} />}
      <Box id="sx-layout-147" className="">
        {children}
      </Box>
    </RootLayout>
  );
}
