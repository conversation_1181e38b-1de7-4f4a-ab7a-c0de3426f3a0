import "./SupplierIReturn.css";

import { useGetSupplierReturnQuery, usePutSupplierReturnMutation } from "@/store/apps/supplier";
import { supplierReturnValidationSchema } from "@/utils/validations/profile/supplier";
import { Box, CircularProgress, FormControl, Grid, Stack } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { resetAddressErrors } from "./utils";
import { TSupplierProfileData, TSupplierReturnData } from "@/store/apps/supplier/types";
import { Icon } from "@iconify/react";
import { TSupplierReturnFormProps } from "./types";
import SectionInfo from "@/components/ui/SectionInfo/SectionInfo";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { isValidUUID, snakeToCamelCaseHookFormWrapper, SnakeToCamelFieldErrorWrapper } from "@/utils/helpers";
import ReturnAddress, { IReturnAddressProps } from "./ReturnAddress";
import LocationsSelect from "@/components/ui/CustomCountrySelect/LocationsSelect";
import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import NumberInput from "@/components/ui/inputs/NumberInput";
import Input from "@/components/ui/inputs/Input";
import Textarea from "@/components/ui/inputs/Textarea/Textarea";
import BottomAction from "@/components/ui/bottomAction/BottomAction";

const SupplierReturn = ({
  address,
  warehouseAddress,
  onChangeActiveStep,
  onProfileSaved
}: TSupplierReturnFormProps) => {
  const { t } = useTranslation();

  const {
    data: supplierReturn,
    isLoading: isSupplierReturnLoading,
    isSuccess,
    isError
  } = useGetSupplierReturnQuery({});
  const [putSupplierReturn, { isLoading: isPutSupplierReturnLoading }] = usePutSupplierReturnMutation();

  const [addressSelected, setAddressSelected] = useState<IReturnAddressProps["type"] | undefined>(undefined);

  const isLoading = isPutSupplierReturnLoading;

  const initialValues = useMemo(() => {
    return {
      address: {
        locationId:
          supplierReturn?.data?.address?.locationId && isValidUUID(supplierReturn?.data?.address?.locationId)
            ? supplierReturn?.data?.address?.locationId
            : "",
        zip: supplierReturn?.data?.address?.zip || "",
        address1: supplierReturn?.data?.address?.address1 || "",
        phoneNumber: supplierReturn?.data?.address?.phoneNumber || ""
      },
      description: supplierReturn?.data?.description || undefined,
      isAllowed: supplierReturn?.data?.isAllowed || false,
      shippingPayer: supplierReturn?.data?.shippingPayer || ("Supplier" as TSupplierReturnData["shippingPayer"]),
      windowTime: !!supplierReturn?.data?.windowTime?.max ? supplierReturn?.data?.windowTime : undefined
    };
  }, [supplierReturn?.data]);

  const {
    control,
    handleSubmit,
    setValue,
    setError,
    formState: { errors },
    watch
  } = useForm<TSupplierReturnData>({
    defaultValues: initialValues,
    resolver: yupResolver(supplierReturnValidationSchema as any) as any,
    mode: "onChange"
  });

  const isAllowed = watch("isAllowed");

  useEffect(() => {
    const setFields = (data: Record<string, any>) => {
      Object.entries(data).forEach(([key, value]) => setValue(key as any, value));
    };

    setFields(initialValues);
  }, [supplierReturn?.data]);

  const onSubmit = async (values: TSupplierReturnData) => {
    const address = {
      locationId: values?.address?.locationId,
      zip: values?.address?.zip,
      address1: values?.address?.address1,
      phoneNumber: values?.address?.phoneNumber
    };

    const allowedBody = { ...values, address } as TSupplierReturnData;
    const notAllowedBody = {
      isAllowed: false
    } as TSupplierReturnData;

    const body = (values?.isAllowed ? allowedBody : notAllowedBody) as unknown as TSupplierReturnData;

    try {
      await putSupplierReturn({ body }).then(res => {
        const error = (res as any)?.error?.data;

        if (error) {
          clientDefaultErrorHandler({
            error: (res as any)?.error,
            bodyError: error,
            setHookFormFieldError: snakeToCamelCaseHookFormWrapper(setError as any)
          });
        }
        if ("data" in res && res?.data) {
          onProfileSaved?.();
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const formatAddress = (address: TSupplierProfileData["address"]) =>
    JSON.stringify({
      locationId: address?.locationId || undefined,
      zip: address?.zip || "",
      address1: address?.address1 || "",
      phoneNumber: address?.phoneNumber || ""
    });

  useEffect(() => {
    const cloneReturnAddress = JSON.stringify(initialValues?.address);
    const cloneAddress = address?.address1 && formatAddress(address);
    const cloneWarehouseAddress = warehouseAddress?.address1 && formatAddress(warehouseAddress);

    if (cloneAddress === cloneReturnAddress) {
      setAddressSelected("address");
    } else if (cloneAddress === cloneWarehouseAddress && cloneAddress === cloneReturnAddress) {
      setAddressSelected("address");
    } else if (cloneReturnAddress === cloneWarehouseAddress) {
      setAddressSelected("warehouseAddress");
    } else {
      setAddressSelected("other");
    }
  }, [initialValues?.address, address, warehouseAddress]);

  if (isSupplierReturnLoading) {
    return (
      <Box id="sx-supplierireturn-17896">
        <CircularProgress />
      </Box>
    );
  }
  return (
    <>
      {(isSuccess || isError) && (
        <form onSubmit={handleSubmit(onSubmit)} className="h-full flex flex-col">
          <div className="xmd:h-fit h-full xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 ">
            <Grid container spacing={{ xs: 1, sm: 1.5 }}>
              <Grid item xs={12} md={12}>
                {/* <SectionInfo title={t("supplier.profile.returnPolicy")} /> */}
                <p className="text-v2-content-primary !font-semibold text-body2-medium">
                  {t("supplier.profile.returnPolicy")}
                </p>
              </Grid>

              <Grid item xs={12} md={12}>
                <div className="xmd:mt-2 mt-1">
                  <div className="flex justify-between items-start ">
                    <p className="text-body3-regular !font-bold text-v2-content-primary">
                      {t("supplier.profile.returnHintTitle")}
                    </p>
                    <Controller
                      name="isAllowed"
                      control={control}
                      render={({ field }) => (
                        <CustomSwitch
                          {...field}
                          checked={field?.value}
                          id="isAllowed"
                          size="small"
                          label={t("supplier.profile.active")}
                          onChange={(e, checked) => field.onChange(checked)}
                        />
                      )}
                    />
                  </div>

                  <p className="text-caption-medium  text-gray-600 xmd:mt-1 mt-2 xmd:ml-28">
                    {t("supplier.profile.returnHintDescription")}
                  </p>
                </div>
              </Grid>

              {isAllowed && (
                <>
                  <Grid item xs={12} md={12} className="mt-2 ">
                    <span className="text-body3-bold text-gray-999">{t("supplier.profile.whoPayer")}</span>
                  </Grid>

                  <Grid item xs={12} md={12}>
                    <FormControl id="sx-supplierinfo-17762">
                      <div className="flex items-center gap-8">
                        <Controller
                          name="shippingPayer"
                          control={control}
                          render={({ field }) => (
                            <CustomRadio
                              {...field}
                              checked={field.value?.toLowerCase() === "customer"}
                              value="Customer"
                              label={t("supplier.profile.customer")}
                            />
                          )}
                        />
                        <Controller
                          name="shippingPayer"
                          control={control}
                          render={({ field }) => (
                            <CustomRadio
                              {...field}
                              checked={field.value?.toLowerCase() === "supplier"}
                              value="Supplier"
                              label={t("supplier.profile.me")}
                            />
                          )}
                        />
                      </div>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Controller
                      name="windowTime.max"
                      control={control}
                      render={({ field }) => (
                        <NumberInput
                          {...field}
                          autoComplete="off"
                          optional={!isAllowed}
                          requiredStar={isAllowed}
                          value={(field?.value as any)?.max || field?.value}
                          placeholder={t("supplier.profile.placeholders.windowTime")}
                          label={t("supplier.profile.windowTime")}
                          onChange={({ target: { value } }) =>
                            setValue("windowTime", { min: 0, max: value ? Number(value) : ("" as any) })
                          }
                          endAdornment={
                            <span className="text-body3-medium text-gray-999 whitespace-nowrap">
                              {t("supplier.profile.daysOfWorking")}
                            </span>
                          }
                          error={Boolean(errors?.windowTime?.max?.message) || !!errors?.windowTime?.message}
                          helperText={errors?.windowTime?.max?.message || errors?.windowTime?.message || ""}
                          labelTooltipTitle={t("supplier.profile.windowTimeTooltipTitle")}
                          labelTooltipDescription={t("supplier.profile.windowTimeTooltipDesc")}
                        />
                      )}
                    />
                    <p className="text-gray-500 text-body4-medium mt-1">{t("windowTimeHint")}</p>
                  </Grid>

                  <Grid item xs={12} md={12}>
                    <Controller
                      name="description"
                      control={control}
                      render={({ field }) => (
                        <Textarea
                          {...field}
                          autoComplete="off"
                          requiredStar={isAllowed}
                          optional={!isAllowed}
                          rows={5}
                          label={t("supplier.profile.description")}
                          placeholder={t("supplier.profile.descriptionPlaceholder")}
                          error={Boolean(errors.description?.message)}
                          helperText={errors?.description?.message || ""}
                        />
                      )}
                    />
                  </Grid>
                </>
              )}
            </Grid>

            {isAllowed && (
              <>
                <Grid item xs={12} md={12} mb={1}>
                  {/* <SectionInfo
                  title={t("supplier.profile.returnAddress")}
                  boxProps={{
                    mb: 1,
                    mt: 3
                  }}
                /> */}
                  <p className="mt-2 text-body2-medium text-v2-content-primary !font-bold">
                    {t("supplier.profile.selectReturnAddress")}
                  </p>
                </Grid>

                <Grid item xs={12} md={12}>
                  <Grid container spacing={2} alignItems="stretch">
                    {address?.address1 && (
                      <Grid item xs={12} md={6}>
                        <ReturnAddress
                          title={t("supplier.profile.addressTitle")}
                          value={address?.address1}
                          type="address"
                          zipCode={address?.zip}
                          contactNumber={address?.phoneNumber}
                          addressSelected={addressSelected}
                          setAddressSelected={setAddressSelected}
                          onClick={() => {
                            setValue("address", address);
                            resetAddressErrors(initialValues, setError);
                          }}
                        />
                      </Grid>
                    )}

                    {!!address?.address1 &&
                      !!warehouseAddress?.address1 &&
                      formatAddress(address) !== formatAddress(warehouseAddress) && (
                        <Grid item xs={12} md={6}>
                          <ReturnAddress
                            title={t("supplier.profile.warehouseAddressTitle")}
                            value={warehouseAddress?.address1}
                            type="warehouseAddress"
                            zipCode={warehouseAddress?.zip}
                            contactNumber={warehouseAddress?.phoneNumber}
                            addressSelected={addressSelected}
                            setAddressSelected={setAddressSelected}
                            onClick={() => {
                              setValue("address", warehouseAddress);
                              resetAddressErrors(initialValues, setError);
                            }}
                          />
                        </Grid>
                      )}
                    <Grid item xs={12} md={12}>
                      <div className="flex items-center gap-2">
                        <CustomSwitch
                          labelClassName="!ml-0"
                          checked={addressSelected === "other"}
                          onChange={(e, checked) => {
                            Object.keys(initialValues.address).forEach(key => {
                              setValue(`address.${key}` as any, "");
                            });
                            if (addressSelected === "other" && address?.address1) {
                              setAddressSelected("address");
                              setValue("address", address);
                            } else setAddressSelected("other");
                          }}
                        />
                        <span className="text-v2-content-primary text-body4-medium">
                          {t("supplier.profile.anotherAddressTitle")}
                        </span>
                      </div>
                    </Grid>

                    <Grid item xs={12} md={12}>
                      {addressSelected === "other" && (
                        <Grid container spacing={{ xs: 1, sm: 1.5 }}>
                          <Grid item xs={12} md={6}>
                            <Controller
                              name="address.locationId"
                              control={control}
                              render={({ field }) => (
                                <LocationsSelect
                                  multiple={false}
                                  label={t("statecity")}
                                  requiredStar={isAllowed}
                                  optional={!isAllowed}
                                  placeholder={t("supplier.profile.cityPlaceholder")}
                                  handleBlur={field?.onBlur}
                                  value={field?.value}
                                  onChange={value => setValue("address.locationId", value as string)}
                                  name="address.locationId"
                                  error={Boolean(errors.address?.locationId?.message)}
                                  helperText={errors?.address?.locationId?.message || ""}
                                />
                              )}
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <Controller
                              name="address.zip"
                              control={control}
                              render={({ field }) => (
                                <NumberInput
                                  {...field}
                                  id="address.zip"
                                  name="address.zip"
                                  autoComplete="off"
                                  requiredStar={isAllowed}
                                  optional={!isAllowed}
                                  label={t("supplier.profile.zip")}
                                  placeholder={t("supplier.profile.zipPlaceholder")}
                                  error={Boolean(errors?.address?.zip?.message)}
                                  helperText={errors?.address?.zip?.message || ""}
                                />
                              )}
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <Controller
                              name="address.phoneNumber"
                              control={control}
                              render={({ field }) => (
                                <NumberInput
                                  {...field}
                                  autoComplete="off"
                                  requiredStar={isAllowed}
                                  optional={!isAllowed}
                                  label={t("supplier.profile.phone")}
                                  placeholder={t("supplier.profile.phonePlaceholder")}
                                  error={Boolean(errors?.address?.phoneNumber?.message)}
                                  helperText={errors?.address?.phoneNumber?.message || ""}
                                />
                              )}
                            />
                          </Grid>

                          <Grid item xs={12} md={12}>
                            <Controller
                              name="address.address1"
                              control={control}
                              render={({ field }) => (
                                <Input
                                  {...field}
                                  autoComplete="off"
                                  label={t("supplier.profile.exactAddress")}
                                  placeholder={t("supplier.profile.exactAddressPlaceholder")}
                                  id="address.address1"
                                  name="address.address1"
                                  requiredStar={isAllowed}
                                  optional={!isAllowed}
                                  error={Boolean(errors.address?.address1?.message)}
                                  helperText={errors?.address?.address1?.message || ""}
                                />
                              )}
                            />
                          </Grid>
                        </Grid>
                      )}
                    </Grid>
                  </Grid>
                </Grid>
              </>
            )}

            <BottomAction
              saveButtonText={isLoading ? <CircularProgress color="info" size={20} /> : t("saveChanges")}
              saveButtonProps={{
                type: "submit",
                disabled: isLoading
              }}
              cancelButtonText={t("supplier.profile.cancel")}
              cancelButtonProps={{
                onClick: () => {
                  onChangeActiveStep(4);
                }
              }}
            />
          </div>

          <div className="xmd:flex hidden justify-end flex-wrap gap-2 ">
            {/* <CustomButton
              color="secondary"
              className="supplier-profile-footer-button"
              onClick={() => {
                onChangeActiveStep(3);
              }}
              startIcon={
                <Icon
                  icon="mingcute:arrow-right-line"
                  width={20}
                  height={20}
                  className="supplier-profile-next-button"
                />
              }
            >
              {t("supplier.profile.buttonPrev")}
            </CustomButton> */}
            <CustomButton type="submit" disabled={isLoading} className="supplier-profile-footer-button">
              {isLoading ? <CircularProgress color="info" size={20} /> : t("saveChanges")}
            </CustomButton>
          </div>
        </form>
      )}
    </>
  );
};

export default React.memo(SupplierReturn, (prev, next) => {
  return prev.activeStep === next.activeStep;
});
