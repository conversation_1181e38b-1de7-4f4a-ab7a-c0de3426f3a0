import { Icon } from "@iconify/react";
import { Box, Typography, Stack } from "@mui/material";
import clsx from "clsx";
import { Dispatch, SetStateAction } from "react";
import "./SupplierIReturn.css";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

export interface IReturnAddressProps {
  title: string;
  value: string;
  onClick?: () => void;
  type: "address" | "warehouseAddress" | "other";
  contactNumber?: string;
  zipCode?: string;
  addressSelected?: IReturnAddressProps["type"];
  setAddressSelected?: Dispatch<SetStateAction<IReturnAddressProps["type"] | undefined>>;
}

function ReturnAddress({
  title,
  value,
  addressSelected,
  setAddressSelected,
  type,
  onClick,
  zipCode,
  contactNumber
}: IReturnAddressProps) {
  const { t } = useTranslation();

  const addressTitle: { [key in IReturnAddressProps["type"]]: string } = {
    address: t("companyAddress"),
    warehouseAddress: t("warehouseAddress"),
    other: t("otherAddress")
  };

  const activeAddress = addressSelected === type;

  return (
    <div
      className={twMerge("border border-solid flex flex-col justify-between h-full rounded-md p-4 cursor-pointer", activeAddress ? "border-purple-500" : "border-gray-50")}
      onClick={() => {
        setAddressSelected?.(type);
        onClick?.();
      }}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-0.5">
          <span className="text-body3-bold text-gray-999 whitespace-nowrap">{addressTitle[type]}: </span>
          <span className="text-body4-medium text-gray-999">{value}</span>
        </div>

        {activeAddress ? (
          <Image src="/images/svgs/user-checked.svg" alt="realUser" width={17} height={17} />
        ) : (
          <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={17} height={17} />
        )}
      </div>

      <div className="flex flex-col gap-1 mt-10">
        {!!zipCode && (
          <div className="flex items-center gap-1">
            <Icon icon="solar:mailbox-bold" className="size-4" color="rgb(var(--color-gray-600))" />
            <span className="text-caption-medium text-gray-600">{zipCode}</span>
          </div>
        )}

        {!!contactNumber && (
          <div className="flex items-center gap-1">
            <Icon icon="solar:phone-bold" className="size-4" color="rgb(var(--color-gray-600))" />
            <span className="text-caption-medium text-gray-600">{contactNumber}</span>
          </div>
        )}
      </div>
    </div>
  );
}

export default ReturnAddress;
