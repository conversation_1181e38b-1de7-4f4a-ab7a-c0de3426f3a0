import { TSupplierReturnData } from "@/store/apps/supplier/types";
import { TFunction } from "i18next";
import { UseFormSetError } from "react-hook-form";

export const getShippingPayerItems = ({ t }: { t: TFunction<"translation", undefined> }) => [
  {
    id: "Customer",
    label: t("supplier.profile.shippingPayerItems.customer")
  },
  {
    id: "Supplier",
    label: t("supplier.profile.shippingPayerItems.supplier")
  }
];

export const getWindowTimeItems = ({ t }: { t: TFunction<"translation", undefined> }) => [
  {
    id: "0-3",
    label: t("supplier.profile.windowTimeItems.3Days")
  },
  {
    id: "0-7",
    label: t("supplier.profile.windowTimeItems.7Days")
  },
  {
    id: "0-14",
    label: t("supplier.profile.windowTimeItems.14Days")
  },
  {
    id: "0-30",
    label: t("supplier.profile.windowTimeItems.30Days")
  },
  {
    id: "0-60",
    label: t("supplier.profile.windowTimeItems.60Days")
  }
];

interface InitialValues {
  address?: { [key: string]: any };
}

export function resetAddressErrors(
  initialValues: InitialValues,
  setFieldError: UseFormSetError<TSupplierReturnData>
): void {
  if (initialValues?.address) {
    Object.keys(initialValues.address).forEach(key => {
      setFieldError(`address.${key}` as any, { message: "" });
      // setTimeout(() => {
      //   setFieldTouched(`address.${key}`, false);
      // }, 0);
    });
  }
}
