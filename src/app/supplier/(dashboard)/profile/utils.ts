import { TFunction } from "i18next";
import { Dispatch, SetStateAction } from "react";
import { ISupplierStore } from "./SupplierStore/SupplierStore";
import { TSupplierProfileData } from "@/store/apps/supplier/types";
import { toCamel } from "ts-case-convert";
import { ApiError } from "@/utils/services";

export const getProcessingTimeItems = ({ t }: { t: TFunction<"translation", undefined> }) => [
  {
    id: "1-2",
    label: t("supplier.profile.processingTimeItems.1_2_DAYS")
  },
  {
    id: "3-4",
    label: t("supplier.profile.processingTimeItems.3_4_DAYS")
  },
  {
    id: "5-7",
    label: t("supplier.profile.processingTimeItems.5_7_DAYS")
  },
  {
    id: "35-40",
    label: t("supplier.profile.shippingTimeItems.35_40_DAYS")
  },
  {
    id: "40-45",
    label: t("supplier.profile.shippingTimeItems.40_45_DAYS")
  }
  // {
  //   id: "7-0",
  //   label: t("supplier.profile.processingTimeItems.MORE_7DAYS")
  // }
];

const bindFieldsToStep = [
  {
    step: 0,
    fields: [
      "birthDay",
      "companyName",
      "companyType",
      "economicCode",
      "isLegalPerson",
      "nationalCode",
      "registrationNumber",
      "sex",
      "vatNumber"
    ] as Array<keyof TSupplierProfileData["identity"]>
  },
  {
    step: 1,
    fields: [
      "bankAccount",
      "contactEmail",
      "contactNumber",
      "cover",
      "description",
      "logo",
      "name",
      "numberOfSkus",
      "processingTime",
      "website"
    ] as Array<keyof ISupplierStore>
  },
  {
    step: 2,
    fields: [
      "address.locationId",
      "address.zip",
      "address.address1",
      "warehouseAddress.locationId",
      "warehouseAddress.zip",
      "warehouseAddress.phoneNumber",
      "warehouseAddress.address1"
    ]
  }
];

export const handleGoToHasErrorTab = (error: ApiError, setActiveStep: Dispatch<SetStateAction<number | undefined>>) => {
  if (error.error_detail) {
    Object.keys(error.error_detail).some(key => {
      const fieldKey = key.includes("identity.") ? key.split("identity.")?.[1] : key;

      const found = bindFieldsToStep.find(a => (a.fields as any).includes(toCamel(fieldKey)));

      if (found) {
        setActiveStep(found.step);
        return true;
      }

      return false;
    });
  }
};
