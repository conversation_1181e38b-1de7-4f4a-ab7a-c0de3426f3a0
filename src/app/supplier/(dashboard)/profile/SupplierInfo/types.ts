import { TSupplierProfileData } from "@/store/apps/supplier/types";
import { TSupplierBaseFormProps } from "../types";
import { TSupplierProfile } from "../SupplierProfile";

export type TSupplierInfoProps = {
  isEdit?: boolean;
  supplierData?: TSupplierProfile;
  onSubmitData: (data: TSupplierProfileData["identity"] & { documents?: TDocuments }) => void;
} & TSupplierBaseFormProps;

export type TDocuments = {
  [key: string]: { url?: string; id: string }[];
};
