import "./SupplierInfo.css";
import { DatePicker } from "@/components/ui/CustomDatePicker";
import { supplierInfoValidationSchema } from "@/utils/validations/profile/supplier";
import { Box, Grid } from "@mui/material";
import React, { ForwardedRef, useEffect, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import UserTypeSwitch from "@/components/ui/UserTypeSwitch/UserTypeSwitch";
import SectionInfo from "@/components/ui/SectionInfo/SectionInfo";
import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import { Icon } from "@iconify/react";
import { TSupplierProfileData } from "@/store/apps/supplier/types";
import { TDocuments, TSupplierInfoProps } from "./types";
import { useForm, Controller, UseFormSetError } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { eighteenYearsAgo } from "@/utils/helpers/dateHelpers";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import AttachMultipleUploadedFileToSupplierProfile from "@/components/containers/AttachUploadedFileToProfiles/AttachMultipleUploadedFileToSupplierProfile";
import { useGetSupplierDocumentsQuery } from "@/store/apps/supplier";
import { SetHookFormError } from "@/utils/services/utils";
import Input from "@/components/ui/inputs/Input";
import NumberInput from "@/components/ui/inputs/NumberInput";
import { twMerge } from "tailwind-merge";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import { legalTooltip, nationalTooltip } from "./utils";

interface ISexItems {
  label: string;
  id: string;
}

export interface IsupplierDataRefProps {
  handleError?: SetHookFormError;
}

const SupplierInfo = (
  { onChangeActiveStep, supplierData, onSubmitData, isEdit }: TSupplierInfoProps,
  ref: ForwardedRef<IsupplierDataRefProps>
) => {
  const { t } = useTranslation();
  const [submitCount, setSubmitCount] = useState(0);

  const sexItems: ISexItems[] = [
    { label: t("supplier.profile.male"), id: "Male" },
    { label: t("supplier.profile.female"), id: "Female" }
  ];

  const { data: documentsInServerData, isLoading: isLoadingGetDocsInServer } = useGetSupplierDocumentsQuery(undefined, {
    skip: !isEdit
  });

  const {
    control,
    handleSubmit,
    setValue,
    setError,
    getValues,
    clearErrors,
    reset,
    watch,
    formState: { errors, isSubmitting }
  } = useForm({
    defaultValues: {
      birthDay: supplierData?.identity?.birthDay || undefined,
      companyName: supplierData?.identity?.companyName || undefined,
      companyType: supplierData?.identity?.companyType || undefined,
      economicCode: supplierData?.identity?.economicCode || undefined,
      isLegalPerson: supplierData?.identity?.isLegalPerson || false,
      nationalCode: supplierData?.identity?.nationalCode || undefined,
      registrationNumber: supplierData?.identity?.registrationNumber || undefined,
      vatNumber: supplierData?.identity?.vatNumber || undefined,
      sex: supplierData?.identity?.sex || undefined,
      documents: {
        "legal-national-front": supplierData?.documents?.["legal-national-front"] || undefined,
        "legal-national-back": supplierData?.documents?.["legal-national-back"] || undefined,
        "legal-newspaper": supplierData?.documents?.["legal-newspaper"] || undefined,
        "legal-certificate-of-added-value": supplierData?.documents?.["legal-certificate-of-added-value"] || undefined,
        "national-front": supplierData?.documents?.["national-front"] || undefined,
        "national-back": supplierData?.documents?.["national-back"] || undefined
      } as any
    },
    resolver: yupResolver(supplierInfoValidationSchema({ isEdit: !!isEdit })),
    mode: "onChange"
  });

  const isLegalPerson = watch("isLegalPerson");
  const documents = watch("documents") as TDocuments;

  useEffect(() => {
    const clearFields = (fields: string[]) => {
      fields.forEach(field => setValue(field as any, ""));
    };

    const setFields = (data: Record<string, any>) => {
      Object.entries(data).forEach(([key, value]) => setValue(key as any, value));
    };

    if (isLegalPerson) {
      setFields({
        companyName: supplierData?.identity?.companyName || "",
        companyType: supplierData?.identity?.companyType || "",
        economicCode: supplierData?.identity?.economicCode || "",
        registrationNumber: supplierData?.identity?.registrationNumber || "",
        vatNumber: supplierData?.identity?.vatNumber || ""
      });

      clearFields(["nationalCode", "birthDay", "sex"]);
    } else {
      setFields({
        nationalCode: supplierData?.identity?.nationalCode || "",
        birthDay: supplierData?.identity?.birthDay || "",
        sex: supplierData?.identity?.sex || "",
        vatNumber: supplierData?.identity?.vatNumber || ""
      });

      clearFields(["companyName", "companyType", "economicCode", "registrationNumber"]);
    }
  }, [isLegalPerson, supplierData?.identity]);

  const onSubmit = (data: TSupplierProfileData["identity"] & { documents?: TDocuments }) => {
    const {
      birthDay,
      companyName,
      vatNumber,
      companyType,
      economicCode,
      isLegalPerson,
      nationalCode,
      registrationNumber,
      sex,
      documents
    } = data;

    const legalBody = {
      companyName,
      companyType,
      economicCode,
      isLegalPerson,
      registrationNumber,
      vatNumber,
      documents
    };
    const personalBody = {
      sex,
      birthDay,
      vatNumber,
      nationalCode,
      isLegalPerson,
      documents
    };
    const body = data?.isLegalPerson ? legalBody : personalBody;

    setSubmitCount(prev => prev + 1);

    onSubmitData(body);
    onChangeActiveStep(1);
  };

  useEffect(() => {
    if (isEdit) {
      [
        "legal-national-front",
        "legal-national-back",
        "legal-newspaper",
        "legal-certificate-of-added-value",
        "national-front",
        "national-back"
      ]?.forEach(tag => {
        const existDocumentsInServer = documentsInServerData?.data?.filter(item => item.tag.startsWith(tag));

        if (existDocumentsInServer?.length) {
          setValue(
            `documents.${tag}` as any,
            existDocumentsInServer.map(item => ({ id: item.mediaId, url: item.media.url }))
          );
        }
      });
    }
  }, [JSON.stringify(documentsInServerData)]);

  useImperativeHandle(
    ref,
    () => ({
      handleError: (field, value) => {
        setError(field as any, { type: "manual", message: value?.message });
      }
    }),
    [setError]
  );

  const onChangeAttachUploadedFileToRetailerProfile = (tag: string, mediaId: string, file?: File) => {
    const currentValue = documents?.[tag] || [];

    if (!Array.isArray(currentValue)) {
      console.error(`Field ${tag} is not an array`);
      return;
    }

    if (file) {
      const reader = new FileReader();
      reader.onload = event => {
        const newItem = { id: mediaId, url: event?.target?.result as string };
        const updatedValue = [...currentValue, newItem];
        setValue(`documents.${tag}` as any, updatedValue);
      };

      reader.readAsDataURL(file); // Read the file as a Data URL
    } else {
      const newItem = { id: mediaId };
      const updatedValue = [...currentValue, newItem];
      setValue(`documents.${tag}` as any, updatedValue);
    }

    setTimeout(() => {
      // setError(`documents.${tag}` as any, { type: "manual", message: undefined });
      clearErrors(`documents.${tag}` as any);
    }, 0);
  };

  const onErrorAttachUploadedFileToRetailerProfile = (tag: string, errorMessage?: string) => {
    // setFieldTouched(`documents.${tag}`, true);
    setTimeout(() => {
      setError(`documents.${tag}` as any, { type: "manual", message: t(`${errorMessage}`) });
    }, 0);
  };

  const onRemoveAttachUploadedFileToRetailerProfile = (tag: string, index: number) => {
    // Get the current value of the tag field
    const currentValue = documents?.[tag];

    // Ensure the current value is an array
    if (Array.isArray(currentValue)) {
      // Remove the item at the specified index
      const updatedValue = currentValue.filter((_, i) => i !== index);

      // Update the field value with the new array
      setValue(`documents.${tag}` as any, !updatedValue?.length ? undefined : updatedValue);
    } else {
      console.error(`Field ${tag} is not an array`);
    }
  };

  // const onFileChange = (tag: string, mediaId: string, file?: File) => {
  //   const fieldPath = `documents.${tag}`;
  //   const currentValue = control.getValues(fieldPath) || [];

  //   if (file) {
  //     const reader = new FileReader();
  //     reader.onload = event => {
  //       const newItem = { id: mediaId, url: event?.target?.result as string };
  //       setValue(fieldPath, [...currentValue, newItem]);
  //     };
  //     reader.readAsDataURL(file);
  //   } else {
  //     const newItem = { id: mediaId };
  //     setValue(fieldPath, [...currentValue, newItem]);
  //   }

  //   clearErrors(fieldPath);
  // };

  // const onFileRemove = (tag: string, index: number) => {
  //   const fieldPath = `documents.${tag}`;
  //   const currentValue = control.getValues(fieldPath) || [];

  //   if (Array.isArray(currentValue)) {
  //     const updatedValue = currentValue.filter((_, i) => i !== index);
  //     setValue(fieldPath, updatedValue.length ? updatedValue : undefined);
  //   }
  // };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="h-fit flex flex-col">
      <Grid
        className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 xmd:pr-3 xmd:pt-3"
        container
        spacing={{ xs: 1, sm: 1.5 }}
      >
        <Grid item xs={12} md={12} className="xmd:mb-5 mb-3">
          <p className="text-gray-500 text-body3-regular mb-3">{t("userSwitch")}</p>

          <Controller
            name="isLegalPerson"
            key="isLegalPerson"
            control={control}
            render={({ field }) => (
              <UserTypeSwitch
                checked={!!field.value}
                onChange={v => {
                  field.onChange(v);
                  clearErrors();

                  // setValue("isLegalPerson", v);

                  setValue(
                    "vatNumber",
                    (supplierData?.identity?.isLegalPerson && v) || (!supplierData?.identity?.isLegalPerson && !v)
                      ? supplierData?.identity?.vatNumber
                      : ""
                  );
                }}
                labels={{
                  legal: t("legalUser.title"),
                  real: t("realUser.title")
                }}
                subTitles={{
                  legal: t("legalUser.subTitle"),
                  real: t("realUser.subTitle")
                }}
              />
            )}
          />
        </Grid>
        {isLegalPerson ? (
          <>
            <Grid item xs={12} md={12}>
              <Grid container spacing={{ xs: 1, sm: 1.5 }}>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="companyType"
                    key="companyType"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        autoComplete="off"
                        placeholder={t("supplier.profile.companyType")}
                        label={t("supplier.profile.companyType")}
                        error={Boolean(errors.companyType?.message)}
                        helperText={errors?.companyType?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="companyName"
                    key="companyName"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        autoComplete="off"
                        label={t("supplier.profile.companyName")}
                        placeholder={t("supplier.profile.companyName")}
                        error={Boolean(errors.companyName?.message)}
                        helperText={errors.companyName?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="registrationNumber"
                    key="registrationNumber"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        {...field}
                        autoComplete="off"
                        placeholder={t("supplier.profile.registrationNumber")}
                        label={t("supplier.profile.registrationNumber")}
                        error={Boolean(errors.registrationNumber?.message)}
                        helperText={errors.registrationNumber?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="economicCode"
                    key="economicCode"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        {...field}
                        placeholder={t("supplier.profile.economicCode")}
                        label={t("supplier.profile.economicCode")}
                        error={Boolean(errors.economicCode?.message)}
                        helperText={errors.economicCode?.message || ""}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="vatNumber"
                key="vatNumber"
                control={control}
                render={({ field }) => (
                  <NumberInput
                    {...field}
                    autoComplete="off"
                    labelTooltipTitle={t("supplier.profile.vatNumberTooltip.title")}
                    labelTooltipDescription={t("supplier.profile.vatNumberTooltip.description")}
                    placeholder={t("supplier.profile.vatNumber")}
                    label={t("supplier.profile.vatNumber")}
                    error={Boolean(errors.vatNumber?.message)}
                    helperText={errors.vatNumber?.message || ""}
                  />
                )}
              />
            </Grid>
          </>
        ) : (
          <>
            <Grid item xs={12} md={12}>
              <Grid container spacing={{ xs: 1, sm: 1.5 }}>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="nationalCode"
                    key="nationalCode"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        {...field}
                        autoComplete="off"
                        placeholder={t("supplier.profile.nationalCode")}
                        label={t("supplier.profile.nationalCode")}
                        error={Boolean(errors.nationalCode?.message)}
                        helperText={errors.nationalCode?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="birthDay"
                    key="birthDay"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        {...field}
                        label={t("supplier.profile.birthDay")}
                        placeholder={t("supplier.profile.birthDay")}
                        maxDate={eighteenYearsAgo}
                        error={Boolean(errors.birthDay?.message)}
                        helperText={errors.birthDay?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="sex"
                    key="sex"
                    control={control}
                    render={({ field }) => (
                      <CustomAutocomplete<ISexItems>
                        {...field}
                        options={sexItems}
                        value={sexItems?.find(item => item.id === field.value)}
                        placeholder={t("supplier.profile.sex")}
                        onChange={(e, v) => setValue("sex", v?.id)}
                        label={t("supplier.profile.sex")}
                        error={Boolean(errors.sex?.message)}
                        helperText={errors.sex?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="vatNumber"
                    key="vatNumber"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        {...field}
                        optional
                        autoComplete="off"
                        requiredStar={false}
                        labelTooltipTitle={t("supplier.profile.vatNumberTooltip.title")}
                        labelTooltipDescription={t("supplier.profile.vatNumberTooltip.description")}
                        placeholder={t("supplier.profile.vatNumber")}
                        label={t("supplier.profile.vatNumber")}
                        error={Boolean(errors.vatNumber?.message)}
                        helperText={errors.vatNumber?.message || ""}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>
          </>
        )}
        <Grid item xs={12} md={12}>
          {/* <p className="xmd:my-1 my-2 text-v2-content-primary text-body3-medium font-bold">
            {t("supplier.profile.sectionInfo.uploadDoc")}
          </p> */}
        </Grid>
        {isLegalPerson ? (
          <>
            {[
              {
                tag: "legal-national-front",
                title: t("supplier.profile.legalDoc.frontNationalCard"),
                header: t("supplier.profile.legalDoc.frontNationalCardHeader")
              },
              { tag: "legal-national-back", title: t("supplier.profile.legalDoc.backNationalCard") },
              {
                tag: "legal-newspaper",
                title: t("supplier.profile.legalDoc.legalNewspaper"),
                header: t("supplier.profile.legalDoc.legalNewspaperHeader")
              },
              { tag: "legal-certificate-of-added-value", title: t("supplier.profile.legalDoc.legalCertificate") }
            ].map(({ tag, title, header }, index) => (
              <Grid item xs={12} md={6} key={tag} alignSelf="flex-end">
                <div className="mb-2.5 text-body3-medium text-v2-content-primary">
                  {!!header && <p className={twMerge("", index > 0 ? "xmd:mt-0 mt-3" : "")}>{header}</p>}
                </div>{" "}
                <Controller
                  name={`documents.${tag}` as any}
                  key={`documents.${tag}` as any}
                  control={control}
                  render={({ field, fieldState }) => (
                    <>
                      <AttachMultipleUploadedFileToSupplierProfile
                        isEdit={isEdit}
                        tag={tag}
                        labelTooltipTitle={legalTooltip[tag]?.title}
                        labelTooltipDescription={legalTooltip[tag]?.description}
                        documents={documents}
                        title={title}
                        onChange={(mediaId, fileTag, file) => {
                          onChangeAttachUploadedFileToRetailerProfile(fileTag, mediaId, file);
                        }}
                        onUploadDoc={() => clearErrors(`documents.${tag}` as any)}
                        onError={(fileTag, error) => onErrorAttachUploadedFileToRetailerProfile(fileTag, error)}
                        onRemove={(fileTag, index) => onRemoveAttachUploadedFileToRetailerProfile(fileTag, index)}
                      />
                      {errors?.documents?.[tag]?.message && (
                        <InputHelper>{errors?.documents?.[tag]?.message as string}</InputHelper>
                      )}
                    </>
                  )}
                />
              </Grid>
            ))}
          </>
        ) : (
          <>
            {[
              {
                tag: "national-front",
                title: t("supplier.profile.personalDoc.frontNationalCard"),
                header: t("supplier.profile.legalDoc.frontNationalCardHeader")
              },
              { tag: "national-back", title: t("supplier.profile.personalDoc.backNationalCard") }
            ].map(({ tag, title, header }) => (
              <Grid item xs={12} md={6} key={tag} alignSelf="flex-end">
                <div className="mb-2.5 text-body3-medium text-v2-content-primary">{!!header && <p>{header}</p>}</div>
                <Controller
                  name={`documents.${tag}` as any}
                  key={`documents.${tag}` as any}
                  control={control}
                  render={({ field, fieldState }) => (
                    <>
                      <AttachMultipleUploadedFileToSupplierProfile
                        isEdit={isEdit}
                        tag={tag}
                        maxCount={1}
                        labelTooltipTitle={nationalTooltip[tag]?.title}
                        labelTooltipDescription={nationalTooltip[tag]?.description}
                        documents={documents}
                        title={title}
                        onChange={(mediaId, fileTag, file) =>
                          onChangeAttachUploadedFileToRetailerProfile(fileTag, mediaId, file)
                        }
                        onUploadDoc={() => clearErrors(`documents.${tag}` as any)}
                        onError={(fileTag, error) => onErrorAttachUploadedFileToRetailerProfile(fileTag, error)}
                        onRemove={(fileTag, index) => onRemoveAttachUploadedFileToRetailerProfile(fileTag, index)}
                      />
                      {errors?.documents?.[tag]?.message && (
                        <InputHelper>{errors?.documents?.[tag]?.message as string}</InputHelper>
                      )}
                    </>
                  )}
                />
              </Grid>
            ))}
          </>
        )}
      </Grid>

      <BottomAction
        saveButtonText={t("saveChanges")}
        saveButtonProps={{
          type: "submit"
        }}
        cancelButtonText={t("supplier.profile.cancel")}
        cancelButtonProps={{
          onClick: () => {
            onChangeActiveStep(undefined);
          }
        }}
      />

      <div className={twMerge("w-full justify-end hidden xmd:flex")}>
        <CustomButton
          type="submit"
          // endIcon={
          //   <Icon icon="mingcute:arrow-left-line" width={20} height={20} className="supplier-profile-next-button" />
          // }
        >
          {t("saveChanges")}
        </CustomButton>
      </div>
    </form>
  );
};

export default React.forwardRef(SupplierInfo);
