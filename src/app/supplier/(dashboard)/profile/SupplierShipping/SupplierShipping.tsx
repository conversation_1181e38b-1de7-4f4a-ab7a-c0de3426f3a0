import { useGetSupplierShippingQuery, usePutSupplierShippingMutation } from "@/store/apps/supplier";
import { TSupplierShippingData } from "@/store/apps/supplier/types";
import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { TSupplierShippingFormProps } from "./types";
import { clientDefaultErrorHandler, SetHookFormError } from "@/utils/services/utils";
import { snakeToCamelCaseHookFormWrapper, SnakeToCamelFieldErrorWrapper } from "@/utils/helpers";
import SupplierShippingForm from "@/components/forms/SupplierShippingForm/SupplierShippingForm";
import { CircularProgress, Stack } from "@mui/material";
import { Icon } from "@iconify/react";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { FormikHelpers } from "formik";
import { UseFormSetError } from "react-hook-form";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import ProfileContentContainer from "@/components/containers/ProfileStepper/ProfileContentContainer";

const SupplierShipping = ({ onChangeActiveStep }: TSupplierShippingFormProps) => {
  const { t } = useTranslation();

  const { data: supplierShipping, isLoading: isSupplierShippingLoading, refetch } = useGetSupplierShippingQuery({});
  const [putSupplierShipping, { isLoading: isPutSupplierShippingLoading }] = usePutSupplierShippingMutation();

  useEffect(() => {
    refetch();
  }, []);

  const isDisabled = isPutSupplierShippingLoading;
  const isLoading = isSupplierShippingLoading;

  const [isShippingFormValid, setIsShippingFormValid] = useState<boolean>(false);

  const nextButtonDisabled = !supplierShipping?.data?.length;

  const onSubmit = async ({ policies: body }: { policies: TSupplierShippingData[] }, setError?: SetHookFormError) => {
    try {
      await putSupplierShipping({ body }).then(res => {
        const error = (res as any)?.error?.data;

        if ((res as any)?.error) {
          clientDefaultErrorHandler({
            error: (res as any)?.error,
            bodyError: error,
            setHookFormFieldError: snakeToCamelCaseHookFormWrapper(setError)
          });
        }
        if ("data" in res && res?.data) {
          // onChangeActiveStep(4);
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const onBack = () => {
    onChangeActiveStep(3);
  };

  const onNext = () => {
    onChangeActiveStep(5);
  };

  const onIsValidChange = useCallback((v: boolean) => {
    setIsShippingFormValid(v);
  }, []);

  return (
    <>
      <MobileAppBar title={t("shipping")} hasBack onBack={onBack} />
      <ProfileContentContainer>
        <div className="xmd:h-full xmd:flex flex-col">
          <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6">
            <SupplierShippingForm
              hasAlert
              hasBottomAction
              isLoading={isLoading}
              handleSubmit={onSubmit}
              isDisabled={isDisabled}
              values={supplierShipping?.data}
              onIsValidChange={onIsValidChange}
              onBack={() => {
                onChangeActiveStep(3);
              }}
            />
          </div>

          <BottomAction
            saveButtonText={t("saveChanges")}
            saveButtonProps={{
              disabled: nextButtonDisabled,
              onClick: () => onNext()
            }}
            cancelButtonText={t("supplier.profile.cancel")}
            cancelButtonProps={{
              onClick: () => {
                onBack();
              }
            }}
          />

          <div className="xmd:flex hidden gap-2 justify-end flex-wrap">
            {/* {!!onBack && (
          <CustomButton
            color="secondary"
            className="supplier-profile-footer-button"
            onClick={onBack}
            startIcon={
              <Icon icon="mingcute:arrow-right-line" width={20} height={20} className="supplier-profile-next-button" />
            }
          >
            {t("supplier.profile.buttonPrev")}
          </CustomButton>
        )} */}
            {!!onNext && (
              <CustomButton
                className="supplier-profile-footer-button"
                onClick={onNext}
                // endIcon={
                //   <Icon icon="mingcute:arrow-left-line" width={20} height={20} className="supplier-profile-next-button" />
                // }
                disabled={nextButtonDisabled}
              >
                {t("saveChanges")}
              </CustomButton>
            )}
          </div>
        </div>
      </ProfileContentContainer>
    </>
  );
};

export default SupplierShipping;
