import { IS_IRAN_SERVED } from "@/constants";
import { Box, CircularProgress, Grid, Stack, useMediaQuery } from "@mui/material";
import { useTranslation } from "react-i18next";
import { supplierBankValidation, supplierStoreValidation } from "@/utils/validations/profile/supplier";
import { ISupplierBank, TSupplierBankFormProps } from "./types";
import React, { ForwardedRef, useEffect, useImperativeHandle, useMemo } from "react";
import { IsupplierDataRefProps } from "../SupplierInfo/SupplierInfo";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { Theme } from "@mui/system";
import { useForm, Controller, SubmitHandler, useFieldArray } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import NumberInput from "@/components/ui/inputs/NumberInput";
import Input from "@/components/ui/inputs/Input";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import { Icon } from "@iconify/react";
import { SetHookFormError } from "@/utils/services/utils";

const SupplierBank = (
  { onChangeActiveStep, supplierData, onSubmitData, onPrev, isLoading }: TSupplierBankFormProps,
  ref: ForwardedRef<IsupplierDataRefProps>
) => {
  const { t } = useTranslation();

  const ibanEndAdornment = IS_IRAN_SERVED ? "IR" : "";

  const initialValues = {
    bankAccount: supplierData?.bankAccount || {
      bic: supplierData?.bankAccount?.bic || undefined,
      holderName: supplierData?.bankAccount?.holderName || undefined,
      iban: supplierData?.bankAccount?.iban || undefined
    }
  };

  const {
    control,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    watch,
    formState: { errors }
  } = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(supplierBankValidation as any) as any,
    mode: "onChange"
  });

  useImperativeHandle(
    ref,
    () => ({
      handleError: (field: string, { message }) => {
        setError(field as any, { type: "manual", message: message });
      }
    }),
    [setError]
  );

  const onSubmit: SubmitHandler<ISupplierBank> = async data => {
    const { bankAccount } = data;

    const body = {
      bankAccount: {
        holderName: bankAccount?.holderName,
        iban: bankAccount?.iban
      }
    };

    onSubmitData(
      body,
      (field, message) => setError(field as any, { message: message as any }) as unknown as SetHookFormError
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="h-full flex flex-col">
      <Grid
        container
        spacing={{ xs: 1, sm: 1.5 }}
        className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 xmd:pr-3 xmd:pt-3 "
      >
        <Grid item xs={12} md={12}>
          <div className="xmd:pb-6 xmd:border-b xmd:border-b-[1.5px] xmd:border-b-v2-border-primary xmd:mb-6 mb-3">
            <h2 className="text-body2-medium font-semibold text-v2-content-primary">
              {t("supplier.profile.step.bankInfo")}
            </h2>
          </div>
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="bankAccount.holderName"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                autoComplete="off"
                placeholder={t("supplier.profile.holderName")}
                label={t("supplier.profile.holderName")}
                error={Boolean(errors?.bankAccount?.holderName?.message)}
                helperText={errors?.bankAccount?.holderName?.message || ""}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="bankAccount.iban"
            control={control}
            render={({ field }) => (
              <NumberInput
                {...field}
                autoComplete="off"
                dir="ltr"
                style={{
                  textAlign: "right",
                  paddingRight: Boolean(errors?.bankAccount?.iban?.message) ? "0px !important" : "12px !important"
                }}
                endAdornment={" " + ibanEndAdornment + " "}
                placeholder={t("supplier.profile.iban")}
                label={t("supplier.profile.iban")}
                error={Boolean(errors?.bankAccount?.iban?.message)}
                helperText={errors?.bankAccount?.iban?.message || ""}
              />
            )}
          />{" "}
        </Grid>

        <BottomAction
          saveButtonText={isLoading ? <CircularProgress color="info" size={20} /> : t("saveBankInfo")}
          cancelButtonText={t("supplier.profile.cancel")}
          saveButtonProps={{
            type: "submit"
          }}
          cancelButtonProps={{
            onClick: () => {
              onPrev(watch());
              onChangeActiveStep(2);
            }
          }}
        />

        <div className="xmd:flex hidden mr-auto items-center flex-wrap gap-2 mt-4">
          {/* <CustomButton
          color="secondary"
          className="supplier-profile-footer-button"
          onClick={() => {
            onPrev(watch());
            onChangeActiveStep(0);
          }}
          startIcon={
            <Icon icon="mingcute:arrow-right-line" width={20} height={20} className="supplier-profile-next-button" />
          }
        >
          {t("supplier.profile.buttonPrev")}
        </CustomButton> */}
          <CustomButton type="submit" className="supplier-profile-footer-button">
            {isLoading ? <CircularProgress color="info" size={20} /> : t("saveBankInfo")}
          </CustomButton>
        </div>

        <Grid item xs={12} md={12}>
          <div className="flex items-center gap-1.5 p-2 rounded-md bg-v2-surface-info ">
            <Icon icon="solar:info-circle-outline" className="size-5 text-v2-content-primary shrink-0" />
            <span className="text-body4-medium text-v2-content-primary">{t("supplier.profile.bankInfoHint")}</span>
          </div>
        </Grid>
      </Grid>
    </form>
  );
};

export default React.forwardRef(SupplierBank);
