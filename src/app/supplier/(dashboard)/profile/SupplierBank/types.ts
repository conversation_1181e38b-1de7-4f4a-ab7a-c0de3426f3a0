import { TSupplierBaseFormProps } from "../types";
import { TSupplierProfile } from "../SupplierProfile";
import { SetHookFormError } from "@/utils/services/utils";

export interface ISupplierBank {
  bankAccount?: {
    bic?: string;
    holderName?: string;
    iban?: string;
  };
}

export type TSupplierBankFormProps = {
  supplierData?: TSupplierProfile;
  onSubmitData: (data: ISupplierBank, setFieldError?: SetHookFormError) => void;
  onPrev: (data: ISupplierBank) => void;
  isLoading?: boolean;
} & TSupplierBaseFormProps;
