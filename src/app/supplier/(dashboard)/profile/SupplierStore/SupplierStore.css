#sx-suppliergeneral-17359 {
  margin-block: 2;
}

#sx-suppliergeneral-17548 {
}

#sx-suppliergeneral-17380 {
  max-height: 400px;
}

.supplier-profile-photo {
  border-radius: 50%;
}

.supplier-profile-store-image-upload-button.custom-button.MuiButton-containedSecondary {
  color: rgb(var(--color-purple-500));
}

.supplier-profile-avatar {
  width: 65px;
  height: 65px;
}

.supplier-profile-store-label-image {
  margin-bottom: 14px;
}

.supplier-profile-store-label-image.styled-custom-form-label.MuiTypography-root {
  color: rgb(var(--color-gray-999));
}

.supplier-profile-store-image-upload-button {
  max-height: 40px;
}

@media (max-width: 768px) {
  .supplier-profile-store-image-stack {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* .supplier-profile-store-image-stack-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
  } */

  .supplier-profile-store-image-upload-button.custom-button.MuiButton-containedSecondary {
    background-color: transparent;
    color: rgb(var(--color-purple-500));
    /* border-color: transparent; */
  }
}
