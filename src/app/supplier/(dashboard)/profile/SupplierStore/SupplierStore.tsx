import "./SupplierStore.css";
import { IS_IRAN_SERVED } from "@/constants";
import { Box, CircularProgress, Grid, Stack, useMediaQuery } from "@mui/material";
import { useTranslation } from "react-i18next";
import { getProcessingTimeItems } from "../utils";
import { supplierStoreValidation } from "@/utils/validations/profile/supplier";
import { TSupplierStoreFormProps } from "./types";
import React, { ForwardedRef, useImperativeHandle, useMemo } from "react";
import SectionInfo from "@/components/ui/SectionInfo/SectionInfo";
import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import { Icon } from "@iconify/react";
import { extractMinMax, findItemByMinMax } from "@/components/containers/ProfileStepper/utils";
import { IsupplierDataRefProps } from "../SupplierInfo/SupplierInfo";
import Image from "next/image";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import CustomFormLabel from "@/components/ui/CustomFormLabel/CustomFormLabel";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import CircleWithButtonImageUploader from "@/components/containers/ImageUploader/withUi/CircleWithButtonImageUploader";
import ImageUploaderWithCropper from "@/components/containers/ImageUploader/ImageUploaderWithCropper";
import { Theme } from "@mui/system";
import { twMerge } from "tailwind-merge";
import { useForm, Controller, SubmitHandler, useFieldArray } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import NumberInput from "@/components/ui/inputs/NumberInput";
import Input from "@/components/ui/inputs/Input";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import Textarea from "@/components/ui/inputs/Textarea/Textarea";
import InputLabel from "@/components/ui/inputs/Input/InputLabel";

export interface ISupplierStore {
  contactEmail?: string;
  contactNumber?: string;
  name?: string;
  logo?: string;
  cover?: string;
  website?: string;
  biography?: string;
  processingTime?: {
    max: number;
    min: number;
  };
  bankAccount?: {
    bic?: string;
    holderName?: string;
    iban?: string;
  };
}

const SupplierStore = (
  { onChangeActiveStep, supplierData, onSubmitData, onPrev }: TSupplierStoreFormProps,
  ref: ForwardedRef<IsupplierDataRefProps>
) => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const ibanEndAdornment = IS_IRAN_SERVED ? "IR" : "";

  const initialValues = useMemo(() => {
    return {
      contactEmail: supplierData?.contactEmail || "",
      contactNumber: supplierData?.contactNumber || "",
      name: supplierData?.name || undefined,
      logo: supplierData?.logo || undefined,
      cover: supplierData?.cover || undefined,
      website: supplierData?.website || undefined,
      biography: supplierData?.biography || undefined,
      processingTime: supplierData?.processingTime || undefined
    };
  }, [supplierData]);

  const {
    control,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    watch,
    formState: { errors }
  } = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(supplierStoreValidation as any) as any,
    mode: "onChange"
  });

  useImperativeHandle(
    ref,
    () => ({
      handleError: (field: string, { message }) => {
        setError(field as any, { type: "manual", message: message });
      }
    }),
    [setError]
  );

  const onSubmit: SubmitHandler<ISupplierStore> = async data => {
    const { bankAccount, ...restValues } = data;

    const body = data;

    setTimeout(() => {
      onSubmitData(body);
      onChangeActiveStep(2);
    }, 0);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="h-full flex flex-col">
      <Grid
        container
        spacing={{ xs: 1, sm: 1.5 }}
        className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 xmd:pr-3 xmd:pt-3 "
      >
        {/* <Grid item xs={12} md={12}>
          <SectionInfo title={t("supplier.profile.initialInfo")} className="mb-2" />
        </Grid> */}

        <Grid item xs={12} md={12}>
          <Controller
            name="cover"
            control={control}
            render={({ field }) => (
              <ImageUploaderWithCropper
                serverFileKind="public"
                value={field?.value}
                cropperProps={{ aspect: 3.9 / 1 }}
                withCompressorMaxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
                maxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
                onUploaded={v => {
                  setValue("cover", v.url);
                  // setError("cover", { message: "" });
                  clearErrors("cover");
                }}
                onError={msg => {
                  // setFieldTouched("cover", true);

                  setTimeout(() => {
                    setError("cover", { message: t(`${msg}`) });
                  }, 0);
                }}
              >
                {({ value, isUploading, isLoading }) => (
                  <div className="relative rounded-[10px] h-[220px] w-full overflow-hidden">
                    {isUploading || isLoading ? (
                      <div className="flex items-center justify-center bg-background h-full">
                        <CircularProgress size={35} />
                      </div>
                    ) : (
                      <>
                        {value ? (
                          <Image src={value || ""} fill alt="cover" className="object-cover" />
                        ) : (
                          <div className="flex items-center justify-center h-full bg-background">
                            <Image src="/images/svgs/cover-empty.svg" width={280} height={155} alt="cover" />
                          </div>
                        )}
                        <div className="absolute bottom-6 left-6 flex items-center gap-2">
                          {value && (
                            <CustomButton
                              className={twMerge(
                                "!bg-v2-content-subtle !border-transparent !max-h-10 min-w-10 w-10 p-0"
                              )}
                              onClick={e => {
                                setValue("cover", "");
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                            >
                              {isMobile ? (
                                <Icon icon="solar:trash-bin-trash-outline" width={15} height={15} />
                              ) : (
                                <Icon icon="solar:trash-bin-trash-outline" width={20} height={20} />
                              )}
                            </CustomButton>
                          )}
                          <CustomButton
                            className={twMerge(
                              "!bg-v2-content-subtle !border-transparent !max-h-10",
                              isMobile && "min-w-14 w-14 p-0"
                            )}
                            startIcon={isMobile ? undefined : <Icon icon="mage:edit-pen" width={18} height={18} />}
                          >
                            {isMobile ? (
                              <Icon icon="mage:edit-pen" width={15} height={15} />
                            ) : (
                              t("supplier.profile.changeCover")
                            )}
                          </CustomButton>
                        </div>
                      </>
                    )}
                  </div>
                )}
              </ImageUploaderWithCropper>
            )}
          />

          {Boolean(errors?.cover?.message) && <InputHelper> {errors?.cover?.message}</InputHelper>}
        </Grid>

        <Grid item xs={12} md={12}>
          <Stack className="supplier-profile-store-image-stack-wrapper">
            <InputLabel optional htmlFor="logo" requiredStar={false} containerClassName="mb-1">
              {t("supplier.profile.profileLogo")}
            </InputLabel>

            <Controller
              name="logo"
              control={control}
              render={({ field }) => (
                <CircleWithButtonImageUploader
                  serverFileKind="public"
                  value={field?.value}
                  onUploaded={v => {
                    setValue("logo", v.url);
                    // setError("logo", { message: "" });
                    clearErrors("logo");
                  }}
                  onError={msg => {
                    setTimeout(() => {
                      setError("logo", { message: t(`${msg}`) });
                    }, 0);
                  }}
                  onRemove={() => {
                    setValue("logo", "");
                    // setTimeout(() => {
                    //   setFieldTouched("logo", true);
                    // }, 0);
                  }}
                  cropperProps={{ aspect: 1 }}
                  withCompressorMaxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
                  maxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
                  buttonLabel={field?.value ? t("supplier.profile.changeLogo") : t("supplier.profile.uploadLogo")}
                  className="w-fit"
                  containerClassName="items-center gap-4 "
                />
              )}
            />
            {!!errors?.logo?.message && <InputHelper>{errors.logo.message}</InputHelper>}
          </Stack>
        </Grid>

        <Grid item xs={12} md={12}>
          <Grid container>
            <Grid item xs={12} md={6}>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    autoComplete="off"
                    placeholder={t("supplier.profile.storeName")}
                    label={t("supplier.profile.storeName")}
                    value={field.value}
                    onChange={field.onChange}
                    onBlur={field.onBlur}
                    error={Boolean(errors.name?.message)}
                    helperText={errors?.name?.message || ""}
                  />
                )}
              />
              <>
                <span className="text-gray-999 text-body4-medium whitespace-nowrap">
                  {t("supplier.profile.allowedStoreNameTitle")} :{" "}
                </span>
                <span className="text-gray-500 text-body4-medium xmd:whitespace-nowrap">
                  {t("supplier.profile.allowedStoreNameDescription")}
                </span>
              </>
            </Grid>
          </Grid>
        </Grid>
        {/* <Grid item xs={12} md={6} className="xmd:flex hidden" />
        <Grid item xs={12} md={6} className="xmd:flex hidden" /> */}

        <Grid item xs={12} md={6}>
          <Controller
            name="website"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                dir="ltr"
                autoComplete="off"
                optional
                requiredStar={false}
                label={t("supplier.profile.website")}
                placeholder={t("supplier.profile.placeholders.website")}
                name="website"
                endAdornment={<Icon icon="solar:earth-outline" className="size-4 text-v2-content-primary" />}
                error={Boolean(errors.website?.message)}
                helperText={errors?.website?.message || ""}
              />
            )}
          />{" "}
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="contactEmail"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                dir="ltr"
                autoComplete="off"
                label={t("supplier.profile.contactEmail")}
                placeholder={t("supplier.profile.placeholders.email")}
                endAdornment={<Icon icon="solar:letter-outline" className="size-4 text-v2-content-primary" />}
                error={Boolean(errors.contactEmail?.message)}
                helperText={errors?.contactEmail?.message || ""}
              />
            )}
          />{" "}
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="contactNumber"
            control={control}
            render={({ field }) => (
              <NumberInput
                {...field}
                autoComplete="off"
                dir="ltr"
                placeholder={t("supplier.profile.contactNumberPlaceholder")}
                label={t("supplier.profile.contactNumber")}
                endAdornment={
                  <Icon
                    icon="solar:smartphone-2-outline"
                    className="size-4 text-v2-content-primary"
                    color="rgb(var(--color-gray-999))"
                  />
                }
                error={Boolean(errors.contactNumber?.message)}
                helperText={errors?.contactNumber?.message || ""}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <InputLabel>{t("supplier.profile.processingTime")}</InputLabel>

          <div className="flex items-start gap-2 mt-1">
            <div className="flex-1">
              <Controller
                name="processingTime"
                control={control}
                render={({ field }) => (
                  <NumberInput
                    {...field}
                    autoComplete="off"
                    value={(field?.value as any)?.min}
                    placeholder={t("supplier.profile.processingTimeFrom")}
                    onChange={({ target: { value } }) =>
                      setValue("processingTime", {
                        min: value ? Number(value) : ("" as any),
                        max: watch("processingTime.max")
                      })
                    }
                    error={Boolean(errors.processingTime?.min?.message) || !!errors.processingTime?.message}
                    helperText={errors?.processingTime?.min?.message || errors?.processingTime?.message || ""}
                    // labelTooltipTitle={t("supplier.profile.processingTimeHintTitle")}
                    // labelTooltipDescription={t("supplier.profile.processingTimeHintDes")}
                  />
                )}
              />
            </div>

            <p className="text-body3-medium text-gray-999 whitespace-nowrap mt-3">{t("till")}</p>
            <div className="flex-1">
              <Controller
                name="processingTime"
                control={control}
                render={({ field }) => (
                  <NumberInput
                    {...field}
                    autoComplete="off"
                    value={(field?.value as any)?.max}
                    placeholder={t("supplier.profile.processingTimeTo")}
                    onChange={({ target: { value } }) =>
                      setValue("processingTime", {
                        min: watch("processingTime.min"),
                        max: value ? Number(value) : ("" as any)
                      })
                    }
                    error={Boolean(errors.processingTime?.max?.message) || !!errors.processingTime?.message}
                    helperText={errors?.processingTime?.max?.message || errors?.processingTime?.message || ""}
                  />
                )}
              />
            </div>
            <p className="text-body3-medium text-gray-999 whitespace-nowrap mt-2 w-fit">
              {t("supplier.profile.daysOfWorking")}
            </p>
          </div>
        </Grid>

        <Grid item xs={12} md={12}>
          <Controller
            name="biography"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                autoComplete="off"
                rows={5}
                // optional
                label={t("supplier.profile.description")}
                placeholder={t("supplier.profile.descriptionPlaceholder")}
                // maxCharLength={4098}
                error={Boolean(errors.biography?.message)}
                helperText={errors?.biography?.message || ""}
              />
            )}
          />{" "}
        </Grid>

        {/* <Grid item xs={12} md={12}>
            <SectionInfo title={t("supplier.profile.storeInfo")} className="xmd:mt-4 xmd:mb-2 mt-2 " />
          </Grid> */}
      </Grid>

      <BottomAction
        saveButtonText={t("saveChanges")}
        cancelButtonText={t("supplier.profile.cancel")}
        saveButtonProps={{
          type: "submit"
        }}
        cancelButtonProps={{
          onClick: () => {
            onPrev(watch());
            onChangeActiveStep(0);
          }
        }}
      />

      <div className="xmd:flex hidden justify-end items-center flex-wrap gap-2">
        {/* <CustomButton
          color="secondary"
          className="supplier-profile-footer-button"
          onClick={() => {
            onPrev(watch());
            onChangeActiveStep(0);
          }}
          startIcon={
            <Icon icon="mingcute:arrow-right-line" width={20} height={20} className="supplier-profile-next-button" />
          }
        >
          {t("supplier.profile.buttonPrev")}
        </CustomButton> */}
        <CustomButton type="submit" className="supplier-profile-footer-button">
          {t("saveChanges")}
        </CustomButton>
      </div>
    </form>
  );
};

export default React.forwardRef(SupplierStore);
