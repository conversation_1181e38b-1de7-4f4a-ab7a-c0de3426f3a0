/* eslint-disable @typescript-eslint/no-unused-vars */
import "./SupplierAddress.css";

import { Box, CircularProgress, Grid, Stack } from "@mui/material";
import { useTranslation } from "react-i18next";
import { supplierAddressValidation } from "@/utils/validations/profile/supplier";
import { TSupplierAddressFormProps } from "./types";
import SectionInfo from "@/components/ui/SectionInfo/SectionInfo";
import LocationsSelect from "@/components/ui/CustomCountrySelect/LocationsSelect";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import React, { ForwardedRef, useEffect, useImperativeHandle, useMemo, useState } from "react";
import { Icon } from "@iconify/react";
import { IRetailerDataRefProps } from "@/app/retailer/(dashboard)/profile/RetailerInfo/types";
import { isValidUUID } from "@/utils/helpers";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { SetHookFormError } from "@/utils/services/utils";
import NumberInput from "@/components/ui/inputs/NumberInput";
import Input from "@/components/ui/inputs/Input";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";

export type TSupplierAddress = {
  address?: {
    locationId?: string;
    zip?: string;
    address1?: string;
  };
  warehouseAddress?: {
    locationId?: string;
    zip?: string;
    address1?: string;
    phoneNumber?: string;
  };
};

const SupplierAddress = (
  { supplierData, onChangeActiveStep, onSubmitData, isLoading, onPrev }: TSupplierAddressFormProps,
  ref: ForwardedRef<IRetailerDataRefProps>
) => {
  const { t } = useTranslation();
  const [addressChecked, setAddressChecked] = useState(false);

  const initialData = useMemo(
    () => ({
      address: {
        locationId:
          supplierData?.address?.locationId && isValidUUID(supplierData?.address?.locationId)
            ? supplierData?.address?.locationId
            : undefined,
        zip: supplierData?.address?.zip || "",
        address1: supplierData?.address?.address1 || ""
      },
      warehouseAddress: {
        locationId:
          supplierData?.warehouseAddress?.locationId && isValidUUID(supplierData?.warehouseAddress?.locationId)
            ? supplierData?.warehouseAddress?.locationId
            : undefined,
        zip: supplierData?.warehouseAddress?.zip || "",
        address1: supplierData?.warehouseAddress?.address1 || "",
        phoneNumber: supplierData?.warehouseAddress?.phoneNumber || ""
      }
    }),
    [supplierData]
  );

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    setError,
    clearErrors,
    formState: { errors, isValid }
  } = useForm<TSupplierAddress>({
    defaultValues: initialData,
    resolver: yupResolver(supplierAddressValidation) as any,
    mode: "onChange"
  });

  const addressLocationId = watch("address.locationId");
  const addressZip = watch("address.zip");
  const addressAddress1 = watch("address.address1");

  const onSubmit = async (values: TSupplierAddress) => {
    setTimeout(() => {
      onSubmitData(values);
      onChangeActiveStep(3);
    }, 0);
  };

  useImperativeHandle(
    ref,
    () => ({
      handleError: (field: string, { message }) => {
        setError(field as any, { message });
      }
    }),
    []
  );

  const onUpdateWareHouseAddress = () => {
    const updates: { [key: string]: string | undefined } = {
      "warehouseAddress.locationId": addressLocationId,
      "warehouseAddress.zip": addressZip,
      "warehouseAddress.address1": addressAddress1,
      "warehouseAddress.phoneNumber": supplierData?.contactNumber
    };

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) setValue(key as any, value);
    });
  };

  const onSetWarehouseAddress = (checked: boolean) => {
    setAddressChecked(checked);

    if (!checked) {
      const updates: { [key: string]: string | undefined } = {
        "warehouseAddress.locationId": undefined,
        "warehouseAddress.zip": "",
        "warehouseAddress.address1": "",
        "warehouseAddress.phoneNumber": ""
      };

      Object.entries(updates).forEach(([key, value]) => {
        setValue(key as any, value);
        clearErrors(key as any);
        // setFieldTouched(key, false);
      });

      return;
    }

    onUpdateWareHouseAddress();
  };

  useEffect(() => {
    const address = JSON.stringify({
      locationId: addressLocationId,
      zip: addressZip,
      address1: addressAddress1,
      phoneNumber: supplierData?.contactNumber
    });
    const warehouseAddress = JSON.stringify(getValues("warehouseAddress"));

    if (!!addressAddress1 && address === warehouseAddress) setAddressChecked(true);
  }, [supplierData, addressLocationId, addressZip, addressAddress1]);

  useEffect(() => {
    if (!addressChecked) return;
    onUpdateWareHouseAddress();
  }, [addressChecked, supplierData?.contactNumber, addressLocationId, addressZip, addressAddress1]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="h-full flex flex-col">
      <Grid
        container
        spacing={{ xs: 1, sm: 1.5 }}
        className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 xmd:pr-4 xmd:pt-4"
      >
        <Grid item xs={12} md={12}>
          <h3 className="text-subtitle-bold text-v2-content-primary">{t("addresses")}</h3>
          <p className="text-body3-regular text-v2-content-tertiary mt-2">{t("addressesSubtitle")}</p>

          <div className="flex items-center gap-2 xmd:mt-6 mt-3">
            <div className="size-6 bg-v2-surface-action-light flex items-center justify-center rounded-md">1</div>
            <p className="text-v2-content-primary text-body2-medium">{t("companyAddress")}</p>
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            name="address.locationId"
            control={control}
            render={({ field }) => (
              <LocationsSelect
                {...field}
                multiple={false}
                label={t("statecity")}
                placeholder={t("supplier.profile.cityPlaceholder")}
                onChange={value => {
                  setValue("address.locationId", value as string);
                  if (errors?.address?.locationId) clearErrors("address.locationId");
                }}
                error={Boolean(errors.address?.locationId?.message)}
                helperText={errors?.address?.locationId?.message || ""}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="address.zip"
            control={control}
            render={({ field }) => (
              <NumberInput
                {...field}
                autoComplete="off"
                placeholder={t("supplier.profile.zipPlaceholder")}
                label={t("supplier.profile.zip")}
                error={Boolean(errors?.address?.zip?.message)}
                helperText={errors?.address?.zip?.message || ""}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={12}>
          <Controller
            name="address.address1"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                autoComplete="off"
                label={t("supplier.profile.exactAddress")}
                placeholder={t("supplier.profile.exactAddressPlaceholder")}
                error={Boolean(errors.address?.address1?.message)}
                helperText={errors?.address?.address1?.message || ""}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={12}>
          {/* <SectionInfo title={t("supplier.profile.WarehouseAddressInfo")} className="xmd:mt-4 xmd:mb-0 my-2" /> */}
          <div className="flex items-center gap-2 xmd:mt-6 mt-2">
            <div className="size-6 bg-v2-surface-action-light flex items-center justify-center rounded-md">2</div>
            <p className="text-v2-content-primary text-body2-medium">{t("warehouseAddress")}</p>
          </div>
        </Grid>

        <Grid item xs={12} md={12}>
          <div className="flex items-center gap-2 ">
            <CustomSwitch
              checked={addressChecked}
              onChange={(e, checked) => onSetWarehouseAddress(checked)}
              labelClassName="!ml-0 !mr-0"
              textClassName="hidden"
            />
            <span>{t("supplier.profile.sameAddress")}</span>
          </div>
        </Grid>

        {!addressChecked && (
          <>
            <Grid item xs={12} md={6}>
              <Controller
                name="warehouseAddress.locationId"
                control={control}
                render={({ field }) => (
                  <LocationsSelect
                    {...field}
                    multiple={false}
                    value={watch("warehouseAddress.locationId")}
                    label={t("statecity")}
                    placeholder={t("supplier.profile.cityPlaceholder")}
                    onChange={value => {
                      setValue("warehouseAddress.locationId", value as string);
                      if (errors?.warehouseAddress?.locationId) clearErrors("warehouseAddress.locationId");
                    }}
                    error={Boolean(errors.warehouseAddress?.locationId?.message)}
                    helperText={errors?.warehouseAddress?.locationId?.message || ""}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="warehouseAddress.zip"
                control={control}
                render={({ field }) => (
                  <NumberInput
                    {...field}
                    autoComplete="off"
                    label={t("supplier.profile.zip")}
                    placeholder={t("supplier.profile.zipPlaceholder")}
                    error={Boolean(errors?.warehouseAddress?.zip?.message)}
                    helperText={errors?.warehouseAddress?.zip?.message || ""}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="warehouseAddress.phoneNumber"
                control={control}
                render={({ field }) => (
                  <NumberInput
                    {...field}
                    autoComplete="off"
                    label={t("supplier.profile.phone")}
                    placeholder={t("supplier.profile.phonePlaceholder")}
                    error={Boolean(errors?.warehouseAddress?.phoneNumber?.message)}
                    helperText={errors?.warehouseAddress?.phoneNumber?.message || ""}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={12}>
              <Controller
                name="warehouseAddress.address1"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    autoComplete="off"
                    label={t("supplier.profile.exactAddress")}
                    placeholder={t("supplier.profile.exactAddressPlaceholder")}
                    error={Boolean(errors.warehouseAddress?.address1?.message)}
                    helperText={errors?.warehouseAddress?.address1?.message || ""}
                  />
                )}
              />
            </Grid>
          </>
        )}
      </Grid>

      <BottomAction
        saveButtonText={isLoading ? <CircularProgress color="info" size={20} /> : t("saveChanges")}
        saveButtonProps={{
          type: "submit",
          disabled: isLoading
        }}
        cancelButtonText={t("supplier.profile.cancel")}
        cancelButtonProps={{
          onClick: () => {
            onPrev(watch());
            onChangeActiveStep(1);
          }
        }}
      />

      <div className="xmd:flex hidden justify-end items-center flex-wrap gap-2">
        {/* <CustomButton
          color="secondary"
          className="supplier-profile-footer-button"
          onClick={() => {
            onPrev(watch());
            onChangeActiveStep(1);
          }}
          startIcon={
            <Icon icon="mingcute:arrow-right-line" width={20} height={20} className="supplier-profile-next-button" />
          }
        >
          {t("supplier.profile.buttonPrev")}
        </CustomButton> */}
        <CustomButton
          type="submit"
          className="supplier-profile-footer-button"
          disabled={isLoading}
          // endIcon={
          //   <Icon icon="mingcute:arrow-left-line" width={20} height={20} className="supplier-profile-next-button" />
          // }
        >
          {isLoading ? <CircularProgress color="info" size={20} /> : t("saveChanges")}
        </CustomButton>
      </div>
    </form>
  );
};

export default React.forwardRef(SupplierAddress);
