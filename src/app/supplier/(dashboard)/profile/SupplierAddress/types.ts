import { TSupplierBaseFormProps } from "../types";
import { TSupplierProfile } from "../SupplierProfile";
import { TSupplierAddress } from "./SupplierAddress";
import { UseFormSetError } from "react-hook-form";
import { SetHookFormError } from "@/utils/services/utils";

export type TSupplierAddressFormProps = {
  supplierData?: TSupplierProfile;
  onSubmitData: (data: TSupplierAddress, setFieldError?: SetHookFormError) => void;
  onPrev: (data: TSupplierAddress) => void;
  isLoading?: boolean;
} & TSupplierBaseFormProps;
