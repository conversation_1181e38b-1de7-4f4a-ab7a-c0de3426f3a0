"use client";

import dynamic from "next/dynamic";
import React from "react";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";
import Loading from "@/app/loading";
import AclWrapper from "@/components/containers/AclWrapper";

const SupplierProductsPage = dynamic(
  () => import("@/components/containers/SupplierProductsPage/SupplierProductsPage"),
  {
    loading: () => (
      <CustomCardContent className="h-full flex flex-col">
        <Loading />
      </CustomCardContent>
    )
  }
);

const ProductPage: React.FC = () => {
  return (
    <AclWrapper for="SUPPLIER">
      <SupplierProductsPage isSupplierView />
    </AclWrapper>
  );
};

export default WithBottomBar(ProductPage);
