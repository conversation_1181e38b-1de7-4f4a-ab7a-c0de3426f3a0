"use client";

import useRole from "@/utils/hooks/useRole";
import { SUPPLIER_USER_TYPE } from "@/constants";
import AclWrapper from "@/components/containers/AclWrapper";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import SupplierProduct from "@/components/containers/SupplierProduct/SupplierProduct";
import { useRouter } from "next/navigation";

const ProductPage: React.FC = () => {
  const userType = useRole();
  const router = useRouter();

  return (
    <AclWrapper for="SUPPLIER">
      <SupplierProduct />
    </AclWrapper>
  );
};

export default WithBottomBar(ProductPage);
