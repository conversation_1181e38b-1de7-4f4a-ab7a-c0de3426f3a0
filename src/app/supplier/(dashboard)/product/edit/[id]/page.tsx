"use client";

import AclWrapper from "@/components/containers/AclWrapper";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import React from "react";
import { useTranslation } from "react-i18next";
import SupplierProductFormWrapper from "../../components/SupplierProductFormWrapper";

const DetailProductPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <AclWrapper for="SUPPLIER">
      <MobileAppBar title={t("editProduct")} hasBack />
      <div className="h-full bg-cards p-4 sm:p-9 rounded-[7px] sm:rounded-lg ">
        <div className="h-full max-w-[790px] mx-auto">
          <SupplierProductFormWrapper />
        </div>
      </div>
    </AclWrapper>
  );
};

export default WithBottomBar(DetailProductPage);
