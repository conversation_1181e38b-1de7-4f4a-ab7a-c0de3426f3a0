import React from "react";
import SelectCategoryWithModal from "@/components/containers/SelectCategoryModal/SelectCategoryWithModal";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import CustomNumberField from "@/components/ui/CustomNumberField/CustomNumberField";
import { useMapCategoriesMutation } from "@/store/apps/product";
import { TMapCategoriesRequest, TProductCategoryUnmapped } from "@/store/apps/product/types";
import i18n from "@/utils/i18n";
import { CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { convertCamelToSnake } from "@/utils/helpers/objectHelper";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";

const validationSchema = yup.array().of(
  yup.object({
    id: yup.string().required(i18n.t("product.validations.required")),
    mappedId: yup.string().required(i18n.t("product.validations.required")),
    adjustmentPercentage: yup.number().typeError(i18n.t("retailer.profile.validations.mustBeNumber")).optional()
  })
);

function Form({ initialValue }: { initialValue: TProductCategoryUnmapped["data"] }) {
  const { t } = useTranslation();
  const router = useRouter();
  const [putCategories, { isLoading: isLoadingPut, isError }] = useMapCategoriesMutation();

  const formik = useFormik({
    initialValues:
      initialValue?.map(item => ({
        ...item,
        adjustmentPercentage: Number(item?.adjustmentPercentage) > 0 ? item?.adjustmentPercentage : undefined
      })) || [],
    validationSchema: validationSchema,
    onSubmit: async values => {
      const body: TMapCategoriesRequest = values?.map(item => ({
        ...convertCamelToSnake(item),
        adjustment_percentage:
          item?.adjustmentPercentage && Number(item?.adjustmentPercentage) > 0
            ? Number(item.adjustmentPercentage)
            : null
      }));

      putCategories(body).then((res: any) => {
        if (!res?.error) {
          router.back();
        }
      });
    }
  });

  const { setFieldValue, values, errors } = formik;

  return (
    <>
      {isError && <div className="text-error-500 font-semibold">{t("product.categorymapper.error")}</div>}

      {!!values?.length && (
        <>
          <div className="font-semibold text-gray-999">{t("product.categorymapper.syncCategoriesTitle")}</div>
          <div className="text-sm font-medium text-gray-500">{t("product.categorymapper.syncCategoriesDesc")}</div>
          <form onSubmit={formik.handleSubmit} className="flex-1 flex flex-col">
            <TableContainer className="overflow-y-auto flex-1">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell align="left" width="42.5%">
                      {t("product.categorymapper.assignedCategory")}
                    </TableCell>
                    <TableCell align="left" width="42.5%">
                      {t("product.categorymapper.category")}
                    </TableCell>
                    <TableCell align="left" width="15%">
                      {t("product.categorymapper.commision")}
                    </TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {values?.map((row, index) => (
                    <TableRow key={row?.id}>
                      <TableCell align="left">{row?.name}</TableCell>
                      <TableCell align="left">
                        <SelectCategoryWithModal
                          initialValue={row?.mappedId || undefined}
                          placeholder={t("product.categoryPlaceholder")}
                          onChange={categoryId => {
                            setFieldValue(`${index}.mappedId`, categoryId);
                          }}
                        />
                        {!!errors?.[index]?.mappedId && (
                          <InputHelper>{errors?.[index]?.mappedId}</InputHelper>
                        )}
                      </TableCell>
                      <TableCell align="left">
                        <CustomNumberField
                          placeholder={t("product.categorymapper.commision")}
                          value={row?.adjustmentPercentage}
                          type="number"
                          returnType="number"
                          isParsFloat
                          onTextChange={e => {
                            setFieldValue(`${index}.adjustmentPercentage`, e);
                          }}
                        />
                        {!!errors?.[index]?.adjustmentPercentage && (
                          <InputHelper>{errors?.[index]?.adjustmentPercentage}</InputHelper>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <div className="flex items-center justify-between mt-auto">
              <CustomButton color="secondary" onClick={() => router.back()} className="w-fit">
                {t("product.categorymapper.cancel")}
              </CustomButton>
              <CustomButton color="primary" type="submit" className="w-fit" disabled={isLoadingPut}>
                {isLoadingPut ? <CircularProgress size={16} /> : t("product.categorymapper.applyChanges")}
              </CustomButton>
            </div>
          </form>
        </>
      )}
    </>
  );
}

export default Form;
