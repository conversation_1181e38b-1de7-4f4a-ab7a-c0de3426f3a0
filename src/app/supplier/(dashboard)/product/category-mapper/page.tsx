"use client";

import AclWrapper from "@/components/containers/AclWrapper";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";
import { useGetProductCategoryUnmappedQuery, useMapCategoriesMutation } from "@/store/apps/product";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import Form from "./form";

function Page() {
  const { t } = useTranslation();
  const router = useRouter();
  const { data: unmappedData, isLoading } = useGetProductCategoryUnmappedQuery();

  return (
    <AclWrapper for="SUPPLIER">
      <CustomCardContent className="h-full flex flex-col gap-4">
        <CustomButton color="info" onClick={() => router.back()} className="w-fit">
          <Icon icon="solar:arrow-right-outline" className="size-6" /> {t("product.categorymapper.back")}
        </CustomButton>

        <div className="flex flex-col gap-4 mx-auto w-full flex-1 lg: px-5">
          {isLoading && (
            <div className="flex items-center justify-center w-full flex-1">
              <CircularProgress />
            </div>
          )}

          {!isLoading && !!unmappedData?.data?.length && <Form initialValue={unmappedData?.data} />}
        </div>
      </CustomCardContent>
    </AclWrapper>
  );
}

export default Page;
