"use client";

import ProductForm from "@/components/forms/product-form/ProductForm/ProductForm";
import {
  PRODUCT_STATUS_ACTIVE,
  PRODUCT_STATUS_INACTIVE,
  PRODUCT_STATUS_INREVIEW,
  STATUS_CLOSE,
  STATUS_FAILED,
  STATUS_LOADING,
  STATUS_SUCCESS
} from "@/constants/product";
import { useTranslation } from "react-i18next";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import useModal from "@/utils/hooks/useModal";
import { useParams, usePathname, useRouter } from "next/navigation";
import { bindModalIcon } from "./utils";
import { useGetProductQuery } from "@/store/apps/product";
import { TProductForm } from "@/store/apps/product/types";
import { useEffect, useMemo } from "react";
import { variantTransFormer } from "@/store/apps/product/transformers";
import { useGetSupplierProfileQuery } from "@/store/apps/supplier";
import { CircularProgress } from "@mui/material";
import { TOnStateChange, TProductUploadState } from "@/components/forms/product-form/ProductForm/types";
import OnBoardingAlert from "@/components/containers/OnboardingAlert/OnboardingAlert";
import { TOpenModalProps } from "@/store/zustand/modalStore/ModalStore";
import CustomBreadcrumb from "@/components/ui/CustomBreadcrumb/CustomBreadcrumb";
import { productAuthenticity, productConditions } from "@/components/forms/product-form/ProductForm/utils";

const SupplierProductFormWrapper = ({}) => {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { showModal, hideModal } = useModal();
  const router = useRouter();
  const params = useParams();
  const pathname = usePathname();
  const productId = params?.id as string;
  const isEdit = !!productId;

  const {
    data: product,
    isLoading: isGetProductLoading,
    isSuccess,
    isError
  } = useGetProductQuery({ id: productId }, { skip: !productId });
  const { error: supplierError, isLoading: isGetSupplierLoading } = useGetSupplierProfileQuery();

  const handleOnStateChange: TOnStateChange = state => {
    // hide any previous modal's at first
    hideModal();

    if (state === STATUS_CLOSE || state === STATUS_LOADING) {
      return;
    }

    const bindtModalTitle = {
      SUCCESS: t("product.sent"),
      LOADING: t("product.loading"),
      FAILED: t("product.createFail"),
      CLOSE: ""
    };

    const bindtModalDesc = {
      SUCCESS: t("product.moderation"),
      LOADING: undefined,
      FAILED: undefined,
      CLOSE: undefined
    };

    const bindModalActions: { [key in TProductUploadState]: TOpenModalProps["actions"] | undefined } = {
      SUCCESS: [
        {
          label: t("product.addNewProduct"),
          variant: "secondaryColor",
          onClick: () => {
            if (pathname.includes("create")) {
              window.location.reload();
            }
            router.push(makePath(routes.createProduct));
            setTimeout(() => {
              hideModal();
            }, 200);
          }
        },
        {
          label: t("product.listButton"),
          onClick: () => {
            hideModal();
            router.push(makePath(routes.product));
          }
        }
      ],
      LOADING: undefined,
      FAILED: [
        {
          label: t("product.confirm"),
          onClick: () => {
            hideModal();
          }
        }
      ],
      CLOSE: undefined
    };

    setTimeout(() => {
      showModal({
        title: bindtModalTitle[state],
        subTitle: bindtModalDesc[state],
        icon: bindModalIcon[state],
        closable: false,
        modalProps: { showCloseIcon: false },
        actions: bindModalActions[state],
        width: 428
      });
    }, 0);
  };
  

  const initialValues: TProductForm = useMemo(() => {
    const hasVariant = product?.data?.hasVariant ?? false;
    const inventory = !hasVariant ? product?.data?.variants?.[0].inventory : undefined;
    const retailPrice = !hasVariant ? product?.data?.variants?.[0].retailPrice : undefined;
    const sku = !hasVariant ? product?.data?.variants?.[0].sku : undefined;

    const authenticity = !hasVariant ? product?.data?.variants?.[0].authenticity : undefined;
    const backorder = !hasVariant ? product?.data?.variants?.[0].backorder : undefined;
    const commission = !hasVariant ? product?.data?.variants?.[0].commission : undefined;
    const condition = !hasVariant ? product?.data?.variants?.[0].condition : undefined;
    const map = !hasVariant ? product?.data?.variants?.[0].map : undefined;
    const isActive = !hasVariant ? product?.data?.variants?.[0].isActive : undefined;

    return {
      variants: (product?.data && product?.data?.variants?.length > 0 ? product?.data?.variants : []).map(
        variantTransFormer
      ) as TProductForm["variants"],
      title: product?.data?.title || "",
      category_id: product?.data?.categoryId ? String(product?.data?.categoryId) : "",
      description: product?.data?.description || "",
      images: product?.data?.images || [],
      sku: sku || "",
      map: map ? Number(map) : undefined,
      inventory: inventory ?? undefined!,
      authenticity: authenticity ?? productAuthenticity?.[0]?.id,
      backorder: backorder ?? undefined!,
      commission: commission ? Number(commission) : undefined!,
      condition: condition ?? productConditions?.[0]?.id,
      is_active: isActive ?? undefined!,
      retail_price: retailPrice ? Number(retailPrice) : undefined!,
      tags: product?.data?.tags ? product?.data?.tags : [],
      status:
        product?.data?.status === PRODUCT_STATUS_ACTIVE || product?.data?.status === PRODUCT_STATUS_INREVIEW
          ? PRODUCT_STATUS_ACTIVE
          : PRODUCT_STATUS_INACTIVE
    };
  }, [product]);

  const BCrumb = [
    {
      title: t("products"),
      to: makePath(routes.product)
    },
    {
      title: productId ? t("product.editProduct") : t("product.createNewProduct")
    }
  ];

  /* -------------------------------------------------------------------------- */
  /*                          handle supplier not found                         */
  /* -------------------------------------------------------------------------- */
  const supplierNotFound = supplierError?.status === 404;

  useEffect(() => {
    if (supplierNotFound) {
      router.replace(makePath(routes.product));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [supplierNotFound]);

  /* -------------------------------------------------------------------------- */
  /*                                loading state                               */
  /* -------------------------------------------------------------------------- */
  if (isGetProductLoading || isGetSupplierLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <CircularProgress />
      </div>
    );
  }

  return (
    <>
      <div className="hidden md:block">
        <CustomBreadcrumb items={BCrumb} className="!py-0 !mb-4" />
      </div>

      {isEdit && (product?.data?.status === "InReview" || product?.data?.status === "Rejected") && (
        <>
          {product?.data?.status === "Rejected" && (
            <OnBoardingAlert
              status="rejected"
              imgSrc="/images/svgs/rejected-icon.svg"
              title={t("product.statusAlert.rejected.title")}
              subtile={product?.data?.rejectionNote || t("product.statusAlert.rejected.subtitle")}
            />
          )}
        </>
      )}
      <ProductForm
        onStateChange={handleOnStateChange}
        initialValues={initialValues}
        initialHasVariants={product?.data?.hasVariant ?? false}
      />
    </>
  );
};

export default SupplierProductFormWrapper;
