import { TProductUploadState } from "@/components/forms/product-form/ProductForm/types";
import { CircularProgress } from "@mui/material";
import { ReactNode } from "react";

type BindModalIcon = {
  [key in TProductUploadState]: ReactNode;
};
export const bindModalIcon: BindModalIcon = {
  SUCCESS: "/images/svgs/verify.svg",
  LOADING: <CircularProgress size={40} className="mx-auto block" />,
  FAILED: "/images/svgs/danger.svg",
  CLOSE: undefined
};
