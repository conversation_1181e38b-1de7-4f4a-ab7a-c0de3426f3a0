import { Options } from "highcharts";
import i18next from "i18next";

export const chartOptions = {
  chart: {
    height: 100,
    width: 83
  },
  tooltip: {
    enabled: false
  },
  yAxis: {
    title: "",
    lineColor: "transparent",
    tickColor: "transparent",
    gridLineColor: "transparent",
    labels: {
      formatter: function () {
        return "";
      }
    }
  },
  xAxis: {
    title: "",
    lineColor: "transparent",
    tickColor: "transparent",
    gridLineColor: "transparent",
    labels: {
      formatter: function () {
        return "";
      }
    }
  }
};

export const firstChartOptions = {
  ...chartOptions,
  plotOptions: {
    areaspline: {
      color: "rgba(137, 121, 255, 1)",
      fillColor: {
        linearGradient: { x1: 0, x2: 0, y1: 0, y2: 1 },
        stops: [
          [0, "rgba(137, 121, 255, 0.3)"],
          [1, "rgba(137, 121, 255, 0.05)"]
        ]
      },
      threshold: 0,
      marker: {
        enabled: false
      }
    }
  }
};

export const secondChartOptions = {
  ...chartOptions,
  plotOptions: {
    areaspline: {
      color: "#FFB74C",
      fillColor: {
        linearGradient: { x1: 0, x2: 0, y1: 0, y2: 1 },
        stops: [
          [0, "#FFD687"],
          [1, "#FFF3D229"]
        ]
      },
      threshold: 0,
      marker: {
        enabled: false
      }
    }
  }
};

export const thirdChartOptions = {
  ...chartOptions,
  plotOptions: {
    areaspline: {
      color: "#04D294",
      fillColor: {
        linearGradient: { x1: 0, x2: 0, y1: 0, y2: 1 },
        stops: [
          [0, "#84FFDA"],
          [1, "#04D29408"]
        ]
      },
      threshold: 0,
      marker: {
        enabled: false
      }
    }
  }
};

export const firstChartData = [
  [new Date("2024-10-01"), 150000000],
  [new Date("2024-10-02"), 150000000],
  [new Date("2024-10-03"), 150000000],
  [new Date("2024-10-04"), 150000000],
  [new Date("2024-10-05"), 150000000]
];

export const secondChartData = [
  [new Date("2024-10-01"), 10000],
  [new Date("2024-10-02"), 10000],
  [new Date("2024-10-16"), 10000],
  [new Date("2024-10-17"), 10000],
  [new Date("2024-10-18"), 10000],
  [new Date("2024-10-05"), 10000]
];

export const thirdChartData = [
  [new Date("2024-10-01"), 400000],
  [new Date("2024-10-02"), 400000],
  [new Date("2024-10-16"), 400000],
  [new Date("2024-10-17"), 400000],
  [new Date("2024-10-18"), 400000],
  [new Date("2024-10-04"), 400000],
  [new Date("2024-10-19"), 400000],
  [new Date("2024-10-05"), 400000]
];

export const primaryChartData = [
  [new Date("2024-10-11"), 120000],
  [new Date("2024-10-12"), 120000],
  [new Date("2024-10-13"), 120000],
  [new Date("2024-10-14"), 120000],
  [new Date("2024-10-15"), 120000],
  [new Date("2024-10-16"), 120000],
  [new Date("2024-10-17"), 120000],
  [new Date("2024-10-18"), 120000],
  [new Date("2024-10-19"), 120000],
  [new Date("2024-10-20"), 120000]
];

export const productData = [
  {
    name: "پلیمر لورینت مدل 01 حجم 30 میلی لیتر اسلویور",
    image: "/images/chart/recentImg1.svg",
    link: "#"
  },
  {
    name: "پلیمر لورینت مدل 01 حجم 30 میلی لیتر اسلویور",
    image: "/images/chart/recentImg2.svg",
    link: "#"
  },
  {
    name: "پلیمر لورینت مدل 01 حجم 30 میلی لیتر اسلویور",
    image: "/images/chart/recentImg3.svg",
    link: "#"
  },
  {
    name: "پلیمر لورینت مدل 01 حجم 30 میلی لیتر اسلویور",
    image: "/images/chart/recentImg4.svg",
    link: "#"
  }
];

export const topProductData = [
  {
    name: "پلیمر لورینت مدل 01 حجم 30 میلی لیتر اسلویور",
    image: "/images/chart/topImg1.svg",
    link: "#"
  },
  {
    name: "پلیمر لورینت مدل 01 حجم 30 میلی لیتر اسلویور",
    image: "/images/chart/topImg2.svg",
    link: "#"
  },
  {
    name: "پلیمر لورینت مدل 01 حجم 30 میلی لیتر اسلویور",
    image: "/images/chart/topImg3.svg",
    link: "#"
  },
  {
    name: "پلیمر لورینت مدل 01 حجم 30 میلی لیتر اسلویور",
    image: "/images/chart/topImg4.svg",
    link: "#"
  }
];

export const chartFilterItems = [
  {
    id: 1,
    name: "1days",
    title: i18next.t("chart.filter.1days")
  },
  {
    id: 2,
    name: "2weeks",
    title: i18next.t("chart.filter.2weeks")
  },
  {
    id: 3,
    name: "1months",
    title: i18next.t("chart.filter.1months")
  }
];

export interface IGetOnboardingProgress {
  title: string;
  icon: string;
  isFilled: boolean | undefined;
  path: string;
  step?: number;
}

export const getOnboardingProgress = (items: IGetOnboardingProgress[]) => {
  const totalItems = items.length;
  const unfilledItems = items.filter(item => !item.isFilled).length;
  return {
    incomplete: Math.round((unfilledItems / totalItems) * 100),
    complete: Math.round(((totalItems - unfilledItems) / totalItems) * 100)
  };
};
