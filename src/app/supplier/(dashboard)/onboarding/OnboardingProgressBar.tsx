import React from "react";

interface OnboardingProgressBarProps {
  progress: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
}

const OnboardingProgressBar: React.FC<OnboardingProgressBarProps> = ({
  progress,
  size = 120,
  strokeWidth = 8,
  color
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`} className="transform -rotate-90 bg-cards rounded-full">
        <circle cx={size / 2} cy={size / 2} r={radius} className="stroke-cards" strokeWidth={strokeWidth} fill="none" />

        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          fill="none"
          className="transition-all duration-300 ease-in-out"
        />
      </svg>

      <div className="absolute text-body2-medium text-v2-content-primary ">{progress}%</div>
    </div>
  );
};

export default OnboardingProgressBar;
