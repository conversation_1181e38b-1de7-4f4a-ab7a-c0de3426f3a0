"use client";

import AclWrapper from "@/components/containers/AclWrapper";
import ChatHeader from "@/components/containers/notifAndChat/chat/ChatHeader";
import NotifAndChat from "@/components/containers/notifAndChat/NotifAndChat";
import ChatsHeader from "@/components/containers/notifAndChat/ChatsHeader";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import Header from "@/components/containers/header/Header";

import { SUPPLIER_USER_TYPE } from "@/constants";
import { useParams, useSearchParams } from "next/navigation";
import { useGetConversationQuery } from "@/store/apps/conversation";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import React from "react";
export default function NotifAndChatPage() {
  const searchParams = useSearchParams();
  const params = useParams();
  const chatId = searchParams?.get("chatId");

  const PageWrapper = params?.page === "chat" && !chatId ? WithBottomBar(NotifAndChatContent) : NotifAndChatContent;

  return (
    <AclWrapper for={SUPPLIER_USER_TYPE}>
      <PageWrapper />
    </AclWrapper>
  );
}

function NotifAndChatContent() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const chatId = searchParams?.get("chatId");

  const { data: conversation, isLoading } = useGetConversationQuery();
  const chatsCount = conversation?.data?.length;

  return (
    <>
      {!isLoading && (
        <>
          {!chatsCount && !chatId ? (
            <Header title={t("messages")} isMobile isSticky />
          ) : (
            <MobileAppBar
              className="!h-20 border-b border-b-v2-border-primary"
              RenderComponent={chatId ? <ChatHeader /> : <ChatsHeader chatsCount={chatsCount} />}
            />
          )}
        </>
      )}
      <div className={twMerge("xmd:mt-auto mt-20", !chatsCount && !chatId && "!mt-4")}>
        <NotifAndChat />
      </div>
    </>
  );
}
