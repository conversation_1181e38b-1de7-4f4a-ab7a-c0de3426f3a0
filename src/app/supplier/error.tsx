"use client"; // Error boundaries must be Client Components

import ErrorPage from "@/components/containers/ErrorPage/ErrorPage";
import * as Sentry from "@sentry/nextjs";

import { useEffect } from "react";

export default function Error({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  return <ErrorPage />;
}
