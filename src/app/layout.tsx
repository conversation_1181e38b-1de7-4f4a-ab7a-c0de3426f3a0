"use client";

import "../styles/global.css";
import React, { useEffect, useMemo } from "react";
import { ThemeProvider, experimental_extendTheme } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import { ThemeSettings } from "@/utils/theme/Theme";
import { persistor, store } from "@/store/store";
import { Provider } from "react-redux";
import { ToastContainer } from "react-toastify";
import { NextAppDirEmotionCacheProvider } from "@/utils/theme/EmotionCache";
import { Experimental_CssVarsProvider as CssVarsProvider } from "@mui/material/styles";
import useDirection from "@/utils/hooks/useDirection";
import LocalizationWrappper from "@/utils/Internationalization";
import ModalProvider from "@/components/ui/modalProvider/ModalProvider";
import { PersistGate } from "redux-persist/integration/react";
import RTL from "@/components/layouts/RTL";
import type { Viewport } from "next";
import { GoogleTagManager } from "@next/third-parties/google";
import { useRouter, useSearchParams } from "next/navigation";
import useSessionStore from "@/store/zustand/sessionStore";
import { USERTYPES } from "@/constants/userTypes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import { NuqsAdapter } from "nuqs/adapters/next/app";

export const viewport: Viewport = {
  initialScale: 1,
  width: "device-width",
  maximumScale: 1,
  userScalable: false
};

export const MyApp = ({ children }: { children: React.ReactNode }) => {
  const theme = ThemeSettings();
  const [activeDir] = useDirection();
  const searchParams = useSearchParams();
  const isRedirected = searchParams?.get("token");
  const { setSession } = useSessionStore();
  const router = useRouter();
  const makePath = useRoleBasePath();

  const cssTheme = useMemo(
    () =>
      experimental_extendTheme({
        cssVarPrefix: "mui",
        ...theme,
        components: {
          ...theme.components,
          MuiCssBaseline: {
            ...theme.components?.MuiCssBaseline,
            styleOverrides: {
              ":root": {
                "--mui-theme-spacing-3": theme.spacing(3),
                "--mui-theme-spacing-2": theme.spacing(2),
                "--mui-theme-spacing-1": theme.spacing(1)
              }
            }
          }
        }
      }),
    [theme]
  );

  useEffect(() => {
    if (typeof window !== "undefined") {
      const metaViewport = document.createElement("meta");
      metaViewport.name = "viewport";
      metaViewport.content = "width=device-width, initial-scale=1.0, user-scalable=no";
      document.head.appendChild(metaViewport);
    }
  }, []);

  useEffect(() => {
    if (!isRedirected) return;

    const token = searchParams.get("token");
    const refreshToken = searchParams.get("refreshToken");
    const expiresIn = searchParams.get("expiresIn");
    const refreshTokenExpiresIn = searchParams.get("refreshTokenExpiresIn");
    const userId = searchParams.get("userId");
    const userType = searchParams.get("userType") as USERTYPES;

    const decodedToken = token ? atob(token) : undefined;
    const decodedRefreshToken = refreshToken ? atob(refreshToken) : undefined;

    setSession({
      access_token: decodedToken,
      expires_in: expiresIn ? Number(expiresIn) : null,
      refresh_token: decodedRefreshToken,
      refresh_token_expires_in: refreshTokenExpiresIn ? Number(refreshTokenExpiresIn) : null,
      user_id: userId,
      user_type: userType
    });

    router.replace(makePath(routes.home));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isRedirected, searchParams]);

  return (
    <>
      <NextAppDirEmotionCacheProvider options={{ key: "modernize" }}>
        <ThemeProvider theme={theme}>
          <CssVarsProvider theme={cssTheme}>
            <RTL direction={activeDir}>
              <ToastContainer />
              {/* CssBaseline kickstart an elegant, consistent, and simple baseline to build upon. */}
              <CssBaseline />
              <ModalProvider />
              {children}
            </RTL>
          </CssVarsProvider>
        </ThemeProvider>
      </NextAppDirEmotionCacheProvider>
    </>
  );
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      {/* <Script id="raychat" type="text/javascript">
        {`
          window.RAYCHAT_TOKEN = "f65f1d95-23c0-4c24-aace-8e1ede563727";
          window.LOAD_TYPE = "FAST_LOAD";
          (function () {
            d = document;
            s = d.createElement("script");
            s.src = "https://widget-react.raychat.io/install/widget.js";
            s.async = 1;
            d.getElementsByTagName("head")[0].appendChild(s);
          })();
        `}
      </Script> */}
      <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GOOGLE_TAGM_ID || ""} />
      <body>
        <Provider store={store}>
          <PersistGate loading={null} persistor={persistor}>
            <LocalizationWrappper>
              <NuqsAdapter>
                <MyApp>{children}</MyApp>
              </NuqsAdapter>
            </LocalizationWrappper>
          </PersistGate>
        </Provider>
      </body>
    </html>
  );
}
