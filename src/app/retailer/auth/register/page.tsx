"use client";

import { useState } from "react";
import { USER_TYPES } from "@/constants/userTypes";
import AuthOtpLogin from "@/app/auth/authForms/AuthOtpLogin";
import { useTranslation } from "react-i18next";
import AuthRegister from "@/app/auth/authForms/AuthRegister";

import "./page.css";
import AuthCreatePassword from "@/app/auth/authForms/AuthCreatePassword";
import { routes } from "@/constants/routes";
import { useRouter } from "next/navigation";

function Register() {
  const router = useRouter();
  const { t } = useTranslation();
  const [username, setUsername] = useState("");
  const [hasCode, setHasCode] = useState(false);

  if (username) {
    if (hasCode) {
      return (
        <AuthCreatePassword
          userType={USER_TYPES.RETAILER}
          username={username}
          onBack={() => setUsername("")}
          onSuccess={() => router.replace(routes.home)}
          title={t("register.changePassword.title")}
          subtitle={t("register.changePassword.subtitle")}
        />
      );
    }
    return (
      <AuthOtpLogin
        title={t("register.otpCode.title")}
        userType={USER_TYPES.RETAILER}
        username={username}
        onSubmitted={() => setHasCode(true)}
        onBack={() => setUsername("")}
      />
    );
  }
  return (
    <AuthRegister
      title={t("changePassword.title")}
      subtitle={t("changePassword.subtitle")}
      userType={USER_TYPES.RETAILER}
      onChangeUsername={value => {
        setUsername(value);
      }}
    />
  );
}

Register.layout = "Blank";

export default Register;
