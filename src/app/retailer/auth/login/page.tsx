"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { USER_TYPES } from "@/constants/userTypes";
import AuthEmailLogin from "@/app/auth/authForms/AuthEmaiLogin";
import AuthOtpLogin from "@/app/auth/authForms/AuthOtpLogin";
import AuthCheckUserName from "@/app/auth/authForms/AuthCheckUserName";

import "./page.css";
import { routes } from "@/constants/routes/index";
import { useTranslation } from "react-i18next";
import { Divider } from "@mui/material";
import Link from "next/link";

function Login() {
  const { t } = useTranslation();
  const router = useRouter();
  const [error, setError] = useState("");
  const [username, setUsername] = useState("");
  const [mode, setMode] = useState<"otp" | "password">("password");

  if (username && !error) {
    if (mode === "otp") {
      return (
        <AuthOtpLogin
          userType={USER_TYPES.RETAILER}
          setError={setError}
          username={username}
          onBack={() => setUsername("")}
          title={t("otpLogin.enterCode")}
          onSubmitted={() => router.replace(routes.home)}
          footer={
            <div className="flex justify-center mt-8 gap-2">
              <Link
                href={process.env.NODE_ENV === "development" ? routes.retailerChangePassword : routes.changePassword}
                className="text-body3-medium text-v2-content-on-action-2"
              >
                {t("emailLogin.forgetPassword")}
              </Link>

              <Divider orientation="vertical" variant="middle" flexItem className="email-login-divider" />
              <span onClick={() => setMode("password")} className="text-body3-medium text-v2-content-tertiary">
                {t("emailLogin.loginWithPassword")}
              </span>
            </div>
          }
        />
      );
    } else if (mode === "password") {
      return (
        <AuthEmailLogin
          userType={USER_TYPES.RETAILER}
          username={username}
          onBack={() => setUsername("")}
          onChangeMode={() => setMode("otp")}
        />
      );
    }
  }
  return (
    <AuthCheckUserName
      userType={USER_TYPES.RETAILER}
      error={error}
      setError={setError}
      onChangeUsername={setUsername}
      title={t("checkUsername.title")}
      subtitle={t("checkUsername.subTitle")}
      footer={
        <div className="flex justify-center mt-8 gap-2">
          <span className="text-body3-medium text-v2-content-tertiary">{t("checkUsername.loginTitle")}</span>
          <Link  className="text-body3-medium text-v2-content-on-action-2" href={process.env.NODE_ENV === "development" ? routes.retailerRegister : routes.register}>
            {t("checkUsername.subTitle2")}
          </Link>
        </div>
      }
    />
  );
}

Login.layout = "Blank";

export default Login;
