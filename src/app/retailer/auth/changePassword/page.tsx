"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { USER_TYPES } from "@/constants/userTypes";
import AuthOtpLogin from "@/app/auth/authForms/AuthOtpLogin";
import AuthCheckUserName from "@/app/auth/authForms/AuthCheckUserName";
import AuthForgetPassword from "@/app/auth/authForms/AuthForgetPassword";

import "./page.css";

function ChangePassword() {
  const router = useRouter();
  const { t } = useTranslation();
  const [error, setError] = useState("");
  const [otpCode, setOtpCode] = useState("");
  const [username, setUsername] = useState("");
  const [hasCode, setHasCode] = useState(false);

  const ChangePasswordForm = () => {
    if (username && !error) {
      if (hasCode) {
        return (
          <AuthForgetPassword
            otpCode={otpCode}
            userType={USER_TYPES.RETAILER}
            username={username}
            onBack={() => setUsername("")}
            title={t("changePassword.form.title")}
            subtitle={t("changePassword.form.subtitle")}
          />
        );
      }
      return (
        <AuthOtpLogin
          username={username}
          setError={setError}
          handleLogin={code => {
            setOtpCode(code);
            setHasCode(true);
          }}
          title={t("changePassword.otpCode.title")}
          userType={USER_TYPES.RETAILER}
          onSubmitted={() => setHasCode(true)}
          onBack={() => setUsername("")}
        />
      );
    }
    return (
      <AuthCheckUserName
        title={t("changePassword.title")}
        error={error}
        setError={setError}
        subtitle={t("changePassword.subtitle")}
        onBack={() => router.back()}
        buttonText={t("otpLogin.confirm")}
        userType={USER_TYPES.RETAILER}
        onChangeUsername={value => {
          setUsername(value);
        }}
      />
    );
  };

  return <ChangePasswordForm />;
}

ChangePassword.layout = "Blank";

export default ChangePassword;
