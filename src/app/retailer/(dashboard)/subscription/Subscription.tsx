import "./Subscription.css";

import { useGetMetaPlansQuery } from "@/store/apps/meta";
import { usePostRetailerPlanMutation } from "@/store/apps/retailer";
import useCurrency from "@/utils/hooks/useCurrency";
import {
  Box,
  Button,
  CardContent,
  List,
  Grid,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Switch,
  CircularProgress
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import Image from "next/image";
import { IconCheck } from "@tabler/icons-react";
import { toCommas } from "@/utils/helpers";
import BlankCard from "@/components/ui/BlankCard/BlankCard";
import { yearlyPrice } from "./utils";
import { useRouter } from "next/navigation";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import PageContainer from "@/components/layouts/PageContainer/PageContainer";
import { TMetaPlansData } from "@/store/apps/meta/types";

const Subscription = () => {
  const router = useRouter();
  const { t } = useTranslation();
  // const { data, isLoading: isMetaPlansLoading } = useGetMetaPlansQuery();
  const [curr] = useCurrency();
  const { render: renderPrice, symbol } = curr ?? { render: v => v };
  const [show, setShow] = useState(false);
  const makeRoute = useRoleBasePath();

  const [postPlan, { isLoading: isPostPlanLoading }] = usePostRetailerPlanMutation();

  const BCrumb = [
    {
      to: "/",
      title: t("subscription.home")
    },
    {
      title: t("subscription.pricing")
    }
  ];

  const onSelectPlan = async (planId: number) => {
    const body = {
      planId
    };

    try {
      const res = await postPlan({ body });

      if ("data" in res && res.data) {
        router.replace(makeRoute(routes.home));
      }
    } catch (err: any) {}
  };

  return null;

  // return (
  //   <PageContainer title={t("subscription.pricing")}>
  //     <Breadcrumb title={t("subscription.pricing")} items={BCrumb} />

  //     {isMetaPlansLoading ? (
  //       <Box id="sx-subscription-14878">
  //         <CircularProgress />
  //       </Box>
  //     ) : (
  //       <>
  //         <Grid container spacing={3} justifyContent="center" mt={3}>
  //           <Grid item xs={12} sm={10} lg={8} textAlign="center">
  //             <Typography variant="h2">{t("subscription.title")}</Typography>
  //             <Box display="flex" alignItems="center" mt={3} justifyContent="center">
  //               <Typography variant="subtitle1">{t("subscription.monthly")}</Typography>
  //               <Switch onChange={() => setShow(!show)} />
  //               <Typography variant="subtitle1">{t("subscription.yearly")}</Typography>
  //             </Box>
  //           </Grid>
  //         </Grid>
  //         <Grid container spacing={3} mt={5}>
  //           {data?.data &&
  //             data.data.map((item, i) => (
  //               <Grid item xs={12} lg={4} sm={6} key={i}>
  //                 <BlankCard>
  //                   <CardContent id="sx-subscription-14905">
  //                     <Typography
  //                       variant="subtitle1"
  //                       fontSize="12px"
  //                       mb={3}
  //                       color="textSecondary"
  //                       textTransform="uppercase"
  //                     >
  //                       {item.name}
  //                     </Typography>
  //                     <Image src="/images/backgrounds/silver.png" alt="avatar" width={90} height={90} />
  //                     <Box my={4}>
  //                       {item.price === 0 ? (
  //                         <Box fontSize="50px" mt={5} fontWeight="600">
  //                           {t("subscription.free")}
  //                         </Box>
  //                       ) : (
  //                         <Box display="flex">
  //                           <Typography variant="h6" mr="8px" mt="-12px">
  //                             {symbol}
  //                           </Typography>
  //                           {show ? (
  //                             <>
  //                               <Typography fontSize="48px" fontWeight="600">
  //                                 {toCommas(yearlyPrice(`${item.price}`, 12)) ?? "0"}
  //                               </Typography>
  //                               <Typography fontSize="15px" fontWeight={400} ml={1} color="textSecondary" mt={1}>
  //                                 /{t("subscription.yearly")}
  //                               </Typography>
  //                             </>
  //                           ) : (
  //                             <>
  //                               <Typography fontSize="48px" fontWeight="600">
  //                                 {toCommas(item?.price) ?? "0"}
  //                               </Typography>
  //                               <Typography fontSize="15px" fontWeight={400} ml={1} color="textSecondary" mt={1}>
  //                                 /{t("subscription.monthly")}
  //                               </Typography>
  //                             </>
  //                           )}
  //                         </Box>
  //                       )}
  //                     </Box>

  //                     <Box mt={3}>
  //                       <List>
  //                         {[
  //                           "premiumProduct",
  //                           "importedProduct",
  //                           "annualPaymentDiscount",
  //                           "orderNumber",
  //                           "siteNumber",
  //                           "trialDays"
  //                         ].map((rule, i) => (
  //                           <Box key={rule + i}>
  //                             <ListItem disableGutters>
  //                               <ListItemIcon id="sx-subscription-14961">
  //                                 <IconCheck width={18} />
  //                               </ListItemIcon>
  //                               <ListItemText>
  //                                 {item[rule as keyof TMetaPlansData]} {t("subscription." + rule)}
  //                               </ListItemText>
  //                             </ListItem>
  //                           </Box>
  //                         ))}
  //                       </List>
  //                     </Box>

  //                     <Button
  //                       id="sx-subscription-14974"
  //                       variant="contained"
  //                       size="large"
  //                       color="primary"
  //                       disabled={isPostPlanLoading}
  //                       onClick={() => onSelectPlan(item.id)}
  //                     >
  //                       {t("subscription.choosePlan", { name: item.name })} (
  //                       {renderPrice(show ? yearlyPrice(`${item.price}`, 12) : item.price)})
  //                     </Button>
  //                   </CardContent>
  //                 </BlankCard>
  //               </Grid>
  //             ))}
  //         </Grid>
  //       </>
  //     )}
  //   </PageContainer>
  // );
};

export default Subscription;
