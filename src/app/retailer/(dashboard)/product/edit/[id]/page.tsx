"use client";

import AclWrapper from "@/components/containers/AclWrapper";
import EditPage from "./EditPage/EditPage";
import { useGetRetailerProductQuery } from "@/store/apps/retailerProduct";
import { CircularProgress } from "@mui/material";
import { useParams } from "next/navigation";

function Page() {
  const params = useParams();
  const productId = params.id as string;

  const { data: product, isLoading } = useGetRetailerProductQuery(productId);

  if (isLoading) {
    return (
      <AclWrapper for="RETAILER">
        <div className="flex items-center justify-center w-full h-full">
          <CircularProgress />
        </div>
      </AclWrapper>
    );
  }

  return (
    <AclWrapper for="RETAILER">
      <EditPage product={product} />
    </AclWrapper>
  );
}

export default Page;
