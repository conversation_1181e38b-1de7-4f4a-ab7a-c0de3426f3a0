"use client";

import Button from "@/components/ui/Button";
import Collapse from "@/components/ui/Collapse/Collapse";
import { routes } from "@/constants/routes";
import { usePostRetailderProductPushMutation, usePutRetailerProductMutation } from "@/store/apps/retailerProduct";
import { TRetailerProductPayloadResponse } from "@/store/apps/retailerProduct/types";
import useModal from "@/utils/hooks/useModal";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import { yupResolver } from "@hookform/resolvers/yup";
import { Icon } from "@iconify/react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import General from "./general";
import Pricing from "./pricing";
import { TFormData } from "./types";
import { validationSchema } from "./validationSchema";

function EditPage({ product }: { product?: snakeCaseToCamelCase<TRetailerProductPayloadResponse> }) {
  const { t } = useTranslation();
  const params = useParams();
  const makePath = useRoleBasePath();
  const router = useRouter();
  const { showModal, hideModal } = useModal();
  const productId = params.id as string;

  const [updateProduct, { isLoading: isLoadingUpdateProduct }] = usePutRetailerProductMutation();
  const [pushProducts, { isLoading: isLoadingPostProductPush }] = usePostRetailderProductPushMutation();
  const [isAlsoPublish, setIsAlsoPublish] = useState(false);

  const initialValues = useMemo(() => {
    return {
      title: product?.data?.title || "",
      description: product?.data?.description || "",
      categoryId: product?.data?.categoryId ? String(product?.data?.categoryId) : "",
      tags: product?.data?.tags ? product?.data?.tags : [],
      images: product?.data?.images || [],
      cover: product?.data?.originProduct?.cover?.url || undefined,
      variants: product?.data?.variants || [],
      isActive: product?.data?.isActive ?? false
    };
  }, [product]);

  const showSuccessModal = () => {
    hideModal();

    setTimeout(() => {
      showModal({
        title: t("retailerProduct.saveSuccessfull"),
        icon: "/images/svgs/verify.svg",
        actions: [
          {
            label: t("retailerProduct.goToProductList"),
            className: "w-full",
            variant: "primary",
            onClick: () => {
              hideModal();
              router.back();
            }
          }
        ]
      });
    }, 0);
  };

  const showErrorModal = (message?: string) => {
    hideModal();

    setTimeout(() => {
      showModal({
        title: t("errors.somethingWentWrong"),
        subTitle: message,
        icon: "/images/svgs/danger.svg",
        actions: [
          {
            label: t("retailerProduct.ok"),
            className: "w-full",
            variant: "primary",
            onClick: () => {
              hideModal();
            }
          }
        ]
      });
    }, 0);
  };

  const formMethods = useForm<TFormData>({
    defaultValues: initialValues,
    resolver: yupResolver(validationSchema as any) as any
  });
  const {
    handleSubmit,
    watch,
    formState: { errors, isValid }
  } = formMethods;

  const isFormValid = !Object.keys(errors)?.length;

  const handlePublish = async () => {
    setIsAlsoPublish(false);

    if (!product?.data?.id) {
      throw new Error("Product ID is missing");
    }

    pushProducts({ body: { ids: [product.data.id] } }).then((res: any) => {
      if (res?.error) {
        showErrorModal();
        return;
      }

      showSuccessModal();
    });
  };

  const onSubmit = async (values: TFormData) => {
    const body = {
      ...values
    };

    if (!isFormValid) {
      showErrorModal();
      throw new Error();
    }

    return updateProduct({ productId, body }).then((res: any) => {
      if (res?.error) {
        showErrorModal();
        return;
      }

      if (isAlsoPublish) {
        handlePublish();
        return;
      }

      showSuccessModal();
    });
  };

  const handleCancel = () => {
    showModal({
      title: t("product.cancelModalTitle"),
      subTitle: t("product.cancelModalTitleDesc"),
      icon: "/images/svgs/danger2.svg",
      actions: [
        {
          label: t("product.cancel"),
          onClick: () => hideModal(),
          variant: "secondaryGray"
        },
        {
          label: t("product.cancelModalYes"),
          variant: "warningPrimary",
          onClick: () => {
            hideModal();

            const hasHistory = typeof window !== "undefined" && window.history.length > 1;

            if (hasHistory) {
              router.back();
            } else {
              router.push(makePath(routes.retailerProductsDrafts));
            }
          }
        }
      ]
    });
  };

  return (
    <div className="p-4 md:p-9 md:bg-v2-surface-primary rounded-lg flex flex-col">
      <div
        onClick={() => router.back()}
        className="w-fit !p-0 text-v2-content-tertiary text-[13px] font-medium flex gap-1 cursor-pointer"
      >
        <Icon icon="solar:arrow-right-outline" className="size-5" /> {t("product.categorymapper.back")}
      </div>

      {/* -------------------------------------------------------------------------- */
      /*                                  top info                                  */
      /* -------------------------------------------------------------------------- */}
      <div className="mt-6 flex gap-3">
        {/* ---------------------------------- image --------------------------------- */}
        <div className="size-14 relative shrink-0">
          {product?.data?.originProduct?.cover?.url && (
            <Image
              src={product?.data?.originProduct?.cover?.url}
              fill
              alt={product?.data?.originProduct?.cover?.alt}
              className="rounded-lg overflow-hidden border border-gray-40"
            />
          )}
        </div>

        <div className="flex-1 flex flex-col gap-2 justify-around">
          {/* ---------------------------------- title --------------------------------- */}
          <div className="text-v2-content-primary text-sm font-semibold">{product?.data?.title}</div>

          {/* --------------------------------- line 2 --------------------------------- */}
          <div className="flex items-center divide-x divide-x-reverse divide-v2-border-primary gap-3 *:pr-3 [&>*:first-child]:!pr-0">
            {!!product?.data?.originProduct?.variants?.length && (
              <div className="text-v2-content-tertiary text-xs font-normal">
                {t("product.inventory")}:{" "}
                <span className="font-medium">
                  {product?.data?.originProduct?.variants?.reduce((intry, variant) => intry + variant.inventory, 0) ??
                    "0"}
                </span>
              </div>
            )}

            {product?.data?.category?.name && (
              <div className="text-v2-content-tertiary text-xs font-normal">
                {t("retailerProduct.category")}: <span className="font-medium">{product?.data?.category?.name}</span>
              </div>
            )}
            {product?.data?.originProduct?.supplier?.name && (
              <div className="text-v2-content-tertiary text-xs font-normal">
                {t("retailerProduct.supplier")}:{" "}
                <span className="font-medium">{product?.data?.originProduct?.supplier?.name}</span>
              </div>
            )}

            <Link href={`${makePath(routes.product)}/${product?.data?.originProduct?.id}`} target="_blank">
              <div className="!pl-0 text-v2-content-on-info text-[13px] font-medium flex items-center gap-2">
                {t("retailerProduct.viewProduct")} <Icon icon="solar:arrow-left-up-linear" width={18} height={18} />
              </div>
            </Link>
          </div>
        </div>
      </div>

      {/* -------------------------------------------------------------------------- */
      /*                                    form                                    */
      /* -------------------------------------------------------------------------- */}
      <div className="mt-6">
        <FormProvider {...formMethods}>
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6">
            <Collapse
              title={t("productForm.steps.productDetails")}
              icon={<Icon icon="solar:widget-2-outline" className="size-6 text-gray-999" />}
              initialIsOpen
            >
              <General images={product?.data?.originProduct?.images} />
            </Collapse>

            <Collapse
              title={t("productForm.pricing")}
              icon={<Icon icon="solar:dollar-minimalistic-outline" className="size-6 text-gray-999" />}
              initialIsOpen
            >
              <Pricing originalProductData={product?.data?.originProduct} />
            </Collapse>

            <div className="flex items-center justify-between gap-2">
              <Button type="button" size="lg" variant="tertiaryGray" onClick={handleCancel}>
                {t("product.alert.cancel")}
              </Button>
              <div className="flex items-center justify-between gap-2">
                <Button
                  size="lg"
                  variant="secondaryGray"
                  type="submit"
                  disabled={isLoadingUpdateProduct || isLoadingPostProductPush}
                >
                  {t("retailerProduct.save")}
                </Button>
                {!product?.data?.isLive && (
                  <Button
                    variant="primary"
                    size="lg"
                    type="submit"
                    disabled={isLoadingUpdateProduct || isLoadingPostProductPush}
                    onClick={() => setIsAlsoPublish(true)}
                  >
                    {t("retailerProduct.saveAndPublish")}
                  </Button>
                )}
              </div>
            </div>
          </form>
        </FormProvider>
      </div>
    </div>
  );
}

export default EditPage;
