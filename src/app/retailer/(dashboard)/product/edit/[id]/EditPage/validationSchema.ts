import i18n from "@/utils/i18n";
import * as yup from "yup";

export const validationSchema = yup.object({
  title: yup
    .string()
    .required(i18n.t("product.validations.required"))
    .min(5, i18n.t("product.validations.minLengthTitle"))
    .max(120, i18n.t("product.validations.maxChar", { amount: 120 })),
  description: yup.string().required(i18n.t("product.validations.required")),
  categoryId: yup.string().required(i18n.t("product.validations.required")),
  tags: yup.array().of(yup.string()),
  images: yup.array().of(
    yup
      .object()
      .shape({
        url: yup.string(),
        markedAsCover: yup.boolean()
      })
      .typeError(i18n.t("product.validations.imagesNotValid"))
  ),
  cover: yup.string().required(i18n.t("product.validations.required")),
  variants: yup
    .array(
      yup.object({
        id: yup
          .string()
          .typeError(i18n.t("product.validations.required"))
          .required(i18n.t("product.validations.required")),
        compareAtPrice: yup.number().nullable().typeError(i18n.t("product.validations.required")),
        // .required(i18n.t("product.validations.required")),
        salesPrice: yup
          .number()
          .typeError(i18n.t("product.validations.required"))
          .required(i18n.t("product.validations.required"))
          .min(1, i18n.t("supplier.profile.validations.positive")),
        isActive: yup.boolean()
        // .required(i18n.t("product.validations.required"))
      })
    )
    .min(1, i18n.t("product.validations.variantsNotCompleted"))
});
