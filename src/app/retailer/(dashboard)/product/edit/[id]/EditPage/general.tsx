import React from "react";
import { Controller, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { TFormData } from "./types";
import Input from "@/components/ui/inputs/Input";
import InputLabel from "@/components/ui/inputs/Input/InputLabel";
import Editor from "@/components/ui/Editor/EditorDynamic";
import { TRetailerProductPayloadResponse } from "@/store/apps/retailerProduct/types";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import Image from "next/image";
import SelectCategoryWithModal from "@/components/containers/SelectCategoryModal/SelectCategoryWithModal";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import { CustomTags } from "@/components/ui/CustomTags";
import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import { useGetRetailerCategoryListQuery } from "@/store/apps/retailerProduct";
import { Icon } from "@iconify/react";
import { shallowEqual } from "react-redux";
import { AppState } from "@/store/store";
import { useSelector } from "@/store/hooks";
import { useParams, useRouter } from "next/navigation";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import { Box, Paper } from "@mui/material";
import { useRetailerStore } from "@/store/zustand/retailerStore/RetailerStore";

function General({
  images
}: {
  images?: snakeCaseToCamelCase<TRetailerProductPayloadResponse>["data"]["originProduct"]["images"];
}) {
  const { t } = useTranslation();
  const { control, watch, setValue } = useFormContext<TFormData>();
  const formDataImages = watch("images");
  const router = useRouter();
  const makePath = useRoleBasePath();
  const params = useParams();

  const { data: categories, isLoading: isRetailerCategoryLoading } = useGetRetailerCategoryListQuery();

  const categoryItems =
    categories?.data?.map(item => ({
      id: item?.id,
      label: item?.hierarchy || item.name
    })) || [];

  const { data: retailerStore } = useRetailerStore();

  const handleCreateNewCategory = () => {
    router.push(`${makePath(routes.retailerListCategories)}`);
  };

  return (
    <div className="flex flex-col gap-4">
      <div>
        <Controller
          name="title"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              autoComplete="off"
              label={t("product.title")}
              placeholder={t("product.title")}
              error={Boolean(error?.message)}
              helperText={error?.message || t("product.titleHint")}
            />
          )}
        />
      </div>

      <div>
        <Controller
          name="description"
          control={control}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <>
              <InputLabel htmlFor="description" containerClassName="mb-1">
                {t("product.description")}
              </InputLabel>
              <Editor
                initialValue={value}
                onChange={value => onChange(value)}
                placeholder={t("product.description")}
                error={Boolean(error?.message)}
                helperText={error?.message || t("product.descriptionHint")}
              />
            </>
          )}
        />
      </div>

      <div className="flex flex-col gap-4">
        <div className="text-v2-content-primary text-base font-medium">{t("retailerProduct.gallery")}</div>

        {!!images && images?.length > 0 && (
          <div className="flex items-center gap-4 flex-wrap">
            {images?.map(image => {
              const isChecked = formDataImages?.find(a => a.url === image?.url);
              const handleClickSelectBox = () => {
                if (isChecked) {
                  // remove from values
                  setValue(
                    "images",
                    formDataImages?.filter(a => a.url !== image?.url)
                  );
                } else {
                  // add to images
                  setValue("images", [...formDataImages, image]);
                }
              };
              return (
                <div className="relative size-[94px] overflow-hidden rounded-lg border border-gray-40" key={image?.url}>
                  <CustomCheckbox
                    checked={!!isChecked}
                    className="absolute -top-px -left-px z-10 !m-0"
                    onClick={handleClickSelectBox}
                  />
                  <Image src={image?.url} alt={image?.alt} fill />
                </div>
              );
            })}
          </div>
        )}
      </div>

      <div className="flex flex-col gap-4">
        <div className="text-v2-content-primary text-base font-medium">{t("retailerProduct.category")}</div>

        <div>
          <Controller
            name="categoryId"
            control={control}
            render={({ field, fieldState }) => (
              <>
                <CustomAutocomplete<(typeof categoryItems)[0]>
                  {...field}
                  value={isRetailerCategoryLoading ? null : categoryItems?.find(item => item?.id === field?.value)}
                  options={categoryItems}
                  placeholder={t("retailer.categoryPlaceholder")}
                  label={t("retailer.category")}
                  error={!!fieldState?.error?.message}
                  helperText={fieldState?.error?.message}
                  onChange={(e, value) => setValue("categoryId", value?.id ?? "")}
                  PaperComponent={({ children }) => (
                    <div className="bg-cards rounded-lg mt-1 shadow-category border border-v2-border-secondary">
                      {retailerStore?.integration?.platform?.key &&
                        ["ShopBuilder"]?.includes(retailerStore?.integration?.platform?.key) && (
                          <div className="px-3 pt-3 ">
                            <div
                              className="flex items-center gap-2 border-b border-b-v2-border-secondary pb-3 cursor-pointer"
                              onMouseDown={e => {
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                              onClick={e => {
                                e.preventDefault();
                                e.stopPropagation();
                                setTimeout(() => {
                                  handleCreateNewCategory();
                                }, 0);
                              }}
                            >
                              <Icon icon="ph:plus" className="size-5 shrink-0  text-v2-surface-action" />
                              <span className="text-v2-surface-action text-caption-medium">
                                {t("retailer.addNewCategory")}
                              </span>
                            </div>
                          </div>
                        )}
                      {children}
                    </div>
                  )}
                />
              </>
            )}
          />
        </div>

        <div>
          <Controller
            name="tags"
            control={control}
            render={({ field: { name, value, onChange, onBlur }, fieldState: { error } }) => (
              <>
                <InputLabel htmlFor="tags" requiredStar={false} containerClassName="mb-1">
                  {t("product.tags")}
                </InputLabel>
                <CustomTags
                  optional
                  requiredStar={false}
                  handleBlur={onBlur}
                  value={value ?? []}
                  onChange={({ tagValues }) => onChange(tagValues)}
                  name={name}
                  error={Boolean(error?.message)}
                  helperText={error?.message ? error?.message : t("product.tagHint")}
                  placeholder={t("product.tagsPlaceholder")}
                />
                {!!error?.message && <InputHelper className="mt-1">{error?.message}</InputHelper>}
              </>
            )}
          />
          <div className="text-xs text-v2-content-tertiary mt-1">{t("product.tagHint")}</div>
        </div>

        <div className="md:-mt-1">
          <Controller
            name="isActive"
            control={control}
            render={({ field: { name, value, onChange, onBlur }, fieldState: { error } }) => (
              <>
                <InputLabel htmlFor="isActive" containerClassName="mb-1">
                  {t("product.status")}
                </InputLabel>
                <div className="-mt-0.5 mb-px">
                  <CustomRadio
                    label={t("product.statusItems.Active")}
                    checked={value === true}
                    value={true}
                    name="isActive"
                    onChange={e => {
                      onChange(e.target.value === "true");
                    }}
                  />
                  <CustomRadio
                    checked={value === false}
                    onChange={e => {
                      onChange(e.target.value === "true");
                    }}
                    name="isActive"
                    label={t("product.statusItems.Inactive")}
                    value={false}
                  />
                </div>
                {!!error?.message && <InputHelper>{error?.message}</InputHelper>}
              </>
            )}
          />
        </div>
      </div>
    </div>
  );
}

export default General;
