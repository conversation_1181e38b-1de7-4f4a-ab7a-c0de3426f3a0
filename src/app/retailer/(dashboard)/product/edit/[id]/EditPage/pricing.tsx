import { calcProfitAmount } from "@/components/containers/productDetailPage/utils";
import Collapse2 from "@/components/ui/Collapse2/Collapse2";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import PriceInput from "@/components/ui/inputs/PriceInput";
import { TRetailerProductPayloadResponse } from "@/store/apps/retailerProduct/types";
import { toCommas } from "@/utils/helpers";
import useCurrency from "@/utils/hooks/useCurrency";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import { useCallback } from "react";
import { Controller, useFieldArray, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { TFormData } from "./types";

function Pricing({
  originalProductData
}: {
  originalProductData?: snakeCaseToCamelCase<TRetailerProductPayloadResponse>["data"]["originProduct"];
}) {
  const { t } = useTranslation();
  const [{ symbol }] = useCurrency();

  const {
    control,
    formState: { errors }
  } = useFormContext<TFormData>();

  const { fields: variants } = useFieldArray({
    control,
    name: "variants",
    keyName: "key"
  });

  const RenderForm = useCallback(
    ({ index }: { index: number }) => {
      return (
        <div className="grid grid-cols-2 gap-3">
          <div>
            <Controller
              name={`variants.${index}.salesPrice`}
              control={control}
              render={({ field, fieldState: { error } }) => (
                <PriceInput
                  {...field}
                  //   showSymbol={false}
                  label={t("retailerProduct.salesPrice")}
                  placeholder={t("retailerProduct.salesPrice")}
                  error={Boolean(error?.message)}
                  helperText={error?.message || t("retailerProduct.salesPriceHint")}
                />
              )}
            />
          </div>
          <div>
            <Controller
              name={`variants.${index}.compareAtPrice`}
              control={control}
              render={({ field, fieldState: { error } }) => (
                <PriceInput
                  {...field}
                  optional
                  requiredStar={false}
                  //   showSymbol={false}
                  label={t("retailerProduct.compareAtPrice")}
                  placeholder={t("retailerProduct.compareAtPrice")}
                  error={Boolean(error?.message)}
                  helperText={error?.message || t("retailerProduct.compareAtPriceHint")}
                />
              )}
            />
          </div>
        </div>
      );
    },
    [control, t]
  );

  return (
    <div>
      {!!variants?.length && variants?.length === 1 && <RenderForm index={0} />}
      {!!variants?.length && variants?.length > 1 && (
        <div className="divide-y-2 divide-v2-surface-thertiary rounded-md border-2 border-v2-surface-thertiary">
          {variants?.map((variant, index) => {
            const originalProductVariant = originalProductData?.variants?.find(a => a.id === variant.id);

            const retailerProfitAmount = calcProfitAmount({
              commission: originalProductVariant?.commission || 0,
              retailPrice: originalProductVariant?.retailPrice || 0
            });
            const retailerProfitPercent = originalProductVariant?.commission;

            return (
              <Collapse2
                key={(variant as any)?.key || variant?.id}
                startAdornment={
                  <div className="flex items-stretch gap-6">
                    <div className="flex flex-col gap-1">
                      <div className="text-[15px] font-medium text-v2-content-primary">
                        {originalProductVariant?.options
                          ? Object.entries(originalProductVariant.options)
                              .map(item => `${item?.[0]}: ${item?.[1]}`)
                              .join(" | ")
                          : "-"}
                      </div>
                      <div className="text-v2-content-tertiary text-xs font-normal hidden md:flex gap-6">
                        <div>
                          <span>{t("inventory")}: </span>
                          {/* {variant.inventory ? toCommas(variant.inventory) : "-"} */}
                          {originalProductVariant?.inventory !== undefined
                            ? originalProductVariant?.inventory.toLocaleString()
                            : "-"}
                        </div>
                        <div>
                          <span>{t("retailerProduct.salesPrice")}: </span>
                          {variant.salesPrice ? toCommas(variant.salesPrice) : "-"} {symbol}
                        </div>
                      </div>
                    </div>

                    {(!!retailerProfitAmount || !!retailerProfitPercent) && (
                      <div className="flex flex-col gap-1">
                        <div className="text-v2-content-tertiary text-xs font-normal">
                          {t("retailerProduct.yourProfitPercent")}
                        </div>

                        <div className="text-sm font-semibold text-v2-content-primary">
                          {retailerProfitAmount && `${toCommas(retailerProfitAmount)} ${symbol}`}{" "}
                          {retailerProfitPercent && (
                            <span className="px-1 py-0.5 rounded-full bg-v2-surface-success-2 text-xs font-medium text-v2-content-on-success-2">
                              {retailerProfitPercent} {t("retailerProduct.percent")}
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                }
                endAdornment={
                  <Controller
                    name={`variants.${index}.isActive`}
                    control={control}
                    key={(variant as any)?.key || variant?.id}
                    render={({ field, fieldState: { error } }) => (
                      <CustomSwitch
                        name={field?.name}
                        label={t("supplier.profile.active")}
                        labelClassName="ml-0 hidden md:block"
                        checked={field?.value === true}
                        onChange={(e, v) => {
                          field.onChange(v === true ? true : false);
                        }}
                      />
                    )}
                  />
                }
              >
                <RenderForm index={index} />
              </Collapse2>
            );
          })}

          {errors.variants && (
            <InputHelper className="">
              {typeof errors.variants?.message === "string"
                ? errors.variants?.message
                : t("product.validations.variantsNotCompleted")}
            </InputHelper>
          )}
        </div>
      )}
    </div>
  );
}

export default Pricing;
