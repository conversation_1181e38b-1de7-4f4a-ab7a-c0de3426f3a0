import AddSupplierStore from "@/components/containers/SupplierStoreSetting/AddSupplierStore";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/inputs/Input";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { Typography } from "@mui/material";
import { isDraft } from "@reduxjs/toolkit";
import { IconPlus } from "@tabler/icons-react";
import Image from "next/image";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { debounce } from "lodash";
import { handleSetFilter } from "@/utils/helpers";
import { useFiltersState } from "../products/Filters/useFiltersState";

interface IProductEmptyProps {
  isDraft: boolean;
  hasSearchValue: boolean;
}

function ProductEmpty({ isDraft, hasSearchValue }: IProductEmptyProps) {
  const makePath = useRoleBasePath();
  const { t } = useTranslation();
  const { filters, setFilters } = useFiltersState();

  const [internalValue, setInternalValue] = useState<string | undefined>(filters?.title || undefined);

  const handleOnChange = useCallback(
    debounce((value: string) => {
      // setFilters({ title: value, page: 1 }, { history: "push" });
      handleSetFilter({ key: "title", value, setFilters });
    }, 2000),
    []
  );

  if (hasSearchValue) {
    return (
      <div className="flex flex-col justify-between h-[85vh] bg-cards p-6 rounded-lg ">
        <Input
          startAdornment={
            <Icon icon="solar:magnifer-linear" width="1.1rem" color="#ADADAD" height="1.1rem" className="ml-1.5" />
          }
          inputSize="sm"
          variant="filled"
          className="max-h-12"
          // inputParentClassName="bg-v2-surface-primary"
          rootClassName="md:w-50 shrink-0"
          value={internalValue || undefined}
          placeholder={`${t("chats.searchQuery")} ...`}
          onChange={e => {
            handleOnChange(e.target.value);
            setInternalValue(e.target.value);
          }}
        />

        <div className="flex flex-col items-center justify-center gap-5 flex-1">
          <div className="w-[85px] h-[100px] xmd:w-[196px] xmd:h-[230px] relative">
            <Image src="/images/product-list-empty-2.svg" fill alt="empty list palceholder" />
          </div>

          <div className="flex gap-2 flex-col text-center">
            <span className="text-h5-bold !font-semibold text-v2-content-primary">
              {isDraft ? t("retailer.product.emptyDraftTitle") : t("retailer.product.emptyImportsTitle")}
            </span>
            <span className="text-body3-medium text-v2-content-tertiary">
              {isDraft ? t("retailer.product.emptyDraftSubTitle") : t("retailer.product.emptyImportsSubTitle")}{" "}
            </span>
          </div>
          {isDraft && (
            <div className="flex flex-col gap-3">
              <Link href={makePath(routes.product)}>
                <Button size="lg" variant="secondaryColor">
                  <IconPlus />
                  {t("retailer.product.emptyDraftAddButton")}
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center h-[70vh] gap-5 bg-cards rounded-lg">
      <div className="w-[85px] h-[100px] xmd:w-[196px] xmd:h-[230px] relative">
        <Image src="/images/product-list-empty-2.svg" fill alt="empty list palceholder" />
      </div>

      <div className="flex gap-2 flex-col text-center">
        <span className="text-h5-bold !font-semibold text-v2-content-primary">
          {isDraft ? t("retailer.product.emptyDraftTitle") : t("retailer.product.emptyImportsTitle")}
        </span>
        <span className="text-body3-medium text-v2-content-tertiary">
          {isDraft ? t("retailer.product.emptyDraftSubTitle") : t("retailer.product.emptyImportsSubTitle")}{" "}
        </span>
      </div>
      {isDraft && (
        <div className="flex flex-col gap-3">
          <Link href={makePath(routes.product)}>
            <Button size="lg" variant="secondaryColor">
              <IconPlus />
              {t("retailer.product.emptyDraftAddButton")}
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}

export default ProductEmpty;
