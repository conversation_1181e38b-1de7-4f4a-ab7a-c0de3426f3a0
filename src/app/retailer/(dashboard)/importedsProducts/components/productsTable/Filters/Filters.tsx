import Input from "@/components/ui/inputs/Input";
import { Icon } from "@iconify/react";
import { Theme } from "@mui/material";
import { useMediaQuery } from "@mui/system";
import { debounce } from "lodash";
import React, { ReactNode, useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { useFiltersState } from "./useFiltersState";
import { handleSetFilter } from "@/utils/helpers";

function Filters({ endAdornment }: { endAdornment?: ReactNode }) {
  const { t } = useTranslation();
  const { filters, setFilters } = useFiltersState();

  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const handleOnChange = useCallback(
    debounce((value: string) => {
      // setFilters({ title: value, page: 1 }, { history: "push" });
      handleSetFilter({ key: "title", value, setFilters });
    }, 2000),
    []
  );
  const [internalValue, setInternalValue] = useState<string | undefined>(filters?.title || undefined);

  const onReset = (key: string | string[]) => {
    if (Array.isArray(key) && key?.length) {
      key?.forEach(item => {
        setFilters({ [item]: null }, { history: "push" });
      });
    } else setFilters({ [key as any]: null }, { history: "push" });
  };

  return (
    <div className="flex items-center gap-4 flex-col justify-start">
      <div className="w-full ">
        <Input
          startAdornment={
            <Icon icon="solar:magnifer-linear" width="1.1rem" color="#ADADAD" height="1.1rem" className="ml-1.5" />
          }
          variant={isMobile ? "filled" : "outline"}
          className="max-h-12"
          inputParentClassName="bg-v2-surface-primary"
          rootClassName="flex-1 shrink-0"
          value={internalValue || undefined}
          placeholder={`${t("chats.searchQuery")} ...`}
          onChange={e => {
            handleOnChange(e.target.value);
            setInternalValue(e.target.value);
          }}
        />
      </div>
      <div className="flex justify-end items-end w-full">{endAdornment}</div>
    </div>
  );
}

export default Filters;
