import { Icon } from "@iconify/react";
import React from "react";
import { useTranslation } from "react-i18next";

function ProductMenu({
  title,
  isDraft,
  onClickDelete,
  onClickEdit,
  onClickPublish,
  onClickUnPublish
}: {
  title?: string;
  isDraft?: boolean;
  onClickDelete?: VoidFunction;
  onClickEdit?: VoidFunction;
  onClickPublish?: VoidFunction;
  onClickUnPublish?: VoidFunction;
}) {
  const { t } = useTranslation();

  const menuItems = [
    {
      title: t("product.edit"),
      icon: <Icon icon="solar:pen-linear" className="size-4" />,
      onClick: onClickEdit
    },
    ...(isDraft
      ? [
          {
            title: t("retailerProduct.publish"),
            icon: <Icon icon="solar:export-outline" className="size-4" />,
            onClick: onClickPublish
          }
        ]
      : []),
    ...(!isDraft
      ? [
          {
            title: t("retailerProduct.unpublish"),
            icon: <Icon icon="solar:export-outline" className="size-4" />,
            onClick: onClickUnPublish
          }
        ]
      : []),
    {
      title: t("product.delete"),
      icon: <Icon icon="solar:trash-bin-trash-outline" className="size-4" />,
      onClick: onClickDelete
    }
  ]?.filter(Boolean);

  return (
    <div className="flex flex-col gap-2">
      <div className="flex justify-between items-center mt-2">
        <div className="text-xs font-medium text-v2-content-primary pe-6">{title}</div>
      </div>

      <div className="flex flex-col divide-y divide-v2-border-primary">
        {menuItems?.map((menu, index) => (
          <div key={index} className="py-4 flex items-center gap-2 cursor-pointer" onClick={menu?.onClick}>
            <div className="size-4 shrink-0 overflow-hidden">{menu?.icon}</div>
            <div className="flex-1 text-sm font-medium text-v2-content-primary">{menu?.title}</div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default ProductMenu;
