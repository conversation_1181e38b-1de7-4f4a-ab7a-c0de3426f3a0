import CustomTabs from "@/components/ui/CustomTabs/CustomTabs";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { useRouter } from "next/navigation";
import React from "react";
import { useTranslation } from "react-i18next";

function Header({ isDraft, className, totalCount }: { isDraft?: boolean; className?: string; totalCount?: number }) {
  const { t } = useTranslation();
  const router = useRouter();
  const makePath = useRoleBasePath();

  const handleOnTabChange = (newValue: string) => {
    router?.replace(
      newValue === "drafts"
        ? `${makePath(routes.retailerProductsDrafts)}`
        : `${makePath(routes.retailerProductsImports)}`
    );
  };

  const value = isDraft ? "drafts" : "published";

  const tabs = [
    {
      id: 1,
      title: t("retailerImport.draft"),
      value: "drafts"
    },
    {
      id: 2,
      title: t("product.importeds"),
      value: "published"
    }
  ];

  return (
    <div className="bg-v2-background-primary border-b border-v2-border-primary">
      <div className="py-4 px-4">
        <div className="text-v2-content-primary text-[15px] leading-6 font-medium">
          {t("products")}{" "}
          {!!totalCount && totalCount > 0 && (
            <span className="text-v2-content-tertiary text-[15px] font-medium">({totalCount})</span>
          )}
        </div>
      </div>

      <div className="w-full px-4">
        <CustomTabs items={tabs} onChange={handleOnTabChange} value={value} className="w-full" />
      </div>
    </div>
  );
}

export default Header;
