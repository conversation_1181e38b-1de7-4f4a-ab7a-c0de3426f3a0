import CustomTablePagination from "@/components/ui/CustomTablePagination/CustomTablePagination";
import RetailerImportedProductCard from "@/components/containers/ProductCard/RetailerImportedProductCard";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import { CircularProgress, SelectChangeEvent } from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import Header from "./components/Header";
import Image from "next/image";
import Filters from "../Filters/Filters";
import { TRetailerImportPayloadResponse } from "@/store/apps/retailerProduct/types";
import useModal from "@/utils/hooks/useModal";
import { useRouter } from "next/navigation";
import ProductMenu from "./components/productMenu";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import Link from "next/link";
import Button from "@/components/ui/Button";
import { IconPlus } from "@tabler/icons-react";

type TProductsTableMobileProps = {
  productData?: snakeCaseToCamelCase<TRetailerImportPayloadResponse>;
  isDraft?: boolean;
  onClickPublish: (id: string[]) => void;
  onClickUnPublish: (id: string[]) => void;
  onClickDelete: (id: string[]) => void;
  isLoading?: boolean;
  totalCount: number;
  pageSize: number;
  page: number;
  handleChangePage: (event: unknown, newPage: number) => void;
  handleChangeRowsPerPage: (event: SelectChangeEvent<number>) => void;
  hasFilters?: boolean;
};

function ProductsTableMobile(props: TProductsTableMobileProps) {
  const {
    productData,
    isLoading,
    isDraft,
    totalCount,
    pageSize,
    page,
    handleChangePage,
    handleChangeRowsPerPage,
    onClickUnPublish,
    onClickPublish,
    onClickDelete,
    hasFilters
  } = props || {};
  const { t } = useTranslation();
  const { showModal, hideModal } = useModal();
  const router = useRouter();
  const makePath = useRoleBasePath();

  const onClickProductMenu = (productId: string) => {
    const foundProduct = productData?.data?.find(a => a.id === productId);

    showModal({
      body: (
        <ProductMenu
          title={foundProduct?.title}
          isDraft={isDraft}
          onClickDelete={() => productId && onClickDelete([productId])}
          onClickEdit={() => {
            router?.push(`${makePath(routes.editRetailerProduct)}/${productId}`);
            hideModal();
          }}
          onClickPublish={() => onClickPublish([productId])}
          onClickUnPublish={() => onClickUnPublish([productId])}
        />
      ),
      modalProps: { showCloseIcon: false }
    });
  };

  return (
    <div className="bg-v2-background-secondary h-full flex flex-col">
      <Header isDraft={isDraft} className="shrink-0" totalCount={totalCount} />

      <div className="p-4 flex-1 flex flex-col">
        <div className="mb-4">
          <Filters />
        </div>
        {isLoading && (
          <div className="h-full w-full flex items-center justify-center">
            <CircularProgress />
          </div>
        )}

        {!isLoading && !productData?.data?.length && (
          <div className="flex items-center justify-center h-full bg-v2-surface-primary rounded-lg px-4 min-h-[60dvh]">
            <div className="flex flex-col items-center justify-center gap-2">
              <Image src="/images/product-list-empty-2.svg" width={85} alt="empty list placeholder" height={100} />

              <div className="flex gap-1 flex-col text-center mt-1">
                <div className="text-v2-content-primary text-lg font-semibold">
                  {hasFilters
                    ? t("product.noProducts")
                    : isDraft
                    ? t("product.emptyList")
                    : t("retailer.categoryPage.emptyPublishedList")}
                </div>

                <div className="text-v2-content-tertiary text-[13px] font-medium">
                  {hasFilters
                    ? t("product.noProductsSubtitle")
                    : isDraft
                    ? t("retailer.categoryPage.emptyListSubTitle")
                    : t("retailer.categoryPage.emptyPublishedListSubTitle")}
                </div>
              </div>

              {!hasFilters && isDraft && (
                <Link href={makePath(routes.product)}>
                  <Button size="lg" variant="secondaryGray">
                    <IconPlus />
                    {t("product.createProduct")}
                  </Button>
                </Link>
              )}
            </div>
          </div>
        )}

        {!isLoading && productData?.data?.length && productData?.data?.length > 0 && (
          <div className="flex-1 flex flex-col justify-between">
            <div className="flex flex-col gap-2">
              {productData?.data?.map(product => {
                return (
                  <RetailerImportedProductCard
                    key={product?.id}
                    productId={product?.id}
                    cover={product?.originProduct?.cover}
                    title={product?.title}
                    supplier={{
                      id: product?.originProduct?.supplier?.id,
                      name: product?.originProduct?.supplier?.name
                    }}
                    onClickMenu={onClickProductMenu}
                  />
                );
              })}
            </div>

            {totalCount > 0 && (
              <div className="mt-4">
                <CustomTablePagination
                  rowsPerPageOptions={[10, 50, 100, 200]}
                  count={totalCount}
                  rowsPerPage={pageSize}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  labelRowsPerPage={t("product.rowPerPage")}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default ProductsTableMobile;
