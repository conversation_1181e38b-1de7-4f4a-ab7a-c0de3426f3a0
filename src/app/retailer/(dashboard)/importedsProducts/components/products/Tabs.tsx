import { Icon } from "@iconify/react";
import React from "react";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import { twMerge } from "tailwind-merge";

export type TTabsProps = {
  className?: string;
  items: { value: string; title: string; error?: boolean }[];
  value?: string;
  onChange?: (value: string) => void;
};

function Tabs({ className, items, value, onChange }: TTabsProps) {
  return (
    <div className="border-b border-v2-border-primary">
      <div className={twMerge("flex items-center cursor-pointer gap-4 ", className)}>
        <Swiper
          dir="rtl"
          slidesPerView={"auto"}
          keyboard
          grabCursor
          spaceBetween={24}
          modules={[Navigation, Keyboard, Mousewheel]}
        >
          {items?.map(item => (
            <SwiperSlide key={item?.value} className="max-w-fit">
              <div
                key={item.value}
                className={twMerge(
                  "shrink-0 border-b-2 py-3 pb-2.5 px-3 cursor-pointer flex items-center justify-center gap-1",
                  item.value === value ? "border-v2-content-on-action-2" : "border-transparent"
                )}
                onClick={() => onChange?.(item.value)}
              >
                <div
                  className={twMerge(
                    "text-sm font-medium text-v2-content-secondary leading-5",
                    item.value === value ? "text-v2-content-on-action-2" : ""
                  )}
                >
                  {item.title}
                </div>

                {item?.error && (
                  <Icon icon="solar:danger-bold" width={20} height={20} className="text-v2-content-on-error-2" />
                )}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
        {/* <div
        className={twMerge(
          "flex items-center cursor-pointer gap-4  -mt-px overflow-auto w-[73vw] scrollbar-hide",
          className
        )}
      ></div> */}
      </div>
    </div>
  );
}

export default Tabs;
