"use client";

import { usePostRetailderProductPushMutation, usePutRetailerProductMutation } from "@/store/apps/retailerProduct";
import { TRetailerImportListData } from "@/store/apps/retailerProduct/types";
import useModal from "@/utils/hooks/useModal";
import { yupResolver } from "@hookform/resolvers/yup";
import { useRouter } from "next/navigation";
import React, { useEffect, useMemo, useState, useImperativeHandle, forwardRef } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import General from "./general";
import Pricing from "./pricing";
import { TFormData } from "./types";
import { validationSchema } from "./validationSchema";
import Tabs from "../Tabs";
import Description from "./Description";
import Images from "./images";
import Variants from "./variants";

interface IEditProps {
  product?: TRetailerImportListData["data"][0];
}

const Edit = forwardRef(({ product }: IEditProps, ref) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { showModal, hideModal } = useModal();
  const [activeTab, setActiveTab] = useState("general");

  const [updateProduct, { isLoading: isLoadingUpdateProduct }] = usePutRetailerProductMutation();
  const [pushProducts, { isLoading: isLoadingPostProductPush }] = usePostRetailderProductPushMutation();
  const [isAlsoPublish, setIsAlsoPublish] = useState(false);

  const initialValues = useMemo(() => {
    return {
      id: product?.id,
      title: product?.title || "",
      description: product?.description || "",
      categoryId: product?.categoryId ? String(product?.categoryId) : "",
      tags: product?.tags ? product?.tags : [],
      images: product?.images || [],
      cover: product?.originProduct?.cover?.url || undefined,
      variants: product?.variants || [],
      isActive: product?.isActive ?? false
    };
  }, [product]);

  const showSuccessModal = () => {
    hideModal();

    setTimeout(() => {
      showModal({
        title: t("retailerProduct.saveSuccessfull"),
        icon: "/images/svgs/verify.svg",
        actions: [
          {
            label: t("retailerProduct.goToProductList"),
            className: "w-full",
            variant: "primary",
            onClick: () => {
              hideModal();
              router.back();
            }
          }
        ]
      });
    }, 0);
  };

  const showErrorModal = (message?: string) => {
    hideModal();

    setTimeout(() => {
      showModal({
        title: t("errors.somethingWentWrong"),
        subTitle: message,
        icon: "/images/svgs/danger.svg",
        actions: [
          {
            label: t("retailerProduct.ok"),
            className: "w-full",
            variant: "primary",
            onClick: () => {
              hideModal();
            }
          }
        ]
      });
    }, 0);
  };

  const formMethods = useForm<TFormData>({
    defaultValues: initialValues,
    resolver: yupResolver(validationSchema as any) as any,
    mode: "all"
  });
  const {
    handleSubmit,
    watch,
    trigger,
    formState: { errors, isValid, isValidating }
  } = formMethods;

  const isFormValid = !Object.keys(errors)?.length;

  // Expose errors and validation status to parent component through ref
  useImperativeHandle(
    ref,
    () => ({
      errors,
      isValid,
      isValidating,
      hasErrors: Object.keys(errors)?.length > 0,
      getErrorsByTab: () => ({
        general: tabGeneralHasErrors,
        description: tabDescriptionHasErrors,
        images: tabImagesHasErrors,
        variants: tabVariantsHasErrors,
        pricing: tabPricingHasErrors
      }),
      submitForm: async () => {
        try {
          // Trigger validation first
          const isValidForm = await trigger();

          if (!isValidForm) {
            console.log("Form validation failed:", errors);
            return Promise.reject(new Error("Form validation failed"));
          }

          // If validation passes, submit the form
          return handleSubmit(onSubmit)();
        } catch (error) {
          console.error("Form submission error:", error);
          return Promise.reject(error);
        }
      }
    }),
    [errors, isValid, isValidating, trigger, handleSubmit]
  );
  const handlePublish = async () => {
    setIsAlsoPublish(false);

    if (!product?.id) {
      throw new Error("Product ID is missing");
    }

    pushProducts({ body: { ids: [product.id] } }).then((res: any) => {
      if (res?.error) {
        showErrorModal();
        return;
      }

      showSuccessModal();
    });
  };

  const onSubmit = async (values: TFormData & { id?: string }) => {
    const { id, ...restValues } = values || {};
    const body = {
      ...restValues
    };

    if (!isFormValid || !id) {
      // showErrorModal();
      // throw new Error();
      return;
    }

    return updateProduct({ productId: id, body }).then((res: any) => {
      if (res?.error) {
        showErrorModal();
        return;
      }

      if (isAlsoPublish) {
        handlePublish();
        return;
      }
    });
  };

  const manuallySubmitTheForm = () => {
    handleSubmit(onSubmit)();
  };

  const tabGeneralHasErrors = Object.keys(errors)?.some(a => ["title", "categoryId", "tags"].includes(a));
  const tabDescriptionHasErrors = Object.keys(errors)?.some(a => ["description"].includes(a));
  const tabImagesHasErrors = Object.keys(errors)?.some(a => ["images"].includes(a));
  const tabVariantsHasErrors = Object.keys(errors)?.some(a => ["variants"].includes(a));
  const tabPricingHasErrors = Object.keys(errors)?.some(a => ["variants"].includes(a));

  return (
    <div className="flex flex-col">
      <Tabs
        value={activeTab}
        onChange={setActiveTab}
        items={[
          { title: "اطلاعات اولیه", value: "general", error: tabGeneralHasErrors },
          { title: "توضیحات", value: "description", error: tabDescriptionHasErrors },
          { title: "تصاویر محصول", value: "images", error: tabImagesHasErrors },
          ...(!!product?.variants?.length && product?.variants?.length > 1
            ? [{ title: "تنوع‌ها", value: "variants", error: tabVariantsHasErrors }]
            : []),
          { title: "قیمت گذاری", value: "pricing", error: tabPricingHasErrors }
        ]}
      />

      <div className="pt-4">
        <FormProvider {...formMethods}>
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6">
            {activeTab === "general" && <General {...{ manuallySubmitTheForm }} />}
            {activeTab === "description" && <Description {...{ manuallySubmitTheForm }} />}
            {activeTab === "images" && (
              <Images images={product?.originProduct?.images} {...{ manuallySubmitTheForm }} />
            )}
            {activeTab === "variants" && (
              <Variants originalProductData={product?.originProduct} {...{ manuallySubmitTheForm }} />
            )}
            {activeTab === "pricing" && (
              <Pricing originalProductData={product?.originProduct} {...{ manuallySubmitTheForm }} />
            )}
          </form>
        </FormProvider>
      </div>
    </div>
  );
});

Edit.displayName = "Edit";

export default Edit;
