import React from "react";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { TFormData } from "./types";
import { TRetailerProductPayloadResponse } from "@/store/apps/retailerProduct/types";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import Image from "next/image";

function Images({
  images,
  manuallySubmitTheForm
}: {
  images?: snakeCaseToCamelCase<TRetailerProductPayloadResponse>["data"]["originProduct"]["images"];
  manuallySubmitTheForm: VoidFunction;
}) {
  const { t } = useTranslation();
  const { watch, setValue } = useFormContext<TFormData>();
  const formDataImages = watch("images");

  return (
    <div className="flex flex-col gap-4">
      {!!images && images?.length > 0 && (
        <div className="flex items-center gap-4 flex-wrap">
          {images?.map(image => {
            const isChecked = formDataImages?.find(a => a.url === image?.url);
            const handleClickSelectBox = () => {
              if (isChecked) {
                // remove from values
                setValue("images", formDataImages?.filter(a => a.url !== image?.url));
              } else {
                // add to images
                setValue("images", [...formDataImages, image]);
              }

              setTimeout(() => {
                manuallySubmitTheForm();
              }, 500);
            };
            return (
              <div className="relative size-[94px] overflow-hidden rounded-lg border border-gray-40" key={image?.url}>
                <CustomCheckbox
                  checked={!!isChecked}
                  className="absolute -top-px -left-px z-10 !m-0"
                  onClick={handleClickSelectBox}
                />
                <Image src={image?.url} alt={image?.alt} fill />
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

export default Images;
