import React from "react";
import { Controller, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { TFormData } from "./types";
import Input from "@/components/ui/inputs/Input";
import InputLabel from "@/components/ui/inputs/Input/InputLabel";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import { CustomTags } from "@/components/ui/CustomTags";
import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import { useGetRetailerCategoryListQuery } from "@/store/apps/retailerProduct";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import SelectCategoryWithModal from "@/components/containers/SelectCategoryModal/SelectCategoryWithModal";
import { useRetailerStore } from "@/store/zustand/retailerStore/RetailerStore";

function General({ manuallySubmitTheForm }: { manuallySubmitTheForm: VoidFunction }) {
  const { t } = useTranslation();
  const { control, setValue } = useFormContext<TFormData>();
  const router = useRouter();
  const makePath = useRoleBasePath();

  const { data: categories, isLoading: isRetailerCategoryLoading } = useGetRetailerCategoryListQuery();

  const categoryItems =
    categories?.data?.map(item => ({
      id: item?.id,
      label: item?.hierarchy || item.name
    })) || [];

  const { data: retailerStore } = useRetailerStore();

  const handleCreateNewCategory = () => {
    router.push(`${makePath(routes.retailerListCategories)}`);
  };

  return (
    <div className="grid xmd:grid-cols-2 grid-cols-1 gap-4">
      <div>
        <Controller
          name="title"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              onBlur={() => {
                manuallySubmitTheForm();
                field?.onBlur();
              }}
              autoComplete="off"
              label={t("product.title")}
              placeholder={t("product.title")}
              error={Boolean(error?.message)}
              helperText={error?.message || t("product.titleHint")}
            />
          )}
        />
      </div>

      <div>
        {retailerStore?.integration?.platform?.key &&
        ["ShopBuilder"]?.includes(retailerStore?.integration?.platform?.key) ? (
          <Controller
            name="categoryId"
            control={control}
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <>
                <InputLabel containerClassName="mb-1">{t("product.productCategory")}</InputLabel>
                <SelectCategoryWithModal
                  initialValue={value ? value : undefined}
                  placeholder={t("product.categoryPlaceholder")}
                  onChange={categoryId => {
                    onChange(categoryId);
                  }}
                  error={Boolean(error?.message)}
                />
                {!!error?.message && <InputHelper className="mt-1">{error?.message}</InputHelper>}
              </>
            )}
          />
        ) : (
          <Controller
            name="categoryId"
            control={control}
            render={({ field, fieldState }) => (
              <>
                <CustomAutocomplete<(typeof categoryItems)[0]>
                  {...field}
                  readOnlyInput={false}
                  value={isRetailerCategoryLoading ? null : categoryItems?.find(item => item?.id === field?.value)}
                  options={categoryItems}
                  placeholder={t("retailer.categoryPlaceholder")}
                  label={t("retailer.category")}
                  error={!!fieldState?.error?.message}
                  helperText={fieldState?.error?.message}
                  onChange={(e, value) => {
                    setValue("categoryId", value?.id ?? "");
                    setTimeout(() => {
                      manuallySubmitTheForm();
                    }, 500);
                  }}
                  PaperComponent={({ children }) => (
                    <div className="bg-cards rounded-lg mt-1 shadow-category border border-v2-border-secondary">
                      {retailerStore?.integration?.platform?.key &&
                        ["ShopBuilder"]?.includes(retailerStore?.integration?.platform?.key) && (
                          <div className="px-3 pt-3 ">
                            <div
                              className="flex items-center gap-2 border-b border-b-v2-border-secondary pb-3 cursor-pointer"
                              onMouseDown={e => {
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                              onClick={e => {
                                e.preventDefault();
                                e.stopPropagation();
                                setTimeout(() => {
                                  handleCreateNewCategory();
                                }, 0);
                              }}
                            >
                              <Icon icon="ph:plus" className="size-5 shrink-0  text-v2-surface-action" />
                              <span className="text-v2-surface-action text-caption-medium">
                                {t("retailer.addNewCategory")}
                              </span>
                            </div>
                          </div>
                        )}
                      {children}
                    </div>
                  )}
                />
              </>
            )}
          />
        )}
      </div>

      <div className="xmd:col-span-2 col-span-1">
        <Controller
          name="tags"
          control={control}
          render={({ field: { name, value, onChange, onBlur }, fieldState: { error } }) => (
            <>
              <InputLabel htmlFor="tags" requiredStar={false} containerClassName="mb-1">
                {t("product.tags")}
              </InputLabel>
              <CustomTags
                optional
                requiredStar={false}
                handleBlur={onBlur}
                value={value ?? []}
                onChange={({ tagValues }) => {
                  onChange(tagValues);

                  setTimeout(() => {
                    manuallySubmitTheForm();
                  }, 500);
                }}
                name={name}
                error={Boolean(error?.message)}
                helperText={error?.message ? error?.message : t("product.tagHint")}
                placeholder={t("product.tagsPlaceholder")}
              />
              {!!error?.message && <InputHelper className="mt-1">{error?.message}</InputHelper>}
            </>
          )}
        />
        <div className="text-xs text-v2-content-tertiary mt-1">{t("product.tagHint")}</div>
      </div>
    </div>
  );

  // return (
  //   <div className="flex flex-col gap-4">
  //     <div className="flex flex-col gap-4">
  //       <div className="text-v2-content-primary text-base font-medium">{t("retailerProduct.category")}</div>

  //       <div className="md:-mt-1">
  //         <Controller
  //           name="isActive"
  //           control={control}
  //           render={({ field: { name, value, onChange, onBlur }, fieldState: { error } }) => (
  //             <>
  //               <InputLabel htmlFor="isActive" containerClassName="mb-1">
  //                 {t("product.status")}
  //               </InputLabel>
  //               <div className="-mt-0.5 mb-px">
  //                 <CustomRadio
  //                   label={t("product.statusItems.Active")}
  //                   checked={value === true}
  //                   value={true}
  //                   name="isActive"
  //                   onChange={e => {
  //                     onChange(e.target.value === "true");
  //                   }}
  //                 />
  //                 <CustomRadio
  //                   checked={value === false}
  //                   onChange={e => {
  //                     onChange(e.target.value === "true");
  //                   }}
  //                   name="isActive"
  //                   label={t("product.statusItems.Inactive")}
  //                   value={false}
  //                 />
  //               </div>
  //               {!!error?.message && <InputHelper>{error?.message}</InputHelper>}
  //             </>
  //           )}
  //         />
  //       </div>
  //     </div>
  //   </div>
  // );
}

export default General;
