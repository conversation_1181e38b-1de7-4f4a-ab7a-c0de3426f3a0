import { calcProfitAmount } from "@/components/containers/productDetailPage/utils";
import PriceInput from "@/components/ui/inputs/PriceInput";
import { TRetailerProductPayloadResponse } from "@/store/apps/retailerProduct/types";
import { toCommas } from "@/utils/helpers";
import useCurrency from "@/utils/hooks/useCurrency";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import { useCallback } from "react";
import { Controller, useFieldArray, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { TFormData } from "./types";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import { twMerge } from "tailwind-merge";

function Pricing({
  originalProductData,
  manuallySubmitTheForm
}: {
  originalProductData?: snakeCaseToCamelCase<TRetailerProductPayloadResponse>["data"]["originProduct"];
  manuallySubmitTheForm: VoidFunction;
}) {
  const { t } = useTranslation();
  const [{ symbol }] = useCurrency();

  const {
    control,
    watch,
    formState: { errors }
  } = useFormContext<TFormData>();

  const variants = watch("variants");

  if (!!variants?.length && variants?.length === 1) {
    return (
      <div className="grid xmd:grid-cols-2 grid-cols-1 gap-3">
        <div>
          <Controller
            name={`variants.0.salesPrice`}
            control={control}
            render={({ field, fieldState: { error } }) => (
              <PriceInput
                {...field}
                onBlur={() => {
                  field?.onBlur();
                  manuallySubmitTheForm();
                }}
                //   showSymbol={false}
                label={t("retailerProduct.salesPrice")}
                placeholder={t("retailerProduct.salesPrice")}
                error={Boolean(error?.message)}
                helperText={error?.message || t("retailerProduct.salesPriceHint")}
              />
            )}
          />
        </div>
        <div>
          <Controller
            name={`variants.0.compareAtPrice`}
            control={control}
            render={({ field, fieldState: { error } }) => (
              <PriceInput
                {...field}
                onBlur={() => {
                  field?.onBlur();
                  manuallySubmitTheForm();
                }}
                optional
                requiredStar={false}
                //   showSymbol={false}
                label={t("retailerProduct.compareAtPrice")}
                placeholder={t("retailerProduct.compareAtPrice")}
                error={Boolean(error?.message)}
                helperText={error?.message || t("retailerProduct.compareAtPriceHint")}
              />
            )}
          />
        </div>
      </div>
    );
  }

  return (
    <TableContainer>
      <Table stickyHeader aria-label="sticky table">
        <TableHead>
          <TableRow>
            <TableCell className="py-[13px]">تنوع ها</TableCell>
            <TableCell className="py-[13px]">قیمت فروش ({symbol})</TableCell>
            <TableCell className="py-[13px]">قیمت تامین کننده ({symbol})</TableCell>
            <TableCell className="py-[13px]"> قیمت مقایسەای ({symbol})</TableCell>
            <TableCell className="py-[13px]"> سود</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {variants?.map((variant, index) => {
            const originalProductVariant = originalProductData?.variants?.find(a => a.id === variant.id);

            const retailerProfitAmount = calcProfitAmount({
              commission: originalProductVariant?.commission || 0,
              retailPrice: originalProductVariant?.retailPrice || 0
            });
            // const retailerProfitPercent = calcProfitPercent(retailerProfitAmount, originalProductVariant?.listingPrice);

            return (
              <TableRow className="!h-14" key={"--" + index}>
                <TableCell width={200} className="text-[13px] font-medium  border-l border-v2-border-primary !py-0">
                  {originalProductVariant?.options
                    ? Object.entries(originalProductVariant.options)
                        .map(item => `${item?.[0]}: ${item?.[1]}`)
                        .join(" | ")
                    : "-"}
                </TableCell>

                <TableCell width={100} className="text-[13px] font-medium  border-l border-v2-border-primary !py-0">
                  <Controller
                    name={`variants.${index}.salesPrice`}
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <PriceInput
                        {...field}
                        onBlur={() => {
                          field?.onBlur();
                          manuallySubmitTheForm();
                        }}
                        showSymbol={false}
                        inputSize="sm"
                        // label={t("retailerProduct.salesPrice")}
                        placeholder={t("retailerProduct.salesPrice")}
                        error={Boolean(error?.message)}
                        inputParentClassName="border-transparent hover:border-v2-border-primary focus-within:bg-v2-surface-primary data-[invalid=true]:border-v2-content-on-error-2"
                        helperText={error?.message}
                        // helperText={error?.message || t("retailerProduct.salesPriceHint")}
                      />
                    )}
                  />
                  {/* {variant.salesPrice ? toCommas(variant.salesPrice) : "-"} {symbol} */}
                </TableCell>
                <TableCell width={100} className="text-[13px] font-medium  border-l border-v2-border-primary !py-0">
                  {originalProductVariant?.retailPrice ? toCommas(originalProductVariant?.retailPrice) : "-"}
                </TableCell>
                <TableCell width={100} className="text-[13px] font-medium  border-l border-v2-border-primary !py-0">
                  <Controller
                    name={`variants.${index}.compareAtPrice`}
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <PriceInput
                        {...field}
                        onBlur={() => {
                          field?.onBlur();
                          manuallySubmitTheForm();
                        }}
                        optional
                        requiredStar={false}
                        showSymbol={false}
                        inputSize="sm"
                        // label={t("retailerProduct.compareAtPrice")}
                        placeholder={t("retailerProduct.compareAtPrice")}
                        error={Boolean(error?.message)}
                        helperText={error?.message}
                        inputParentClassName="border-transparent hover:border-v2-border-primary focus-within:bg-v2-surface-primary data-[invalid=true]:border-v2-content-on-error-2"
                        // helperText={error?.message || t("retailerProduct.compareAtPriceHint")}
                      />
                    )}
                  />
                  {/* {variant.compareAtPrice ? toCommas(variant.compareAtPrice) : "-"} {symbol} */}
                </TableCell>
                <TableCell
                  width={100}
                  className={twMerge(
                    "text-[13px] font-medium !py-0",
                    retailerProfitAmount && retailerProfitAmount > 0 ? "text-v2-content-on-success-2" : "",
                    retailerProfitAmount && retailerProfitAmount < 0 ? "text-v2-content-on-error-2" : ""
                  )}
                >
                  {!!retailerProfitAmount ? `${toCommas(retailerProfitAmount)}` : "-"}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
}

export default Pricing;
