import { TRetailerProductPayloadResponse } from "@/store/apps/retailerProduct/types";
import useCurrency from "@/utils/hooks/useCurrency";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import { Controller, useFieldArray, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { TFormData } from "./types";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";

function Variants({
  originalProductData,
  manuallySubmitTheForm
}: {
  originalProductData?: snakeCaseToCamelCase<TRetailerProductPayloadResponse>["data"]["originProduct"];
  manuallySubmitTheForm: VoidFunction;
}) {
  const { t } = useTranslation();

  const { control } = useFormContext<TFormData>();

  const { fields: variants } = useFieldArray({
    control,
    name: "variants",
    keyName: "key"
  });

  if (!variants?.length || (!!variants?.length && variants?.length === 1)) {
    return null;
  }

  const variantOptions = originalProductData?.variants?.[0]?.options;

  return (
    <TableContainer>
      <Table stickyHeader aria-label="sticky table">
        <TableHead>
          <TableRow>
            <TableCell className="py-[13px]">شناسه</TableCell>

            {variantOptions &&
              Object.keys(variantOptions)?.map((item, index) => (
                <TableCell key={index} className="py-[13px]">
                  {item}
                </TableCell>
              ))}

            <TableCell className="py-[13px]">موجودی</TableCell>
            <TableCell className="py-[13px]">افزودن بە فروشگاه</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {variants?.map((variant, index) => {
            const originalProductVariant = originalProductData?.variants?.find(a => a.id === variant.id);

            return (
              <TableRow className="h-12" key={"--" + index}>
                <TableCell width={100} className="text-[13px] border-l border-v2-border-primary">
                  {originalProductVariant?.sku}
                </TableCell>

                {originalProductVariant?.options &&
                  Object.entries(originalProductVariant.options).map((item, index) => (
                    <TableCell key={index} width={100} className="text-[13px] border-l border-v2-border-primary">
                      {item?.[1]}
                    </TableCell>
                  ))}

                <TableCell width={50} className="text-[13px] border-l border-v2-border-primary">
                  {originalProductVariant?.inventory}
                </TableCell>
                <TableCell width={50} className="text-[13px] !px-4">
                  <Controller
                    name={`variants.${index}.isActive`}
                    control={control}
                    key={(variant as any)?.key || variant?.id}
                    render={({ field, fieldState: { error } }) => (
                      <CustomSwitch
                        name={field?.name}
                        labelClassName="ml-0 hidden md:block"
                        checked={field?.value === true}
                        onChange={(e, v) => {
                          field.onChange(v === true ? true : false);

                          setTimeout(() => {
                            manuallySubmitTheForm();
                          }, 500);
                        }}
                      />
                    )}
                  />
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
}

export default Variants;
