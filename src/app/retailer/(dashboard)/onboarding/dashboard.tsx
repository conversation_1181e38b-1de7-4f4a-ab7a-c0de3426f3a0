import Chart from "@/components/ui/Chart/Chart";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";
import { Box } from "@mui/material";
import React from "react";
import { useState } from "react";
import {
  chartFilterItems,
  firstChartData,
  firstChartOptions,
  primaryChartData,
  productData,
  secondChartData,
  secondChartOptions,
  thirdChartData,
  thirdChartOptions,
  topProductData
} from "./utils";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import PrimaryChart from "./PrimaryChart";
import useCurrency from "@/utils/hooks/useCurrency";
import { Icon } from "@iconify/react";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import OnBoardingAlert from "@/components/containers/OnboardingAlert/OnboardingAlert";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { TRetailerProfileData } from "@/store/apps/retailer/types";
import useModal from "@/utils/hooks/useModal";
import RetailerSignContractModal from "@/components/containers/ProfilesSignContractModal/RetailerSignContractModal";

export const getPercentColor = (percent: number) => {
  switch (true) {
    case percent === 0:
      return { bgColor: "white", color: "black" };
    case percent < 0:
      return { bgColor: "#FEF6F5", color: "#E61B06" };
    case percent > 0:
      return { bgColor: "#E6FBF4", color: "#04D294" };
    default:
      return { bgColor: "white", color: "black" };
  }
};

export const getPercentIcon = (percent: number) => {
  switch (true) {
    case percent === 0:
      return "";
    case percent < 0:
      return "solar:arrow-down-linear";
    case percent > 0:
      return "solar:arrow-up-linear";
    default:
      return "";
  }
};

interface IDashboardProps {
  retailerData: TRetailerProfileData;
}

const Dashboard = ({ retailerData }: IDashboardProps) => {
  const { t } = useTranslation();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };
  const makePath = useRoleBasePath();
  const { showModal, hideModal } = useModal();

  const termsAndConditionsApproved = !!retailerData?.termsAndConditionsApproved || false;

  const statisticsData = [
    {
      id: 1,
      title: t("chart.newOrder"),
      subtitle: t("chart.newOrderSubtitle"),
      count: 0,
      percent: 0,
      chartData: firstChartData,
      chartOptions: firstChartOptions
    },
    {
      id: 2,
      title: t("chart.deliveredOrder"),
      subtitle: t("chart.deliveredOrderSubtitle"),
      count: 0,
      percent: 0,
      chartData: secondChartData,
      chartOptions: secondChartOptions
    },
    {
      id: 3,
      title: t("chart.averageIncome"),
      subtitle: t("chart.averageIncomeSubtitle"),
      count: 0,
      isPrice: true,
      percent: 0,
      chartData: thirdChartData,
      chartOptions: thirdChartOptions
    }
  ];

  const handleShowSignContractModal = () => {
    showModal({
      width: 801,
      modalProps: { showCloseIcon: false },
      body: (
        <RetailerSignContractModal
          close={hideModal}
          onSuccess={() => {
            hideModal();
          }}
        />
      )
    });
  };

  return (
    <>
      {/* {retailerData?.status === "InReview" && (
        <OnBoardingAlert
          imgSrc="/images/svgs/inReview-icon.svg"
          status="inReview"
          subtile={t("retailer.onboarding.inReview.subtitle")}
          title={t("retailer.onboarding.inReview.title")}
        />
      )} */}

      {/* {retailerData?.id && !termsAndConditionsApproved && (
        <OnBoardingAlert
          imgSrc="/images/svgs/contract.svg"
          status="contract"
          subtile={t("retailer.onboarding.contract.subtitle")}
          title={t("retailer.onboarding.contract.title")}
          titleClassName="text-v2-content-on-info"
          subtitleClassName="!text-v2-content-primary"
          endAdornment={
            <CustomButton
              onClick={handleShowSignContractModal}
              className="retailer-onboarding-alert-endadornment"
              endIcon={<Icon icon="iconamoon:arrow-left-2-light" width={24} height={24} />}
              color="secondary"
            >
              {t("retailer.onboarding.contractButton")}
            </CustomButton>
          }
        />
      )} */}

      <div className="flex flex-col gap-4">
        {" "}
        {/* Metrics cards */}
        <div className=" grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ">
          {statisticsData?.map(item => (
            <div className="bg-cards rounded-lg py-6 ps-6 pe-3 flex justify-between max-h-32" key={item?.id}>
              <div>
                <h3 className="text-gray-900 text-body2-medium">{item?.title}</h3>
                <div className="flex items-center gap-2 mt-4">
                  <div>
                    {item?.isPrice ? (
                      <p className="text-h5-medium text-gray-999">{renderPrice(item?.count)}</p>
                    ) : (
                      <p className="text-h5-medium text-gray-999">{item?.count}</p>
                    )}
                  </div>
                  <div className=" flex items-center gap-1">
                    <div
                      className="rounded-full size-[14px] flex items-center justify-center"
                      style={{
                        background: getPercentColor(item?.percent)?.bgColor,
                        color: getPercentColor(item?.percent)?.color
                      }}
                    >
                      <Icon icon={getPercentIcon(item?.percent)} className="size-[10px]" />
                    </div>

                    <span
                      className="text-caption-regular"
                      style={{
                        color: getPercentColor(item?.percent)?.color
                      }}
                    >
                      %{item?.percent}
                    </span>
                  </div>
                </div>
                <p className="text-gray-500 text-caption-regular mt-1">{item?.subtitle}</p>
              </div>
              <div className="mt-1">
                <Chart data={item?.chartData} options={item?.chartOptions as any} />
              </div>
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Product List - Best Selling Products */}

          <div className="bg-cards py-6 px-7 rounded-lg">
            <div className="flex  justify-between mb-4 items-center">
              <h3 className="text-body1-regular text-gray-600 ">{t("chart.recentProduct")}</h3>
              <Link href={makePath(routes.retailerProductsDrafts)} className="flex items-center gap-1">
                <span className="text-gray-600 text-body4-medium">{t("chart.all")}</span>
                <Icon icon="solar:arrow-left-up-linear" className="size-4 text-gray-500" />
              </Link>
            </div>
            {topProductData.map((product, index) => (
              <div
                key={index}
                className="flex items-center mb-4 gap-4 border border-solid border-b border-transparent border-b-gray-50 pb-4 last-of-type:border-b-transparent last-of-type:mb-0 last-of-type:pb-0"
              >
                <Image src={product.image} alt="product" className=" rounded-md" width={50} height={50} />
                <div className="flex flex-col gap-1">
                  <p className="text-gray-999 text-body4-medium !whitespace-normal">{product.name}</p>
                  <div className="flex items-center gap-1 text-cyan-500">
                    <Link href={product.link} className="text-body4-medium">
                      {t("chart.showProduct")}
                    </Link>
                    <Icon icon="solar:arrow-left-up-linear" className="size-4 text-cyan-500" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="bg-cards py-6 px-7 rounded-lg">
            <div className="flex  justify-between mb-4 items-center">
              <h3 className="text-body1-regular text-gray-600 ">{t("chart.topProduct")}</h3>
              <Link href={makePath(routes.retailerProductsDrafts)} className="flex items-center gap-1">
                <span className="text-gray-600 text-body4-medium">{t("chart.all")}</span>
                <Icon icon="solar:arrow-left-up-linear" className="size-4 text-gray-500" />
              </Link>
            </div>
            {productData.map((product, index) => (
              <div
                key={index}
                className="flex items-center mb-4 gap-4 border border-solid border-b border-transparent border-b-gray-50 pb-4 last-of-type:border-b-transparent last-of-type:mb-0 last-of-type:pb-0"
              >
                <Image src={product.image} alt="product" className=" rounded-md" width={50} height={50} />
                <div className="flex flex-col gap-1">
                  <p className="text-gray-999 text-body4-medium truncate !whitespace-normal">{product.name}</p>
                  <div className="flex items-center gap-1 text-cyan-500">
                    <Link href={product.link} className="text-body4-medium">
                      {t("chart.showProduct")}
                    </Link>
                    <Icon icon="solar:arrow-left-up-linear" className="size-4 text-cyan-500" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="bg-cards rounded-xl px-8 py-7">
          <h3 className="text-gray-600 mb-5"> {t("chart.sellStatus")}</h3>

          <PrimaryChart />
        </div>
      </div>
    </>
  );
};

export default Dashboard;
