import "./retailerOnboarding.css";

import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import { routes } from "@/constants/routes/index";
import { TSupplierProfileData, TSupplierShippingData } from "@/store/apps/supplier/types";
import { useGetSupplierOnboardingQuery } from "@/store/apps/supplierDashboard";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { Box, CircularProgress, Stack, Typography } from "@mui/material";
import clsx from "clsx";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import OnBoardingAlert from "@/components/containers/OnboardingAlert/OnboardingAlert";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import SupplierSignContractModal from "@/components/containers/ProfilesSignContractModal/SupplierSignContractModal";
import useModal from "@/utils/hooks/useModal";
import { twMerge } from "tailwind-merge";
import OnboardingProgressBar from "@/app/supplier/(dashboard)/onboarding/OnboardingProgressBar";
import Dashboard from "./dashboard";
import { getOnboardingProgress } from "@/app/supplier/(dashboard)/onboarding/utils";
import { useGetRetailerOnboardingQuery } from "@/store/apps/retailer";
import { TRetailerProfileData } from "@/store/apps/retailer/types";

function RetailerOnboarding() {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { showModal, hideModal } = useModal();

  const { data: onboardingData, isLoading: isOnboardingLoading } = useGetRetailerOnboardingQuery();

  const retailerProfile = useSelector((state: any) => state?.Retailer?.queries["getRetailerProfile(undefined)"] || {});
  const retailerProfileData = retailerProfile?.data?.data as TRetailerProfileData;

  const retailerProfileLoading = retailerProfile?.status === "pending";
  const isLoading = retailerProfileLoading || isOnboardingLoading;

  const activeProfile = retailerProfileData?.status === "Active";
  const rejectProfile = retailerProfileData?.status === "Rejected";

  const onboardingItems = [
    {
      title: t("retailer.onboarding.items.userInfoTitle"),
      icon: "flowbite:profile-card-outline",
      isFilled: onboardingData?.data?.profile,
      path: makePath(routes.profile),
      disabled: false,
      step: 0
    },
    {
      title: t("retailer.onboarding.items.storeInfoTitle"),
      icon: "fluent-mdl2:return-to-session",
      isFilled: onboardingData?.data?.hasStore,
      path: `${makePath(routes.profile)}?settingType=store`,
      disabled: false
    },
    {
      title: t("retailer.onboarding.items.addressInfoTitle"),
      icon: "fluent-mdl2:return-to-session",
      isFilled: onboardingData?.data?.profile,
      path: makePath(routes.profile),
      disabled: false,
      step: 1
    }
  ];

  const incompletePercent = getOnboardingProgress(onboardingItems)?.complete;

  const isFilled = onboardingItems?.every(item => item?.isFilled);
  const notCompleted = onboardingItems?.every(item => !item?.isFilled);

  const handleShowSignContractModal = () => {
    showModal({
      width: 801,
      modalProps: { showCloseIcon: false },
      body: (
        <SupplierSignContractModal
          close={hideModal}
          onSuccess={() => {
            hideModal();
          }}
        />
      )
    });
  };

  if (isLoading) {
    return (
      <Box className="retailer-onboarding-container">
        <Stack alignItems="center" justifyContent="center" minHeight="50vh">
          <CircularProgress />
        </Stack>
      </Box>
    );
  }

  // if (activeProfile || (supplierProfileData?.status === "InReview" && isFilled)) {
  //   return <Dashboard supplierData={supplierProfileData} isFilled={isFilled} />;
  // }

  const hasStatusBar =
    (retailerProfileData?.id && !retailerProfileData?.termsAndConditionsApproved) ||
    (!activeProfile && !rejectProfile) ||
    retailerProfileData?.status === "Rejected";

  return (
    <>
      {!activeProfile && !rejectProfile && (
        <>
          {isFilled ? (
            <div className="min-h-[200px] max-h-[200px] xmd:max-h-full rounded-lg  bg-v2-surface-success-2 w-full flex justify-between">
              <div className="px-6 py-9">
                <div className="text-v2-content-on-warning-2">
                  <div className="flex items-center gap-5">
                    <OnboardingProgressBar
                      progress={100}
                      size={50}
                      strokeWidth={4}
                      color={"rgb(var(--success-success))"}
                    />
                    <div className="flex flex-col gap-2">
                      <span className="text-subtitle-bold text-v2-content-on-success-2 ">{t("profileCompleted")}</span>
                      <span className="text-v2-content-primary text-body4-medium ">
                        {t("profileCompletedSubtitle")}
                      </span>
                    </div>
                  </div>

                  <Link
                    href={makePath(routes.profile)}
                    className="mt-5 bg-cards text-v2-content-primary rounded-lg max-h-10 border border-v2-border-primary p-3 w-fit flex items-center gap-2"
                  >
                    <Icon icon="solar:scanner-outline" className="size-5" />
                    <span className="text-body4-medium text-v2-content-primary">{t("checkProfile")}</span>
                  </Link>
                </div>
              </div>
              <img
                src="/images/profile/dashboard-checked.png"
                alt="completedProfile"
                className="xmd:size-[220px] 2xmd:size-[120px] 2xmd:block hidden xmd:-rotate-12 xmd:ml-20 "
              />
            </div>
          ) : (
            <div className="min-h-[200px] max-h-[200px] xmd:max-h-full rounded-lg bg-warning-50 flex justify-between">
              <div className="px-6 py-9">
                <div className="flex items-center gap-5">
                  <OnboardingProgressBar
                    progress={incompletePercent}
                    size={50}
                    strokeWidth={4}
                    color={"rgb(var(--warning-warning-dark))"}
                  />
                  <div className="flex flex-col gap-2">
                    <span className="text-subtitle-bold text-v2-content-on-warning-2 ">{t("successCompleted")}</span>
                    <span className="text-v2-content-primary text-body4-medium ">{t("successCompletedSubtitle")}</span>
                  </div>
                </div>
                <Link
                  href={makePath(routes.profile)}
                  className="mt-5 bg-cards text-v2-content-primary rounded-lg max-h-10 border border-v2-border-primary p-3 w-fit flex items-center gap-2"
                >
                  <Icon icon="solar:code-scan-outline" className="size-5" />
                  <span className="text-body4-medium text-v2-content-primary">{t("completeProfile")}</span>
                </Link>
              </div>
              <img
                src={
                  notCompleted ? "/images/profile/dashboard-notcompleted.png" : "/images/profile/dashboard-checking.png"
                }
                alt="inprogressProfile"
                className="xmd:size-[220px] 2xmd:size-[120px] 2xmd:block hidden xmd:-rotate-12 xmd:ml-20 "
              />
            </div>
          )}
        </>
      )}

      {/* {retailerProfileData?.id && !retailerProfileData?.termsAndConditionsApproved && (
        <div className="mt-4">
          <OnBoardingAlert
            imgSrc="/images/svgs/contract.svg"
            status="contract"
            subtile={t("retailer.onboarding.contract.subtitle")}
            title={t("retailer.onboarding.contract.title")}
            titleClassName="text-v2-content-on-info"
            subtitleClassName="!text-v2-content-primary"
            endAdornment={
              <CustomButton
                onClick={handleShowSignContractModal}
                className="!border-transparent hover:border-transparent focus:border-transparent bg-transparent !text-v2-content-on-info"
                endIcon={<Icon icon="iconamoon:arrow-left-2-light" width={24} height={24} />}
                color="secondary"
              >
                {t("retailer.onboarding.contractButton")}
              </CustomButton>
            }
          />
        </div>
      )} */}

      {/* {retailerProfileData?.status === "InReview" && isFilled && (
        <div className="mt-4">
          <OnBoardingAlert
            imgSrc="/images/svgs/inReview-icon.svg"
            status="inReview"
            subtile={t("supplier.onboarding.inReview.subtitle")}
            title={t("supplier.onboarding.inReview.title")}
          />
        </div>
      )} */}

      {retailerProfileData?.status === "Rejected" && (
        <div className="mt-4">
          <OnBoardingAlert
            status="rejected"
            imgSrc="/images/svgs/rejected-icon.svg"
            title={t("supplier.onboarding.rejected.title")}
            subtile={
              retailerProfileData?.rejectionNote
                ? t("reason") + " " + retailerProfileData?.rejectionNote
                : t("supplier.onboarding.rejected.subtitle")
            }
            endAdornment={
              <CustomButton
                LinkComponent={Link}
                href={makePath(routes.profile)}
                className="retailer-onboarding-alert-endadornment"
                endIcon={<Icon icon="iconamoon:arrow-left-2-light" width={24} height={24} />}
                color="secondary"
              >
                {t("supplier.onboarding.recompleteProfile")}
              </CustomButton>
            }
          />
        </div>
      )}

      <div className={twMerge("bg-cards rounded-lg p-4 ", hasStatusBar ? "my-4" : "xmd:my-4 mb-4")}>
        <div className="flex justify-between">
          <div>
            <h5 className="text-v2-content-primary text-body1-medium">{t("supplier.onboardingStatusTitle")}</h5>
            <p className="text-body3-medium text-v2-content-tertiary mt-2.5">
              {t("supplier.onboardingStatusSubtitle")}
            </p>
          </div>

          <div className="flex gap-2 h-fit flex-wrap">
            <span className="text-body2-medium whitespace-nowrap">{t("accountStatus")}</span>
            <div className="rounded-full px-2 text-body4-medium xmd:py-0.5 py-0 bg-success-50 text-success-500">
              {t("active")}
            </div>
          </div>
        </div>
        <div className="grid items-center mt-3  gap-3 lg:grid-cols-3 xmd:grid-cols-2 grid-cols-1 grid-rows-1">
          {onboardingItems?.map((item, index) => (
            <Link
              href={item?.disabled ? "" : item?.step ? `${item?.path}?step=${item?.step}` : item?.path}
              className={twMerge(
                "flex items-center h-14 px-4 py-5 border border-v2-border-primary rounded-lg justify-between",
                item?.disabled && "pointer-events-none"
              )}
              key={index}
            >
              <span className="text-v2-content-primary text-body3-medium">{item?.title}</span>
              {item?.isFilled ? (
                <Icon icon="teenyicons:tick-circle-solid" className="size-6 text-v2-content-on-info" />
              ) : (
                <Icon icon="solar:alt-arrow-left-outline" className="size-4 text-v2-content-subtle" />
              )}
            </Link>
          ))}
        </div>
      </div>
      <Dashboard retailerData={retailerProfileData} />
    </>
  );
}

export default WithBottomBar(RetailerOnboarding);
