import Chart from "@/components/ui/Chart/Chart";
import React, { useState } from "react";
import { chartFilterItems, primaryChartData } from "./utils";

const PrimaryChart: React.FC = () => {
  const [selectedFilter, setSelectedFilter] = useState<string>("1months");

  const handleFilterClick = (filter: string) => {
    setSelectedFilter(prev => (prev === filter ? "" : filter));
  };

  const filterData = (data: (number | Date)[][], filter: string) => {
    const today = new Date();
    switch (filter) {
      case "1days":
        return data.filter(([date]) => date >= new Date(today.setDate(today.getDate() - 1)));
      case "2weeks":
        return data.filter(([date]) => date >= new Date(today.setDate(today.getDate() - 14)));
      case "1months":
        return data.filter(([date]) => date >= new Date(today.setMonth(today.getMonth() - 1)));
      default:
        return data; // If no filter is selected, return all data
    }
  };

  const chartData = filterData(primaryChartData, selectedFilter)?.map(item => [(item[0] as Date)?.getTime(), item[1]]);

  return (
    <>
      <div className="">
        <div className="flex flex-wrap gap-2 mb-2">
          {chartFilterItems.map(filter => (
            <button
              key={filter.id}
              onClick={() => handleFilterClick(filter.name)}
              className={`px-3 py-1.5 rounded-full transition-colors duration-200  text-body2-medium border border-gray-50 text-gray-999 bg-cards
                ${selectedFilter === filter.name ? "bg-purple-50 text-purple-500 border border-purple-500" : ""}`}
            >
              {filter.title}
            </button>
          ))}
        </div>
      </div>

      <div className="px-3">
        <Chart data={chartData} />
      </div>
    </>
  );
};

export default PrimaryChart;
