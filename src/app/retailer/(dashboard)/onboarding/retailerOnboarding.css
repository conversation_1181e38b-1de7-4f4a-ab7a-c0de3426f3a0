.retailer-onboarding-container {
  background-color: rgb(var(--color-cards));
  border-radius: 8px;
  padding: 36px 60px;
}

.retailer-onboarding-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 10px;
}

.retailer-onboarding-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: rgb(var(--color-gray-400));
}

.retailer-onboarding-item-container {
  border: 1px solid rgb(var(--color-gray-50));
  border-radius: 8px;
  padding: 16px;
  max-width: 306px;
}

.retailer-onboarding-item-number {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgb(var(--color-purple-500));
  padding-block-start: 2px;
  padding-inline-end: 1px;
}

.retailer-onboarding-item-number-text {
  color: #ffffff;
  font-size: 19px;
  font-weight: bold;
}

.retailer-onboarding-item-title {
  font-size: 14px;
  font-weight: 500;
  color: rgb(var(--color-gray-400));
  margin-top: 16px;
}

.retailer-onboarding-item-title-active {
  color: rgb(var(--color-gray-999));
}

.retailer-onboarding-item-button {
  margin-top: 16px;
}

.retailer-onboarding-wrapper {
  margin-top: 24px;
  border-radius: 7px;
}

.retailer-onboarding-alert-endadornment.custom-button.MuiButton-containedSecondary {
  color: rgb(var(--color-gray-999));
}

.retailer-onboarding-alert-endadornment {
  margin-block: 8px;
}

@media (max-width: 768px) {
  .retailer-onboarding-item-container {
    max-width: 100%;
  }

  .retailer-onboarding-container {
    padding: 34px 16px;
  }

  .retailer-onboarding-wrapper {
    margin-top: 16px;
  }

  .retailer-onboarding-title {
    font-size: 18px;
    font-weight: 500;
  }

  .retailer-onboarding-subtitle {
    font-size: 14px;
    font-weight: 500;
  }

  .retailer-onboarding-item-title {
    font-size: 13px;
    font-weight: 400;
  }

  .retailer-onboarding-item-image {
    width: 24px;
    height: 24px;
  }
}
