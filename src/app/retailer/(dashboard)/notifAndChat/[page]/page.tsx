"use client";

import AclWrapper from "@/components/containers/AclWrapper";
import ChatHeader from "@/components/containers/notifAndChat/chat/ChatHeader";
import NotifAndChat from "@/components/containers/notifAndChat/NotifAndChat";
import ChatsHeader from "@/components/containers/notifAndChat/ChatsHeader";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import Header from "@/components/containers/header/Header";

import { RETAILER_USER_TYPE } from "@/constants";
import { useSearchParams } from "next/navigation";
import { useGetConversationQuery } from "@/store/apps/conversation";
import { useTranslation } from "react-i18next";

export default function NotifAndChatPage() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const chatId = searchParams?.get("chatId");

  const { data: conversation } = useGetConversationQuery();

  const chatsCount = conversation?.data?.length;

  return (
    <AclWrapper for={RETAILER_USER_TYPE}>
      {!chatsCount && !chatId ? (
        <Header title={t("messages")} isMobile isSticky />
      ) : (
        <MobileAppBar
          className="!h-20 border-b border-b-v2-border-primary"
          RenderComponent={chatId ? <ChatHeader /> : <ChatsHeader chatsCount={chatsCount} />}
        />
      )}
      <div className="xmd:mt-auto mt-20">
        <NotifAndChat />
      </div>
    </AclWrapper>
  );
}
