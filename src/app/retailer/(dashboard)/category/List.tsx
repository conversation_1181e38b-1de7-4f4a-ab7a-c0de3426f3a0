import AddCategoryModal from "@/app/retailer/(dashboard)/category/AddCategoryModal";
import { routes } from "@/constants/routes";
import { useGetRetailerCategoryListQuery } from "@/store/apps/retailerProduct";
import useModal from "@/utils/hooks/useModal";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";

function List() {
  const { t } = useTranslation();
  const router = useRouter();
  const { showModal } = useModal();
  const makePath = useRoleBasePath();

  const { data, isLoading } = useGetRetailerCategoryListQuery();

  const handleOpenAddCategoryModal = (initialValues?: { name: string; id: string }) => {
    showModal({
      body: <AddCategoryModal initialValues={initialValues} />,
      modalProps: { showCloseIcon: false },
      width: 475
    });
  };

  return (
    <div className="p-4 md:p-9 md:bg-v2-surface-primary rounded-lg flex flex-col">
      <div className="flex justify-between items-center">
        <div className="flex gap-2 items-center text-v2-content-primary text-sm font-medium">
          <Icon
            icon="solar:arrow-right-outline"
            width={24}
            height={24}
            className="cursor-pointer"
            onClick={() => router.push(makePath(routes.retailerProductsImports))}
          />
          {t("retailer.categoryPage.categories")}
        </div>
        <div
          className="flex gap-2 items-center text-v2-content-on-info text-sm font-medium cursor-pointer"
          onClick={() => handleOpenAddCategoryModal()}
        >
          <Icon icon="ph:plus" width={24} height={24} className="" />
          {t("retailer.categoryPage.addNewCategory")}
        </div>
      </div>

      <div
        className="flex flex-col gap-2 mt-6 overflow-auto max-w-3xl mx-auto w-full"
        style={{ height: "calc(100dvh - 250px)" }}
      >
        {isLoading && (
          <div className="flex items-center justify-center h-full">
            <CircularProgress />
          </div>
        )}

        {!isLoading && !data?.data?.length && (
          <div className="flex flex-col items-center justify-center h-full gap-5">
            <div className="">
              <Image src="/images/product-list-empty-2.svg" width={196} height={230} alt="empty list palceholder" />
            </div>

            <div className="flex gap-2 flex-col text-center">
              <div className="text-v2-content-primary font-bold text-lg">{t("retailer.noCategory")}</div>
              <div className="text-v2-content-tertiary font-medium text-sm">{t("retailer.noCategoryDesc")}</div>
            </div>
          </div>
        )}
        {!isLoading &&
          data?.data?.length &&
          data?.data?.map(item => (
            <div key={item?.id} className="rounded-lg p-4 border-2 border-v2-border-primary flex gap-4 items-center">
              <div className="shrink-0">
                <Image src="/images/icons/add-img.svg" alt="" height={40} width={40} />
              </div>
              <div className="text-v2-content-primary text-[13px] font-medium flex-1">{item?.name}</div>

              <div>
                <div
                  className="rounded-full border-2 border-v2-border-secondary p-1 cursor-pointer"
                  onClick={() => handleOpenAddCategoryModal({ ...item })}
                >
                  <Icon icon="solar:pen-2-outline" width={20} height={20} className="text-v2-content-tertiary" />
                </div>
              </div>
            </div>
          ))}
      </div>
    </div>
  );
}

export default List;
