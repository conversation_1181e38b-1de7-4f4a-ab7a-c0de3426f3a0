import Button from "@/components/ui/Button";
import Input from "@/components/ui/inputs/Input";
import { usePutRetailerCategoryMutation } from "@/store/apps/retailerProduct";
import useModal from "@/utils/hooks/useModal";
import i18n from "@/utils/i18n";
import { yupResolver } from "@hookform/resolvers/yup";
import { Close } from "@mui/icons-material";
import Image from "next/image";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import * as yup from "yup";

const Validation = yup.object({
  name: yup.string().required(i18n.t("validations.requiredField"))
});

function AddCategoryModal({ initialValues }: { initialValues?: { name: string; id: string } }) {
  const { t } = useTranslation();
  const { hideModal } = useModal();
  const [mutate, { isLoading }] = usePutRetailerCategoryMutation();

  const form = useForm({
    defaultValues: {
      name: initialValues?.name || "",
      id: initialValues?.id || ""
    },
    resolver: yupResolver(Validation) as any
  });

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = form;

  const onSubmit = (data: { name: string; id: string }) => {
    mutate({ name: data.name, id: data?.id })
      .unwrap()
      .then(() => {
        hideModal();
      });
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div className="text-[13px] font-medium text-v2-content-primary flex gap-2 items-center">
          <Image src="/images/icons/category.svg" alt="" width={24} height={24} className="block" />
          {initialValues ? t("retailer.categoryPage.EditCategory") : t("retailer.categoryPage.addNewCategory")}
        </div>
        <div onClick={hideModal} className="cursor-pointer">
          <Close className="size-5 text-v2-content-tertiary" />
        </div>
      </div>
      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              autoComplete="off"
              label={t("retailer.categoryPage.name")}
              placeholder={t("retailer.categoryPage.name")}
              error={Boolean(errors.name?.message)}
              helperText={errors?.name?.message || ""}
            />
          )}
        />

        <div className="flex gap-4">
          <Button
            type="button"
            variant="secondaryGray"
            size="xl"
            className="w-full"
            onClick={hideModal}
            disabled={isLoading}
          >
            {t("retailer.categoryPage.cancel")}
          </Button>
          <Button type="submit" variant="primary" size="xl" className="w-full" disabled={isLoading}>
            {t("retailer.categoryPage.submit")}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default AddCategoryModal;
