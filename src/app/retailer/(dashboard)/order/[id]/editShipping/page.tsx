"use client";

import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import { useGetOrderQuery } from "@/store/apps/order";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useTranslation } from "react-i18next";
import OrderShippingAddress from "../orderDetail/OrderShippingAddress";

function EditShipping() {
  const params = useParams();
  const { t } = useTranslation();
  const router = useRouter();

  const id = params?.id as string;

  const { data: retailerOrder } = useGetOrderQuery({ id }, { skip: !id });

  const onBack = () => {
    router.back();
  };

  return (
    <>
      <MobileAppBar hasBack onBack={onBack} backIcon="ic:baseline-close" title={t("editAddress")} />
      <div className="px-4 pt-2 pb-6 rounded-lg bg-v2-surface-primary mt-16 ">
        <OrderShippingAddress
          shippingAddress={retailerOrder?.data?.shippingAddress}
          oid={retailerOrder?.data?.id}
          hasUncalculatedShipping={retailerOrder?.data?.hasUncalculatedShipping}
        />
      </div>
    </>
  );
}

export default EditShipping;
