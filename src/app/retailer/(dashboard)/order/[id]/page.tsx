"use client";
import AclWrapper from "@/components/containers/AclWrapper";
import RetailerOrderDetail from "./RetailerOrderDetail";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import { useTranslation } from "react-i18next";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import React from "react";

const OrderDetailPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <AclWrapper for="RETAILER">
      <MobileAppBar title={t("orderDetail")} hasBack />
      <RetailerOrderDetail />
    </AclWrapper>
  );
};

export default WithBottomBar(OrderDetailPage);
