import { CircularProgress } from "@mui/material";
import Image from "next/image";
import { useTranslation } from "react-i18next";

function PaymentRedirecting() {
  const { t } = useTranslation();
  return (
    <div>
      <div className="flex flex-col items-center pt-10">
        <CircularProgress size={48} />
        <span className="text-subtitle-bold text-purple-500 mt-5">{t("order.pleaseWait")}...</span>
        <p className="text-body3-medium text-gray-999 mt-5">{t("order.redirecting")}</p>
      </div>
      <div className="border border-solid border-t-gray-50 border-transparent py-4 mt-6 flex items-center justify-center">
        <Image src="/images/svgs/paymentRedirection.svg" width={82} height={20} alt="payment" />
      </div>
    </div>
  );
}

export default PaymentRedirecting;
