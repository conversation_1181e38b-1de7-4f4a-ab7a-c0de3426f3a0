import React from "react";
import { Box, Stack, Typography, Link, Divider, CircularProgress } from "@mui/material";
import { Icon } from "@iconify/react";
import clsx from "clsx";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { useTranslation } from "react-i18next";
import useCurrency from "@/utils/hooks/useCurrency";
import { TOrderData } from "@/store/apps/order/types";
import { ensureUrlScheme } from "@/utils/helpers";

// import "./OrderSummary.css";
import DownloadInvoice from "@/components/containers/orders/DownloadInvoice";
import useLanguage from "@/utils/hooks/useLanguage";
import useLocations from "@/utils/hooks/useLocations";
import { useParams } from "next/navigation";
import { twMerge } from "tailwind-merge";
import useClipboard from "@/utils/hooks/useClipboard";
import OrderStatus, { IOrderStatusItems } from "@/app/supplier/(dashboard)/order/[id]/orderDetail/OrderStatus";
import OrderDetailRow from "./OrderDetailRow";
import RetailerOrderPay from "../../components/RetailerOrderPay";
import OrderDetailShipment from "./OrderDetailShipment";

interface IMobileOrderSummaryProps {
  retailerOrder?: { data: TOrderData };
  handleTrackingCode: () => void;
  handleOpenShippingAddress: () => void;
  subTotal: number;
  vat: number;
  shippingCost: number;
  isRetailerOrderFetching: boolean;
  isConfirmingPayment: boolean;
  onConfirmPayment: () => Promise<void>;
}

const MobileOrderDetail = ({
  retailerOrder,
  handleTrackingCode,
  handleOpenShippingAddress,
  subTotal,
  vat,
  shippingCost,
  onConfirmPayment,
  isConfirmingPayment,
  isRetailerOrderFetching
}: IMobileOrderSummaryProps) => {
  const [{ renderDate }] = useLanguage();
  const { t } = useTranslation();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };
  const { getLocation } = useLocations();
  const params = useParams();
  const id = params?.id as string;
  const { isCopied, copyToClipboard } = useClipboard();

  return (
    <>
      <div className="flex flex-col flex-wrap bg-cards rounded-lg p-4">
        <div className="flex items-center gap-3 text-h4-bold text-v2-content-primary flex-wrap justify-between">
          <div className="flex items-center">
            <span className="whitespace-nowrap">{t("retailerOrder.orderSummary.orderNumber")} : </span>
            <span className="mr-1">{retailerOrder?.data?.orderNumber ?? ""}</span>#
          </div>
          <div>
            {!!retailerOrder?.data?.state && (
              <OrderStatus
                id={retailerOrder?.data?.state as IOrderStatusItems["id"]}
                title={t(`retailerOrder.orderStateItems.${retailerOrder?.data?.state?.toLowerCase()}`)}
              />
            )}
          </div>
        </div>

        <div className="flex items-center gap-2 mt-4">
          <Icon icon="solar:calendar-outline" className="size-4" />
          {!!retailerOrder?.data?.createdAt && (
            <div className="flex items-center gap-2 ">
              <span className="text-body3-medium text-v2-content-tertiary">
                {t("retailerOrder.orderSummary.orderAt")} :{" "}
              </span>
              <span className="text-body3-medium text-v2-content-tertiary">
                {!!retailerOrder?.data?.createdAt && renderDate(retailerOrder?.data?.createdAt, "HH:mm - YYYY/MM/DD")}
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center justify-between mt-4 gap-2 flex-wrap">
          {retailerOrder?.data?.state === "Done" && (
            <div
              onClick={handleTrackingCode}
              className="flex items-center gap-1.5 text-v2-content-primary cursor-pointer"
            >
              <Icon icon="solar:object-scan-outline" className="size-3.5 text-v2-content-primary" />
              <span className="text-body4-medium text-v2-content-primary">{t("order.trackingCode")}</span>
            </div>
          )}

          {(retailerOrder?.data?.paymentState === "Paid" || retailerOrder?.data?.paymentState === "Captured") && (
            <DownloadInvoice
              orderId={id}
              orderNumber={retailerOrder?.data?.orderNumber}
              printCustomerTitle={t("retailerOrder.orderSummary.printSellInvoice")}
              printSellerTitle={t("retailerOrder.orderSummary.printBuyInvoice")}
            />
          )}

          <RetailerOrderPay
            id={retailerOrder?.data?.id}
            state={retailerOrder?.data?.state}
            paymentState={retailerOrder?.data?.paymentState}
            hasUncalculatedShipping={retailerOrder?.data?.hasUncalculatedShipping}
          />
          {retailerOrder?.data?.state === "Pending" && (
            <CustomButton onClick={onConfirmPayment}>
              {isConfirmingPayment ? <CircularProgress size={16} color="inherit" /> : t("confirmPayment")}
            </CustomButton>
          )}
        </div>
      </div>

      <div className="bg-cards rounded-lg p-4 mt-3">
        <div>
          <span className="text-v2-content-primary text-body2-medium font-semibold">
            {t("retailerOrder.orderSummary.statuses")}
          </span>

          <div className="flex items-center justify-between pb-3 border-b border-b-v2-border-secondary mt-6">
            <span className="text-body4-regular text-v2-content-primary">
              {t("retailerOrder.orderSummary.orderState")}
            </span>

            {!!retailerOrder?.data?.state && (
              <OrderStatus
                id={retailerOrder?.data?.state as IOrderStatusItems["id"]}
                title={t(`retailerOrder.orderStateItems.${retailerOrder?.data?.state?.toLowerCase()}`)}
              />
            )}
          </div>

          <div className="flex items-center justify-between py-3 border-b border-b-v2-border-secondary">
            <span className="text-body4-regular text-v2-content-primary">
              {t("retailerOrder.orderSummary.paymentState")}
            </span>

            {!!retailerOrder?.data?.paymentState && (
              <OrderStatus
                id={retailerOrder?.data?.paymentState}
                title={t(`retailerOrder.paymentStateItems.${retailerOrder?.data?.paymentState?.toLowerCase()}`)}
              />
            )}
          </div>

          <div className="flex items-center justify-between pt-3 ">
            <span className="text-body4-regular text-v2-content-primary">
              {t("retailerOrder.orderSummary.deliveryState")}
            </span>

            {!!retailerOrder?.data?.shippingState && (
              <OrderStatus
                id={retailerOrder?.data?.shippingState}
                title={t(`retailerOrder.deliveryStateItems.${retailerOrder?.data?.shippingState?.toLowerCase()}`)}
              />
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 mt-3">
        <div className=" rounded-lg p-4 bg-cards">
          <div className="flex items-center gap-2 pb-6 border-b border-b-v2-border-secondary">
            <Icon icon="solar:cart-3-outline" className="size-6" />
            <span className="text-v2-content-primary text-body2-bold font-semibold">
              {t("retailerOrder.orderSummary.addedOrder")}
            </span>
          </div>

          <div className="pt-3">
            {retailerOrder?.data?.lineItems?.map((item, index) => (
              <OrderDetailRow
                key={item?.id}
                item={item}
                isLastItem={(retailerOrder?.data?.lineItems?.length || 0) - 1 === index}
              />
            ))}
          </div>
        </div>

        <div className=" rounded-lg p-4 bg-cards mt-3">
          <div>
            <span className="text-v2-content-primary text-body2-medium font-semibold">
              {t("retailerOrder.orderSummary.customerInfo")}
            </span>
          </div>

          <div className="flex items-center justify-between mt-6 pb-3 border-b border-b-v2-border-secondary">
            <div className="flex items-center gap-1">
              <Icon icon="carbon:user-avatar" width={15} height={15} />
              <span className="text-body4-regular text-v2-content-primary">
                {t("retailerOrder.orderSummary.customer")}
              </span>
            </div>

            <span className="text-body4-regular text-v2-content-primary">
              {" "}
              {retailerOrder?.data?.customer?.firstName} {retailerOrder?.data?.customer?.lastName}
            </span>
          </div>

          <div className="flex items-center justify-between  py-3 border-b border-b-v2-border-secondary">
            <div className="flex items-center gap-1">
              <Icon icon="mage:email" width={15} height={15} />
              <span className="text-body4-regular text-v2-content-primary">
                {t("retailerOrder.orderSummary.email")}
              </span>
            </div>
            <span className="text-body4-regular text-v2-content-primary">
              {" "}
              {retailerOrder?.data?.customer?.email ?? "-"}{" "}
            </span>
          </div>

          <div className="flex items-center justify-between pt-3">
            <div className="flex items-center gap-1">
              <Icon icon="solar:outgoing-call-outline" width={15} height={15} />{" "}
              <span className="text-body4-regular text-v2-content-primary">
                {t("retailerOrder.orderSummary.phone")}
              </span>
            </div>
            <span className="text-body4-regular text-v2-content-primary">
              {retailerOrder?.data?.customer?.phoneNumber ? retailerOrder?.data?.customer?.phoneNumber : "-"}{" "}
            </span>
          </div>
        </div>

        <div className=" rounded-lg p-4 mt-3 bg-cards">
          <div className="flex items-center gap-2 ">
            <Icon icon="solar:card-outline" className="size-6" />
            <span className="text-v2-content-primary text-body2-bold font-semibold">
              {t("retailerOrder.orderSummary.payInfo.title")}
            </span>
            <div className="mr-2">
              {!!retailerOrder?.data?.paymentState && (
                <OrderStatus
                  id={retailerOrder?.data?.paymentState}
                  title={t(`retailerOrder.paymentStateItems.${retailerOrder?.data?.paymentState?.toLowerCase()}`)}
                />
              )}
            </div>
          </div>
          <p className="text-body4-regular text-v2-content-primary mt-2">
            {t("retailerOrder.orderSummary.payInfo.subtitle")}
          </p>

          <div className="mt-6">
            <div className="flex justify-between">
              <div className="flex flex-col gap-1">
                <span className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("retailerOrder.orderSummary.grandTotal")}
                </span>
                <span className="flex-1 text-caption-medium text-v2-content-tertiary">
                  {retailerOrder?.data?.lineItems?.reduce((sum, item) => sum + item?.quantity, 0)}
                </span>
              </div>
              <div className="flex-1 text-body3-medium text-v2-content-primary text-end ">{renderPrice(subTotal)}</div>
            </div>

            <div className="flex justify-between mt-6">
              <div className="flex flex-col gap-1">
                <span className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("retailerOrder.orderSummary.discount")}
                </span>
                <span className="flex-1 text-caption-medium text-v2-content-tertiary ">-</span>
              </div>
              <div className="flex-1 text-body3-medium text-v2-content-primary text-end">-</div>
            </div>

            <div className="flex justify-between mt-6">
              <div className="flex flex-col gap-1">
                <span className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("retailerOrder.orderSummary.shippingCost")}
                </span>
                <span className="flex-1 text-caption-medium text-v2-content-tertiary ">
                  {/* {t("retailerOrder.orderSummary.freeIfBiggerThan2mil")} */}
                </span>
              </div>
              <div className="flex-1 text-body3-medium text-v2-content-primary text-end">
                {renderPrice(shippingCost)}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between mt-6 bg-v2-surface-info rounded-lg px-3 py-2">
            <p className="flex-1 text-body3-medium !font-bold text-v2-content-primary whitespace-nowrap">
              {t("retailerOrder.orderSummary.finalTotalPrice")}
            </p>
            <p className="flex-1" />
            <p className="flex-1 text-end text-body3-medium !font-bold text-v2-content-primary">
              {renderPrice(retailerOrder?.data?.totalPrice)}
            </p>
          </div>
        </div>
      </div>

      <div className=" w-full ">
        <div className=" rounded-lg p-4 bg-cards mt-3">
          <div className="flex items-center justify-between">
            <span className="text-v2-content-primary text-body2-medium font-semibold">
              {t("order.postAddressInfo")}
            </span>
            {retailerOrder?.data?.hasUncalculatedShipping && (
              <Icon
                onClick={handleOpenShippingAddress}
                icon="solar:pen-2-outline"
                className="size-4 text-gray-400 cursor-pointer"
              />
            )}
          </div>

          <div className="flex items-center gap-1 pb-3 border-b border-b-v2-border-secondary mt-6">
            <Icon icon="carbon:user-avatar" width={15} height={15} />
            <span className="text-body4-regular text-v2-content-primary">
              {retailerOrder?.data?.customer?.firstName || ""} {retailerOrder?.data?.customer?.lastName || ""}
            </span>
          </div>

          {/* <div className="flex items-center ">
                <Icon icon="carbon:user-avatar" width={15} height={15} />
                <span>
                  {t("retailerOrder.orderSummary.cityState") + retailerOrder?.data?.shippingAddress?.state ||
                    getLocation(retailerOrder?.data?.shippingAddress?.locationId ?? "")?.parent?.name ||
                    "-"}
                </span>
              </div> */}
          <div className="flex items-center gap-1 py-3 ">
            <Icon icon="solar:map-arrow-square-outline" width={15} height={15} />
            <span className="text-body4-regular text-v2-content-primary">
              {t("retailerOrder.orderSummary.cityState")}{" "}
              {getLocation(retailerOrder?.data?.shippingAddress?.locationId ?? "")?.parent?.name || "-"}
            </span>
          </div>

          <div className="mb-2">
            <span className="text-body4-regular text-v2-content-primary">
              {t("supplierOrder.orderSummary.city")}:{" "}
              {getLocation(retailerOrder?.data?.shippingAddress?.locationId ?? "")?.name || "-"}
            </span>
          </div>

          <div>
            <span className="text-body4-regular text-v2-content-primary">
              {retailerOrder?.data?.shippingAddress?.address1 || ""}
            </span>
          </div>

          {/* <div className="flex items-center justify-between mt-2">
            <span className="text-body4-regular text-gray-600">{t("order.phoneNumber")}</span>
            <span className="text-body4-regular text-v2-content-primary">
              {retailerOrder?.data?.shippingAddress?.phoneNumber
                ? retailerOrder?.data?.shippingAddress?.phoneNumber
                : "-"}
            </span>
          </div> */}

          <div className="flex items-center justify-between mt-1">
            <span className="text-body4-regular text-gray-600">{t("supplier.profile.zip")}</span>
            <span className="text-body4-regular text-v2-content-primary">
              {retailerOrder?.data?.shippingAddress?.zip ? retailerOrder?.data?.shippingAddress?.zip : "-"}
            </span>
          </div>

          {retailerOrder?.data?.hasUncalculatedShipping && (
            <div className="p-4 mt-3 bg-v2-surface-warining-1 rounded-lg border border-v2-content-on-warning-2 flex flex-col">
              <div className="flex items-center gap-1">
                <Icon icon="solar:info-circle-bold" className="text-v2-content-primary" />
                <span className="text-v2-content-primary text-body4-medium">
                  {t("retailerOrder.orderSummary.UncalculatedShipping.title")}
                </span>
              </div>

              <div className="mt-1">
                <span className="text-caption-medium text-v2-content-secondary leading-3">
                  {t("retailerOrder.orderSummary.UncalculatedShipping.subtitle")}
                </span>
              </div>

              <CustomButton
                onClick={handleOpenShippingAddress}
                className="text-v2-content-on-action-hover-1 !bg-v2-surface-warining-2 !border-transparent mt-5 !mr-auto "
                endIcon={<Icon icon="oui:arrow-left" className=" size-5 text-v2-content-on-action-hover-1 " />}
              >
                {t("retailerOrder.orderSummary.UncalculatedShipping.editAddress")}
              </CustomButton>
            </div>
          )}
        </div>

        {/* <div className="bg-cards rounded-lg p-4 mt-3">
          <div className="flex items-center justify-between">
            <span className="text-v2-content-primary text-body2-medium font-semibold">
              {t("order.postAddressInfo")}
            </span>
            {retailerOrder?.data?.hasUncalculatedShipping && (
              <Icon
                onClick={handleOpenShippingAddress}
                icon="solar:pen-2-outline"
                className="size-4 text-gray-400 cursor-pointer"
              />
            )}
          </div>

          <div className="mt-6">
            {retailerOrder?.data?.shipmentDetails?.map(item => (
              <div key={item?.orderId}>
                <div className="flex items-center justify-between pb-3 border-b border-b-v2-border-secondary">
                  <div className="flex items-center gap-1">
                    <Icon icon="solar:square-alt-arrow-left-outline" width={15} height={15} />
                    <span className="text-body4-regular text-v2-content-primary"> {t("order.shippingType")}</span>
                  </div>
                  <span className="text-body4-medium text-v2-content-primary"> {t("order.shippingType")}</span>
                </div>

                <div className="flex items-center justify-between pt-3 ">
                  <div className="flex items-center gap-1">
                    <Icon icon="solar:scanner-outline" width={15} height={15} />
                    <span className="text-body4-regular text-v2-content-primary"> {t("order.trackingCode")}</span>
                  </div>
                  <Link
                    target="_blank"
                    rel="noopener noreferrer"
                    href={ensureUrlScheme(item?.trackingUrl)}
                    className="py-0.5 px-1 rounded-full flex items-center gap-2 text-v2-content-on-info bg-v2-surface-info"
                  >
                    <span className="text-caption-medium">{t("retailerOrder.orderSummary.tracking")}</span>
                    <Icon
                      icon="solar:round-alt-arrow-left-bold"
                      width={14}
                      height={14}
                      className="text-v2-content-on-info"
                    />
                  </Link>
                </div>

                <div className="flex items-center gap-1 mt-4 mr-auto w-fit">
                  <span className="text-body4-medium text-v2-content-primary">{item?.trackingCode}</span>
                  <Icon
                    icon={isCopied ? "flat-color-icons:checkmark" : "icon-park-outline:copy"}
                    className={twMerge(isCopied ? "size-4 mb-1" : "size-3.5 ", "text-gray-600 cursor-pointer")}
                    onClick={e => {
                      e.stopPropagation();
                      copyToClipboard(item?.trackingCode);
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div> */}
      </div>

      {!!retailerOrder?.data?.shipmentDetails?.length && (
        <div className="bg-cards rounded-lg p-4 mt-3">
          <div className="flex items-center gap-2.5">
            <Icon icon="hugeicons:truck-delivery" className="size-4" />
            <span className="text-v2-content-primary text-body2-medium">
              {t("retailerOrder.orderSummary.addressInfoTitle")}
            </span>
          </div>
          <div>
            <span className="text-body4-regular text-v2-content-primary">
              {t("retailerOrder.orderSummary.addressInfoSubTitle")}
            </span>
          </div>

          <div className="mt-2">
            {retailerOrder?.data?.shipmentDetails?.map(shipItem => (
              <OrderDetailShipment key={shipItem?.carrierId} retailerOrder={retailerOrder} shipItem={shipItem} />
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default MobileOrderDetail;
