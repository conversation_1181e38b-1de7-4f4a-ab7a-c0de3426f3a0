#sx-fulfillmentdialog-15561 {
  margin-inline-start: auto;
  margin-block-start: 20px;
  margin-inline-end: 20px;
  color: rgb(var(--color-gray-400));
}
#sx-fulfillmentdialog-15588 {
  border-radius: 8px;
  margin-block: var(--mui-theme-spacing-2);
}

.supplier-order-detail-fullfillment-tick {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  margin-bottom: 16px;
  width: 86px;
  height: 86px;
  border-radius: 50%;
}

.supplier-order-detail-state-dialog-paper {
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
  box-shadow: unset;
  border-radius: 8px;
}

.supplier-order-detail-fullfillment-container {
  min-width: 350px;
}

@media (max-width: 500px) {
  .supplier-order-detail-state-dialog-paper {
    margin: 0;
  }
  .supplier-order-detail-fullfillment-container {
    min-width: 0;
  }
}

/* .custom-autocomplete.MuiAutocomplete-root .MuiOutlinedInput-root {
  padding-block: 1px;
} */

.supplier-order-detail-state-dialog-backdrop {
  background: rgba(0, 0, 0, 0.1);
}

.supplier-order-detail-fullfillment-title {
  font-size: 14px;
  font-weight: 500;
}

/* .order-detail-fullfillment-state-button-wrapper .custom-button {
  max-height: 40px;
} */
