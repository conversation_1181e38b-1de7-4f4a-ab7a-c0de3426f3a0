import "./FulfillmentDialog.css";

import React from "react";
import { Box, CircularProgress, Grid, Stack, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { ShipmentDetail, TOrderShipmentBody } from "@/store/apps/order/types";
import { usePutOrderShipmentMutation } from "@/store/apps/order";
import { Formik } from "formik";
import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import { supplierOrderStateValidationSchema } from "@/utils/validations/profile/supplier";
import useModal from "@/utils/hooks/useModal";
import { useGetMetaCarrierQuery } from "@/store/apps/meta";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { clientDefaultErrorHandler } from "@/utils/services/utils";

import { Icon } from "@iconify/react";
import Input from "@/components/ui/inputs/Input";
import Textarea from "@/components/ui/inputs/Textarea/Textarea";
import RectangleImageUploader2 from "@/components/containers/ImageUploader/withUi/RectangleImageUploader2";

interface IFulfillmentDialogProps {
  orderState: string;
  orderId?: string;
  shipmentDetail?: ShipmentDetail;
}
const FulfillmentDialog = ({ orderId, shipmentDetail }: IFulfillmentDialogProps) => {
  const { t } = useTranslation();
  const { hideModal } = useModal();

  const { data: carrierData, isLoading: isCarrierLoading } = useGetMetaCarrierQuery();

  const shippingCarrierItems = carrierData?.data?.length
    ? carrierData?.data?.map(item => ({
        id: item?.id,
        label: item?.name
      }))
    : [];

  const shippingCarrierValue = (shippingCarrier: string) =>
    shippingCarrierItems?.find(item => item.id === shippingCarrier);

  const [updateOrderState, { isLoading }] = usePutOrderShipmentMutation();

  const initialValues = {
    carrierId: shipmentDetail?.carrierId || undefined,
    trackingCode: shipmentDetail?.trackingCode || undefined,
    note: shipmentDetail?.note || undefined,
    documentId: shipmentDetail?.documentId || undefined
  };

  const onUpdateOrderState = async (value: Omit<TOrderShipmentBody["body"], "state">) => {
    const body = {
      carrierId: value?.carrierId,
      trackingCode: value?.trackingCode,
      note: value?.note,
      documentId: value?.documentId
    } as TOrderShipmentBody["body"];

    try {
      const res = await updateOrderState({ id: orderId, body });

      if ((res as any)?.error) {
        clientDefaultErrorHandler({ error: (res as any)?.error });
      }

      if ("data" in res && res.data) {
        hideModal();
      }
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  return (
    <div>
      {/* <Typography className="text-sm text-center text-gray-999">{t("order.orderDetailTitle")}</Typography> */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Icon icon="solar:delivery-bold" className="text-purple-500 size-6" />
          <span className="text-v2-content-primary text-body4-medium">
            {t("supplierOrder.orderSummary.orderDetail")}
          </span>
        </div>
        <Icon icon="iconamoon:close-light" className="size-5 cursor-pointer" onClick={hideModal} />
      </div>
      <div className="mt-4">
        <Formik
          initialValues={initialValues}
          validationSchema={supplierOrderStateValidationSchema}
          onSubmit={onUpdateOrderState}
        >
          {({ values, handleChange, handleBlur, touched, setFieldValue, errors, handleSubmit }) => (
            <form onSubmit={handleSubmit} className="mt-4">
              <Grid container spacing={2}>
                <Grid item xs={12} md={12}>
                  {isCarrierLoading ? (
                    <Stack className="flex items-center justify-center w-full h-full">
                      <CircularProgress size={16} />
                    </Stack>
                  ) : (
                    <CustomAutocomplete
                      value={values?.carrierId ? shippingCarrierValue(values?.carrierId ?? "") : null}
                      options={shippingCarrierItems}
                      size="medium"
                      label={t("order.carrierId")}
                      placeholder={t("order.carrierId")}
                      onChange={(e, value) => setFieldValue(`carrierId`, value?.id)}
                      error={touched?.carrierId && Boolean(errors?.carrierId)}
                      helperText={touched?.carrierId ? errors?.carrierId : undefined}
                    />
                  )}
                </Grid>

                <Grid item xs={12} md={12}>
                  <Input
                    // size="medium"
                    label={t("order.trackingCode")}
                    placeholder={t("order.trackingCode")}
                    id="trackingCode"
                    name="trackingCode"
                    value={values?.trackingCode}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched?.trackingCode && Boolean(errors?.trackingCode)}
                    helperText={touched?.trackingCode && errors?.trackingCode}
                  />
                </Grid>

                <Grid item xs={12} md={12}>
                  <RectangleImageUploader2
                    title={t("order.uploadDocument")}
                    subTitle={t("order.uploadDocumentDesc")}
                    uploadBtnLabel={t("order.uploadDocumentBtn")}
                    hasCropper={false}
                    onUploaded={res => {
                      setFieldValue("documentId", res.id);
                    }}
                    onRemove={() => {
                      setFieldValue("documentId", undefined);
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={12}>
                  <Textarea
                    // size="medium"
                    rows={5}
                    label={t("order.note")}
                    placeholder={t("order.notePlaceholder")}
                    id="note"
                    name="note"
                    value={values?.note}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched?.note && Boolean(errors?.note)}
                    helperText={touched?.note && errors?.note}
                  />
                </Grid>
              </Grid>

              <Stack
                flexDirection="row"
                gap={1}
                mt={2}
                alignItems="center"
                className="order-detail-fullfillment-state-button-wrapper"
              >
                <CustomButton fullWidth color="secondary" onClick={() => hideModal()}>
                  {t("order.cancel")}
                </CustomButton>
                <CustomButton type="submit" fullWidth disabled={isLoading} size="small">
                  {isLoading ? <CircularProgress color="info" size={20} /> : t("order.save")}
                </CustomButton>
              </Stack>
            </form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default FulfillmentDialog;
