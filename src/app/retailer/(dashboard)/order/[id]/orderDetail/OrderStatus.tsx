import { Box, Stack, Typography } from "@mui/material";
import "./OrderStatus.css";
import { Icon } from "@iconify/react";
import { orderStatusVariants } from "../utils";

export interface IOrderStatusItems {
  title: string;
  id: keyof typeof orderStatusVariants;
}

interface IOrderStatusVariant {
  color: string;
  bgColor: string;
}

interface IOrderStatusProps extends IOrderStatusItems {
  hasDot?: boolean;
}

function OrderStatus({ title, id, hasDot = false }: IOrderStatusProps) {
  const variant = orderStatusVariants[id] as IOrderStatusVariant;

  return (
    <Box className="order-status-container">
      <Stack
        direction="row"
        alignItems="center"
        gap={0.5}
        className="order-status-wrapper"
        sx={{
          background: variant?.["bgColor"] ?? ""
        }}
      >
        {!!hasDot && <Icon icon="icon-park-outline:dot" width={8} height={8} color={variant?.["color"] ?? ""} />}
        <Typography
          className="order-status-text"
          whiteSpace="nowrap"
          sx={{
            color: variant?.["color"] ?? ""
          }}
        >
          {title}
        </Typography>
      </Stack>
    </Box>
  );
}

export default OrderStatus;
