"use client";

import "./OrderDetail.css";

import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Box, CircularProgress, Divider, Theme, useMediaQuery } from "@mui/material";
import useLanguage from "@/utils/hooks/useLanguage";
import { useGetConfirmOrderPaymentMutation, useGetOrderQuery } from "@/store/apps/order";
import useLocations from "@/utils/hooks/useLocations";
import { Icon } from "@iconify/react";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import OrderDetailRow from "./OrderDetailRow";
import useCurrency from "@/utils/hooks/useCurrency";
import isNumber from "lodash/isNumber";
import Link from "next/link";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { useEffect } from "react";
import useModal from "@/utils/hooks/useModal";
import DownloadInvoice from "@/components/containers/orders/DownloadInvoice";
import OrderShippingAddress from "./OrderShippingAddress";
import RetailerOrderPay from "../../components/RetailerOrderPay";
import { routes } from "@/constants/routes";
import { twMerge } from "tailwind-merge";
import OrderStatus, { IOrderStatusItems } from "@/app/supplier/(dashboard)/order/[id]/orderDetail/OrderStatus";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import useClipboard from "@/utils/hooks/useClipboard";
import MobileOrderDetail from "./MobileOrderDetail";
import FulfillmentDialog from "./FulfillmentDialog";
import OrderDetailShipment from "./OrderDetailShipment";

const OrderDetail = () => {
  const [{ renderDate }] = useLanguage();
  const { t } = useTranslation();
  const params = useParams();
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = params?.id as string;
  const [curr] = useCurrency();
  const { showModal, hideModal } = useModal();
  const { render: renderPrice } = curr ?? { render: v => v };
  const { getLocation } = useLocations();
  const isTablet = useMediaQuery((theme: Theme) => theme.breakpoints.down(1024));
  const paymentStatus = searchParams?.get("payment_status") as "failed" | "success";
  const makePath = useRoleBasePath();
  const { isCopied, copyToClipboard } = useClipboard();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const {
    data: retailerOrder,
    isLoading: isRetailerOrderLoading,
    isFetching: isRetailerOrderFetching
  } = useGetOrderQuery({ id }, { skip: !id });

  const [confirmPayment, { isLoading: isConfirmingPayment }] = useGetConfirmOrderPaymentMutation();

  const handleOpenShippingAddress = () => {
    if (isMobile) {
      router.push(`${makePath(routes.order)}/${id}/editShipping`);
      return;
    }

    showModal({
      // icon: "/images/svgs/orderShippingAddress.svg",
      body: () => (
        <OrderShippingAddress
          shippingAddress={retailerOrder?.data?.shippingAddress}
          oid={retailerOrder?.data?.id}
          hasUncalculatedShipping={retailerOrder?.data?.hasUncalculatedShipping}
        />
      ),
      width: isTablet ? undefined : 723
    });
  };

  const subTotal = retailerOrder?.data?.lineItems?.reduce((acc, item) => acc + item.quantity * item?.listPrice, 0) ?? 0;
  const vat = 0;
  const shippingCost =
    retailerOrder?.data?.lineItems?.reduce(
      (acc, item) => acc + (isNumber(item.shippingCost) ? item.shippingCost : +item.shippingCost),
      0
    ) ?? 0;

  const onConfirmPayment = async () => {
    try {
      const res = await confirmPayment({ id });

      if ((res as any)?.error) {
        clientDefaultErrorHandler({ error: (res as any)?.error });
      }
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const handleTrackingCode = () => {
    showModal({
      width: 450,
      body: (
        <FulfillmentDialog
          orderState="Fulfilled"
          orderId={retailerOrder?.data?.id}
          shipmentDetail={retailerOrder?.data?.shipmentDetails?.[0]}
        />
      ),
      modalProps: {
        showCloseIcon: false
      }
    });
  };

  const onBack = () => {
    const hasHistory = typeof window !== "undefined" && window.history.length > 1;

    if (hasHistory) {
      router.back();
    } else {
      router.push(makePath(routes.order));
    }
  };

  useEffect(() => {
    if (!paymentStatus) return;

    const paymentModalValues = {
      failed: {
        icon: "/images/svgs/payFailed.svg",
        title: t("payFailed")
      },
      success: {
        icon: "/images/svgs/paySuccess.svg",
        title: t("paySuccess")
      }
    };

    showModal({
      icon: paymentModalValues[paymentStatus]?.icon,
      title: paymentModalValues[paymentStatus]?.title,
      actions: [
        {
          label: t("confirm"),
          onClick: () => {
            router.replace(pathname);
            hideModal();
          }
        }
      ],
      onClose: () => {
        router.replace(pathname);
      }
    });
  }, [paymentStatus]);

  if (isRetailerOrderLoading) {
    return (
      <Box id="sx-ordersummary-15641">
        <CircularProgress />
      </Box>
    );
  }

  if (isMobile) {
    return (
      <MobileOrderDetail
        handleOpenShippingAddress={handleOpenShippingAddress}
        handleTrackingCode={handleTrackingCode}
        shippingCost={shippingCost}
        subTotal={subTotal}
        retailerOrder={retailerOrder}
        vat={vat}
        isConfirmingPayment={isConfirmingPayment}
        onConfirmPayment={onConfirmPayment}
        isRetailerOrderFetching={isRetailerOrderFetching}
      />
    );
  }

  return (
    <>
      <div className="xmd:flex hidden items-center justify-between border-b border-b-gray-40 pb-7">
        <div onClick={onBack} className="items-center gap-2 flex cursor-pointer">
          <Icon icon="solar:arrow-right-outline" className=" size-6" />
          <span className="text-subtitle-bold text-v2-content-primary">
            {t("retailerOrder.orderSummary.orderDetails")}
          </span>
        </div>
        {/* <div className="flex items-center rounded-lg bg-gray-40 gap-2 p-2">
          <Icon
            icon="solar:alt-arrow-right-outline"
            className={twMerge("size-4", isDisabledNext ? "text-gray-600" : "text-gray-999")}
          />
          <Icon
            icon="solar:alt-arrow-left-outline"
            className={twMerge("size-4", isDisabledPrev ? "text-gray-600" : "text-gray-999")}
          />
        </div> */}
      </div>

      <div className="flex items-center justify-between mt-4 flex-wrap">
        <div className="flex items-center text-h4-bold text-v2-content-primary flex-wrap gap-4">
          <div className="flex items-center">
            <span className="whitespace-nowrap">{t("retailerOrder.orderSummary.orderNumber")} : </span>
            <span className="mr-1">{retailerOrder?.data?.orderNumber ?? ""}</span>#
          </div>
          <div>
            {!!retailerOrder?.data?.state && (
              <OrderStatus
                id={retailerOrder?.data?.state as IOrderStatusItems["id"]}
                title={t(`retailerOrder.orderStateItems.${retailerOrder?.data?.state?.toLowerCase()}`)}
              />
            )}
          </div>
        </div>

        <div className="flex items-center gap-1">
          {/* {retailerOrder?.data?.state === "Done" && (
            <div
              onClick={handleTrackingCode}
              className="flex items-center gap-1.5 px-5 py-2.5 text-v2-content-primary cursor-pointer"
            >
              <Icon icon="solar:scanner-outline" className="size-3.5 text-v2-content-primary" />
              <span className="text-body4-medium text-v2-content-primary">{t("order.trackingCode")}</span>
            </div>
          )} */}

          {/* <Divider orientation="vertical" variant="middle" flexItem className="bg-gray-50" /> */}

          {retailerOrder?.data?.state === "Done" && (
            <div
              onClick={handleTrackingCode}
              className="flex items-center gap-1.5 px-5 py-2.5 text-v2-content-primary cursor-pointer"
            >
              <Icon icon="solar:object-scan-outline" className="size-5 text-v2-content-primary" />
              <span className="text-body4-medium text-v2-content-primary">{t("order.trackingCode")}</span>
            </div>
          )}

          {retailerOrder?.data?.state === "Done" && (
            <Divider orientation="vertical" variant="middle" flexItem className="bg-gray-50 my-3" />
          )}

          {(retailerOrder?.data?.paymentState === "Paid" || retailerOrder?.data?.paymentState === "Captured") && (
            <DownloadInvoice
              orderId={id}
              orderNumber={retailerOrder?.data?.orderNumber}
              printCustomerTitle={t("retailerOrder.orderSummary.printSellInvoice")}
              printSellerTitle={t("retailerOrder.orderSummary.printBuyInvoice")}
            />
          )}
          <div className="flex items-center gap-2">
            <RetailerOrderPay
              id={retailerOrder?.data?.id}
              state={retailerOrder?.data?.state}
              paymentState={retailerOrder?.data?.paymentState}
              hasUncalculatedShipping={retailerOrder?.data?.hasUncalculatedShipping}
            />
            {retailerOrder?.data?.state === "Pending" && (
              <CustomButton onClick={onConfirmPayment}>
                {isConfirmingPayment ? <CircularProgress size={16} color="inherit" /> : t("confirmPayment")}
              </CustomButton>
            )}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2 mt-1">
        <Icon icon="solar:calendar-outline" className="size-4" />
        {!!retailerOrder?.data?.createdAt && (
          <div className="flex items-center gap-2 ">
            <span className="text-body3-medium text-v2-content-tertiary">
              {t("retailerOrder.orderSummary.orderAt")} :{" "}
            </span>
            <span className="text-body3-medium text-v2-content-tertiary">
              {!!retailerOrder?.data?.createdAt && renderDate(retailerOrder?.data?.createdAt, "HH:mm - YYYY/MM/DD")}
            </span>
          </div>
        )}
      </div>

      <div className="mt-4 flex gap-4 flex-wrap">
        <div className="flex-1">
          <div className="border border-v2-border-primary rounded-lg p-6">
            <div className="flex items-center gap-2 pb-6 border-b border-b-v2-border-secondary">
              <Icon icon="solar:cart-3-outline" className="size-6" />
              <span className="text-v2-content-primary text-body2-bold font-semibold">
                {t("retailerOrder.orderSummary.addedOrder")}
              </span>
            </div>

            {/*aa: needs to do it */}
            <div className="pt-3">
              {retailerOrder?.data?.lineItems?.map((item, index) => (
                <OrderDetailRow
                  key={item?.id}
                  item={item}
                  isLastItem={(retailerOrder?.data?.lineItems?.length || 0) - 1 === index}
                />
              ))}
            </div>
          </div>

          <div className="border border-v2-border-primary rounded-lg p-6 mt-2.5">
            <div className="flex items-center gap-2 ">
              <Icon icon="solar:card-outline" className="size-6" />
              <span className="text-v2-content-primary text-body2-bold font-semibold">
                {t("retailerOrder.orderSummary.payInfo.title")}
              </span>
              <div className="mr-2">
                {!!retailerOrder?.data?.paymentState && (
                  <OrderStatus
                    id={retailerOrder?.data?.paymentState}
                    title={t(`retailerOrder.paymentStateItems.${retailerOrder?.data?.paymentState?.toLowerCase()}`)}
                  />
                )}
              </div>
            </div>
            <p className="text-body4-regular text-v2-content-primary mt-2">
              {t("retailerOrder.orderSummary.payInfo.subtitle")}
            </p>

            <div className="mt-6">
              <div className="flex items-center justify-between">
                <div className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("retailerOrder.orderSummary.grandTotal")}
                </div>
                <div className="flex-1 text-body4-medium text-v2-content-tertiary">
                  {retailerOrder?.data?.lineItems?.reduce((sum, item) => sum + item?.quantity, 0)}
                </div>
                <div className="flex-1 text-body3-medium text-v2-content-primary text-center">
                  {renderPrice(subTotal)}
                </div>
              </div>

              <div className="flex items-center justify-between mt-6">
                <div className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("retailerOrder.orderSummary.discount")}
                </div>
                <div className="flex-1 text-body4-medium text-v2-content-tertiary">-</div>
                <div className="flex-1 text-body3-medium text-v2-content-primary text-center">-</div>
              </div>

              <div className="flex items-center justify-between mt-6">
                <div className="flex-1 text-body4-medium text-v2-content-primary">
                  {t("retailerOrder.orderSummary.shippingCost")}
                </div>
                <div className="flex-1 text-body4-medium text-v2-content-tertiary">
                  {/* {t("retailerOrder.orderSummary.freeIfBiggerThan2mil")} */}
                </div>
                <div className="flex-1 text-body3-medium text-v2-content-primary text-center">
                  {renderPrice(shippingCost)}
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between mt-6 bg-v2-surface-info rounded-lg px-3 py-2">
              <p className="flex-1 text-body3-medium font-bold text-v2-content-primary">
                {t("retailerOrder.orderSummary.finalTotalPrice")}
              </p>
              <p className="flex-1" />
              <p className="flex-1 text-center text-body3-medium font-bold text-v2-content-primary">
                {renderPrice(retailerOrder?.data?.totalPrice)}
              </p>
            </div>
          </div>

          {!!retailerOrder?.data?.shipmentDetails?.length && (
            <div className="border border-v2-border-primary rounded-lg p-6 mt-2.5">
              <div className="flex items-center gap-2.5">
                <Icon icon="hugeicons:truck-delivery" className="size-4" />
                <span className="text-v2-content-primary text-body2-medium">
                  {t("retailerOrder.orderSummary.addressInfoTitle")}
                </span>
              </div>
              <div>
                <span className="text-body4-regular text-v2-content-primary">
                  {t("retailerOrder.orderSummary.addressInfoSubTitle")}
                </span>
              </div>

              <div className="mt-2">
                {retailerOrder?.data?.shipmentDetails?.map(shipItem => (
                  <OrderDetailShipment key={shipItem?.carrierId} retailerOrder={retailerOrder} shipItem={shipItem} />
                ))}
              </div>
            </div>
          )}
        </div>
        <div className="lg:w-[284px] w-full ">
          <div className="border border-v2-border-primary rounded-lg p-6">
            <div>
              <span className="text-v2-content-primary text-body2-medium font-semibold">
                {t("retailerOrder.orderSummary.statuses")}
              </span>

              <div className="flex items-center justify-between pb-3 border-b border-b-v2-border-secondary mt-6">
                <span className="text-body4-regular text-v2-content-primary">
                  {t("retailerOrder.orderSummary.orderState")}
                </span>

                {!!retailerOrder?.data?.state && (
                  <OrderStatus
                    id={retailerOrder?.data?.state as IOrderStatusItems["id"]}
                    title={t(`retailerOrder.orderStateItems.${retailerOrder?.data?.state?.toLowerCase()}`)}
                  />
                )}
              </div>

              <div className="flex items-center justify-between py-3 border-b border-b-v2-border-secondary">
                <span className="text-body4-regular text-v2-content-primary">
                  {t("retailerOrder.orderSummary.paymentState")}
                </span>

                {!!retailerOrder?.data?.paymentState && (
                  <OrderStatus
                    id={retailerOrder?.data?.paymentState}
                    title={t(`retailerOrder.paymentStateItems.${retailerOrder?.data?.paymentState?.toLowerCase()}`)}
                  />
                )}
              </div>

              <div className="flex items-center justify-between pt-3 ">
                <span className="text-body4-regular text-v2-content-primary">
                  {t("retailerOrder.orderSummary.deliveryState")}
                </span>

                {!!retailerOrder?.data?.shippingState && (
                  <OrderStatus
                    id={retailerOrder?.data?.shippingState}
                    title={t(`retailerOrder.deliveryStateItems.${retailerOrder?.data?.shippingState?.toLowerCase()}`)}
                  />
                )}
              </div>
            </div>
          </div>

          <div className="border border-v2-border-primary rounded-lg p-6 mt-2.5">
            <div>
              <span className="text-v2-content-primary text-body2-medium font-semibold">
                {t("retailerOrder.orderSummary.customerInfo")}
              </span>
            </div>

            <div className="flex items-center justify-between mt-6 pb-3 border-b border-b-v2-border-secondary">
              <div className="flex items-center gap-1">
                <Icon icon="carbon:user-avatar" width={15} height={15} />
                <span className="text-body4-regular text-v2-content-primary">
                  {t("retailerOrder.orderSummary.customer")}
                </span>
              </div>

              <span className="text-body4-regular text-v2-content-primary">
                {" "}
                {retailerOrder?.data?.customer?.firstName} {retailerOrder?.data?.customer?.lastName}
              </span>
            </div>

            <div className="flex items-center justify-between  py-3 border-b border-b-v2-border-secondary">
              <div className="flex items-center gap-1">
                <Icon icon="mage:email" width={15} height={15} />
                <span className="text-body4-regular text-v2-content-primary">
                  {t("retailerOrder.orderSummary.email")}
                </span>
              </div>
              <span className="text-body4-regular text-v2-content-primary">
                {" "}
                {retailerOrder?.data?.customer?.email ?? "-"}{" "}
              </span>
            </div>

            <div className="flex items-center justify-between pt-3">
              <div className="flex items-center gap-1">
                <Icon icon="solar:outgoing-call-outline" width={15} height={15} />{" "}
                <span className="text-body4-regular text-v2-content-primary">
                  {t("retailerOrder.orderSummary.phone")}
                </span>
              </div>
              <span className="text-body4-regular text-v2-content-primary">
                {retailerOrder?.data?.customer?.phoneNumber ? retailerOrder?.data?.customer?.phoneNumber : "-"}{" "}
              </span>
            </div>
          </div>

          <div className="border border-v2-border-primary rounded-lg p-6 mt-2.5">
            <div className="flex items-center justify-between">
              <span className="text-v2-content-primary text-body2-medium font-semibold">
                {t("order.postAddressInfo")}
              </span>
              {retailerOrder?.data?.hasUncalculatedShipping && (
                <Icon
                  icon="solar:pen-2-outline"
                  className="size-4 text-gray-400 cursor-pointer"
                  onClick={handleOpenShippingAddress}
                />
              )}
            </div>

            <div className="flex items-center gap-1 pb-3 border-b border-b-v2-border-secondary mt-6">
              <Icon icon="carbon:user-avatar" width={15} height={15} />
              <span className="text-body4-regular text-v2-content-primary">
                {retailerOrder?.data?.customer?.firstName || ""} {retailerOrder?.data?.customer?.lastName || ""}
              </span>
            </div>

            {/* <div className="flex items-center ">
                <Icon icon="carbon:user-avatar" width={15} height={15} />
                <span>
                  {t("retailerOrder.orderSummary.cityState") + retailerOrder?.data?.shippingAddress?.state ||
                    getLocation(retailerOrder?.data?.shippingAddress?.locationId ?? "")?.parent?.name ||
                    "-"}
                </span>
              </div> */}

            <div className="flex items-center gap-1 py-3 ">
              <Icon icon="solar:map-arrow-square-outline" width={15} height={15} />
              <span className="text-body4-regular text-v2-content-primary">
                {t("retailerOrder.orderSummary.cityState")}{" "}
                {getLocation(retailerOrder?.data?.shippingAddress?.locationId ?? "")?.parent?.name || "-"}
              </span>
            </div>

            <div className="mb-2 mt-3">
              <span className="text-body4-regular text-v2-content-primary">
                {t("supplierOrder.orderSummary.city")}:{" "}
                {getLocation(retailerOrder?.data?.shippingAddress?.locationId ?? "")?.name || "-"}
              </span>
            </div>

            <div>
              <span className="text-body4-regular text-v2-content-primary">
                {retailerOrder?.data?.shippingAddress?.address1 || ""}
              </span>
            </div>
            {/* 
            <div className="flex items-center justify-between mt-2">
              <span className="text-body4-regular text-gray-600">{t("order.phoneNumber")}</span>
              <span className="text-body4-regular text-v2-content-primary">
                {retailerOrder?.data?.shippingAddress?.phoneNumber
                  ? retailerOrder?.data?.shippingAddress?.phoneNumber
                  : "-"}
              </span>
            </div> */}

            <div className="flex items-center justify-between mt-1">
              <span className="text-body4-regular text-gray-600">{t("supplier.profile.zip")}</span>
              <span className="text-body4-regular text-v2-content-primary">
                {retailerOrder?.data?.shippingAddress?.zip ? retailerOrder?.data?.shippingAddress?.zip : "-"}
              </span>
            </div>

            {retailerOrder?.data?.hasUncalculatedShipping && (
              <div className="p-4 mt-6 bg-v2-surface-warining-1 rounded-lg border border-v2-content-on-warning-2 flex flex-col">
                <div className="flex items-center gap-1">
                  <Icon icon="solar:info-circle-bold" className="text-v2-content-primary" />
                  <span className="text-v2-content-primary text-body4-medium">
                    {t("retailerOrder.orderSummary.UncalculatedShipping.title")}
                  </span>
                </div>

                <div className="mt-1">
                  <span className="text-caption-medium text-v2-content-secondary leading-3">
                    {t("retailerOrder.orderSummary.UncalculatedShipping.subtitle")}
                  </span>
                </div>

                <CustomButton
                  onClick={handleOpenShippingAddress}
                  className="text-v2-content-on-action-hover-1 !bg-v2-surface-warining-2 !border-transparent mt-5 !mr-auto "
                  endIcon={<Icon icon="oui:arrow-left" className=" size-5 text-v2-content-on-action-hover-1 " />}
                >
                  {t("retailerOrder.orderSummary.UncalculatedShipping.editAddress")}
                </CustomButton>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default OrderDetail;
