import { TOrderData } from "@/store/apps/order/types";

export const mockOrderData = {
  shipmentDetails: [
    {
      carrier: {
        id: "carr_123",
        name: "FedEx",
        logo: "https://example.com/fedex-logo.png",
        isActive: true,
        position: 1,
        trackingUrlPattern: "https://www.fedex.com/tracking/{trackingCode}",
        createdAt: "2024-01-01T10:00:00Z",
        updatedAt: "2024-01-01T10:00:00Z"
      },
      carrierId: "carr_123",
      createdAt: "2024-01-05T14:30:00Z",
      document: {
        id: "doc_123",
        bucket: "shipping-labels",
        key: "label-123456.pdf",
        url: "https://example.com/shipping-labels/label-123456.pdf",
        userId: "user_789",
        createdAt: "2024-01-05T14:30:00Z",
        user: {
          id: "user_789",
          username: "<PERSON>tor",
          email: "<EMAIL>"
        }
      },
      documentId: "doc_123",
      note: "Handle with care",
      orderId: "ord_456",
      supplierId: "sup_789",
      trackingCode: "FX123456789",
      trackingUrl: "https://www.fedex.com/tracking/FX123456789",
      updatedAt: "2024-01-05T14:30:00Z"
    }
  ],
  hasUncalculatedShipping: false,
  billingAddress: {
    address1: "123 Billing St",
    address2: "Suite 400",
    city: 1001,
    company: "Acme Corp",
    locationId: "loc_123",
    phoneNumber: "******-0123",
    state: 5,
    zip: "12345"
  },
  createdAt: "2024-01-05T12:00:00Z",
  orderAt: "2024-01-05T12:00:00Z",
  customer: {
    company: "Acme Corp",
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    phoneNumber: "******-0123",
    title: "Mr"
  },
  id: "ord_456",
  lineItems: [
    {
      options: {
        color: "Blue",
        size: "Large"
      },
      listPrice: 99.99,
      discount: 10.0,
      id: "li_123",
      orderId: "ord_456",
      quantity: 2,
      product: {
        id: "prod_123",
        name: "Premium Widget",
        description: "High-quality widget"
      },
      salePrice: 89.99,
      supplierId: "sup_789",
      shippingCarrier: "FedEx",
      shippingCost: 12.5,
      shippingTime: {
        min: 2,
        max: 5
      },
      sku: "WDG-BLU-L",
      state: "InProgress",
      title: "Premium Widget - Blue, Large",
      availableStates: ["Pending", "InProgress", "Fulfilled"],
      trackingCode: "FX123456789",
      updatedAt: "2024-01-06T09:00:00Z",
      variantId: "var_456"
    }
  ],
  note: "Please deliver during business hours",
  orderNumber: "ORD-2024-456",
  paymentState: "Captured",
  shippingAddress: {
    address1: "456 Shipping Ave",
    address2: "Floor 3",
    city: 1001,
    company: "Acme Corp",
    locationId: "loc_124",
    phoneNumber: "******-0124",
    state: 5,
    zip: "12345"
  },
  retailerId: "ret_789",
  shippingState: "ShippedPartially",
  sourceOrderNumber: "SO-123456",
  state: "InProgress",
  stateHistories: {
    comment: "Order processed and payment captured",
    createdAt: "2024-01-05T12:30:00Z",
    orderId: "ord_456",
    state: "InProgress",
    type: "Order",
    userId: "user_789"
  },
  storeId: "store_123",
  subtotalPrice: 179.98,
  totalPrice: 192.48,
  transactions: {
    orderId: "ord_456",
    transactionId: "tx_123",
    userId: "user_789"
  },
  updatedAt: "2024-01-06T09:00:00Z"
};

export const mockOrdersData = [mockOrderData, mockOrderData];
