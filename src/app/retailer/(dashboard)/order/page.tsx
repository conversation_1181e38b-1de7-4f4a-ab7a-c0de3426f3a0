"use client";
import RetailerOrderTable from "./components/RetailerOrderTable";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import React from "react";
import AclWrapper from "@/components/containers/AclWrapper";

const OrderPage: React.FC = () => {
  return (
    <AclWrapper for="RETAILER">
      {/* <MobileAppBar /> */}
      <RetailerOrderTable />
    </AclWrapper>
  );
};

export default WithBottomBar(OrderPage);
