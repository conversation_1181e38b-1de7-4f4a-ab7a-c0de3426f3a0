import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";

interface IOrderCustomerInfoProps {
  name?: string;
  phoneNumber?: string;
  email?: string;
}

function OrderCustomerInfo({ name, phoneNumber, email }: IOrderCustomerInfoProps) {
  const { t } = useTranslation();

  return (
    <div className="p-4">
      <div className="flex items-center gap-1.5 ">
        <Icon icon="solar:user-circle-bold" width={16} height={16} />
        <p className="text-gray-999 text-body4-medium">{name}</p>
      </div>

      <div className="flex items-center mt-3 gap-x-6 flex-wrap ">
        <div>
          <p className="text-gray-500 text-caption-regular whitespace-nowrap">{t("order.phone")}</p>
          <p className="text-gray-999 text-body3-medium font-bold leading-8 whitespace-nowrap">
            {phoneNumber ? phoneNumber : "-"}
          </p>
        </div>

        <div>
          <p className="text-gray-500 text-caption-regular">{t("order.email")}</p>
          <p className="text-gray-999 text-body3-medium font-bold leading-8">{email || "-"}</p>
        </div>
      </div>
    </div>
  );
}

export default OrderCustomerInfo;
