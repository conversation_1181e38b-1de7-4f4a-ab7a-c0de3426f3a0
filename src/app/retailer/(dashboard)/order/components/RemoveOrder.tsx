import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { useRemoveOrderStateMutation } from "@/store/apps/order";
import useModal from "@/utils/hooks/useModal";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { CircularProgress } from "@mui/material";
import { useTranslation } from "react-i18next";

interface IRemoveOrderProps {
  oid: string;
  vid: string;
}

function RemoveOrder({ oid, vid }: IRemoveOrderProps) {
  const { t } = useTranslation();
  const { hideModal } = useModal();

  const [removeOrder, { isLoading }] = useRemoveOrderStateMutation();

  const handleRemoveOrder = async () => {
    try {
      const res = await removeOrder({ oid, vid });

      if ("data" in res && res.data) {
        hideModal();
      }

      if ((res as any)?.error) {
        clientDefaultErrorHandler({ error: (res as any)?.error });
      }
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  return (
    <div className="mt-4">
      <div className="flex flex-col gap-2 items-center">
        <span className="text-gray-999 text-subtitle-bold">{t("order.remove.title")}</span>
        <span className="text-gray-600 text-body4-regular">{t("order.remove.subtitle")}</span>
      </div>
      <div className="flex items-center gap-2 mt-4">
        <CustomButton onClick={() => hideModal()} color="secondary" fullWidth>
          {t("order.remove.button.cancel")}
        </CustomButton>
        <CustomButton
          onClick={handleRemoveOrder}
          color="error"
          className="bg-error-500 text-[#fff] hover:bg-error-500"
          fullWidth
        >
          {isLoading ? <CircularProgress color="info" size={20} /> : t("order.remove.button.delete")}
        </CustomButton>
      </div>
    </div>
  );
}

export default RemoveOrder;
