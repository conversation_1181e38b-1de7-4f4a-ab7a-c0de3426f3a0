import React from "react";
import { twMerge } from "tailwind-merge";
import { Swiper, SwiperSlide } from "swiper/react";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import "swiper/swiper-bundle.css";
import OrderStateItem from "./orderStateItem";
import OrderStateFilter from "@/components/containers/Filters/orderState";
import { useFilters } from "./useFilters";
import { useTranslation } from "react-i18next";
import { handleSetFilter } from "@/utils/helpers";

interface IOrderStateFilterProps {
  totalCount: number;
}

const RetailerOrderStateFilter = ({ totalCount }: IOrderStateFilterProps) => {
  const { t } = useTranslation();
  const { setFilters, filters } = useFilters();

  const stateItems = [
    {
      id: "All",
      label: t("supplierOrder.orderStateItems.all")
    },
    {
      id: "Pending",
      label: t("supplierOrder.orderStateItems.pending")
    },
    {
      id: "InProgress",
      label: t("supplierOrder.orderStateItems.inprogress")
    },
    {
      id: "Done",
      label: t("supplierOrder.orderStateItems.done")
    },
    {
      id: "Refunded",
      label: t("supplierOrder.orderStateItems.refunded")
    },
    {
      id: "Canceled",
      label: t("supplierOrder.orderStateItems.canceled")
    }
  ];

  return (
    <OrderStateFilter
      onChangeState={val => {
        // setFilters({ state: val as any }, { history: "push" });
        handleSetFilter({ key: "state", value: val, setFilters });
      }}
      state={filters?.state as any}
      stateItems={stateItems}
      totalCount={totalCount}
    />
  );
};

export default RetailerOrderStateFilter;
