import { TOrdersData } from "@/store/apps/order/types";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

export type OrderState = "All" | "Pending" | "InProgress" | "Done" | "Refunded" | "Canceled";

interface OrderStore {
  Pending: TOrdersData | null;
  InProgress: TOrdersData | null;
  Done: TOrdersData | null;
  Refunded: TOrdersData | null;
  Canceled: TOrdersData | null;

  filterState: OrderState | null;
  setFilterState: (state: OrderState) => void;
  getFilterState: () => OrderState | null;

  setOrderData: (state: OrderState, data?: TOrdersData) => void;
  getOrderData: (state: OrderState | null) => TOrdersData | null;
}

export const useRetailerOrderStateData = create<OrderStore>()(
  devtools(
    (set, get) => ({
      Pending: null,
      InProgress: null,
      Done: null,
      Refunded: null,
      Canceled: null,

      filterState: null,

      setFilterState: (state: OrderState) => {
        set({ filterState: state }, false, `setFilterState/${state}`);
      },

      getFilterState: () => {
        return get().filterState;
      },

      setOrderData: (state: OrderState, data?: TOrdersData) => {
        set({ [state]: data }, false, `setOrderData/${state}`);
      },

      getOrderData: (state: OrderState | null) => {
        return (get() as any)?.[state || ""] || null;
      }
    }),
    {
      name: "order-store"
    }
  )
);
