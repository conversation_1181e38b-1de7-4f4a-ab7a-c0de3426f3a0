import { inferParserType, parseAsInteger, parseAsString, parseAsStringEnum, useQueryStates } from "nuqs";

const filtersParsers = {
  page: parseAsInteger.withDefault(1),
  pageSize: parseAsInteger.withDefault(10),
  id: parseAsString,
  store_id: parseAsString,
  state: parseAsStringEnum(["Pending", "InProgress", "Done", "Refunded", "Cancelled"]),
  order_number: parseAsInteger,
  shipping_location: parseAsString,
  first_name: parseAsString,
  last_name: parseAsString,
  email: parseAsString,
  phone_number: parseAsString,
  query: parseAsString,
  created_from: parseAsString,
  created_to: parseAsString,
  updated_from: parseAsString,
  updated_to: parseAsString,
  created_at: parseAsStringEnum(["asc", "desc"]),
  updated_at: parseAsStringEnum(["asc", "desc"]),
  source_order_number: parseAsString,
  payment_state: parseAsStringEnum([
    "Pending",
    "Authorized",
    "Captured",
    "Paid",
    "Refunded",
    "RefundedPartially",
    "Voided",
    "Failed"
  ]),
  shipping_state: parseAsStringEnum(["Pending", "Shipped", "ShippedPartially", "Cancelled"])
};

export const useFilters = () => {
  const [filters, setFilters] = useQueryStates(filtersParsers, { history: "push", shallow: false });
  const { page, pageSize, created_at, updated_at, ...restFilters } = filters || {};

  return {
    filters: restFilters,
    setFilters,
    pagination: { page, pageSize },
    sorts: { created_at, updated_at }
  };
};

export type TFilters = inferParserType<typeof filtersParsers>;
