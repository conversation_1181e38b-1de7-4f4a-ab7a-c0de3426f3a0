import React, { useEffect, useState } from "react";
import "swiper/swiper-bundle.css";
import { useGetOrderListQuery } from "@/store/apps/order";
import { generateBackendFilters } from "@/utils/services/transformers";
import { omitEmptyValues } from "@/utils/helpers";
import { CircularProgress } from "@mui/material";
import { OrderState, useRetailerOrderStateData } from "./useRetailerOrderStateData";
import { apiService } from "@/utils/services";
import { orderApiRoutes } from "@/constants/apiRoutes/apiServiceRoutes";
import { TOrdersData } from "@/store/apps/order/types";

interface IOrderStateFilterProps {
  state: string;
}

const OrderStateItem = ({ state }: IOrderStateFilterProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { setOrderData, getOrderData } = useRetailerOrderStateData();
  const finalFilters = generateBackendFilters(omitEmptyValues({ state }));

  const queryParts = [`page_size=${10}`, `page=${1}`, finalFilters ? `filters=${finalFilters}` : ""].filter(
    part => part !== ""
  );
  const queryString = queryParts.join("&");

  useEffect(() => {
    setIsLoading(true);
    const fetFunc = async () => {
      const res: TOrdersData | undefined = await apiService.get(`${orderApiRoutes.order}?${queryString}`);
      setOrderData(state as any, res);
      setIsLoading(false);
    };

    fetFunc();
  }, [state]);

  return <>{isLoading ? <CircularProgress size={8} /> : getOrderData(state as any)?.pagination?.total || 0}</>;
};

export default OrderStateItem;
