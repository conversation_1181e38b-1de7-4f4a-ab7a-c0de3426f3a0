import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import Image from "next/image";
import { useTranslation } from "react-i18next";

import "./RetailerOrder.css";

function OrdersEmptyList() {
  const { t } = useTranslation();
  return (
    <Stack alignItems="center">
      <Image
        className="order-list-empty-image"
        src="/images/svgs/order-empty.svg"
        width={220}
        height={195}
        alt="order-empty-list"
      />
      <Typography className="order-list-empty-title">{t("supplierOrder.empty.title")}</Typography>
      <Typography className="order-list-empty-subtitle">{t("supplierOrder.empty.subtitle")}</Typography>
    </Stack>
  );
}

export default OrdersEmptyList;
