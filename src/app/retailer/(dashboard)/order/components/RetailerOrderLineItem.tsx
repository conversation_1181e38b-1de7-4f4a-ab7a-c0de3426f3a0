import { TOrderData, TOrderLineItem } from "@/store/apps/order/types";
import { Avatar, Box, CircularProgress, IconButton, TableBody, TableCell, TableRow, Tooltip } from "@mui/material";
import OrderStatus, { IOrderStatusItems } from "../[id]/orderDetail/OrderStatus";
import { useConversationStart } from "@/utils/hooks/useConversationStart";
import { Icon } from "@iconify/react";
import useCurrency from "@/utils/hooks/useCurrency";
import { MouseEvent } from "react";
import { useTranslation } from "react-i18next";
import HoverPopover from "@/components/ui/popover/HoverPopover";

interface IRetailerOrderLineItemProps {
  item: TOrderLineItem;
  row: TOrderData;
  index: number;
  handleClick: (event: MouseEvent<HTMLTableRowElement, globalThis.MouseEvent>, id: string) => void;
  RenderRemoveIcon: ({ row, lineItem }: { row: TOrderData; lineItem: TOrderLineItem }) => React.JSX.Element | null;
}

function RetailerOrderLineItem({ item, row, index, handleClick, RenderRemoveIcon }: IRetailerOrderLineItemProps) {
  const { t } = useTranslation();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };

  const { onChatStart, isConversationLoading } = useConversationStart();

  const labelId = `enhanced-table-checkbox-${index}-${row.id}`;

  return (
    <TableBody>
      <TableRow
        className={
          row?.lineItems?.length && row?.lineItems?.length - 1 !== index
            ? "border-b border-solid border-b-gray-50 "
            : ""
        }
        onClick={event => handleClick(event, row.id)}
        tabIndex={-1}
        key={labelId}
      >
        <TableCell className="py-2 px-0">
          <Avatar src={item?.product?.cover?.url} alt={item?.product?.cover?.alt} className="w-12 h-12 rounded-md" />
        </TableCell>

        <TableCell width={150}>
          <HoverPopover
            content={
              <div className="flex flex-col gap-1 max-w-64">
                <div className="text-gray-999 font-medium text-sm">{item?.title}</div>
              </div>
            }
            popOverProps={{
              anchorOrigin: { vertical: "top", horizontal: "center" },
              transformOrigin: { vertical: "bottom", horizontal: "center" }
            }}
          >
            <p className="text-body4-medium text-gray-600 truncate w-[150px]">{item.title}</p>
          </HoverPopover>
        </TableCell>

        <TableCell>
          <span className="text-body4-medium text-gray-600">{item?.quantity}</span>
        </TableCell>

        <TableCell>
          <span className="text-body4-medium text-gray-600">{renderPrice(item?.listPrice)}</span>
        </TableCell>

        <TableCell>
          <Box display="flex" alignItems="center">
            {!!item?.state && (
              <OrderStatus
                id={item?.state as IOrderStatusItems["id"]}
                title={t(`supplierOrder.orderStateItems.${item?.state?.toLowerCase()}`)}
              />
            )}
          </Box>
        </TableCell>

        <TableCell>
          <span className="text-body4-medium text-gray-600">{item?.product?.supplier?.name || "-"}</span>
        </TableCell>

        <TableCell>
          <div className="flex items-center gap-2 text-gray-500">
            <IconButton
              disabled={isConversationLoading}
              className="cursor-pointer"
              onClick={() =>
                onChatStart({ content: t("chats.order", { name: row?.orderNumber }), partnerId: item?.supplierId })
              }
            >
              {isConversationLoading ? (
                <CircularProgress size={16} />
              ) : (
                <Icon icon="mage:message-dots" width={16} height={16} />
              )}
            </IconButton>
            <RenderRemoveIcon row={row} lineItem={item} />
          </div>
        </TableCell>
      </TableRow>
    </TableBody>
  );
}

export default RetailerOrderLineItem;
