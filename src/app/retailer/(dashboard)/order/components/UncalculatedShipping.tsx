import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";

export function UncalculatedShipping() {
  const { t } = useTranslation();

  return (
    <div className="flex items-center bg-warning-50 border border-solid border-warning-500 rounded-md gap-1 px-2 py-[3px] xmd:w-auto w-full ">
      <Icon icon="solar:info-circle-linear" width={16} height={16} />
      <span className="text-body4-medium text-gray-999 whitespace-nowrap">{t("order.uncalculatedShipping")}</span>
    </div>
  );
}
