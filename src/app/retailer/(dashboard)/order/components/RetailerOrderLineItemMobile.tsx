import { TOrderData, TOrderLineItem } from "@/store/apps/order/types";
import { Accordion, AccordionDetails, AccordionSummary, Avatar, CircularProgress, IconButton } from "@mui/material";
import OrderStatus, { IOrderStatusItems } from "../[id]/orderDetail/OrderStatus";
import { useConversationStart } from "@/utils/hooks/useConversationStart";
import { Icon } from "@iconify/react";
import useCurrency from "@/utils/hooks/useCurrency";
import { useTranslation } from "react-i18next";

interface IRetailerOrderLineItemProps {
  item: TOrderLineItem;
  row: TOrderData;
  index: number;
  RenderRemoveIcon: ({ row, lineItem }: { row: TOrderData; lineItem: TOrderLineItem }) => React.JSX.Element | null;
}

function RetailerOrderLineItem({ item, row, index, RenderRemoveIcon }: IRetailerOrderLineItemProps) {
  const { t } = useTranslation();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };

  const { onChatStart, isConversationLoading } = useConversationStart();

  return (
    <div key={item.id}>
      <Accordion key={item.id} className="rounded-none shadow-none">
        <div className="flex items-center gap-2">
          <Avatar
            src={item?.product?.cover?.url}
            alt={item?.product?.cover?.alt}
            className="w-[60px] h-[60px] rounded-md"
          />
          <div className="flex flex-col w-full">
            <span className="text-body3-medium text-gray-999">{item?.title}</span>

            <div className="flex items-center justify-between xmd:mt-0 mt-1">
              <div className="flex items-center gap-2">
                <span className="text-body4-regular whitespace-nowrap">{t("order.totalPrice")}: </span>
                <span className="text-gray-999 text-text-body4-medium whitespace-nowrap">
                  {renderPrice(row?.totalPrice)}
                </span>
              </div>
              <div className="flex items-center gap-4 h-4">
                <IconButton
                  className="cursor-pointer"
                  onClick={() =>
                    onChatStart({ content: t("chats.order", { name: row?.orderNumber }), partnerId: item?.supplierId })
                  }
                >
                  {isConversationLoading ? (
                    <CircularProgress size={16} />
                  ) : (
                    <Icon icon="tabler:message-2" width={16} height={16} className="text-gray-500" />
                  )}
                </IconButton>
                <RenderRemoveIcon lineItem={item} row={row} />
                <AccordionSummary
                  className="!min-h-[unset] p-0"
                  classes={{
                    content: "min-h-[unset] m-0",
                    expandIconWrapper: "w-4 h-4 block"
                    // expanded: "h-0"
                  }}
                  expandIcon={
                    <Icon icon="solar:alt-arrow-down-outline" width={16} height={16} className="text-gray-500 me-4" />
                  }
                  aria-controls={`panel${index}-content`}
                  id={`panel${index}-header`}
                  // ref={el => {
                  //   // if(accordionRefs.current.length === 0)
                  //   //   el?.click()
                  //   accordionRefs.current[index] = el;
                  // }}
                />
              </div>
            </div>
          </div>
        </div>
        <AccordionDetails className="p-0 mt-3">
          <div className="bg-gray-20 rounded-lg p-2">
            <div className="flex items-center justify-between pb-2 border-b border-solid border-gray-50">
              <span className="text-body4-medium text-gray-600">{t("order.supplier")}</span>
              <span className="text-body4-medium text-gray-600">{item?.product?.supplier?.name || "-"}</span>
            </div>

            <div className="flex items-center justify-between py-2 border-b border-solid border-gray-50">
              <span className="text-body4-medium text-gray-600">{t("order.status")}</span>
              <span className="text-body4-medium text-gray-600">
                {!!item?.state && (
                  <OrderStatus
                    id={item?.state as IOrderStatusItems["id"]}
                    title={t(`supplierOrder.orderStateItems.${item?.state?.toLowerCase()}`)}
                  />
                )}{" "}
              </span>
            </div>

            <div className="flex items-center justify-between py-2 ">
              <span className="text-body4-medium text-gray-600">{t("order.quantity")}</span>
              <span className="text-body4-medium text-gray-600">{item?.quantity}</span>
            </div>
          </div>
        </AccordionDetails>
      </Accordion>
    </div>
  );
}

export default RetailerOrderLineItem;
