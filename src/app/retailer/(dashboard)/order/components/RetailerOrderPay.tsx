import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { routes } from "@/constants/routes";
import { usePostOrderPaymentMutation } from "@/store/apps/order";
import useModal from "@/utils/hooks/useModal";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { CircularProgress } from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import PaymentRedirecting from "../[id]/orderDetail/PaymentRedirecting";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { TOrderData } from "@/store/apps/order/types";

interface IRetailerOrderPayProps {
  id?: string;
  state?: TOrderData["state"];
  paymentState?: TOrderData["paymentState"];
  hasUncalculatedShipping?: boolean;
}

function RetailerOrderPay({ id, hasUncalculatedShipping, paymentState, state }: IRetailerOrderPayProps) {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { showModal } = useModal();
  const [isRedirecting, setIsRedirecting] = useState(false);

  const [postOrderPayment, { isLoading: isPaying }] = usePostOrderPaymentMutation();

  const handlePay = async (id?: string) => {
    if (!id) return;

    setIsRedirecting(true);

    const callbackUrl = `${makePath(routes.order)}/${id}`;

    try {
      const res = await postOrderPayment({
        body: {
          callbackUrl,
          paymentMethod: "zarinpal"
        },
        id
      });

      if ((res as any)?.error) {
        clientDefaultErrorHandler({ error: (res as any)?.error });
      }

      if ("data" in res && res.data) {
        const url = res.data?.data?.url;
        showModal({
          closable: false,
          body: <PaymentRedirecting />,
          modalProps: {
            containerClassName: "p-0"
          }
        });
        window.location.assign(url);
      }
      setIsRedirecting(false);
    } catch (err: any) {
      setIsRedirecting(false);
      clientDefaultErrorHandler({ error: err });
    }
  };

  const isActivePay =
    state === "Pending" && (paymentState === "Pending" || paymentState === "Voided" || paymentState === "Failed");

  return (
    <CustomButton
      color="secondary"
      className="xmd:w-auto w-fit"
      disabled={isPaying || isRedirecting || hasUncalculatedShipping || !isActivePay}
      onClick={() => handlePay(id)}
    >
      {isPaying || isRedirecting ? (
        <div className="flex items-center gap-1.5">
          <CircularProgress size={16} color="inherit" />
          <span className="text-body3-medium">{t("order.payRedirect")}</span>
        </div>
      ) : (
        t("order.payOrder")
      )}
    </CustomButton>
  );
}

export default RetailerOrderPay;
