import i18next from "i18next";

export const shippingStateItems = [
  {
    id: "Pending",
    label: i18next.t("supplierOrder.orderStateItems.pending")
  },
  {
    id: "Shipped",
    label: i18next.t("supplierOrder.orderStateItems.inprogress")
  },
  {
    id: "ShippedPartially",
    label: i18next.t("supplierOrder.orderStateItems.done")
  },
  {
    id: "Canceled",
    label: i18next.t("supplierOrder.orderStateItems.canceled")
  }
];

export const paymentStateItems = [
  {
    id: "Pending",
    label: i18next.t("supplierOrder.paymentStateItems.pending")
  },
  {
    id: "Authorized",
    label: i18next.t("supplierOrder.paymentStateItems.authorized")
  },
  // {
  //   id: "Captured",
  //   label: i18next.t("supplierOrder.paymentStateItems.captured")
  // },
  // {
  //   id: "Paid",
  //   label: i18next.t("supplierOrder.paymentStateItems.paid")
  // },
  {
    id: "Refunded",
    label: i18next.t("supplierOrder.paymentStateItems.refunded")
  },
  {
    id: "RefundedPartially",
    label: i18next.t("supplierOrder.paymentStateItems.refundedpartially")
  },
  {
    id: "Voided",
    label: i18next.t("supplierOrder.paymentStateItems.voided")
  },
  {
    id: "Failed",
    label: i18next.t("supplierOrder.paymentStateItems.failed")
  }
];

export const stateItems = [
  {
    id: "Pending",
    label: i18next.t("supplierOrder.orderStateItems.pending")
  },
  {
    id: "InProgress",
    label: i18next.t("supplierOrder.orderStateItems.inprogress")
  },
  {
    id: "Done",
    label: i18next.t("supplierOrder.orderStateItems.done")
  },
  {
    id: "Refunded",
    label: i18next.t("supplierOrder.orderStateItems.refunded")
  },
  {
    id: "Canceled",
    label: i18next.t("supplierOrder.orderStateItems.canceled")
  }
];
