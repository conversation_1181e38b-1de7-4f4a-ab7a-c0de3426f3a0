import { TOrderData } from "@/store/apps/order/types";
import { Icon } from "@iconify/react";
import { IconButton, Menu } from "@mui/material";
import { useState } from "react";
import OrderCustomerInfo from "./OrderCustomerInfo";

interface ICustomerInfoPopoverProps {
  row: TOrderData;
}

function CustomerInfoPopover({ row }: ICustomerInfoPopoverProps) {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick2 = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Icon
        aria-label="show 11 new notifications"
        aria-haspopup="true"
        id={`customer-info-${row?.id}`}
        icon="solar:info-circle-linear"
        className="cursor-pointer"
        onClick={handleClick2}
        width={18}
        height={18}
      />
      <Menu
        id={`customer-info-${row?.id}`}
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
        className="rounded-lg"
        PaperProps={{
          style: {
            boxShadow: "4px 4px 15px 0px #00000026"
          }
        }}
        classes={{
          paper: " w-[280px] rounded-lg"
        }}
      >
        <OrderCustomerInfo
          email={row?.customer?.email}
          name={`${row?.customer?.firstName}${row?.customer?.lastName}`}
          phoneNumber={row?.customer?.phoneNumber}
        />
      </Menu>
    </>
  );
}

export default CustomerInfoPopover;
