import "./AccountInfo.css";

import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import { LANGUAGES, LanguageType } from "@/constants/localization";
import useLanguage from "@/utils/hooks/useLanguage";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import i18next from "i18next";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import Image from "next/image";

function AccountLanguage() {
  const { t } = useTranslation();
  const { hideModal, showModal } = useModal();
  const [currentLang, getPathLang, pathname] = useLanguage();
  const router = useRouter();

  const changeLang = (lang: LanguageType) => {
    const pathLocale = getPathLang();
    let toPath = pathname;
    if (lang.value === pathLocale) return;
    if (pathLocale) {
      toPath = toPath.replace("/" + pathLocale, "/" + lang.value);
    } else {
      toPath = "/" + lang.value + pathname;
    }
    i18next.changeLanguage(lang?.value);
    router.replace(toPath);
  };

  const handleOpenModal = () => {
    showModal({
      icon: "/images/svgs/language-modal.svg",
      body: (
        <div>
          <div className="flex flex-col">
            {LANGUAGES.map(item => (
              <CustomRadio
                color="secondary"
                key={item.value}
                disabled={item?.disabled}
                checked={item.value === currentLang?.value}
                onChange={() => changeLang(item)}
                value={item.value}
                label={item.flagname}
              />
            ))}
          </div>
          <CustomButton fullWidth onClick={() => hideModal()} className="mt-4">
            {t("confirm")}
          </CustomButton>
        </div>
      )
    });
  };

  return (
    <>
      <div className="flex items-center gap-4 flex-1">
        <div className="size-10 rounded-full flex items-center justify-center bg-gray-40">
          <Icon icon="solar:earth-outline" className="size-6 text-gray-999" />
        </div>
        {/* <span className="text-body3-medium text-gray-999">{t("language")}</span> */}
        <p className="text-body3-medium text-gray-999 w-14 whitespace-nowrap">{t("language")}</p>
        <p className="text-gray-600 text-body4-medium">{currentLang?.flagname}</p>
      </div>
      <CustomButton className="max-h-10" color="secondary" onClick={handleOpenModal}>
        {t("change")}
      </CustomButton>
    </>
  );
}
export default AccountLanguage;
