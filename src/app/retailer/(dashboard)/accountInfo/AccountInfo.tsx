import { Divider, useMediaQuery } from "@mui/material";
import StoreInfo from "../store/storeInfo";
import AccountOverallInfo from "./AccountOverallInfo";
import AccountAction from "./AccountAction";
import "./AccountInfo.css";
import { Theme } from "@mui/system";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { logOut } from "@/utils/helpers";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";

const AccountInfo = () => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const makePath = useRoleBasePath();

  const dispatch = useDispatch();

  if (isMobile) {
    return (
      <div className="flex-1">
        <CustomCardContent className="!m-0 !pt-0.5">
          <AccountOverallInfo />
          <StoreInfo />
        </CustomCardContent>
        <CustomCardContent className="mt-4 !px-4 !py-1">
          <AccountAction />
        </CustomCardContent>

        <CustomCardContent className="mt-4">
          <Link href={makePath(routes.chat)} className="flex items-center justify-between pb-4 py-1 ">
            <div className="flex items-center gap-2">
              <Icon icon="solar:chat-round-line-outline" width={16} height={16} className="text-gray-999" />
              <span>{t("settings.support")}</span>
            </div>
            <Icon icon="solar:alt-arrow-left-line-duotone" width={16} height={16} className="text-gray-200" />
            {/* <ArrowBackIosIcon fontSize="small" className="msgs-menu-item-arrow-icon" /> */}
          </Link>

          <Divider className="border-gray-40 mb-2" />

          <div className="flex items-center justify-between py-2 cursor-pointer" onClick={() => logOut()}>
            <div className="flex items-center gap-2">
              <Icon icon="solar:logout-outline" width={16} height={16} color="#EB4937" />
              <span>{t("profileMenu.logout")}</span>
            </div>
          </div>
        </CustomCardContent>
      </div>
    );
  }

  return (
    <>
      <div className="flex-1">
        <AccountOverallInfo />
        <AccountAction />
      </div>

      <Divider orientation="vertical" variant="middle" flexItem />

      <div className="min-w-[360px]">
        <StoreInfo />
      </div>
    </>
  );
};

export default AccountInfo;
