import { Box, CircularProgress, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { routes } from "@/constants/routes";
import { Icon } from "@iconify/react";
import Link from "next/link";

import { accountTypeValue } from "./utils";
import "./AccountInfo.css";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { useGetRetailerProfileQuery } from "@/store/apps/retailer";

const AccountOverallInfo = () => {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { data: profileData, isLoading, isError } = useGetRetailerProfileQuery();

  return (
    <>
      {isError ? (
        <Box className="account-info-overallInfo-loading-wrapper">
          <Typography>{t("errors.somethingWentWrong")}</Typography>
        </Box>
      ) : isLoading ? (
        <Box className="account-info-overallInfo-loading-wrapper">
          <CircularProgress />
        </Box>
      ) : (
        <>
          <div className="flex items-start justify-between mt-4">
            <div>
              <span className="text-gray-999 text-subtitle-bold">{profileData?.data?.name}</span>
              <div>
                <span className="text-gray-500 text-body4-medium">
                  {t("account")}{" "}
                  {profileData?.data?.identity.isLegalPerson
                    ? t("supplier.profile.registerAs.legal")
                    : t("supplier.profile.registerAs.personal")}
                </span>
              </div>
            </div>
            <Link
              href={`${makePath(routes.profile)}?step=0`}
              className="w-8 h-8 rounded-full bg-cyan-50 flex items-center justify-center text-cyan-500"
            >
              <Icon icon="mage:edit-pen" className="text-cyan-500" width={18} height={18} />
            </Link>
          </div>

          <div className="my-6">
            <div className="flex ">
              <div className="border flex-1 border-solid border-transparent border-e-gray-50 border-b-gray-50">
                <div className="pb-3">
                  <span className="text-gray-500 text-caption-regular">{t("supplier.profile.phone")}</span>
                  <div>
                    <span className="text-body3-medium text-gray-999">
                      {profileData?.data?.contactNumber ? profileData?.data?.contactNumber : "-"}
                    </span>
                  </div>
                </div>
              </div>
              <div className="border flex-1 border-solid border-transparent  border-b-gray-50">
                <div className="ps-6 pb-3">
                  <span className="text-gray-500 text-caption-regular">{t("supplier.profile.email")}</span>
                  <div>
                    <span className="text-body3-medium text-gray-999">{profileData?.data?.contactEmail}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex ">
              <div className="border flex-1 border-solid border-transparent border-e-gray-50 border-b-transparent">
                <div className="pb-3 pt-6">
                  <span className="text-gray-500 text-caption-regular">{t("supplier.profile.nationalCode")}</span>
                  <div>
                    <span className="text-body3-medium text-gray-999">
                      {profileData?.data?.identity?.nationalCode ? profileData?.data?.identity?.nationalCode : "-"}
                    </span>
                  </div>
                </div>
              </div>
              <div className="border flex-1 border-solid border-transparent  border-b-transparent">
                {!!profileData?.data?.status && (
                  <div className="ps-6 pt-6 pb-3">
                    <span className="text-gray-500 text-caption-regular">{t("supplier.profile.accountstatus")}</span>
                    <div>
                      <span className="text-body3-medium text-gray-999">
                        {accountTypeValue({ t })[profileData?.data?.status]?.title}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default AccountOverallInfo;
