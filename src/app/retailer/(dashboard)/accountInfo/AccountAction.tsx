import "./AccountInfo.css";

import { useTranslation } from "react-i18next";
import { routes } from "@/constants/routes";
import { Icon } from "@iconify/react";
import AccountLanguage from "./AccountLanguage";
import AccountCurrency from "./AccountCurrency";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import useModal from "@/utils/hooks/useModal";
import AuthChangePassword from "@/app/auth/authForms/AuthChangePassword";
import { USER_TYPES } from "@/constants/userTypes";
import PasswordChangeSuccess from "@/app/auth/authForms/PasswordChangeSuccess";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { shallowEqual } from "react-redux";
import { useSelector } from "@/store/hooks";
import { TMeResponse } from "@/store/apps/auth/types";
import Image from "next/image";

const AccountAction = () => {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { showModal } = useModal();

  const me = useSelector(state => state?.Auth?.queries[`getMe(undefined)`]?.data, shallowEqual) as TMeResponse;
  const username = me?.data?.phoneNumber || me?.data?.email;

  const handleSuccess = () => {
    showModal({
      icon: "/images/svgs/password-successfull.svg",
      body: <PasswordChangeSuccess isLogin={false} redirectPath={makePath(routes.home)} />
    });
  };

  const handleOpenModal = () => {
    showModal({
      body: (
        <AuthChangePassword
          username={username}
          onSuccess={handleSuccess}
          userType={USER_TYPES.SUPPLIER}
          className="supplier-profile-change-password"
          submitText={t("supplier.profile.saveChanges")}
        />
      ),
      icon: "/images/svgs/change-password.svg"
    });
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="py-4 border-b border-solid border-transparent border-b-gray-40 rounded-xl flex items-center justify-between">
        <div className="flex items-center gap-4 flex-1">
          <div className="size-10 rounded-full flex items-center justify-center bg-gray-40">
            <Icon icon="solar:password-minimalistic-input-outline" className="size-6 text-gray-999" />
          </div>
          <span className="text-body3-medium text-gray-999">{t("accountPassword")}</span>
        </div>

        <CustomButton color="secondary" className="max-h-10" onClick={handleOpenModal}>
          {t("change")}
        </CustomButton>
      </div>

      <div className="py-4 border-b border-solid border-transparent border-b-gray-40 rounded-xl flex items-center justify-between">
        <AccountCurrency />
      </div>

      <div className="py-4 rounded-xl flex items-center justify-between">
        <AccountLanguage />
      </div>
    </div>
  );
};

export default AccountAction;
