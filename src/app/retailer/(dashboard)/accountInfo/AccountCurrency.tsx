import "./AccountInfo.css";

import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import { usePutRetailerProfileMutation } from "@/store/apps/retailer";
import { TRetailerProfileData } from "@/store/apps/retailer/types";
import useCurrency from "@/utils/hooks/useCurrency";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { Box, CircularProgress } from "@mui/material";
import { useTranslation } from "react-i18next";
import { shallowEqual, useSelector } from "react-redux";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { isValidUUID } from "@/utils/helpers";
import Image from "next/image";

export interface ICurrency {
  createdAt: string;
  isActive: boolean;
  symbolPosition: "After" | "Before";
  updatedAt: string;
  id: string;
  iso: string;
  name: string;
  precision: 0;
  symbol: string;
}

function AccountCurrency() {
  const { t } = useTranslation();
  const { hideModal, showModal } = useModal();
  const [selected, selectCurrency, currencyOptions] = useCurrency();

  const [putRetailerProfile] = usePutRetailerProfileMutation();

  const retailerData = useSelector(
    (state: any) => state?.Retailer?.queries[`getRetailerProfile(undefined)`]?.data,
    shallowEqual
  ) as { data: TRetailerProfileData };

  const statusCurrency = useSelector(
    (state: any) => state?.Meta?.queries[`getMetaCurrencies(undefined)`],
    shallowEqual
  ) as { status: string };

  const loadingCurrency = statusCurrency?.status === "pending";

  const profileData = {
    // ...retailerData,
    ...retailerData?.data,
    address: {
      address1: retailerData?.data?.address?.address1,
      locationId:
        retailerData?.data?.address?.locationId && isValidUUID(retailerData?.data?.address?.locationId)
          ? retailerData?.data?.address?.locationId
          : "",
      zip: retailerData?.data?.address?.zip
    },
    identity: retailerData?.data?.identity
  };

  const handleChangeCurrency = async (checked: boolean, currency: ICurrency) => {
    if (checked) selectCurrency(currency?.id as any);

    const body = { ...profileData, currencyId: currency?.id } as unknown as TRetailerProfileData;

    try {
      const retailerProfileApi = putRetailerProfile({ body });

      await retailerProfileApi.then(res => {
        const error = (res as any)?.error;

        if (error) {
          clientDefaultErrorHandler({ error });
        }

        if ("data" in res && res?.data) {
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const handleOpenModal = () => {
    showModal({
      icon: "/images/svgs/currency-modal.svg",
      body: (
        <div>
          {loadingCurrency ? (
            <Box mt={2}>
              <CircularProgress size={20} />
            </Box>
          ) : (
            <>
              <div className="flex flex-col">
                {currencyOptions?.map((currency: ICurrency) => (
                  <div
                    className="border border-solid border-transparent border-b-gray-50 last-of-type:border-none"
                    key={currency?.id}
                  >
                    <CustomRadio
                      color="secondary"
                      key={currency?.id}
                      label={currency?.name}
                      checked={selected?.id === currency?.id}
                      onChange={(e, checked) => {
                        handleChangeCurrency(checked, currency);
                      }}
                    />
                  </div>
                ))}
              </div>
              <CustomButton fullWidth onClick={() => hideModal()} className="mt-4">
                {t("confirm")}
              </CustomButton>
            </>
          )}
        </div>
      )
    });
  };

  return (
    <>
      <div className="flex items-center gap-4 flex-1">
        <div className="size-10 rounded-full flex items-center justify-center bg-gray-40">
          <Icon icon="solar:dollar-outline" className="size-6 text-gray-999" />
        </div>
        <p className="text-body3-medium text-gray-999 w-14 whitespace-nowrap">{t("currency")}</p>
        <p className="text-gray-600 text-body4-medium">{selected?.name}</p>
      </div>
      <CustomButton color="secondary" className="max-h-10" onClick={handleOpenModal}>
        {t("change")}
      </CustomButton>
    </>
  );
}

export default AccountCurrency;
