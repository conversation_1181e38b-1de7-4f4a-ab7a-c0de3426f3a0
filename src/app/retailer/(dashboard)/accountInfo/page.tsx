"use client";

import { useTranslation } from "react-i18next";
import AccountInfo from "./AccountInfo";
import AclWrapper from "@/components/containers/AclWrapper";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import React from "react";
import SectionInfo from "@/components/ui/SectionInfo/SectionInfo";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";

const AccountInfoPage: React.FC = () => {
  const { t } = useTranslation();
  return (
    <AclWrapper for="RETAILER">
      <MobileAppBar title={t("accountInfo")} />
      <CustomCardContent className="xmd:!bg-cards !bg-transparent">
        <div className="xmd:block hidden">
          <SectionInfo title={t("accountInfo")} />
        </div>
        <div className="flex gap-4 h-full flex-wrap  ">
          <AccountInfo />
        </div>
      </CustomCardContent>
    </AclWrapper>
  );
};

export default WithBottomBar(AccountInfoPage);
