import { TFunction } from "i18next";

export const accountTypeValue = ({ t }: { t: TFunction<"translation", undefined> }) => ({
  InReview: { title: t("supplier.profile.accountStatusItems.inReview"), icon: "abler:clock-filled", color: "#2970FF" },
  Active: {
    title: t("supplier.profile.accountStatusItems.active"),
    icon: "solar:verified-check-bold",
    color: "#18B466"
  },
  Rejected: {
    title: t("supplier.profile.accountStatusItems.rejected"),
    icon: "solar:user-block-bold",
    color: "#6C737F"
  },
  Disabled: {
    title: t("supplier.profile.accountStatusItems.disabled"),
    icon: "solar:lock-keyhole-minimalistic-bold",
    color: "#6C737F"
  }
});
