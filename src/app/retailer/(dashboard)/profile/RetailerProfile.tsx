/* eslint-disable @typescript-eslint/no-unused-vars */
import "./RetailerProfile.css";

import ProfileStepper from "@/components/containers/ProfileStepper/ProfileTopStepper";
import { routes } from "@/constants/routes";
import { USER_TYPES } from "@/constants/userTypes";
import {
  usePostRetailerDocumentsMutation,
  usePostRetailerProfileMutation,
  usePutRetailerProfileMutation
} from "@/store/apps/retailer";
import { TRetailerDocumentsPostBodyData, TRetailerProfileData } from "@/store/apps/retailer/types";
import {
  isValidUUID,
  SetFieldErrorFunction,
  snakeToCamelCaseHookFormWrapper,
  SnakeToCamelFieldErrorWrapper
} from "@/utils/helpers";
import useCurrency from "@/utils/hooks/useCurrency";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Box, CircularProgress, Theme, useMediaQuery } from "@mui/material";
import { isEmpty } from "lodash";
import checkValues from "lodash/values";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import RetailerAddress, { TRetailerAddress } from "./RetailerAddress/RetailerAddress";
import RetailerInfo from "./RetailerInfo/RetailerInfo";
import { IRetailerDataRefProps } from "./RetailerInfo/types";
import { TRetailerProfile } from "./types";
import { handleGoToHasErrorTab } from "./utils";
import { clientDefaultErrorHandler, SetHookFormError } from "@/utils/services/utils";
import useModal from "@/utils/hooks/useModal";
import RetailerSignContractModal from "@/components/containers/ProfilesSignContractModal/RetailerSignContractModal";
import { ApiError } from "@/utils/services";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import ProfileContentContainer from "@/components/containers/ProfileStepper/ProfileContentContainer";

function RetailerProfile() {
  const { t } = useTranslation();
  const router = useRouter();
  const makePath = useRoleBasePath();
  const searchParams = useSearchParams();
  const profileInfoRef = useRef<IRetailerDataRefProps>(null);
  const profileAddressRef = useRef<IRetailerDataRefProps>(null);
  const [currency, selectCurrency, curencyOptions, currencyId] = useCurrency();
  const [retailerProfile, setRetailerProfile] = useState<TRetailerProfile>();
  const activeStepParams = searchParams?.get("step");
  const inComplete = searchParams?.get("status") === "incomplete";
  const { showModal, hideModal } = useModal();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const [activeStep, setActiveStep] = useState<undefined | number>(
    isMobile ? undefined : activeStepParams ? +activeStepParams : 0
  );

  // const { data: retailerData, isLoading: isGetRetailerLoading } = useGetRetailerProfileQuery();
  const retailerSelector = useSelector((state: any) => state?.Retailer?.queries["getRetailerProfile(undefined)"] || {});
  const retailerData = retailerSelector?.data as { data: TRetailerProfileData };
  const isGetRetailerLoading = retailerSelector?.status === "pending";

  const hasRetailer = !!checkValues(retailerData?.data).length;
  const termsAndConditionsApproved = !!retailerData?.data?.termsAndConditionsApproved || false;

  const [postRetailerInfo, { isLoading: isPostRetailerInfoLoading }] = usePostRetailerProfileMutation();
  const [putRetailerInfo, { isLoading: isPutRetailerInfoLoading }] = usePutRetailerProfileMutation();

  const handleShowSuccessModal = () => {
    showModal({
      icon: "/images/svgs/paySuccess.svg",
      body: (
        <p className="mt-4 xmd:mb-0 mb-4 text-center w-full xmd:text-subtitle-bold text-subtitle-bold text-gray-999">
          {hasRetailer ? t("updateProfile") : t("successfullProfile")}
        </p>
      ),
      actions: [
        {
          label: t("confirm"),
          onClick: () => {
            router.replace(makePath(routes.home));
            hideModal();
          }
        }
      ],
      modalProps: {
        containerClassName: "pt-7"
      }
    });
  };

  const handleShowSignContractModal = () => {
    showModal({
      width: 801,
      modalProps: { showCloseIcon: false },
      body: (
        <RetailerSignContractModal
          close={hideModal}
          onSuccess={() => {
            hideModal();
            handleShowSuccessModal();
          }}
        />
      )
    });
  };

  const onProfileSaved = () => {
    // if (!termsAndConditionsApproved) {
    //   handleShowSignContractModal();
    // } else {
    //   handleShowSuccessModal();
    // }
    handleShowSuccessModal();
  };

  /* ------------------------------- upload doc ------------------------------- */
  const documents: { tag: string; documents: TRetailerDocumentsPostBodyData[] }[] = !isEmpty(retailerProfile?.documents)
    ? Object.entries(retailerProfile?.documents!)?.map(([k, v]) => ({
        tag: k,
        documents: v
          ?.map(item => ({
            tag: k,
            mediaId: item?.id || ""
          }))
          ?.filter(a => a.mediaId)
      }))
    : [];

  const [postRetailerDoc, { isLoading: isDocLoading }] = usePostRetailerDocumentsMutation();

  const onPostRetailerDoc = async (documents: { tag: string; documents: TRetailerDocumentsPostBodyData[] }[]) => {
    const isLegal = retailerProfile?.identity?.isLegalPerson;
    const documentValues: TRetailerDocumentsPostBodyData[] = documents
      ?.filter(item => (isLegal ? item?.tag?.startsWith("legal") : !item?.tag?.startsWith("legal")))
      .flatMap(item =>
        item.documents.map(doc => ({
          tag: item.tag,
          mediaId: doc.mediaId
        }))
      );

    const body = documentValues;

    try {
      await postRetailerDoc({
        body
      }).then(res => {
        const error = (res as any)?.error?.data as ApiError;

        if ("data" in res && res?.data) {
          onProfileSaved();
        } else if ((res as any)?.error) {
          clientDefaultErrorHandler({ error: (res as any)?.error });
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const isProfileLoading =
    isPostRetailerInfoLoading || isPutRetailerInfoLoading || isGetRetailerLoading || isDocLoading;

  useEffect(() => {
    if (!inComplete) return;

    showModal({
      icon: "/images/svgs/danger.svg",
      // title: t("errorTitle"),
      subTitle: t("retailerProductWarning"),
      actions: [
        {
          label: t("confirm"),
          onClick: () => {
            router.replace(`${makePath(routes.profile)}?step=0`);
            hideModal();
          }
        }
      ],
      onClose: () => router.replace(`${makePath(routes.profile)}?step=0`)
    });
  }, [inComplete]);

  useEffect(() => {
    if (activeStepParams) setActiveStep(+activeStepParams);
  }, [activeStepParams]);

  useEffect(() => {
    const supplierAddress: TRetailerAddress = {
      address: {
        address1: retailerData?.data?.address?.address1,
        locationId:
          retailerData?.data?.address?.locationId && isValidUUID(retailerData?.data?.address?.locationId)
            ? retailerData?.data?.address?.locationId
            : "",
        zip: retailerData?.data?.address?.zip
      }
    };

    if (!!retailerData?.data) {
      const { identity, ...restData } = retailerData?.data || {};

      setRetailerProfile({
        identity: retailerData?.data?.identity,
        ...restData,
        ...supplierAddress
      });
    }
  }, [retailerData]);

  const onSubmitProfile = async (data: TRetailerProfile, setFieldError?: SetHookFormError) => {
    const { documents: dataDocuments, ...restData } = data;
    const body = { ...restData, currencyId } as TRetailerProfileData;

    try {
      const supplierProfileApi = hasRetailer ? putRetailerInfo({ body }) : postRetailerInfo({ body });

      await supplierProfileApi.then(res => {
        const error = (res as any)?.error?.data;

        if (error) {
          handleGoToHasErrorTab(error, setActiveStep);

          setTimeout(() => {
            clientDefaultErrorHandler({
              error: (res as any)?.error,
              bodyError: error,
              setHookFormFieldError: snakeToCamelCaseHookFormWrapper(profileInfoRef.current?.handleError)
            });
            clientDefaultErrorHandler({
              error: (res as any)?.error,
              bodyError: error,
              setHookFormFieldError: snakeToCamelCaseHookFormWrapper(profileAddressRef.current?.handleError)
            });
            clientDefaultErrorHandler({
              error: (res as any)?.error,
              bodyError: error,
              setHookFormFieldError: snakeToCamelCaseHookFormWrapper(setFieldError)
            });
          }, 0);
        }
        if ("data" in res && res?.data) {
          // toast.success(t("retailer.profile.saveChangesSuccessfully"));

          if (hasRetailer) {
            onProfileSaved();
          }
          if (!hasRetailer) {
            onPostRetailerDoc(documents);
          }
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const RenderContent = useCallback(() => {
    switch (activeStep) {
      case 0: {
        return (
          <>
            <MobileAppBar title={t("accountInfo")} hasBack onBack={() => setActiveStep(undefined)} />
            <ProfileContentContainer>
              {isGetRetailerLoading ? (
                <Box id="sx-retailerprofile-16768">
                  <CircularProgress />
                </Box>
              ) : (
                <RetailerInfo
                  isEdit={hasRetailer}
                  ref={profileInfoRef}
                  activeStep={activeStep}
                  retailerData={retailerProfile}
                  onChangeActiveStep={setActiveStep}
                  onSubmitData={data => setRetailerProfile(prev => ({ ...prev, ...data }))}
                />
              )}
            </ProfileContentContainer>
          </>
        );
      }
      case 1: {
        return (
          <>
            <MobileAppBar title={t("addressInfo")} hasBack onBack={() => setActiveStep(0)} />
            <ProfileContentContainer>
              <RetailerAddress
                isLoading={isProfileLoading}
                ref={profileAddressRef}
                activeStep={activeStep}
                onChangeActiveStep={setActiveStep}
                retailerData={retailerProfile}
                onPrev={data => setRetailerProfile(prev => ({ ...prev, ...data }))}
                onSubmitData={(data, setFieldError) => {
                  setRetailerProfile(prev => ({ ...prev, ...data }));
                  onSubmitProfile({ ...retailerProfile, ...data }, setFieldError);
                }}
              />
            </ProfileContentContainer>
          </>
        );
      }
      default:
        return null;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeStep, hasRetailer, isGetRetailerLoading, isProfileLoading, retailerProfile]);

  return (
    <ProfileStepper
      hasData={hasRetailer}
      defaultActiveStep={activeStep}
      userType={USER_TYPES.RETAILER}
      RenderContent={RenderContent}
      onChangeStep={setActiveStep}
      title={t("accountInfo")}
      isLegal={retailerData?.data?.identity?.isLegalPerson}
      name={retailerData?.data?.name}
      contactNumber={retailerData?.data?.contactNumber}
      profileStatus={retailerData?.data?.status}
      // stepperProps={{ sx: { justifyContent: "start" } }}
    />
  );
}

export default RetailerProfile;
