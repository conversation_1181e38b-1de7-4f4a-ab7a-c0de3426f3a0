import { TRetailerProfileData } from "@/store/apps/retailer/types";
import { TRetailerAddress } from "./RetailerAddress/RetailerAddress";
import { TDocuments } from "./RetailerInfo/types";

export type TRetailerBaseFormProps = {
  activeStep: number;
  onChangeActiveStep: (step?: number) => void;
};

export type TRetailerInfo = {
  contactEmail?: string;
  contactNumber?: string;
  name?: string;
  identity?: TRetailerProfileData["identity"];
  bankAccount?: TRetailerProfileData["bankAccount"];
  markup?: number;
  markupType?: string;
};
export type TRetailerProfile = {
  id?: string;
  documents?: TDocuments;
} & TRetailerInfo &
  TRetailerAddress;

export type TRetailerInfoFormValues = TRetailerInfo["identity"] &
  Omit<TRetailerInfo, "identity"> & { documents: TDocuments } & {
    legalName?: string;
    legalContactEmail?: string;
    legalContactNumber?: string;
    legalBankAccount?: TRetailerProfileData["bankAccount"];
  };
