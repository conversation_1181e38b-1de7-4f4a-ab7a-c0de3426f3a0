import { TRetailerBaseFormProps, TRetailerInfo, TRetailerProfile } from "../types";
import { SetHookFormError } from "@/utils/services/utils";

export type TRetailerInfoProps = {
  retailerData?: TRetailerProfile;
  onSubmitData: (data: TRetailerInfo & { documents?: TDocuments }) => void;
  isEdit?: boolean;
} & TRetailerBaseFormProps;

export interface IRetailerDataRefProps {
  handleError?: SetHookFormError;
}

export type TDocuments = {
  [key: string]: { url?: string; id: string }[];
};
