import i18next from "i18next";

type Tooltip = {
  title: string;
  description: string;
};

export const legalTooltip: Record<string, Tooltip> = {
  "legal-national-front": {
    title: i18next.t("retailer.profile.docUploadTooltip.legalNationalFront.title"),
    description: i18next.t("retailer.profile.docUploadTooltip.legalNationalFront.description")
  },
  "legal-national-back": {
    title: i18next.t("retailer.profile.docUploadTooltip.legalNationalBack.title"),
    description: i18next.t("retailer.profile.docUploadTooltip.legalNationalBack.description")
  },
  "legal-newspaper": {
    title: i18next.t("retailer.profile.docUploadTooltip.legalNewspaper.title"),
    description: i18next.t("retailer.profile.docUploadTooltip.legalNewspaper.description")
  },
  "legal-certificate-of-added-value": {
    title: i18next.t("retailer.profile.docUploadTooltip.legalCertificateOfAddedValue.title"),
    description: i18next.t("retailer.profile.docUploadTooltip.legalCertificateOfAddedValue.description")
  }
};

export const nationalTooltip: Record<string, Tooltip> = {
  "national-front": {
    title: i18next.t("retailer.profile.docUploadTooltip.nationalFront.title"),
    description: i18next.t("retailer.profile.docUploadTooltip.nationalFront.description")
  },
  "national-back": {
    title: i18next.t("retailer.profile.docUploadTooltip.nationalBack.title"),
    description: i18next.t("retailer.profile.docUploadTooltip.nationalBack.description")
  }
};
