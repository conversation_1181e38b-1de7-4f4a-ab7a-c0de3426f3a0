import "./RetailerInfo.css";

import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { DatePicker } from "@/components/ui/CustomDatePicker";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import SectionInfo from "@/components/ui/SectionInfo/SectionInfo";
import UserTypeSwitch from "@/components/ui/UserTypeSwitch/UserTypeSwitch";
import { IS_IRAN_SERVED } from "@/constants";
import { eighteenYearsAgo } from "@/utils/helpers/dateHelpers";
import { retailerInfoValidationSchema } from "@/utils/validations/profile/retailer";
import { Icon } from "@iconify/react";
import { Box, Grid } from "@mui/material";
import { useFormik } from "formik";
import React, { ForwardedRef, useEffect, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { TRetailerInfoFormValues } from "../types";
import { IRetailerDataRefProps, TDocuments, TRetailerInfoProps } from "./types";
import AttachMultipleUploadedFileToRetailerProfile from "@/components/containers/AttachUploadedFileToProfiles/AttachMultipleUploadedFilesToRetailerProfile";
import { useGetRetailerDocumentsQuery } from "@/store/apps/retailer";
import NumberInput from "@/components/ui/inputs/NumberInput";
import Input from "@/components/ui/inputs/Input";
import { yupResolver } from "@hookform/resolvers/yup";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { legalTooltip, nationalTooltip } from "./utils";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import { twMerge } from "tailwind-merge";

const RetailerInfo = (
  { onChangeActiveStep, retailerData, onSubmitData, isEdit }: TRetailerInfoProps,
  ref: ForwardedRef<IRetailerDataRefProps>
) => {
  const { t } = useTranslation();
  const [submitCount, setSubmitCount] = useState(0);

  const { data: documentsData } = useGetRetailerDocumentsQuery(undefined, {
    skip: !isEdit
  });

  const formMethods = useForm({
    defaultValues: {
      contactEmail: retailerData?.id && !retailerData?.identity?.isLegalPerson ? retailerData?.contactEmail : "",
      legalContactEmail: retailerData?.identity?.isLegalPerson ? retailerData?.contactEmail : "",
      contactNumber: retailerData?.id && !retailerData?.identity?.isLegalPerson ? retailerData?.contactNumber : "",
      legalContactNumber: retailerData?.identity?.isLegalPerson ? retailerData?.contactNumber : "",
      name: retailerData?.id && !retailerData?.identity?.isLegalPerson ? retailerData?.name : "",
      legalName: retailerData?.id && retailerData?.identity?.isLegalPerson ? retailerData?.name : "",
      birthDay:
        retailerData?.id && !retailerData?.identity?.isLegalPerson ? retailerData?.identity?.birthDay : undefined,
      companyName: retailerData?.identity?.companyName || undefined,
      companyType: retailerData?.identity?.companyType || undefined,
      economicCode: retailerData?.identity?.economicCode || undefined,
      isLegalPerson: retailerData?.identity?.isLegalPerson || false,
      nationalCode: retailerData?.identity?.nationalCode || undefined,
      registrationNumber: retailerData?.identity?.registrationNumber || undefined,
      vatNumber: retailerData?.identity?.vatNumber || undefined,
      bankAccount:
        retailerData?.id && !retailerData?.identity?.isLegalPerson
          ? retailerData?.bankAccount
          : {
              bic: "",
              holderName: "",
              iban: ""
            },
      legalBankAccount: retailerData?.identity?.isLegalPerson
        ? retailerData?.bankAccount || {
            bic: "",
            holderName: "",
            iban: ""
          }
        : {
            bic: "",
            holderName: "",
            iban: ""
          },
      markup: retailerData?.markup || undefined,
      markupType: retailerData?.markupType || undefined,
      documents: {
        "legal-national-front": retailerData?.documents?.["legal-national-front"] || undefined,
        "legal-national-back": retailerData?.documents?.["legal-national-back"] || undefined,
        "legal-newspaper": retailerData?.documents?.["legal-newspaper"] || undefined,
        "legal-certificate-of-added-value": retailerData?.documents?.["legal-certificate-of-added-value"] || undefined,
        "national-front": retailerData?.documents?.["national-front"] || undefined,
        "national-back": retailerData?.documents?.["national-back"] || undefined
      } as TDocuments
    },
    resolver: yupResolver(retailerInfoValidationSchema({ isEdit: !!isEdit }) as any) as any,
    mode: "onChange"
  });

  const {
    control,
    reset,
    handleSubmit,
    setValue,
    setError,
    getValues,
    resetField,
    clearErrors,
    watch,
    formState: { errors }
  } = formMethods;

  const documents = watch("documents");
  const isLegalPerson = watch("isLegalPerson");

  useEffect(() => {
    if (retailerData?.id) {
      reset({
        contactEmail: retailerData?.id && !retailerData?.identity?.isLegalPerson ? retailerData?.contactEmail : "",
        legalContactEmail: retailerData?.id && retailerData?.identity?.isLegalPerson ? retailerData?.contactEmail : "",
        contactNumber: retailerData?.id && !retailerData?.identity?.isLegalPerson ? retailerData?.contactNumber : "",
        legalContactNumber: retailerData?.identity?.isLegalPerson ? retailerData?.contactNumber : "",
        name: retailerData?.id && !retailerData?.identity?.isLegalPerson ? retailerData?.name : "",
        legalName: retailerData?.id && retailerData?.identity?.isLegalPerson ? retailerData?.name : "",
        birthDay:
          retailerData?.id && !retailerData?.identity?.isLegalPerson ? retailerData?.identity?.birthDay : undefined,
        companyName: retailerData?.identity?.companyName || undefined,
        companyType: retailerData?.identity?.companyType || undefined,
        economicCode: retailerData?.identity?.economicCode || undefined,
        isLegalPerson: retailerData?.identity?.isLegalPerson || false,
        nationalCode: retailerData?.identity?.nationalCode || undefined,
        registrationNumber: retailerData?.identity?.registrationNumber || undefined,
        vatNumber: retailerData?.identity?.vatNumber || undefined,
        bankAccount:
          retailerData?.id && !retailerData?.identity?.isLegalPerson
            ? retailerData?.bankAccount
            : {
                bic: "",
                holderName: "",
                iban: ""
              },
        legalBankAccount: retailerData?.identity?.isLegalPerson
          ? retailerData?.bankAccount || {
              bic: "",
              holderName: "",
              iban: ""
            }
          : {
              bic: "",
              holderName: "",
              iban: ""
            },
        markup: retailerData?.markup || undefined,
        markupType: retailerData?.markupType || undefined
        // documents: {
        //   "legal-national-front": retailerData?.documents?.["legal-national-front"] || undefined,
        //   "legal-national-back": retailerData?.documents?.["legal-national-back"] || undefined,
        //   "legal-newspaper": retailerData?.documents?.["legal-newspaper"] || undefined,
        //   "legal-certificate-of-added-value":
        //     retailerData?.documents?.["legal-certificate-of-added-value"] || undefined,
        //   "national-front": retailerData?.documents?.["national-front"] || undefined,
        //   "national-back": retailerData?.documents?.["national-back"] || undefined
        // }
      });
    }
  }, [retailerData]);

  const markupTypeItems = [
    { label: t("retailer.profile.markupTypeFixed"), id: "Fixed" },
    { label: t("retailer.profile.markupTypePercentage"), id: "Percentage" }
  ];

  const onSubmit = (values: TRetailerInfoFormValues) => {
    const {
      birthDay,
      companyName,
      vatNumber,
      companyType,
      economicCode,
      isLegalPerson,
      nationalCode,
      documents,
      bankAccount,
      legalBankAccount,
      registrationNumber,
      contactEmail,
      legalContactEmail,
      contactNumber,
      legalContactNumber,
      markup,
      name,
      legalName,
      markupType,
      ...restValues
    } = values;

    const legalBody = {
      identity: {
        companyName,
        companyType,
        economicCode,
        isLegalPerson,
        registrationNumber,
        vatNumber
      },
      name: legalName,
      contactEmail: legalContactEmail,
      contactNumber: legalContactNumber,
      bankAccount: legalBankAccount,
      markup,
      markupType,
      documents,
      ...restValues
    };

    const notLegalBody = {
      identity: {
        birthDay,
        nationalCode,
        vatNumber,
        isLegalPerson
      },
      name,
      contactEmail,
      contactNumber,
      bankAccount,
      markup,
      markupType,
      documents,
      ...restValues
    };

    onSubmitData(values?.isLegalPerson ? legalBody : notLegalBody);
    onChangeActiveStep(1);
    setSubmitCount(prev => prev + 1);
  };

  useEffect(() => {
    if (isEdit && documentsData?.data) {
      [
        "legal-national-back",
        "legal-national-front",
        "legal-newspaper",
        "legal-certificate-of-added-value",
        "national-front",
        "national-back"
      ].forEach(tag => {
        const existDocumentsInServer = documentsData?.data?.filter(item => item.tag.startsWith(tag));
        if (existDocumentsInServer?.length) {
          setValue(
            `documents.${tag}`,
            existDocumentsInServer?.map(item => ({ id: item?.mediaId, url: item?.media?.url }))
          );
        }
      });
    }
  }, [JSON.stringify(documentsData)]);

  useImperativeHandle(
    ref,
    () => ({
      handleError: (field, { message }) => {
        const fieldKey = field.includes("identity.") ? field.split("identity.")[1] : field;
        setError(fieldKey as any, { type: "manual", message });
      }
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [setError]
  );

  const ibanEndAdornment = IS_IRAN_SERVED ? "IR" : "";

  const onChangeAttachUploadedFileToRetailerProfile = (tag: string, mediaId: string, file?: File) => {
    const currentValue = documents?.[tag] || [];

    if (!Array.isArray(currentValue)) {
      console.error(`Field ${tag} is not an array`);
      return;
    }

    if (file) {
      const reader = new FileReader();
      reader.onload = event => {
        const newItem = { id: mediaId, url: event?.target?.result as string };
        const updatedValue = [...currentValue, newItem];
        setValue(`documents.${tag}`, updatedValue);
      };

      reader.readAsDataURL(file); // Read the file as a Data URL
    } else {
      const newItem = { id: mediaId };
      const updatedValue = [...currentValue, newItem];
      setValue(`documents.${tag}`, updatedValue);
    }

    setTimeout(() => {
      setError(`documents.${tag}`, { message: undefined });
    }, 0);
  };

  const onErrorAttachUploadedFileToRetailerProfile = (tag: string, errorMessage?: string) => {
    // setFieldTouched(`documents.${tag}`, true);
    // setTimeout(() => {
    setError(`documents.${tag}`, { message: t(`${errorMessage}`) });
    // }, 0);
  };

  const onRemoveAttachUploadedFileToRetailerProfile = (tag: string, index: number) => {
    // Get the current value of the tag field
    const currentValue = documents?.[tag];

    // Ensure the current value is an array
    if (Array.isArray(currentValue)) {
      // Remove the item at the specified index
      const updatedValue = currentValue.filter((_, i) => i !== index);

      // Update the field value with the new array
      setValue(`documents.${tag}`, updatedValue);
    } else {
      console.error(`Field ${tag} is not an array`);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="h-full flex flex-col">
      <Grid
        container
        spacing={{ xs: 1, sm: 1.5 }}
        className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 xmd:pr-4 xmd:pt-4"
      >
        <Grid item xs={12} md={12} className="xmd:mb-5 mb-3">
          <p className="text-gray-500 text-body3-regular mb-3">{t("userSwitch")}</p>

          <Controller
            name="isLegalPerson"
            key="isLegalPerson"
            control={control}
            render={({ field }) => (
              <UserTypeSwitch
                checked={!!field.value}
                onChange={v => {
                  field.onChange(v);
                  // setFieldValue("isLegalPerson", v);
                  // setTouched({}, false);

                  // reset vat number once we switched
                  setValue(
                    "vatNumber",
                    (retailerData?.identity?.isLegalPerson && v) ||
                      (retailerData?.id && !retailerData?.identity?.isLegalPerson && !v)
                      ? retailerData?.identity?.vatNumber || ""
                      : ""
                  );
                  setValue(
                    "markup",
                    (retailerData?.identity?.isLegalPerson && v) ||
                      (retailerData?.id && !retailerData?.identity?.isLegalPerson && !v)
                      ? retailerData?.markup || ""
                      : ("" as any)
                  );
                  setValue(
                    "markupType",
                    (retailerData?.identity?.isLegalPerson && v) ||
                      (retailerData?.id && !retailerData?.identity?.isLegalPerson && !v)
                      ? retailerData?.markupType || ""
                      : ""
                  );

                  clearErrors();
                }}
                labels={{
                  legal: t("retailerLegalUser.title"),
                  real: t("retailerRealUser.title")
                }}
                subTitles={{
                  legal: t("retailerLegalUser.subTitle"),
                  real: t("retailerRealUser.subTitle")
                }}
              />
            )}
          />
        </Grid>

        {isLegalPerson ? (
          <>
            <Grid item xs={12} md={12}>
              <Grid container spacing={{ xs: 1, sm: 1.5 }}>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="legalName"
                    key="legalName"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="legalName"
                        name="legalName"
                        autoComplete="off"
                        placeholder={t("retailer.profile.name")}
                        label={t("retailer.profile.name")}
                        error={Boolean(errors.legalName?.message)}
                        helperText={errors?.legalName?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="legalContactEmail"
                    key="legalContactEmail"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="legalContactEmail"
                        autoComplete="off"
                        placeholder={t("retailer.profile.email")}
                        label={t("retailer.profile.contactEmail")}
                        name="legalContactEmail"
                        error={Boolean(errors.legalContactEmail?.message)}
                        helperText={errors?.legalContactEmail?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="legalContactNumber"
                    key="legalContactNumber"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        {...field}
                        id="legalContactNumber"
                        autoComplete="off"
                        placeholder={t("retailer.profile.contactNumber")}
                        label={t("retailer.profile.contactNumber")}
                        name="legalContactNumber"
                        error={Boolean(errors.legalContactNumber?.message)}
                        helperText={errors?.legalContactNumber?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="economicCode"
                    key="economicCode"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        {...field}
                        name="economicCode"
                        autoComplete="off"
                        placeholder={t("retailer.profile.economicCode")}
                        label={t("retailer.profile.economicCode")}
                        error={Boolean(errors.economicCode?.message)}
                        helperText={errors?.economicCode?.message || ""}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>
          </>
        ) : (
          <>
            <Grid item xs={12} md={12}>
              <Grid container spacing={{ xs: 1, sm: 1.5 }}>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="name"
                    key="name"
                    control={control}
                    render={({ field }) => {
                      return (
                        <Input
                          {...field}
                          id="name"
                          name="name"
                          autoComplete="off"
                          placeholder={t("retailer.profile.name")}
                          label={t("retailer.profile.name")}
                          error={Boolean(errors.name?.message)}
                          helperText={errors?.name?.message || ""}
                        />
                      );
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="contactEmail"
                    key="contactEmail"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="contactEmail"
                        autoComplete="off"
                        placeholder={t("retailer.profile.email")}
                        label={t("retailer.profile.contactEmail")}
                        name="contactEmail"
                        error={Boolean(errors.contactEmail?.message)}
                        helperText={errors?.contactEmail?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="contactNumber"
                    key="contactNumber"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        {...field}
                        id="contactNumber"
                        autoComplete="off"
                        placeholder={t("retailer.profile.contactNumber")}
                        label={t("retailer.profile.contactNumber")}
                        name="contactNumber"
                        error={Boolean(errors.contactNumber?.message)}
                        helperText={errors?.contactNumber?.message || ""}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="vatNumber"
                    key="vatNumber"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        {...field}
                        label={t("retailer.profile.vatNumber")}
                        placeholder={t("retailer.profile.vatNumber")}
                        labelTooltipTitle={t("supplier.profile.vatNumberTooltip.title")}
                        labelTooltipDescription={t("supplier.profile.vatNumberTooltip.description")}
                        id="vatNumber"
                        requiredStar={false}
                        optional
                        name="vatNumber"
                        autoComplete="off"
                        error={Boolean(errors.vatNumber?.message)}
                        helperText={errors?.vatNumber?.message || ""}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>
          </>
        )}

        {isEdit && (
          <>
            <Grid item xs={12} md={6}>
              <Controller
                name="markupType"
                key="markupType"
                control={control}
                render={({ field }) => (
                  <CustomAutocomplete<(typeof markupTypeItems)[0]>
                    {...field}
                    value={markupTypeItems?.find(item => item?.id === field?.value)}
                    options={markupTypeItems}
                    placeholder={t("retailer.profile.markupType")}
                    label={t("retailer.profile.markupType")}
                    error={Boolean(errors.markupType?.message)}
                    helperText={errors?.markupType?.message || ""}
                    onChange={(e, value) => setValue("markupType", value?.id)}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="markup"
                key="markup"
                control={control}
                render={({ field }) => (
                  <NumberInput
                    {...field}
                    id="markup"
                    key="markup"
                    autoComplete="off"
                    placeholder={t("retailer.profile.markup")}
                    label={t("retailer.profile.markup")}
                    name="markup"
                    error={Boolean(errors.markup?.message)}
                    helperText={errors?.markup?.message || ""}
                  />
                )}
              />
            </Grid>
          </>
        )}

        {isLegalPerson ? (
          <>
            <Grid item xs={12} md={12}>
              <Grid container spacing={{ xs: 1, sm: 1.5 }}>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="companyName"
                    key="companyName"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        name="companyName"
                        autoComplete="off"
                        label={t("retailer.profile.companyName")}
                        placeholder={t("retailer.profile.companyName")}
                        error={Boolean(errors.companyName?.message)}
                        helperText={errors?.companyName?.message || ""}
                        type="text"
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="companyType"
                    key="companyType"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        name="companyType"
                        autoComplete="off"
                        placeholder={t("retailer.profile.companyType")}
                        label={t("retailer.profile.companyType")}
                        error={Boolean(errors.companyType?.message)}
                        helperText={errors?.companyType?.message || ""}
                        type="text"
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="vatNumber"
                key="vatNumber"
                control={control}
                render={({ field }) => (
                  <NumberInput
                    {...field}
                    autoComplete="off"
                    // requiredStar={false}
                    // optional
                    label={t("retailer.profile.vatNumber")}
                    placeholder={t("retailer.profile.vatNumber")}
                    id="vatNumber"
                    name="vatNumber"
                    error={Boolean(errors.vatNumber?.message)}
                    helperText={errors?.vatNumber?.message || ""}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="registrationNumber"
                key="registrationNumber"
                control={control}
                render={({ field }) => (
                  <NumberInput
                    {...field}
                    autoComplete="off"
                    name="registrationNumber"
                    placeholder={t("retailer.profile.registrationNumber")}
                    label={t("retailer.profile.registrationNumber")}
                    error={Boolean(errors.registrationNumber?.message)}
                    helperText={errors?.registrationNumber?.message || ""}
                  />
                )}
              />
            </Grid>
          </>
        ) : (
          <>
            <Grid item xs={12} md={12}>
              <Grid container spacing={{ xs: 1, sm: 1.5 }}>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="nationalCode"
                    key="nationalCode"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        {...field}
                        name="nationalCode"
                        autoComplete="off"
                        placeholder={t("retailer.profile.nationalCode")}
                        label={t("retailer.profile.nationalCode")}
                        error={Boolean(errors.nationalCode?.message)}
                        helperText={errors?.nationalCode?.message || ""}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="birthDay"
                    key="birthDay"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        {...field}
                        label={t("retailer.profile.birthDay")}
                        placeholder={t("retailer.profile.birthDay")}
                        inputProps={{
                          placeholder: t("retailer.profile.birthDay")
                        }}
                        maxDate={eighteenYearsAgo}
                        name="birthDay"
                        onChange={value =>
                          setValue("birthDay", value ? new Date(value as string)?.toISOString() : undefined)
                        }
                        error={Boolean(errors.birthDay?.message)}
                        helperText={errors?.birthDay?.message || ""}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>
          </>
        )}

        <Grid item xs={12} md={12}>
          <p className="xmd:mt-4 mt-2 text-v2-content-primary text-body3-medium">{t("retailer.profile.bankInfo")}</p>
        </Grid>

        {isLegalPerson ? (
          <>
            <Grid item xs={12} md={6}>
              <Controller
                name="legalBankAccount.holderName"
                key="legalBankAccount.holderName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    autoComplete="off"
                    placeholder={t("retailer.profile.holderName")}
                    label={t("retailer.profile.holderName")}
                    id="legalBankAccount.holderName"
                    name="legalBankAccount.holderName"
                    error={Boolean(errors?.legalBankAccount?.holderName?.message)}
                    helperText={errors?.legalBankAccount?.holderName?.message || ""}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="legalBankAccount.iban"
                key="legalBankAccount.iban"
                control={control}
                render={({ field }) => (
                  <NumberInput
                    {...field}
                    dir="ltr"
                    className="text-left"
                    autoComplete="off"
                    endAdornment={<div className="pr-3">{ibanEndAdornment}</div>}
                    placeholder={t("retailer.profile.iban")}
                    label={t("retailer.profile.iban")}
                    id="legalBankAccount.iban"
                    name="legalBankAccount.iban"
                    error={Boolean(errors?.legalBankAccount?.iban?.message)}
                    helperText={errors?.legalBankAccount?.iban?.message || ""}
                  />
                )}
              />
            </Grid>
          </>
        ) : (
          <>
            <Grid item xs={12} md={6}>
              <Controller
                name="bankAccount.holderName"
                key="bankAccount.holderName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    autoComplete="off"
                    placeholder={t("retailer.profile.holderName")}
                    label={t("retailer.profile.holderName")}
                    id="bankAccount.holderName"
                    name="bankAccount.holderName"
                    error={Boolean(errors?.bankAccount?.holderName?.message)}
                    helperText={errors?.bankAccount?.holderName?.message || ""}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="bankAccount.iban"
                key="bankAccount.iban"
                control={control}
                render={({ field }) => (
                  <NumberInput
                    {...field}
                    autoComplete="off"
                    dir="ltr"
                    className="text-left"
                    endAdornment={<div className="pr-3">{ibanEndAdornment}</div>}
                    placeholder={t("retailer.profile.iban")}
                    label={t("retailer.profile.iban")}
                    id="bankAccount.iban"
                    name="bankAccount.iban"
                    error={Boolean(errors?.bankAccount?.iban?.message)}
                    helperText={errors?.bankAccount?.iban?.message || ""}
                  />
                )}
              />
            </Grid>
          </>
        )}

        {/* <Grid item xs={12} md={12}>
          <p className="mb-4 mt-4 text-v2-content-primary text-body3-medium">
            {t("retailer.profile.sectionInfo.uploadDoc")}
          </p>
        </Grid> */}

        {isLegalPerson ? (
          <>
            {[
              {
                tag: "legal-national-front",
                title: t("retailer.profile.legalDoc.frontNationalCard"),
                header: t("retailer.profile.legalDoc.frontNationalCardHeader")
              },
              { tag: "legal-national-back", title: t("retailer.profile.legalDoc.backNationalCard") },
              { tag: "legal-newspaper", title: t("retailer.profile.legalDoc.legalNewspaper") },
              { tag: "legal-certificate-of-added-value", title: t("retailer.profile.legalDoc.legalCertificate") }
            ].map(({ tag, title, header }) => (
              <Grid item xs={12} md={6} key={tag} alignSelf="flex-end">
                <div className="mb-2.5 text-body3-medium text-v2-content-primary">{!!header && <p>{header}</p>}</div>{" "}
                <Controller
                  name={`documents.${tag}` as any}
                  key={`documents.${tag}` as any}
                  control={control}
                  render={({ field, fieldState }) => (
                    <>
                      <AttachMultipleUploadedFileToRetailerProfile
                        isEdit={isEdit}
                        tag={tag}
                        labelTooltipTitle={legalTooltip[tag]?.title}
                        labelTooltipDescription={legalTooltip[tag]?.description}
                        documents={documents}
                        title={title}
                        onChange={(mediaId, fileTag, file) =>
                          onChangeAttachUploadedFileToRetailerProfile(fileTag, mediaId, file)
                        }
                        onUpload={() => clearErrors(`documents.${tag}` as any)}
                        onError={(fileTag, error) => onErrorAttachUploadedFileToRetailerProfile(fileTag, error)}
                        onRemove={(fileTag, index) => onRemoveAttachUploadedFileToRetailerProfile(fileTag, index)}
                      />
                      {errors?.documents?.[tag]?.message && (
                        <InputHelper>{errors?.documents?.[tag]?.message as string}</InputHelper>
                      )}
                    </>
                  )}
                />
              </Grid>
            ))}
          </>
        ) : (
          <>
            {[
              {
                tag: "national-front",
                title: t("retailer.profile.personalDoc.frontNationalCard"),
                header: t("retailer.profile.personalDoc.frontNationalCardHeader")
              },
              { tag: "national-back", title: t("retailer.profile.personalDoc.backNationalCard") }
            ].map(({ tag, title, header }) => (
              <Grid item xs={12} md={6} key={tag} alignSelf="flex-end">
                <div className="mb-2.5 text-body3-medium text-v2-content-primary">{!!header && <p>{header}</p>}</div>{" "}
                <Controller
                  name={`documents.${tag}` as any}
                  key={`documents.${tag}` as any}
                  control={control}
                  render={({ field, fieldState }) => (
                    <>
                      <AttachMultipleUploadedFileToRetailerProfile
                        isEdit={isEdit}
                        tag={tag}
                        labelTooltipTitle={nationalTooltip[tag]?.title}
                        labelTooltipDescription={nationalTooltip[tag]?.description}
                        documents={documents}
                        title={title}
                        onChange={(mediaId, fileTag, file) =>
                          onChangeAttachUploadedFileToRetailerProfile(fileTag, mediaId, file)
                        }
                        onUpload={() => clearErrors(`documents.${tag}` as any)}
                        onError={(fileTag, error) => onErrorAttachUploadedFileToRetailerProfile(fileTag, error)}
                        onRemove={(fileTag, index) => onRemoveAttachUploadedFileToRetailerProfile(fileTag, index)}
                        maxCount={1}
                      />
                      {errors?.documents?.[tag]?.message && (
                        <InputHelper>{errors?.documents?.[tag]?.message as string}</InputHelper>
                      )}
                    </>
                  )}
                />
              </Grid>
            ))}
          </>
        )}
      </Grid>

      <BottomAction
        saveButtonText={t("saveChanges")}
        saveButtonProps={{
          type: "submit"
        }}
        cancelButtonText={t("supplier.profile.cancel")}
        cancelButtonProps={{
          onClick: () => {
            onChangeActiveStep(undefined);
          }
        }}
      />

      <div className="xmd:flex hidden items-center justify-end">
        <CustomButton type="submit">{t("saveChanges")}</CustomButton>
      </div>
    </form>
  );
};

export default React.forwardRef(RetailerInfo);
