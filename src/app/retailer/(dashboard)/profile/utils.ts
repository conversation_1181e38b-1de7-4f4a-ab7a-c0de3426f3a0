import { Dispatch, SetStateAction } from "react";
import { TRetailerInfo } from "./types";
import { TRetailerProfileData } from "@/store/apps/retailer/types";
import { toCamel } from "ts-case-convert";
import { ApiError } from "@/utils/services";

const bindFieldsToStep = [
  {
    step: 0,
    fields: [
      "bankAccount",
      "contactEmail",
      "contactNumber",
      "identity",
      "markup",
      "markupType",
      "birthDay",
      "companyName",
      "companyType",
      "economicCode",
      "isLegalPerson",
      "nationalCode",
      "registrationNumber",
      "vatNumber",
      "bankAccount.holderName",
      "bankAccount.iban"
    ] as Array<keyof Omit<TRetailerInfo, "identity"> | keyof TRetailerProfileData["identity"]>
  },
  {
    step: 1,
    fields: ["address.locationId", "address.zip", "address.address1"]
  }
];

export const handleGoToHasErrorTab = (error: ApiError, setActiveStep: Dispatch<SetStateAction<number | undefined>>) => {
  if (error.error_detail) {
    Object.keys(error.error_detail).some(key => {
      const fieldKey = key.includes("identity.") ? key.split("identity.")?.[1] : key;

      const found = bindFieldsToStep.find(a => (a.fields as any).includes(toCamel(fieldKey)));

      if (found) {
        setActiveStep(found.step);
        return true;
      }

      return false;
    });
  }
};
