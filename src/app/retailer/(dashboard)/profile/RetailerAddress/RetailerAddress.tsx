import { Box, CircularProgress, Grid, Stack } from "@mui/material";
import { useTranslation } from "react-i18next";
import React, { ForwardedRef, useImperativeHandle, useMemo } from "react";
import { useForm, Controller } from "react-hook-form";
import { TRetailerAddressFormProps } from "./types";
import SectionInfo from "@/components/ui/SectionInfo/SectionInfo";
import LocationsSelect from "@/components/ui/CustomCountrySelect/LocationsSelect";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { IRetailerDataRefProps } from "../RetailerInfo/types";
import { isValidUUID } from "@/utils/helpers";
import NumberInput from "@/components/ui/inputs/NumberInput";
import Input from "@/components/ui/inputs/Input";
import { retailerAddressValidation } from "@/utils/validations/profile/retailer";
import { yupResolver } from "@hookform/resolvers/yup";
import { SetHookFormError } from "@/utils/services/utils";
import BottomAction from "@/components/ui/bottomAction/BottomAction";

export type TRetailerAddress = {
  address?: {
    locationId?: string;
    zip?: string;
    address1?: string;
  };
};

const RetailerAddress = (
  { retailerData, onChangeActiveStep, onSubmitData, isLoading, onPrev }: TRetailerAddressFormProps,
  ref: ForwardedRef<IRetailerDataRefProps>
) => {
  const { t } = useTranslation();

  const defaultValues = useMemo(
    () => ({
      address: {
        locationId:
          retailerData?.address?.locationId && isValidUUID(retailerData?.address?.locationId)
            ? retailerData?.address?.locationId
            : undefined,
        zip: retailerData?.address?.zip || "",
        address1: retailerData?.address?.address1 || ""
      }
    }),
    [retailerData]
  );

  const {
    watch,
    control,
    handleSubmit,
    setError,
    formState: { errors }
  } = useForm({
    defaultValues,
    resolver: yupResolver(retailerAddressValidation) as any,
    mode: "onChange"
  });

  useImperativeHandle(ref, () => ({
    handleError: (field: string, { message }: { message: string }) => {
      setError(field as keyof TRetailerAddress, { message });
    }
  }));

  const onSubmit = (data: TRetailerAddress) => {
    onSubmitData(data, setError as SetHookFormError);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="h-full flex flex-col ">
      <Grid
        container
        spacing={{ xs: 1, sm: 1.5 }}
        className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 xmd:pr-4 xmd:pt-4"
      >
        <Grid item xs={12} md={12} className="xmd:mb-2">
          <h2 className="text-v2-content-primary text-body2-medium mb-2">{t("addresses")}</h2>
          <span className="text-v2-content-tertiary text-body3-regular">{t("retailerAddressHint")}</span>
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="address.locationId"
            control={control}
            render={({ field }) => (
              <LocationsSelect
                multiple={false}
                label={t("statecity")}
                {...field}
                error={Boolean(errors.address?.locationId?.message)}
                helperText={errors?.address?.locationId?.message || ""}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="address.zip"
            control={control}
            render={({ field }) => (
              <NumberInput
                {...field}
                id="address.zip"
                autoComplete="off"
                placeholder={t("retailer.profile.zip")}
                label={t("retailer.profile.zip")}
                error={Boolean(errors?.address?.zip?.message)}
                helperText={errors?.address?.zip?.message || ""}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={12}>
          <Controller
            name="address.address1"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                autoComplete="off"
                label={t("retailer.profile.address")}
                placeholder={t("retailer.profile.address")}
                error={Boolean(errors.address?.address1?.message)}
                helperText={errors?.address?.address1?.message || ""}
              />
            )}
          />
        </Grid>
      </Grid>

      <BottomAction
        saveButtonText={t("saveChanges")}
        saveButtonProps={{
          type: "submit"
        }}
        cancelButtonText={t("supplier.profile.cancel")}
        cancelButtonProps={{
          onClick: () => {
            onChangeActiveStep(undefined);
          }
        }}
      />

      <div className="xmd:flex hidden justify-end flex-wrap gap-2 ">
        {/* <CustomButton
          color="secondary"
          className="retailer-profile-footer-button"
          onClick={() => {
            onPrev(watch());
            onChangeActiveStep(0);
          }}
          startIcon={
            <Icon icon="mingcute:arrow-right-line" width={20} height={20} className="retailer-profile-next-button" />
          }
        >
          {t("retailer.profile.buttonPrev")}
        </CustomButton> */}
        <CustomButton type="submit" className="retailer-profile-footer-button" disabled={isLoading}>
          {isLoading ? <CircularProgress color="info" size={20} /> : t("saveChanges")}
        </CustomButton>
      </div>
    </form>
  );
};

export default React.forwardRef(RetailerAddress);
