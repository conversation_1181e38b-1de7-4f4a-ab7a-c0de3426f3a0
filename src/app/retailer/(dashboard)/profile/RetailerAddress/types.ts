import { SetHookFormError } from "@/utils/services/utils";
import { TRetailerBaseFormProps, TRetailerProfile } from "../types";
import { TRetailerAddress } from "./RetailerAddress";

export type TRetailerAddressFormProps = {
  retailerData?: TRetailerProfile;
  onSubmitData: (data: TRetailerAddress, setFieldError?: SetHookFormError) => void;
  onPrev: (data: TRetailerAddress) => void;
  isLoading?: boolean;
} & TRetailerBaseFormProps;
