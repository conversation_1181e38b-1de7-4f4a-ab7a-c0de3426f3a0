import { Formik } from "formik";
import { Box, CircularProgress, Grid, Stack } from "@mui/material";
import { useTranslation } from "react-i18next";
import React from "react";
import CustomFormLabel from "@/components/ui/CustomFormLabel/CustomFormLabel";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import CircleWithButtonImageUploader from "@/components/containers/ImageUploader/withUi/CircleWithButtonImageUploader";
import { BrandedInvoicing, IStoreInvoiceData, TRetailerStoreResponse } from "@/store/apps/retailer/types";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import { Retailer, usePutRetailerStoreInvoiceMutation } from "@/store/apps/retailer";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { SnakeToCamelFieldErrorWrapper } from "@/utils/helpers";
import { invoiceBrandValidation } from "@/utils/validations/store/invoiceBrand";
import { useDispatch } from "@/store/hooks";
import { RETAILER_STORE_KEY } from "@/constants/queryKeys";
import Textarea from "@/components/ui/inputs/Textarea/Textarea";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import Input from "@/components/ui/inputs/Input";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import { useRouter } from "next/navigation";

export interface IInvoiceBrandProps {
  retailerStoreSingle?: TRetailerStoreResponse;
  isRetailerStoreSingleLoading: boolean;
}

const InvoiceBrand = ({ retailerStoreSingle, isRetailerStoreSingleLoading }: IInvoiceBrandProps) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();

  const brandedInvoicing = retailerStoreSingle?.data?.brandedInvoicing;

  const [putInvoice, { isLoading: isPutLoading }] = usePutRetailerStoreInvoiceMutation();

  const initialValues = {
    contactNumber: brandedInvoicing?.contactNumber || "",
    isBranded: brandedInvoicing?.isBranded || false,
    email: brandedInvoicing?.email || undefined,
    logo: brandedInvoicing?.logo || undefined,
    description: brandedInvoicing?.description || undefined
  };

  const onSubmit = async (values: BrandedInvoicing, { setFieldError }: any) => {
    const body = { ...values, retailerId: retailerStoreSingle?.data?.retailerId } as IStoreInvoiceData;

    try {
      await putInvoice({ body, id: retailerStoreSingle?.data?.id as string }).then(res => {
        const error = (res as any)?.error?.data;

        if (error) {
          clientDefaultErrorHandler({
            error: (res as any)?.error,
            bodyError: error,
            setFieldError: SnakeToCamelFieldErrorWrapper(setFieldError)
          });
        }
        if ("data" in res && res?.data) {
          const id = retailerStoreSingle?.data?.id;
          dispatch(Retailer.util.invalidateTags([{ type: RETAILER_STORE_KEY, id }]));
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  if (isRetailerStoreSingleLoading)
    return (
      <div className="flex items-center justify-center w-full  h-full">
        <CircularProgress />
      </div>
    );

  return (
    <Formik initialValues={initialValues} validationSchema={invoiceBrandValidation} onSubmit={onSubmit}>
      {({
        values,
        handleChange,
        handleBlur,
        handleSubmit,
        errors,
        touched,
        setFieldValue,
        setFieldError,
        setFieldTouched
      }) => (
        <form onSubmit={handleSubmit}>
          <Box>
            <Grid container spacing={1.5}>
              <Grid item xs={12} md={12}>
                <div className="flex items-start justify-between gap-1">
                  <div>
                    <h2 className="text-body3-bold text-v2-content-primary ">
                      {t("store.invoiceBrandForm.title")}
                    </h2>
                    <span className="text-caption-medium text-v2-content-tertiary">
                      {t("store.invoiceBrandForm.subtitle")}
                    </span>
                  </div>
                  <CustomSwitch
                    id="isBranded"
                    name="isBranded"
                    checked={values?.isBranded}
                    label={t("store.active")}
                    labelClassName="!ml-0"
                    onChange={(e, checked) => setFieldValue("isBranded", checked)}
                  />
                </div>
              </Grid>

              <Grid item xs={12} md={12}>
                <Stack mb={2} className="supplier-profile-store-image-stack-wrapper">
                  <CustomFormLabel className="mb-4 text-gray-999" htmlFor="logo">
                    {t("store.logo")}
                  </CustomFormLabel>

                  <CircleWithButtonImageUploader
                    serverFileKind="public"
                    value={values?.logo}
                    onUploaded={v => {
                      setFieldValue("logo", v.url);
                      setFieldError("logo", "");
                    }}
                    onError={msg => {
                      setFieldTouched("logo", true);

                      setTimeout(() => {
                        setFieldError("logo", t(`${msg}`));
                      }, 0);
                    }}
                    onRemove={() => {
                      setFieldValue("logo", "");
                      setTimeout(() => {
                        setFieldTouched("logo", true);
                      }, 0);
                    }}
                    cropperProps={{ aspect: 1 }}
                    withCompressorMaxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
                    maxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
                    buttonLabel={
                      values.logo ? t("store.storeInfoForm.changePhoto") : t("store.storeInfoForm.uploadProfile")
                    }
                    wrapperClassName="rounded-md"
                    containerClassName="xmd:flex-row flex-col"
                    className="w-fit xmd:mx-0 mx-auto"
                  />

                  {Boolean(errors?.logo) && touched?.logo && (
                    <InputHelper className="mx-auto xmd:mx-0" error>
                      {" "}
                      {errors?.logo}
                    </InputHelper>
                  )}
                </Stack>
              </Grid>

              <Grid item xs={12} md={6}>
                <Input
                  id="email"
                  name="email"
                  placeholder={t("store.email")}
                  label={t("store.email")}
                  value={values.email}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.email && Boolean(errors.email)}
                  helperText={touched?.email && errors?.email}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Input
                  id="contactNumber"
                  name="contactNumber"
                  placeholder={t("store.contactNumber")}
                  label={t("store.contactNumber")}
                  value={values.contactNumber}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.contactNumber && Boolean(errors.contactNumber)}
                  helperText={touched?.contactNumber && errors?.contactNumber}
                />
              </Grid>

              <Grid item xs={12} md={12}>
                <Textarea
                  rows={6}
                  optional
                  id="description"
                  name="description"
                  requiredStar={false}
                  placeholder={t("store.descriptionPlaceholder")}
                  label={t("store.description")}
                  value={values.description}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.description && Boolean(errors.description)}
                  helperText={touched?.description && errors?.description}
                />
              </Grid>
            </Grid>

            <BottomAction
              saveButtonText={isPutLoading ? <CircularProgress color="info" size={20} /> : t("saveChanges")}
              saveButtonProps={{
                type: "submit"
              }}
              cancelButtonText={t("supplier.profile.cancel")}
              cancelButtonProps={{
                onClick: () => router.back()
              }}
            />

            <div className="xmd:flex hidden justify-end flex-wrap gap-2 mt-6">
              <CustomButton type="submit" className="xmd:w-auto w-full">
                {isPutLoading ? <CircularProgress color="info" size={20} /> : t("saveChanges")}
              </CustomButton>
            </div>
          </Box>
        </form>
      )}
    </Formik>
  );
};

export default InvoiceBrand;
