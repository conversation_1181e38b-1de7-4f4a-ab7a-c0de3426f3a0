import { useSearchParams } from "next/navigation";
import ConfigureStore from "./ConfigureStore";
import StoreDynamicForm from "./StoreDynamicForm";
import { useGetMetaIntegrationQuery } from "@/store/apps/meta";
import { sortConfigFormByPosition } from "./utils";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import ProfileContentContainer from "@/components/containers/ProfileStepper/ProfileContentContainer";
import { useTranslation } from "react-i18next";

function StoreInvoiceManager() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const isEdit = searchParams.get("edit");
  const id = searchParams.get("integrationId");

  const { data: metaIntegrations, isLoading } = useGetMetaIntegrationQuery();

  const integration = metaIntegrations?.data?.find(item => item?.key === id);
  const forms = sortConfigFormByPosition(integration?.configForm || {});

  if (isEdit) {
    return (
      <>
        <MobileAppBar title={t("storeInfo")} hasBack />
        <ProfileContentContainer>
          <ConfigureStore configForm={forms} isLoading={isLoading} isEdit />
        </ProfileContentContainer>
      </>
    );
  }

  return (
    <>
      <MobileAppBar title={t("storeInfo")} hasBack />
      <ProfileContentContainer>
        <StoreDynamicForm configForm={forms} isLoading={isLoading} />{" "}
      </ProfileContentContainer>
    </>
  );
}

export default StoreInvoiceManager;
