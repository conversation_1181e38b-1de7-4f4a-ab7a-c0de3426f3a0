/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { FormElementData, InputType } from "@/store/apps/meta/types";
import * as yup from "yup";
import i18next from "i18next";
import Cookies from "js-cookie";
import { STORE_ID } from "@/constants/cookies";
import CustomTextField from "@/components/ui/CustomTextField/CustomTextField";
import CustomPasswordField from "@/components/ui/CustomPasswordField/CustomPasswordField";
import CustomNumberField from "@/components/ui/CustomNumberField/CustomNumberField";
import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import CircleWithButtonImageUploader from "@/components/containers/ImageUploader/withUi/CircleWithButtonImageUploader";
import { Divider } from "@mui/material";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";

export function sortConfigFormByPosition(configForm: { [key: string]: FormElementData }): {
  [key: string]: FormElementData;
} {
  const sortedEntries = Object.entries(configForm).sort(([, a], [, b]) => (a.position ?? 0) - (b.position ?? 0));

  return Object.fromEntries(sortedEntries);
}
export const TextFieldComponent = ({ field, form, setFieldValue, ...props }: any) => (
  <CustomTextField
    {...field}
    {...props}
    inputProps={{
      dir: props?.isLtr && "ltr",
      sx: {
        textAlign: props?.isLtr && "right !important",
        paddingRight: !props?.prepend && !props?.addon ? "" : props?.isLtr && "0 !important"
      }
    }}
    InputProps={{
      startAdornment: props?.append ? (
        <div dir="ltr" className=" w-28 truncate  pl-1">
          <span className=" text-body4-medium w-full">{props?.append}</span>
          <Divider orientation="vertical" className="absolute top-0 right-32 " />
        </div>
      ) : null,
      endAdornment: props?.prepend ? (
        <div dir="ltr" className=" w-36 truncate pr-1">
          <span className=" text-body4-medium w-full">{props?.prepend}</span>
          <Divider orientation="vertical" className="absolute top-0 left-[160px] " />
        </div>
      ) : props?.addonType === "icon" ? (
        <div dangerouslySetInnerHTML={{ __html: props?.addon }} />
      ) : props?.addon ? (
        <span className="text-body4-bold">{props?.addon}</span>
      ) : undefined
    }}
  />
);

export const NumberFieldComponent = ({ field, form, onChange, ...props }: any) => (
  <CustomNumberField
    {...field}
    {...props}
    onTextChange={value => props?.setFieldValue(field.name, value)}
    InputProps={{
      startAdornment: props?.append ? (
        <div dir="ltr" className=" w-28 truncate  pl-1">
          <span className=" text-body4-medium w-full">{props?.append}</span>
          <Divider orientation="vertical" className="absolute top-0 right-32 " />
        </div>
      ) : null,
      endAdornment: props?.prepend ? (
        <div dir="ltr" className=" w-36 truncate pr-1">
          <span className=" text-body4-medium w-full">{props?.prepend}</span>
          <Divider orientation="vertical" className="absolute top-0 left-[148px] " />
        </div>
      ) : null
    }}
    inputProps={{
      dir: props?.isLtr && "ltr",
      sx: {
        textAlign: props?.isLtr && "right !important",
        paddingRight: !props?.prepend && !props?.addon ? "" : props?.isLtr && "0 !important"
      }
    }}
  />
);

export const PasswordFieldComponent = ({ field, form, ...props }: any) => (
  <CustomPasswordField
    autoComplete="off"
    InputProps={{
      autoComplete: "off"
    }}
    inputProps={{
      autoComplete: "off",
      form: {
        autocomplete: "off"
      },
      dir: props?.isLtr && "ltr",
      sx: {
        textAlign: props?.isLtr && "right !important",
        paddingRight: props?.isLtr && "0 !important"
      }
    }}
    {...field}
    {...props}
  />
);

export const RadioFieldComponent = ({ field, form, ...props }: any) => <CustomRadio {...field} {...props} />;

export const CheckBoxFieldComponent = ({ field, form, ...props }: any) => <CustomCheckbox {...field} {...props} />;

export const FileFieldComponent = ({ field, form, setFieldValue, ...props }: any) => (
  <>
    <CircleWithButtonImageUploader
      serverFileKind="public"
      value={field.value}
      onUploaded={v => {
        setFieldValue("logo", v.url);
        form.setFieldError("logo", "");
      }}
      onError={msg => {
        form.setFieldTouched("logo", true);

        setTimeout(() => {
          form.setFieldError("logo", i18next.t(`${msg}`));
        }, 0);
      }}
      onRemove={() => {
        setFieldValue("logo", "");
        setTimeout(() => {
          form.setFieldTouched("logo", true);
        }, 0);
      }}
      wrapperClassName="rounded-md"
      containerClassName="xmd:flex-row flex-col"
      cropperProps={{ aspect: 1 }}
      withCompressorMaxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
      maxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
      buttonLabel={
        field.value ? i18next.t("store.storeInfoForm.changePhoto") : i18next.t("store.storeInfoForm.uploadProfile")
      }
      className="w-fit xmd:mx-0 mx-auto"
    />

    {!!props?.error && props?.helperText && <InputHelper error> {props?.helperText}</InputHelper>}
  </>
);

export const getValidationSchema = (configForm: { [key: string]: FormElementData }) => {
  const schema: { [key: string]: any } = {};

  for (const key in configForm) {
    const field = configForm[key];
    let validation = yup.string();

    if (field.type === InputType.Email) {
      validation = yup.string().email(i18next.t("validations.email"));
    }

    if (field.required) {
      validation = validation.required(i18next.t("validations.requiredField"));
    }

    if (field.requiredIfNotPresent && !schema[field.requiredIfNotPresent]) {
      validation = validation.when(field.requiredIfNotPresent, {
        is: (value: any) => !value,
        then: yup.string().required(i18next.t("validations.requiredField"))
      });
    }

    if (field.max) {
      validation = validation.max(field.max, i18next.t("validations.max", { number: field.max }));
    }

    if (field.min) {
      validation = validation.min(field.min, i18next.t("validations.min", { number: field.min }));
    }

    if (field?.validationRegex) {
      validation = validation.matches(new RegExp(field.validationRegex), i18next.t("validations.invalidFormat"));
    }

    schema[key] = validation;
  }

  return yup.object().shape(schema);
};

export const storeId = () => {
  const id = Cookies.get(STORE_ID);

  if (id) return id;
  return "";
};
