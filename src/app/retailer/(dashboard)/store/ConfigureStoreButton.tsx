import React from "react";
import { Button } from "@mui/material";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import Link from "next/link";

type ConfigureRetailerStoreProps = {
  href: string;
  disabled?: boolean;
  handleNotActiveUser?: () => void;
  onclose?: () => void;
};

const ConfigureStoreButton: React.FC<ConfigureRetailerStoreProps> = ({
  href,
  disabled,
  onclose,
  handleNotActiveUser
}) => {
  const { t } = useTranslation();

  return (
    <Button
      color="info"
      variant="contained"
      href={disabled ? "" : href}
      onClick={() => {
        handleNotActiveUser && disabled && handleNotActiveUser();
        onclose?.();
      }}
      LinkComponent={Link}
      className=" bg-gray-20 !shadow-none hover:bg-gray-50 text-gray-600 text-body4-medium rounded-md py-3 max-h-10 px-4 "
      startIcon={<Icon icon="solar:tuning-square-linear" className="text-gray-600 " />}
    >
      <div className="flex items-center justify-between w-full">
        <span>{t("store.configureStore")}</span>
        <Icon icon="solar:alt-arrow-left-linear" className="text-gray-600 " />
      </div>
    </Button>
  );
};

export default ConfigureStoreButton;
