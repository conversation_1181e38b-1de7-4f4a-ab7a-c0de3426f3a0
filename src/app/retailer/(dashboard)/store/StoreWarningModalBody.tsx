import Button from "@/components/ui/Button";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { ReactNode } from "react";

interface IStoreModalBodyProps {
  title: string;
  subtitle: string;
  buttonText?: string;
  icon?: ReactNode;
}

function StoreModalBody({ title, subtitle, buttonText, icon }: IStoreModalBodyProps) {
  const { hideModal } = useModal();

  return (
    <div className="flex flex-col xmd:pt-0 pt-10 xmd:items-start items-center ">
      <div className="xmd:ml-auto ml-0 w-fit">
        {icon || <Icon icon="solar:info-square-bold" className="size-12 text-v2-content-on-warning-2" />}
      </div>

      <p className="mt-4 text-v2-content-primary text-subtitle-bold">{title}</p>
      <p className="text-body4-medium text-v2-content-tertiary mt-2">{subtitle}</p>
      <Button onClick={hideModal} className="xmd:mt-5 mt-7 w-full">
        {buttonText}
      </Button>
    </div>
  );
}

export default StoreModalBody;
