import { Avatar, CircularProgress, Divider, Theme, useMediaQuery } from "@mui/material";
import { useTranslation } from "react-i18next";
import React, { useEffect } from "react";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { ensureUrlScheme } from "@/utils/helpers";
import { useGetRetailerStoresQuery } from "@/store/apps/retailer";
import { useSelector } from "@/store/hooks";
import { useRetailerStore } from "@/store/zustand/retailerStore/RetailerStore";
import useLanguage from "@/utils/hooks/useLanguage";
import useModal from "@/utils/hooks/useModal";
import { twMerge } from "tailwind-merge";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { TRetailerProfileData } from "@/store/apps/retailer/types";
import Link from "next/link";
import StoreMoreMenu from "./StoreMoreMenu";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import ProfileContentContainer from "@/components/containers/ProfileStepper/ProfileContentContainer";
import SelectWebsite from "@/app/retailer/(dashboard)/store/SelectWebsite";
import useModalStore from "@/store/zustand/modalStore";
import StoreModalBody from "../StoreWarningModalBody";

const StoreList = () => {
  const { t } = useTranslation();
  const [currentLang] = useLanguage();

  const { hideModal, showModal } = useModal();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const retailerProfile = useSelector((state: any) => state?.Retailer?.queries["getRetailerProfile(undefined)"] || {});
  const retailerProfileData = retailerProfile?.data?.data as TRetailerProfileData;
  const activeProfile = retailerProfileData?.status === "Active";

  const handleNotActive = () => {
    showModal({
      body: (
        <StoreModalBody
          title={t("userNotActive.title")}
          subtitle={t("userNotActive.subtitle")}
          buttonText={t("understand")}
        />
      ),
      width: isMobile ? undefined : 428
    });
  };

  const handleSelectWebsite = () => {
    if (!activeProfile) {
      handleNotActive();
      return;
    } else
      showModal({
        body: ({ drawer }) => <SelectWebsite drawer={drawer} />,
        modalProps: {
          showCloseIcon: false
        },
        width: isMobile ? undefined : 450
      });
  };


  const { data: selectedStore, isLoading: isStoreLoading, selectedRetailerStoreId } = useRetailerStore();
  const { data: retailerStoreData, isLoading: isRetailerStoreLoading } = useGetRetailerStoresQuery();

  return (
    <>
      <MobileAppBar title={t("storeInfo")} hasBack />
      <ProfileContentContainer>
        <div className="h-full flex flex-col ">
          <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 xmd:pr-4 xmd:pt-4 xmd:h-full">
            {isStoreLoading || isRetailerStoreLoading ? (
              <div className="flex items-center justify-center py-8">
                <CircularProgress />
              </div>
            ) : !selectedStore?.id ? (
              <div className="flex flex-col gap-4 items-center h-full justify-center">
                <div>
                  <Image src="/images/profile/noStore.png" alt="noStore" width={120} height={120} />
                </div>
                <div>
                  <p className="text-body1-medium text-v2-content-primary text-center">
                    {t("retailer.profile.noStoreTitle")}
                  </p>
                  <p className="text-body4-medium text-v2-content-secondary text-center mt-1">
                    {t("retailer.profile.noStoreSubtitle")}
                  </p>
                </div>
                <CustomButton
                  className="!bg-v2-surface-info !border-v2-surface-info text-v2-content-on-info"
                  onClick={handleSelectWebsite}
                  startIcon={<Icon icon="ic:sharp-plus" className="size-5 text-v2-content-on-info" />}
                >
                  {t("retailer.profile.addStore")}
                </CustomButton>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between mb-4">
                  <span className="text-v2-content-primary text-body3-medium font-bold">
                    {t("retailer.profile.stores")}
                  </span>
                  <CustomButton
                    className="!text-v2-content-on-action-2"
                    color="secondary"
                    onClick={handleSelectWebsite}
                    startIcon={<Icon icon="ic:sharp-plus" className="size-5 text-v2-content-on-action-2" />}
                  >
                    {t("retailer.profile.addNewStore")}
                  </CustomButton>
                </div>

                <div className="grid lg:grid-cols-2 grid-cols-1 items-center gap-3  ">
                  {retailerStoreData?.data
                    // ?.filter(item => item?.id !== selectedRetailerStoreId)
                    ?.map(item => (
                      <div key={item?.id}>
                        <div className="p-4 border border-v2-border-primary rounded-lg relative">
                          <div className="absolute top-2 left-2">
                            <StoreMoreMenu
                              item={item}
                              activeProfile={activeProfile}
                              handleNotActiveUser={handleNotActive}
                              config={selectedStore?.integration?.config}
                              integrationId={item?.integration?.platform?.key}
                            />
                          </div>

                          <div className={twMerge("flex items-center gap-3 ")}>
                            {!!item?.logo && (
                              <Avatar src={item?.logo} alt={"ProfileImg"} className="size-12 rounded-md" />
                            )}

                            <div>
                              <div className="flex gap-2 items-center">
                                <p className="text-gray-999 text-body4-medium whitespace-nowrap leading-1">
                                  {item?.name}
                                </p>
                                <div className="flex items-center gap-2">
                                  {!item?.isActive && (
                                    <div className="px-2 py-0.5 text-[#9A1C13] bg-[#FCE5E4] rounded-full text-caption-medium flex items-center gap-2">
                                      <Icon icon="jam:close-rectangle-f" className="size-4 text-v2-content-on-error" />
                                      <span>{t("deActive")}</span>
                                    </div>
                                  )}
                                  {selectedRetailerStoreId === item?.id && (
                                    <div className="px-2 py-0.5 text-v2-content-on-info-2 bg-v2-surface-info rounded-full text-caption-medium flex items-center gap-2">
                                      <Icon
                                        icon="teenyicons:tick-circle-solid"
                                        className="size-4 text-v2-content-on-info"
                                      />
                                      <span>{t("selected")}</span>
                                    </div>
                                  )}
                                </div>

                                {/* <span className="text-gray-500 leading-[0] text-caption-regular whitespace-nowrap">
                            {item?.integration?.subtitle?.find(item => item.iso === currentLang?.value)?.text}
                          </span> */}
                              </div>
                              <div className="flex items-center w-full gap-2 mt-1">
                                {item?.integration?.platform?.logo && (
                                  <>
                                    <div
                                      className="[&>svg]:size-4"
                                      dangerouslySetInnerHTML={{ __html: item?.integration?.platform?.logo }}
                                    />
                                    <Divider orientation="vertical" flexItem className="bg-v2-border-primary my-1 " />
                                  </>
                                )}

                                <div className="flex items-center gap-1">
                                  {/* <span className="text-caption-medium text-v2-content-on-info">
                                {t("retailer.profile.openWebsite")}
                              </span> */}
                                  <Link
                                    className="text-caption-medium text-v2-content-on-info"
                                    href={ensureUrlScheme(item?.url)}
                                    target="_blank"
                                  >
                                    {t("retailer.profile.openWebsite")}
                                  </Link>
                                  <Icon icon="fluent:ios-arrow-24-filled" className="size-4 text-v2-content-on-info" />
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 mt-4">
                            <div className="rounded-full px-2 text-caption-medium flex items-center gap-1 py-0.5 bg-v2-surface-thertiary text-v2-content-secondary ">
                              <Icon icon="iconoir:chat-bubble" className="size-4" />
                              <span>{t("messages")} : </span>
                              <span>0</span>
                            </div>
                            <div className="rounded-full px-2 text-caption-medium flex items-center gap-1 py-0.5 bg-v2-surface-success-2 text-v2-content-on-success-2">
                              <Icon icon="solar:bag-4-outline" className="size-4" />
                              <span>{t("orders")} : </span>
                              <span>0</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </>
            )}
          </div>
        </div>
      </ProfileContentContainer>
    </>
  );
};

export default StoreList;
