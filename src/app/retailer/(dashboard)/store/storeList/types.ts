import { SetHookFormError } from "@/utils/services/utils";
import { TRetailerBaseFormProps, TRetailerProfile } from "../../profile/types";
import { TRetailerStoreData } from "@/store/apps/retailer/types";

export type TRetailerAddressFormProps = {
  retailerData?: TRetailerProfile;
  onSubmitData: (data: TRetailerStoreData, setFieldError?: SetHookFormError) => void;
  onPrev: (data: TRetailerStoreData) => void;
  isLoading?: boolean;
} & TRetailerBaseFormProps;
