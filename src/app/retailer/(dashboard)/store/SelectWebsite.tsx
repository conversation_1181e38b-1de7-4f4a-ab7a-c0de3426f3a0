import Button from "@/components/ui/Button";
import { LanguageType } from "@/constants/localization";
import { routes } from "@/constants/routes";
import { useGetMetaIntegrationQuery } from "@/store/apps/meta";
import { IntegrationData } from "@/store/apps/meta/types";
import useLanguage from "@/utils/hooks/useLanguage";
import useModal from "@/utils/hooks/useModal";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import { CupertinoPane } from "cupertino-pane";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

interface ISelectWebsiteProps {
  storeId?: string;
  drawer?: CupertinoPane | null;
}

export default function SelectWebsite({ storeId, drawer }: ISelectWebsiteProps) {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { hideModal } = useModal();
  const [currentLang] = useLanguage();
  const router = useRouter();

  const { data: integrations, isLoading } = useGetMetaIntegrationQuery();
  const [selectedId, setSelectedId] = useState("");

  useEffect(() => {
    setTimeout(() => {
      drawer?.calcFitHeight();
    }, 0);
  }, [drawer, isLoading]);

  const path = (id: string) =>
    storeId
      ? `${routes.profile}?settingType=configureStore&integrationId=${id}&id=${id}&type=form&edit={true}`
      : `${routes.profile}?settingType=configureStore&integrationId=${id}&id=${id}&type=form`;

  // if (isLoading) {
  //   return (
  //     <div className="flex items-center justify-center">
  //       <CircularProgress />
  //     </div>
  //   );
  // }

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-v2-content-primary text-body4-medium">{t("accountInfoItem.addWebsite")}</span>
        </div>
        <Icon icon="iconamoon:close-light" className="size-5 cursor-pointer" onClick={hideModal} />
      </div>
      <div className="xmd:min-h-[unset] xmd:pt-0 pt-10 min-h-[150px]">
        {isLoading ? (
          <div className="flex items-center justify-center">
            <CircularProgress />
          </div>
        ) : (
          <div className="bg-cards rounded-lg  ">
            <p className="mt-4 text-body2-medium text-gray-999">{t("accountInfoItem.sellInWebsite")}</p>

            <div className="flex gap-4 flex-col mt-4 ">
              {integrations?.data
                ?.filter(item => item?.key !== "ShopBuilder")
                ?.map(item => (
                  <RenderItem
                    item={item}
                    key={item?.key}
                    selectedId={selectedId}
                    currentLang={currentLang}
                    setSelectedId={setSelectedId}
                  />
                ))}
            </div>

            <div>
              <p className="my-4 text-v2-content-primary text-body2-medium">{t("accountInfoItem.notHaveStore")}</p>
              {integrations?.data
                ?.filter(item => item?.key === "ShopBuilder")
                ?.map(item => (
                  <RenderItem
                    item={item}
                    key={item?.key}
                    selectedId={selectedId}
                    currentLang={currentLang}
                    setSelectedId={setSelectedId}
                  />
                ))}
            </div>

            <div className="flex items-center justify-between mt-4">
              <Button
                onClick={hideModal}
                variant="secondaryGray"
                size="xl"
                startAdornment={<Icon icon="mdi-light:chevron-right" />}
              >
                {t("returnBack")}
              </Button>

              <Button
                onClick={() => {
                  router.push(makePath(path(selectedId)));
                  hideModal();
                }}
                size="xl"
                endAdornment={<Icon icon="mdi-light:chevron-left" />}
              >
                {t("continue")}
              </Button>
            </div>
          </div>
        )}
      </div>
    </>
  );
}

interface IRenderItemProps {
  selectedId: string;
  item: IntegrationData;
  setSelectedId: Dispatch<SetStateAction<string>>;
  currentLang?: LanguageType;
}

const RenderItem = ({ setSelectedId, selectedId, item, currentLang }: IRenderItemProps) => {
  return (
    <div key={item?.key} onClick={() => setSelectedId(item.key)}>
      <div
        className={twMerge(
          "px-4 py-4 flex cursor-pointer items-center gap-2  rounded-lg  border relative",
          selectedId === item?.key ? "border-v2-border-active" : " border-solid border-v2-primary"
        )}
        key={item?.key}
      >
        {/* <Avatar src={item?.logo} className="w-[37px] h-[42px] rounded-sm" /> */}
        {!!item?.logo && (
          <div
            dangerouslySetInnerHTML={{ __html: item?.logo }}
            className="w-[37px] h-[42px] rounded-sm [&>svg]:w-[35px] [&>svg]:h-[35px]"
          />
        )}

        <div className="flex flex-col ">
          <p className="text-v2-content-primary text-body3-medium">
            {item?.name?.find(l => l.iso === currentLang?.value)?.text}
          </p>
          <p className="text-v2-content-tertiary text-caption-medium">
            {item?.subtitle?.find(l => l.iso === currentLang?.value)?.text}
          </p>
        </div>

        <div className="absolute left-4  ">
          {selectedId === item?.key ? (
            <Image src="/images/svgs/user-checked.svg" alt="realUser" width={24} height={24} />
          ) : (
            <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={24} height={24} />
          )}
        </div>
      </div>
    </div>
  );
};
