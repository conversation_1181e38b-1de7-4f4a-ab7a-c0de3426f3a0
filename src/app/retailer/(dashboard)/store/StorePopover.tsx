import React, { useState } from "react";
import { Avatar, Box, CircularProgress, Stack, Theme, useMediaQuery } from "@mui/material";
import { Button } from "@mui/material";
import { Menu } from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { useSelector } from "@/store/hooks";
import { useRetailerStore } from "@/store/zustand/retailerStore/RetailerStore";
import { Icon } from "@iconify/react";
import { t } from "i18next";
import useLanguage from "@/utils/hooks/useLanguage";
import SelectWebsite from "./SelectWebsite";
import useModal from "@/utils/hooks/useModal";
import SelectStorePopover from "./SelectStorePopover";
import { TRetailerProfileData } from "@/store/apps/retailer/types";
import { twMerge } from "tailwind-merge";

import StoreModalBody from "./StoreWarningModalBody";

const StorePopover = () => {
  const [currentLang] = useLanguage();
  const { showModal, hideModal } = useModal();
  const [anchorEl2, setAnchorEl2] = useState(null);
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const retailerProfile = useSelector((state: any) => state?.Retailer?.queries["getRetailerProfile(undefined)"] || {});
  const retailerProfileData = retailerProfile?.data?.data as TRetailerProfileData;
  const activeProfile = retailerProfileData?.status === "Active";

  const handleNotActiveUSer = () => {
    showModal({
      body: (
        <StoreModalBody
          title={t("userNotActive.title")}
          subtitle={t("userNotActive.subtitle")}
          buttonText={t("understand")}
        />
      ),
      width: isMobile ? undefined : 428
    });
  };

  const handleClick = (event: any) => {
    if (!activeProfile) {
      return handleNotActiveUSer();
    } else setAnchorEl2(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl2(null);
  };

  const handleSelectWebsite = () => {
    if (!activeProfile) {
      return handleNotActiveUSer();
    } else
      showModal({
        body: ({ drawer }) => <SelectWebsite drawer={drawer} />,
        modalProps: {
          showCloseIcon: false
        },
        width: isMobile ? undefined : 450
      });
  };


  const { data, isLoading: isStoreLoading } = useRetailerStore();

  if (isStoreLoading) {
    return (
      <div>
        <CircularProgress size={21} />
      </div>
    );
  }

  if (!data?.id) {
    return (
      <button
        onClick={handleSelectWebsite}
        className={twMerge("flex bg-transparent outline-none border-none items-center gap-2 cursor-pointer px-4 ")}
      >
        <Box className="flex justify-center items-center rounded-md bg-purple-50 size-9 text-purple-500">
          <Icon icon="solar:shop-linear" width={21} height={21} />
        </Box>
        <div className="flex flex-col justify-between">
          <span className="text-gray-999 text-body4-medium">{t("noStore")}</span>
          <div className={twMerge("flex items-center gap-1 text-purple-500 ")}>
            <Icon icon="si:add-line" className="size-5" />
            <span className="text-caption-medium">{t("store.add")}</span>
          </div>
        </div>
      </button>
    );
  }

  return (
    <>
      <Button
        size="large"
        aria-label="show 11 new notifications"
        color="inherit"
        variant="contained"
        aria-haspopup="true"
        id="store-popover"
        className="flex bg-gray-20 rounded-[9px] py-2 px-2 !mx-4"
        // sx={{
        //   ...(typeof anchorEl2 === "object" && {
        //     color: "primary.main"
        //   })
        // }}
        onClick={handleClick}
      >
        <Box className="flex items-center gap-14">
          <Box className="flex items-center gap-2">
            <Avatar src={data?.logo} alt={"ProfileImg"} className="size-7 rounded-lg" />

            <Box className="flex flex-col items-start  gap-2">
              <p className="leading-[8px] text-body4-medium text-gray-999">{data?.name}</p>
              <span className="leading-none text-gray-500 text-caption-bold">
                {" "}
                {data?.integration?.subtitle?.find(item => item.iso === currentLang?.value)?.text}
              </span>
            </Box>
          </Box>

          <KeyboardArrowDownIcon htmlColor="#B1A4A4" />
        </Box>
      </Button>
      {/* ------------------------------------------- */}
      {/* Message Dropdown */}
      {/* ------------------------------------------- */}
      {!!anchorEl2 && (
        <Menu
          anchorEl={anchorEl2}
          keepMounted
          id="store-popover"
          open={Boolean(anchorEl2)}
          onClose={handleClose}
          PaperProps={{
            className: "w-[290px] px-0 top-[105px] shadow-md rounded-md"
          }}
        >
          <Box className="p-4 ">
            <SelectStorePopover onclose={handleClose} />
          </Box>
        </Menu>
      )}
    </>
  );
};

export default StorePopover;
