import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { Avatar, CircularProgress, Divider } from "@mui/material";
import clsx from "clsx";
import { useTranslation } from "react-i18next";
import AddStore from "./AddStore";
import { useSelector } from "@/store/hooks";
import { IntegrationDataItem } from "@/store/apps/retailer/types";
import useLanguage from "@/utils/hooks/useLanguage";
import { useRetailerStore } from "@/store/zustand/retailerStore/RetailerStore";

export default function SelectStore() {
  const { t } = useTranslation();
  const [currentLang] = useLanguage();
  const { hideModal } = useModal();


  const { selectedRetailerStoreId, setRetailerStoreId, setRetailerStoreData } = useRetailerStore();

  const retailerStoreSelector = useSelector(
    (state: any) => state?.Retailer?.queries["getRetailerStores(undefined)"] || {}
  );
  const retailerStoreData = retailerStoreSelector?.data as { data: IntegrationDataItem[] };
  const isRetailerStoreLoading = retailerStoreSelector?.status === "pending";

  const selected = (id: string) => id === selectedRetailerStoreId;

  if (isRetailerStoreLoading) {
    return (
      <div className="flex items-center justify-center">
        <CircularProgress />
      </div>
    );
  }

  return (
    <div className="bg-cards rounded-lg ">
      <span className="text-caption-regular text-gray-500">{t("accountInfoItem.selectStore")}</span>

      {retailerStoreData?.data?.map(item => (
        <div
          key={item?.id}
          onClick={() => {
            hideModal();
            setRetailerStoreId(item?.id);
            setRetailerStoreData(item);
            window.location.reload();
          }}
        >
          <div
            className={clsx(
              "flex items-center p-2 gap-2 mt-2 rounded-lg cursor-pointer",
              selected(item?.id) ? "bg-gray-20 border border-solid border-gray-50" : ""
            )}
          >
            {!!item?.logo && <Avatar src={item?.logo} alt={"ProfileImg"} className="size-9 rounded-md" />}
            <div>
              <p className="text-gray-999 text-body4-medium whitespace-nowrap">{item?.name}</p>
              <span className="text-gray-500 leading-2 text-caption-regular whitespace-nowrap">
                {item?.integration?.subtitle?.find(item => item.iso === currentLang?.value)?.text}
              </span>
            </div>
            {selected(item?.id) && (
              <Icon icon="solar:check-circle-bold" width={20} height={20} className="text-purple-500 mr-auto ml-1" />
            )}
          </div>
        </div>
      ))}

      <Divider className="mt-1" />

      <div className="mt-4">
        <AddStore />
      </div>
    </div>
  );
}
