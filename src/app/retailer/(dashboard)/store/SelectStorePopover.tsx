import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { Avatar, CircularProgress, Divider, Tooltip } from "@mui/material";
import clsx from "clsx";
import { useTranslation } from "react-i18next";
import AddStore from "./AddStore";
import { useSelector } from "@/store/hooks";
import { IntegrationDataItem } from "@/store/apps/retailer/types";
import useLanguage from "@/utils/hooks/useLanguage";
import { useRetailerStore } from "@/store/zustand/retailerStore/RetailerStore";
import Link from "next/link";
import useClipboard from "@/utils/hooks/useClipboard";
import { ensureUrlScheme } from "@/utils/helpers";
import { twMerge } from "tailwind-merge";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import DisconnectStoreButton from "./DisconnectStore";
import ConfigureStoreButton from "./ConfigureStoreButton";

interface ISelectStorePopoverProps {
  onclose: () => void;
}

export default function SelectStorePopover({ onclose }: ISelectStorePopoverProps) {
  const { t } = useTranslation();
  const [currentLang] = useLanguage();
  const { hideModal } = useModal();
  const makePath = useRoleBasePath();
  const { copyToClipboard, isCopied } = useClipboard();


  const {
    data: selectedRetailerStoreData,
    selectedRetailerStoreId,
    setRetailerStoreId,
    setRetailerStoreData
  } = useRetailerStore();

  const retailerStoreSelector = useSelector(
    (state: any) => state?.Retailer?.queries["getRetailerStores(undefined)"] || {}
  );
  const retailerStoreData = retailerStoreSelector?.data as { data: IntegrationDataItem[] };
  const isRetailerStoreLoading = retailerStoreSelector?.status === "pending";

  const selected = (id: string) => id === selectedRetailerStoreId;

  if (isRetailerStoreLoading) {
    return (
      <div className="flex items-center justify-center">
        <CircularProgress />
      </div>
    );
  }

  return (
    <div className="bg-cards rounded-lg ">
      {!!selectedRetailerStoreId && (
        <div>
          <span className="text-caption-regular text-gray-600">{t("store.storeLink")}</span>

          {!!selectedRetailerStoreData?.url && (
            <Tooltip title={selectedRetailerStoreData?.url} placement="top-end">
              <div className="flex items-center gap-1 mt-2">
                <Link
                  dir="ltr"
                  className="text-gray-999 text-body4-medium truncate w-52  mr-auto "
                  href={ensureUrlScheme(selectedRetailerStoreData?.url ?? "")}
                  target="_blank"
                >
                  {selectedRetailerStoreData?.url}
                </Link>
                <div className="size-4 flex items-center justify-center">
                  <Icon
                    icon={isCopied ? "flat-color-icons:checkmark" : "icon-park-outline:copy"}
                    className={twMerge(isCopied ? "size-4 mb-1" : "size-3 ", "text-gray-200 cursor-pointer")}
                    onClick={e => {
                      e.stopPropagation();
                      copyToClipboard(selectedRetailerStoreData?.url);
                    }}
                  />
                </div>
              </div>
            </Tooltip>
          )}

          <div className="mt-3 flex flex-col gap-2">
            <ConfigureStoreButton
              href={makePath(
                `${routes.profile}?settingType=configureStore&integrationId=${selectedRetailerStoreData?.integration?.platform?.key}&id=${selectedRetailerStoreData?.id}&type=form&edit={true}`
              )}
              onclose={onclose}
            />

            <DisconnectStoreButton
              config={selectedRetailerStoreData?.integration?.config}
              integrationId={selectedRetailerStoreData?.integration?.platform?.key}
              selectedRetailerStoreId={selectedRetailerStoreId}
              onclose={onclose}
              retailerId={selectedRetailerStoreData?.retailerId ?? ""}
              defaultIsActive={!!selectedRetailerStoreData?.isActive}
            />
          </div>
          {retailerStoreData?.data?.length > 1 && (
            <p className="text-gray-500 mt-2 text-caption-regular">{t("store.changeStore")}</p>
          )}
        </div>
      )}

      {retailerStoreData?.data
        ?.filter(item => item?.id !== selectedRetailerStoreId)
        ?.map(item => (
          <div
            key={item?.id}
            onClick={() => {
              hideModal();
              setRetailerStoreId(item?.id);
              setRetailerStoreData(item);
              onclose();

              setTimeout(() => {
                window.location.reload();
              }, 0);
            }}
          >
            <div>
              <div className={clsx("flex items-center py-1 px-1 gap-2 rounded-lg cursor-pointer")}>
                {!!item?.logo && <Avatar src={item?.logo} alt={"ProfileImg"} className="size-9 rounded-md" />}
                <div>
                  <p className="text-gray-999 text-body4-medium whitespace-nowrap leading-1">{item?.name}</p>
                  <span className="text-gray-500 leading-[0] text-caption-regular whitespace-nowrap">
                    {item?.integration?.subtitle?.find(item => item.iso === currentLang?.value)?.text}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}

      <Divider className="mt-1 border-gray-20" />

      <div className="mt-4">
        <AddStore />
      </div>
    </div>
  );
}
