import { routes } from "@/constants/routes";
import { TRetailerStoreResponse } from "@/store/apps/retailer/types";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import { Dispatch, SetStateAction } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

type ConfigureStoreType = "form" | "invoice";

type TabItem = {
  id: ConfigureStoreType;
  icon: string;
  label: string;
};

interface IStoreTabProps {
  activeTab: ConfigureStoreType;
  setActiveTab: Dispatch<SetStateAction<ConfigureStoreType>>;
  retailerStoreSingle?: TRetailerStoreResponse;
}

function StoreTab({ activeTab, retailerStoreSingle, setActiveTab }: IStoreTabProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const makePath = useRoleBasePath();

  const tabs: TabItem[] = [
    {
      id: "form",
      icon: "solar:info-circle-outline",
      label: t("store.storeInfo")
    },
    {
      id: "invoice",
      icon: "solar:document-outline",
      label: t("store.invoiceBrand")
    }
  ];

  return (
    <div className="flex items-center gap-2  ">
      {tabs?.map(tab => (
        <div
          className={twMerge(
            "flex items-center gap-2 cursor-pointer rounded-full px-2.5 py-2 bg-v2-surface-thertiary text-v2-content-primary",
            activeTab === tab?.id && "bg-v2-surface-action text-v2-surface-primary"
          )}
          key={tab?.id}
          onClick={() => {
            const formPath = makePath(
              `${routes.profile}?settingType=configureStore&integrationId=${retailerStoreSingle?.data?.integration?.platform?.key}&id=${retailerStoreSingle?.data?.id}&type=form&edit={true}`
            );
            const invoicePath = makePath(`${routes.profile}?settingType=configureStore&id=${retailerStoreSingle?.data?.id}&type=invoice&edit={true}`);
            const path = tab?.id === "form" ? formPath : invoicePath;

            setActiveTab(tab.id);
            router.push(path);
          }}
        >
          <Icon icon={tab.icon} className="size-5" />
          <span className="text-body4-medium">{tab?.label}</span>
        </div>
      ))}
    </div>
  );
}

export default StoreTab;
