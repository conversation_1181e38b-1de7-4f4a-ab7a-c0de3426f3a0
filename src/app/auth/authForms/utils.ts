import * as <PERSON><PERSON> from "@sentry/nextjs";

/* --------- Function to save credentials using CredentialsContainer -------- */
export async function saveCredentials(username: string, password: string): Promise<void> {
  if ("credentials" in navigator && "PasswordCredential" in window) {
    try {
      const credential = new window.PasswordCredential({
        id: username,
        password: password,
        name: username
      });

      await navigator.credentials.store(credential);
      // console.info("Credential stored in the user agent's credential manager.");
    } catch (err) {
      console.error("Error while storing the credential: ", err);
      Sentry.captureException(err);
    }
  } else {
    console.error("Credentials API is not supported in this browser.");
    Sentry.captureException(new Error("Credentials API is not supported in this browser."));
  }
}
