.auth-otp-code-input {
  width: 48px;
  height: 48px;
  background-color: transparent;
  border-radius: 6px;
  border: 1px solid transparent;
  background-color: rgb(var(--color-gray-40));
  text-align: center;
  margin-top: 10px;
  color: rgb(var(--color-gray-999));
}

.otp-login-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: rgb(var(--color-gray-400));
  /* margin-top: 10px; */
}

.auth-form-otp-username-wrapper {
  margin-top: 10px;
}

.otp-login-input-container {
  max-width: 300px;
}

.otp-login-input-container > div {
  direction: ltr;
  justify-content: center;
  gap: 12px;
  margin-top: 10px;
}

@media (max-width: 500px) {
  .otp-login-subtitle {
    font-size: 13px;
  }

  .otp-login-input-container > div {
    gap: 8px;
  }

  .auth-form-otp-username-wrapper {
    margin-top: 0;
  }

  .auth-otp-code-input {
    width: 40px;
    height: 40px;
  }
}

.otp-login-username-box {
  background-color: #e6f9fd;
  cursor: pointer;
  border-radius: 4px;
  padding: 2px 6px;
}

.otp-login-username-box-username {
  font-size: 14px;
  font-weight: 500;
  color: #000;

  max-width: 150px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  direction: ltr;
}

.otp-login-input-wrapper {
  align-items: center;
  margin-top: 24px;
}

.otp-login-retry-code {
  color: rgb(var(--color-purple-500));
  font-size: 14px;
  font-weight: 400;
}
