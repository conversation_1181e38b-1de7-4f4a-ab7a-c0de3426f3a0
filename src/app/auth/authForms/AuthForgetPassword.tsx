import "./AuthEmaiLogin.css";

import CustomButton from "@/components/ui/CustomButton/CustomButton";
import PasswordInput from "@/components/ui/inputs/PasswordInput";
import { USER_TYPES } from "@/constants/userTypes";
import { useChangePasswordMutation } from "@/store/apps/auth";
import useModal from "@/utils/hooks/useModal";
import { identifyInput } from "@/utils/identifyInput";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { loginType } from "./auth";
import PasswordChangeSuccess from "./PasswordChangeSuccess";
import { toast } from "@/components/ui/toast";

interface IAuthForgetPasswordProps extends loginType {
  userType: USER_TYPES;
  username: string;
  otpCode: string;
  onBack: () => void;
}

const AuthForgetPassword = ({ username, otpCode, title, subtitle, userType, onBack }: IAuthForgetPasswordProps) => {
  const { t } = useTranslation();
  const { showModal } = useModal();

  const [changePassword, { isLoading }] = useChangePasswordMutation();

  const validationSchema = yup.object({
    password: yup
      .string()
      .test("is-not-farsi", t("register.validation.farsiNotAllowed"), value => {
        if (!value) return true;
        const farsiRegex = /[\u0600-\u06FF\u0750-\u077F]/;
        return !farsiRegex.test(value);
      })
      .min(8, t("register.validation.minCharcter", { min: 8 }))
      .required(t("emailLogin.validation.requiredPassword")),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password"), null], t("register.validation.passwordsMustMatch"))
      .required(t("emailLogin.validation.requiredPassword"))
  });

  const formik = useFormik({
    initialValues: {
      password: "",
      confirmPassword: ""
    },
    validationSchema: validationSchema,
    onSubmit: async (values, { setFieldError }) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { confirmPassword, password } = values;

      const usernameType = identifyInput(username);

      const usernameKey = usernameType === "phone" ? { phone_number: username } : { email: username };

      const body = {
        ...usernameKey,
        userType,
        otp: otpCode,
        new_password: password
      };

      try {
        const res = await changePassword({ body });

        const error = (res as any)?.error?.data;
        const status = (res as any)?.error?.status;

        if (status === 401) {
          // const bodyError = {
          //   ...error,
          //   error_detail: { new_password: ["auth401"] }
          // };
          // clientDefaultErrorHandler({ error: (res as any)?.error, bodyError, setFieldError });
          toast(t("serverErrors.fields.auth401"), { type: "error" });
        } else if ((res as any)?.error) {
          clientDefaultErrorHandler({ error: (res as any)?.error });
          return;
        }

        // if ("error" in res && res.error?.data?.fields.email?.includes("duplicated")) {

        if ("data" in res && res.data) {
          showModal({
            icon: "/images/svgs/password-successfull.svg",
            body: <PasswordChangeSuccess />
          });
        }
      } catch (err: any) {
        clientDefaultErrorHandler({ error: err });
      }
    }
  });

  return (
    <div>
      <div className="flex items-center">
        <Icon
          icon="solar:arrow-right-outline"
          className="size-5 text-v2-content-tertiary cursor-pointer"
          onClick={onBack}
        />

        <p className="text-center text-v2-content-primary text-h5-bold font-semibold flex-1">{title}</p>
      </div>

      {/* <p className="text-center text-body3-medium text-v2-content-tertiary mt-1">{subtitle}</p> */}

      <form onSubmit={formik.handleSubmit}>
        <Stack mt={4} gap={0}>
          <Box>
            <PasswordInput
              id="password"
              label={t("emailLogin.password")}
              placeholder={t("auth.placeholders.password")}
              name="password"
              autoComplete="password4"
              aria-autocomplete="none"
              value={formik.values.password}
              onChange={formik.handleChange}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={formik.touched.password && formik.errors.password}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
          <Box>
            <PasswordInput
              id="confirmPassword"
              label={t("emailLogin.confirmPassword")}
              placeholder={t("auth.placeholders.password")}
              name="confirmPassword"
              autoComplete="confirmPassword4"
              aria-autocomplete="none"
              value={formik.values.confirmPassword}
              onChange={formik.handleChange}
              error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
              helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
        </Stack>
        <Box className="auth-form-submit-button-wrapper">
          <CustomButton color="primary" size="large" fullWidth type="submit" disabled={isLoading}>
            {isLoading ? <CircularProgress color="info" size={26} /> : t("otpLogin.confirm")}
          </CustomButton>
        </Box>
      </form>
    </div>
  );
};

export default AuthForgetPassword;
