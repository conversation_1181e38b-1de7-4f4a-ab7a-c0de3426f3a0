import "./AuthEmaiLogin.css";

import CustomButton from "@/components/ui/CustomButton/CustomButton";
import PasswordInput from "@/components/ui/inputs/PasswordInput";
import { routes } from "@/constants/routes";
import { USER_TYPES } from "@/constants/userTypes";
import { usePostLoginByPasswordMutation } from "@/store/apps/auth";
import { TLoginByPasswordResponse } from "@/store/apps/auth/types";
import useSessionStore from "@/store/zustand/sessionStore";
import { identifyInput } from "@/utils/identifyInput";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { Icon } from "@iconify/react";
import { CircularProgress, Divider } from "@mui/material";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useFormik } from "formik";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { loginType } from "./auth";
import { saveCredentials } from "./utils";
import { toast } from "@/components/ui/toast";

declare module "yup" {
  interface StringSchema {
    identifyInput(errorMessage: string): StringSchema;
  }
}
// Adding a custom validation method to Yup
yup.addMethod(yup.string, "identifyInput", function (errorMessage: string) {
  return this.test("identifyInput", errorMessage, function (value) {
    const { path, createError } = this;

    const result = identifyInput(value as string);
    return result !== "unknown" || createError({ path, message: errorMessage });
  });
});

interface IAuthEmailLoginProps extends loginType {
  userType: USER_TYPES;
  username: string;
  onBack: () => void;
  onChangeMode: () => void;
}

const AuthEmailLogin = ({ username, userType, onBack, onChangeMode }: IAuthEmailLoginProps) => {
  const usernameType = identifyInput(username);
  const usernameKey = usernameType === "phone" ? { phone_number: username } : { email: username };
  const router = useRouter();
  const { t } = useTranslation();
  const { setSession } = useSessionStore();

  const [postLoginByPassword, { isLoading: isPostLoginByOtp }] = usePostLoginByPasswordMutation();

  const validationSchema = yup.object({
    password: yup
      .string()
      // .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})/, t("register.validation.validPassword"))
      .required(t("emailLogin.validation.requiredPassword"))
  });

  const formik = useFormik({
    initialValues: {
      email: username || "",
      password: ""
    },
    validationSchema: validationSchema,
    onSubmit: async (values, { setFieldError }) => {
      const { email, password } = values;
      const usernameKey = usernameType === "phone" ? { phone_number: email } : { email: email };

      const body = {
        ...usernameKey,
        password,
        userType
      };

      try {
        const res: any = await postLoginByPassword({
          body
        });

        const error = (res as any)?.error?.data;

        if ((res as any)?.error?.status === 401) {
          // const bodyError = {
          //   ...error,
          //   error_detail: { password: ["auth401"] }
          // };
          // clientDefaultErrorHandler({ error: (res as any)?.error, bodyError, setFieldError });
          toast(t("serverErrors.fields.wrongPassword"), { type: "error" });
        } else if (error) {
          clientDefaultErrorHandler({ error: (res as any)?.error, bodyError: error, setFieldError });
        }

        if (res?.data) {
          const data = res?.data as TLoginByPasswordResponse;
          setSession({
            access_token: data?.data?.token,
            expires_in: data?.data?.expires_in,
            refresh_token: data?.data?.refresh_token,
            refresh_token_expires_in: data?.data?.refresh_token_expires_in,
            user_id: data?.data?.user_id,
            user_type: userType
          });
          setTimeout(() => {
            router.replace(routes.home);
          }, 0);

          saveCredentials(username, password);
        }
      } catch (err: any) {
        clientDefaultErrorHandler({ error: err });
      }
    }
  });

  return (
    <div>
      {/* <Typography textAlign="center" color="textSecondary" fontSize="20px" fontWeight="700">
        {t("emailLogin.enterPass")}{" "}
      </Typography>

      <Typography
        textAlign="center"
        color="textSecondary"
        className="auth-form-subtitle"
        fontSize="14px"
        fontWeight="500"
      >
      </Typography> */}

      <div className="flex items-center ">
        <Icon
          icon="solar:arrow-right-outline"
          className="size-5 text-v2-content-tertiary cursor-pointer"
          onClick={onBack}
        />
        <p className="text-center text-v2-content-primary text-h5-bold font-semibold flex-1">
          {t("emailLogin.enterPass")}
        </p>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <Stack mt={4}>
          <Box>
            <PasswordInput
              id="password"
              label={t("emailLogin.password")}
              name="password"
              placeholder="••••••••"
              autoComplete="password3"
              aria-autocomplete="none"
              value={formik.values.password}
              onChange={formik.handleChange}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={formik.touched.password && formik.errors.password}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
        </Stack>
        <Box className="auth-form-submit-button-wrapper">
          <CustomButton color="primary" size="large" fullWidth type="submit" disabled={isPostLoginByOtp}>
            {isPostLoginByOtp ? <CircularProgress color="info" size={26} /> : t("emailLogin.buttonText")}
          </CustomButton>
        </Box>

        <div className="flex justify-center items-center mt-8 gap-2">
          <Link
            className="text-v2-content-tertiary text-body3-medium"
            href={process.env.NODE_ENV === "development" ? routes.supplierChangePassword : routes.changePassword}
          >
            {t("emailLogin.forgetPassword")}
          </Link>
          {/* v2-border-primary */}
          <Divider orientation="vertical" variant="middle" flexItem className=" border-v2-border-primary h-4 mt-3" />

          <span className="text-v2-content-tertiary text-body3-medium cursor-pointer" onClick={onChangeMode}>
            {t("emailLogin.loginWithCode")}
          </span>
        </div>
      </form>
    </div>
  );
};

export default AuthEmailLogin;
