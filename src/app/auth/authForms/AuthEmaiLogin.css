.login-input-password-error {
  width: 260px !important;
}

.email-login-divider {
  border-color: rgb(var(--color-gray-200));
  height: 16px;
  margin-top: 6px !important;
}

.email-login-back {
  /* position: absolute; */
  margin-bottom: 20px;
  color: rgb(var(--color-gray-400));
  cursor: pointer;
}

.email-login-back-text {
  font-size: 14px;
  font-weight: 500;
}

.auth-form-wrapper {
  /* border: 1px solid rgb(var(--color-v2-surface-primary)); */
  background: rgb(var(--base-white));
  border-radius: 16px;
  padding: 36px;
  width: 100%;
}

.auth-form-title {
  font-size: 20px;
  font-weight: 700;
  color: #050505;
}

.auth-form-subtitle {
  margin-top: 10px;
  color: rgb(var(--color-gray-400));
}

@media (max-width: 500px) {
  .auth-form-title {
    font-size: 16px;
    font-weight: 700;
  }

  .auth-form-subtitle {
    margin-top: 2px;
  }

  .auth-form-wrapper {
    padding: 24px;
  }

  .auth-form-subtitle-text {
    font-size: 13px;
    cursor: pointer;
  }

  .email-login-divider {
    margin-top: 4px;
  }
}

@media (min-width: 500px) {
  .auth-form-wrapper {
    padding: 36px;
  }

  .auth-form-subtitle-text {
    font-size: 15px;
    cursor: pointer;
  }
}

.auth-form-submit-button-wrapper {
  margin-top: 4px;
}
