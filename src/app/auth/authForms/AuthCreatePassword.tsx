import "./AuthEmaiLogin.css";

import CustomButton from "@/components/ui/CustomButton/CustomButton";
import PasswordInput from "@/components/ui/inputs/PasswordInput";
import { USER_TYPES } from "@/constants/userTypes";
import { useUpdatePasswordMutation } from "@/store/apps/auth";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import clsx from "clsx";
import { useFormik } from "formik";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { loginType } from "./auth";
import { saveCredentials } from "./utils";
import { toast } from "@/components/ui/toast";

interface IAuthCreatePasswordProps extends loginType {
  userType: USER_TYPES;
  username: string;
  onBack?: () => void;
  submitText?: string;
  className?: string;
  onSuccess: () => void;
}

const AuthCreatePassword = ({
  title,
  subtitle,
  userType,
  onBack,
  submitText,
  onSuccess,
  username,
  className
}: IAuthCreatePasswordProps) => {
  const { t } = useTranslation();
  const [updatePassword, { isLoading: isUpdatingPassword }] = useUpdatePasswordMutation();

  const validationSchema = yup.object({
    password: yup
      .string()
      .test("is-not-farsi", t("register.validation.farsiNotAllowed"), value => {
        if (!value) return true;
        const farsiRegex = /[\u0600-\u06FF\u0750-\u077F]/;
        return !farsiRegex.test(value);
      })
      .min(8, t("register.validation.minCharcter", { min: 8 }))
      .required(t("emailLogin.validation.requiredPassword")),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password"), null], t("register.validation.passwordsMustMatch"))
      .required(t("emailLogin.validation.requiredPassword"))
  });

  const formik = useFormik({
    initialValues: {
      password: "",
      confirmPassword: ""
    },
    validationSchema: validationSchema,
    onSubmit: async (values, { setFieldError }) => {
      const { password } = values;

      const body = {
        userType,
        new_password: password,
        old_password: ""
      };

      try {
        const res = await updatePassword({
          body
        });

        const error = (res as any)?.error?.data;
        const status = (res as any)?.error?.status;

        if (status === 401) {
          // const bodyError = {
          //   ...error,
          //   error_detail: { new_password: ["auth401"] }
          // };
          // clientDefaultErrorHandler({ bodyError, setFieldError });
          toast(t("serverErrors.fields.auth401"), { type: "error" });
        } else if ((res as any)?.error) {
          clientDefaultErrorHandler({ error: (res as any)?.error });
        }

        if ("data" in res && res.data) {
          saveCredentials(username, password);
          onSuccess();
        }
      } catch (err: any) {
        clientDefaultErrorHandler({ error: err });
      }
    }
  });

  return (
    <div className={className}>
      <div className="flex items-center">
        <Icon
          icon="solar:arrow-right-outline"
          className="size-5 text-v2-content-tertiary cursor-pointer"
          onClick={onBack}
        />

        <p className="text-center text-v2-content-primary text-h5-bold font-semibold flex-1">{title}</p>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <Stack mt={4} gap={0}>
          <Box>
            <PasswordInput
              id="password"
              label={t("emailLogin.password")}
              placeholder={t("auth.placeholders.password")}
              name="password"
              autoComplete="password1"
              aria-autocomplete="none"
              value={formik.values.password}
              onChange={formik.handleChange}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={formik.touched.password && formik.errors.password}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
          <Box>
            <PasswordInput
              id="confirmPassword"
              label={t("emailLogin.confirmPassword")}
              placeholder={t("auth.placeholders.password")}
              name="confirmPassword"
              autoComplete="confirmPassword2"
              aria-autocomplete="none"
              value={formik.values.confirmPassword}
              onChange={formik.handleChange}
              error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
              helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
        </Stack>
        <Box className="auth-form-submit-button-wrapper">
          <CustomButton color="primary" size="large" fullWidth type="submit" disabled={isUpdatingPassword}>
            {isUpdatingPassword ? <CircularProgress color="info" size={26} /> : submitText || t("otpLogin.confirm")}
          </CustomButton>
        </Box>
      </form>
    </div>
  );
};

export default AuthCreatePassword;
