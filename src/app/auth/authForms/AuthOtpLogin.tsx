import "./AuthOtpLogin.css";

import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import * as yup from "yup";
import { useFormik } from "formik";
import OTPInput from "react-otp-input";
import { CircularProgress, FormHelperText } from "@mui/material";
import { useTranslation } from "react-i18next";
import { USER_TYPES } from "@/constants/userTypes";
import { ReactNode, useEffect, useState } from "react";
import { Icon } from "@iconify/react";
import ErrorIcon from "@mui/icons-material/Error";
import { identifyInput } from "@/utils/identifyInput";
import OTPCountdown from "@/components/containers/OtpCountDown/OtpCountDown";
import { usePostLoginByOtpMutation, useRequestOtpMutation } from "@/store/apps/auth";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { loginType } from "./auth";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import useSessionStore from "@/store/zustand/sessionStore";
import { TLoginByOtpResponse } from "@/store/apps/auth/types";
import { twMerge } from "tailwind-merge";
import { toast } from "@/components/ui/toast";
import Input from "@/components/ui/inputs/Input";
import NumberInput from "@/components/ui/inputs/NumberInput";

interface IAuthEmailLoginProps extends loginType {
  userType: USER_TYPES;
  title: string;
  onBack: () => void;
  onSubmitted: () => void;
  username: string;
  footer?: ReactNode;
  handleLogin?: (value: string) => void;
  setError?: (value: string) => void;
}

const AuthOtpLogin = ({
  username,
  footer,
  setError,
  userType,
  onBack,
  onSubmitted,
  title,
  handleLogin
}: IAuthEmailLoginProps) => {
  const { t } = useTranslation();
  const usernameType = identifyInput(username);
  const { setSession } = useSessionStore();

  const [otpTime, setOtpTime] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [requestOtp, { isLoading: isRequestOtpLoading }] = useRequestOtpMutation();
  const [postLoginByOtp, { isLoading: isPostLoginByOtp }] = usePostLoginByOtpMutation();

  const handleRequestOtp = async (username: string) => {
    const usernameType = identifyInput(username);
    const usernameKey = usernameType === "phone" ? { phone_number: username } : { email: username };
    const requestOtpBody = { ...usernameKey, userType };
    const requestOtpRes = await requestOtp({ body: requestOtpBody });

    const status = (requestOtpRes as any)?.error?.status;

    if (status === 404) {
      setError?.(t("otpLogin.validation.notfound"));
      toast(t("otpLogin.validation.notfound"), { type: "error" });
    } else if ((requestOtpRes as any)?.error) {
      clientDefaultErrorHandler({ error: (requestOtpRes as any)?.error });
    }

    if ("data" in requestOtpRes && requestOtpRes.data) {
      setOtpTime(requestOtpRes.data.data.expires_in);
    }
  };

  useEffect(() => {
    const requestOtp = async () => {
      await handleRequestOtp(username);
    };

    requestOtp();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const validationSchema = yup.object({
    otpCode: yup.string().min(5, t("otpLogin.validation.minOtpCode")).required(t("otpLogin.validation.requiredOtpCode"))
  });

  const formik = useFormik({
    initialValues: {
      otpCode: ""
    },
    validationSchema: validationSchema,
    onSubmit: async (values, { setFieldError }) => {
      const { otpCode } = values;
      setIsSubmitting(true);

      if (handleLogin) {
        handleLogin(otpCode);
      } else {
        const usernameKey = usernameType === "phone" ? { phone_number: username } : { email: username };

        const body = {
          ...usernameKey,
          otp: otpCode,
          userType
        };

        try {
          // await setApiUserContextHeader(userType);

          const res: any = await postLoginByOtp({
            body
          });

          const error = (res as any)?.error?.data;

          if ((res as any)?.error?.status === 401) {
            // const bodyError = {
            //   ...error,
            //   error_detail: { otpCode: ["otp401"] }
            // };
            // clientDefaultErrorHandler({ error: (res as any)?.error, bodyError, setFieldError });
            toast(t("serverErrors.fields.otp401"), { type: "error" });
            setIsSubmitting(false);
          } else if (error) {
            // const bodyError = {
            //   ...error,
            //   error_detail: { otpCode: ["otp401"] }
            // };
            // clientDefaultErrorHandler({ error: (res as any)?.error, bodyError, setFieldError });
            toast(t("serverErrors.fields.otp401"), { type: "error" });
            setIsSubmitting(false);
          }

          if (!!res?.data) {
            const data = res?.data as TLoginByOtpResponse;

            setSession({
              access_token: data?.data?.token,
              expires_in: data?.data?.expires_in,
              refresh_token: data?.data?.refresh_token,
              refresh_token_expires_in: data?.data?.refresh_token_expires_in,
              user_id: data?.data?.user_id,
              user_type: userType
            });

            setTimeout(() => {
              onSubmitted();
            }, 0);
          }
        } catch (err: any) {
          setIsSubmitting(false);

          clientDefaultErrorHandler({ error: err });
        }
      }
    }
  });

  useEffect(() => {
    if (formik.values.otpCode?.length === 5) {
      formik.handleSubmit();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik?.values]);

  return (
    <div>
      <div className="flex items-center">
        <Icon
          icon="solar:arrow-right-outline"
          className="size-5 text-v2-content-tertiary cursor-pointer"
          onClick={onBack}
        />

        <p className="text-center text-v2-content-primary text-h5-bold font-semibold flex-1">{title}</p>
      </div>

      <p className="text-center text-body3-medium text-v2-content-tertiary mt-1">
        {usernameType === "phone" ? t("otpLogin.subtitlePhone") : t("otpLogin.subtitleEmail")}
      </p>

      <div className="flex items-center gap-2.5 bg-v2-surface-info  mt-1.5 w-fit mx-auto py-1 px-2 rounded-[18px]">
        <span className="text-v2-content-primary text-body3-medium">{username}</span>
        <Icon
          icon="hugeicons:edit-02"
          className="text-v2-content-on-action-2 size-3.5 shrink-0 cursor-pointer"
          onClick={onBack}
        />
      </div>

      <form onSubmit={formik.handleSubmit}>
        <Stack className="otp-login-input-wrapper">
          <Box
            className="otp-login-input-container"
            sx={{ maxWidth: "300px" }}
            // onBlur={() => formik.errors.otpCode && formik.setFieldTouched("otpCode", true)}
          >
            <OTPInput
              shouldAutoFocus
              numInputs={5}
              onChange={v => {
                formik.setFieldValue("otpCode", v);
              }}
              value={formik.values.otpCode}
              renderInput={props => (
                <NumberInput
                  {...props}
                  placeholder="-"
                  // className="auth-otp-code-input"
                  className={twMerge(
                    "xmd:w-12 xmd:h-12 w-10 h-10 bg-transparent rounded-lg border border-v2-border-primary text-center text-v2-content-primary text-h4-bold placeholder:text-v2-border-primary",
                    !!formik.touched.otpCode && !!formik.errors.otpCode && "!border-v2-content-on-action-2"
                  )}
                  style={{}}
                />
              )}
            />
            {!!formik.touched.otpCode && !!formik.errors.otpCode && (
              <FormHelperText
                error
                style={{
                  width: "260px"
                }}
              >
                {!!formik.touched.otpCode && !!formik.errors.otpCode ? (
                  <Stack direction="row" alignItems="center" gap={0.5}>
                    <ErrorIcon className="custom-textfield-error-icon" />
                    {formik.errors.otpCode}
                  </Stack>
                ) : (
                  " "
                )}
              </FormHelperText>
            )}
          </Box>
        </Stack>
        <OTPCountdown
          initialTime={otpTime}
          onRetry={() => {
            handleRequestOtp(username);
            setOtpTime(0);
          }}
          isLoading={isRequestOtpLoading}
        />
        <Box className="auth-form-submit-button-wrapper">
          <CustomButton
            color="primary"
            size="large"
            fullWidth
            type="submit"
            disabled={isPostLoginByOtp || isRequestOtpLoading || !formik.isValid || !formik.dirty || isSubmitting}
          >
            {isPostLoginByOtp ? <CircularProgress color="info" size={26} /> : t("otpLogin.confirm")}
          </CustomButton>
        </Box>
      </form>
      {/* {footer} */}
    </div>
  );
};

export default AuthOtpLogin;
