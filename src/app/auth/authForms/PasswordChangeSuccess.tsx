import "./PasswordChangeSuccess.css";

import React from "react";
import { useTranslation } from "react-i18next";
import { Box, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import { routes } from "@/constants/routes";
import useModal from "@/utils/hooks/useModal";
import CustomButton from "@/components/ui/CustomButton/CustomButton";

interface IPasswordChangeSuccessProps {
  isLogin?: boolean;
  redirectPath?: string;
}

const PasswordChangeSuccess = ({ isLogin = true, redirectPath }: IPasswordChangeSuccessProps) => {
  const router = useRouter();
  const { hideModal } = useModal();
  const { t } = useTranslation();

  const loginPath = process.env.NODE_ENV === "development" ? routes.supplierLogin : routes.login;

  return (
    <Box>
      <Box className="Password-change-success-wrapper">
        <Typography className="text-base text-gray-900 font-bold mt-5">{t("auth.changedPassword.text")}</Typography>
        {/* <Typography className="text-[13px] mt-1 font-normal text-gray-400">
        {t("auth.changedPassword.subtext")}
      </Typography> */}
      </Box>
      <CustomButton
        fullWidth
        color="primary"
        className="Password-change-success-button"
        onClick={() => {
          hideModal();
          // router.replace(redirectPath || loginPath);
        }}
      >
        {t("confirm")}
      </CustomButton>
    </Box>
  );
};

export default PasswordChangeSuccess;
