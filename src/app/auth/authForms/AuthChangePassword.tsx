import "./AuthEmaiLogin.css";

import CustomButton from "@/components/ui/CustomButton/CustomButton";
import PasswordInput from "@/components/ui/inputs/PasswordInput";
import { USER_TYPES } from "@/constants/userTypes";
import { useUpdatePasswordMutation } from "@/store/apps/auth";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { CircularProgress } from "@mui/material";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { loginType } from "./auth";
import { saveCredentials } from "./utils";

interface IAuthChangePasswordProps extends loginType {
  userType: USER_TYPES;
  onBack?: () => void;
  submitText?: string;
  className?: string;
  onSuccess: () => void;
  username: string;
}

const AuthChangePassword = ({ userType, onSuccess, username }: IAuthChangePasswordProps) => {
  const { t } = useTranslation();

  const [updatePassword, { isLoading: isUpdatingPassword }] = useUpdatePasswordMutation();

  const validationSchema = yup.object({
    old_password: yup
      .string()
      .test("is-not-farsi", t("register.validation.farsiNotAllowed"), value => {
        if (!value) return true;
        const farsiRegex = /[\u0600-\u06FF\u0750-\u077F]/;
        return !farsiRegex.test(value);
      })
      .min(8, t("register.validation.minCharcter", { min: 8 }))
      .required(t("supplier.profile.validations.requiredField")),
    password: yup
      .string()
      .test("is-not-farsi", t("register.validation.farsiNotAllowed"), value => {
        if (!value) return true;
        const farsiRegex = /[\u0600-\u06FF\u0750-\u077F]/;
        return !farsiRegex.test(value);
      })
      .min(8, t("register.validation.minCharcter", { min: 8 }))
      .required(t("supplier.profile.validations.requiredField")),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password"), null], t("register.validation.passwordsMustMatch"))
      .required(t("supplier.profile.validations.requiredField"))
  });

  const formik = useFormik({
    initialValues: {
      old_password: "",
      password: "",
      confirmPassword: ""
    },
    validationSchema: validationSchema,
    onSubmit: async (values, { setFieldError }) => {
      const { password, old_password } = values;

      const body = {
        userType,
        old_password,
        new_password: password
      };

      try {
        const res = await updatePassword({
          body
        });

        const error = (res as any)?.error?.data;

        if (error) {
          let errorBody = error;
          if (error?.error_detail?.old_password?.includes("invalid value")) {
            errorBody = {
              ...error,
              error_detail: { old_password: ["invalidValue"] }
            };
          }

          clientDefaultErrorHandler({ error: (res as any)?.error, bodyError: errorBody, setFieldError });
        }

        if ("data" in res && res.data) {
          saveCredentials(username, password);
          onSuccess();
        }
      } catch (err: any) {
        clientDefaultErrorHandler({ error: err });
      }
    }
  });

  return (
    <div>
      <form onSubmit={formik.handleSubmit}>
        <Stack mt={4} gap={0}>
          <Box>
            <PasswordInput
              id="old_password"
              label={t("emailLogin.oldPassword")}
              placeholder="••••••••"
              name="old_password"
              autoComplete="oldPassword"
              aria-autocomplete="none"
              value={formik.values.old_password}
              onChange={formik.handleChange}
              error={formik.touched.old_password && Boolean(formik.errors.old_password)}
              helperText={formik.touched.old_password && formik.errors.old_password}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
          <Box>
            <PasswordInput
              id="password"
              label={t("emailLogin.newPassword")}
              placeholder="••••••••"
              name="password"
              autoComplete="newPassword"
              aria-autocomplete="none"
              value={formik.values.password}
              onChange={formik.handleChange}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={formik.touched.password && formik.errors.password}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
          <Box>
            <PasswordInput
              id="confirmPassword"
              label={t("emailLogin.confirmNewPassword")}
              placeholder="••••••••"
              name="confirmPassword"
              autoComplete="confirmNewPassword"
              aria-autocomplete="none"
              value={formik.values.confirmPassword}
              onChange={formik.handleChange}
              error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
              helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
        </Stack>
        <Box className="auth-form-submit-button-wrapper">
          <CustomButton color="primary" size="large" fullWidth type="submit" disabled={isUpdatingPassword}>
            {isUpdatingPassword ? <CircularProgress color="info" size={26} /> : t("emailLogin.changePassword")}
          </CustomButton>
        </Box>
      </form>
    </div>
  );
};

export default AuthChangePassword;
