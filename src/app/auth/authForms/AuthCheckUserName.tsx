import "./AuthEmaiLogin.css";

import CustomButton from "@/components/ui/CustomButton/CustomButton";
import Input from "@/components/ui/inputs/Input";
import { USER_TYPES } from "@/constants/userTypes";
import { useRequestOtpMutation } from "@/store/apps/auth";
import { identifyInput } from "@/utils/identifyInput";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { loginType } from "./auth";

declare module "yup" {
  interface StringSchema {
    identifyInput(errorMessage: string): StringSchema;
  }
}

interface ICheckUsernameProps extends loginType {
  buttonText?: string;
  error: string;
  userType: USER_TYPES;
  onChangeUsername: (value: string) => void;
  setError: (value: string) => void;
  onBack?: () => void;
}

const AuthCheckUserName = ({
  title,
  error,
  subtitle,
  setError,
  footer,
  onBack,
  userType,
  buttonText,
  onChangeUsername
}: ICheckUsernameProps) => {
  const { t } = useTranslation();
  const [requestOtp, { isLoading }] = useRequestOtpMutation();

  const handlePhoneLogin = async (username: string, setFieldError: any) => {
    const body = {
      phone_number: username,
      userType
    };

    try {
      const res = await requestOtp({ body });

      const error = (res as any)?.error?.data;

      if ((res as any)?.error?.status === 401) {
        const bodyError = {
          ...error,
          error_detail: { username: ["auth401"] }
        };

        clientDefaultErrorHandler({ error: (res as any)?.error, bodyError, setFieldError });
      } else if ((res as any)?.error?.status === 400) {
        const bodyError = {
          ...error,
          error_detail: { username: ["phoneNumber"] }
        };
        clientDefaultErrorHandler({ error: (res as any)?.error, bodyError, setFieldError });
        return;
      } else if ("error" in res && res.error?.status === 404) {
        const notfoundError = {
          ...error,
          error_detail: { username: ["notfound"] }
        };

        clientDefaultErrorHandler({ error: (res as any)?.error, bodyError: notfoundError, setFieldError });
        return;
      } else if ((res as any)?.error) {
        clientDefaultErrorHandler({ error: (res as any)?.error });
        return;
      } else if ("data" in res && res.data) {
        onChangeUsername(username);
      }
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  // Adding a custom validation method to Yup
  yup.addMethod(yup.string, "identifyInput", function (errorMessage: string) {
    return this.test("identifyInput", errorMessage, function (value) {
      const { path, createError } = this;

      const result = identifyInput(value as string);
      // If the result is "Unknown", return a custom error message
      return result !== "unknown" || createError({ path, message: errorMessage });
    });
  });

  const validationSchema = yup.object({
    username: yup
      .string()
      .required(t("checkUsername.validation.requiredUsername"))
      .identifyInput(t("checkUsername.validation.identifyUsername"))
  });
  const formik = useFormik({
    initialValues: {
      username: ""
    },
    validationSchema: validationSchema,
    onSubmit: async values => {
      onChangeUsername(values?.username);
    }
  });

  // useEffect(() => {
  //   if (error) {
  //     setTimeout(() => {
  //       formik.setFieldError("username", error);
  //     }, 0);
  //     formik.setFieldTouched("username", true);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [error]);

  return (
    <div>
      <div className="flex items-center">
        {!!onBack && (
          <Icon
            icon="solar:arrow-right-outline"
            className="size-5 text-v2-content-tertiary cursor-pointer"
            onClick={onBack}
          />
        )}
        <p className="text-center text-v2-content-primary text-h5-bold font-semibold flex-1">{title}</p>
      </div>
      <p className="text-center text-body3-medium text-v2-content-tertiary mt-2">{subtitle}</p>

      <form onSubmit={formik.handleSubmit}>
        <Stack mt={4}>
          <Box>
            <Input
              onChange={e => {
                if (error) {
                  setError("");
                  onChangeUsername("");
                }
                formik.setFieldValue("username", e?.target?.value);
              }}
              onBlur={formik.handleBlur}
              label={t("checkUsername.username")}
              placeholder={t("auth.placeholders.email")}
              id="username"
              name="username"
              requiredStar={false}
              value={formik.values.username}
              error={formik?.touched?.username && Boolean(formik?.errors?.username)}
              helperText={(formik?.touched?.username && formik?.errors?.username) || undefined}
              hasEmptySpace
            />
          </Box>
        </Stack>
        <Box className="auth-form-submit-button-wrapper">
          <CustomButton color="primary" size="large" fullWidth type="submit" disabled={isLoading}>
            {isLoading ? <CircularProgress color="info" size={26} /> : buttonText || t("checkUsername.buttonText")}
          </CustomButton>
        </Box>
        {footer}
      </form>
    </div>
  );
};

export default AuthCheckUserName;
