import "./AuthEmaiLogin.css";

import CustomButton from "@/components/ui/CustomButton/CustomButton";
import Input from "@/components/ui/inputs/Input";
import { routes } from "@/constants/routes/index";
import { USER_TYPES } from "@/constants/userTypes";
import { useRegisterMutation } from "@/store/apps/auth";
import { SnakeToCamelFieldErrorWrapper } from "@/utils/helpers";
import { identifyInput } from "@/utils/identifyInput";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { CircularProgress } from "@mui/material";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useFormik } from "formik";
import Link from "next/link";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { loginType } from "./auth";
import { toast } from "@/components/ui/toast";

interface IRegisterProps extends loginType {
  userType: USER_TYPES;
  onChangeUsername: (value: string) => void;
}

const AuthRegister = ({ userType, onChangeUsername }: IRegisterProps) => {
  const { t } = useTranslation();
  const [register, { isLoading: isRegisterLoading }] = useRegisterMutation();

  const isLoading = isRegisterLoading;

  yup.addMethod(yup.string, "identifyInput", function (errorMessage: string) {
    return this.test("identifyInput", errorMessage, function (value) {
      const { path, createError } = this;

      const result = identifyInput(value as string);
      // If the result is "Unknown", return a custom error message
      return result !== "unknown" || createError({ path, message: errorMessage });
    });
  });

  const validationSchema = yup.object({
    firstName: yup
      .string()
      .required(t("checkUsername.validation.requiredUsername"))
      .min(3, t("supplier.profile.validations.min3")),
    lastName: yup
      .string()
      .required(t("checkUsername.validation.requiredUsername"))
      .min(3, t("supplier.profile.validations.min3")),
    username: yup
      .string()
      .required(t("checkUsername.validation.requiredUsername"))
      .identifyInput(t("checkUsername.validation.identifyUsername"))
  });
  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      username: ""
    },
    validationSchema: validationSchema,
    validateOnChange: false,
    validateOnBlur: false,
    onSubmit: async (values, { setFieldError }) => {
      const username = values?.username;
      const usernameType = identifyInput(username);

      const usernameKey = usernameType === "phone" ? { phone_number: username } : { email: username };

      const registerBody = {
        ...usernameKey,
        userType,
        first_name: values?.firstName,
        last_name: values?.lastName
      };

      try {
        const res = await register({ body: registerBody });

        const error = (res as any)?.error?.data;
        const status = (res as any)?.error?.status;

        if (status === 401) {
          // const bodyError = {
          //   ...error,
          //   error_detail: { firstName: ["auth401"], lastName: ["auth401"], username: ["auth401"] }
          // };
          // clientDefaultErrorHandler({ bodyError, setFieldError });
          toast(t("serverErrors.fields.auth401"), { type: "error" });
        } else if (error) {
          let badRequestErr = error;
          if (badRequestErr.error_detail.email?.length) {
            // badRequestErr = {
            //   error: error?.error,
            //   error_detail: {
            //     username: registerAuthError(error?.error_detail?.email)
            //   }
            // };
            // clientDefaultErrorHandler({ bodyError: badRequestErr, setFieldError });
            toast(t("serverErrors.fields.duplicatedEmail"), { type: "error" });
          } else if (badRequestErr.error_detail.phone_number?.length) {
            if (badRequestErr.error_detail.phone_number[0]?.includes("IR")) {
              toast(t("serverErrors.fields.invalidPhoneNumber"), { type: "error" });
            }
            // badRequestErr = {
            //   error: error?.error,
            //   error_detail: {
            //     username: registerPhoneError(error?.error_detail?.phone_number)
            //   }
            // };
            // clientDefaultErrorHandler({ bodyError: badRequestErr, setFieldError });
            else toast(t("serverErrors.fields.duplicatedPhone"), { type: "error" });
          } else {
            clientDefaultErrorHandler({
              error: (res as any)?.error,
              bodyError: error,
              setFieldError: SnakeToCamelFieldErrorWrapper(setFieldError)
            });
          }
        } else if ("data" in res && res.data) {
          onChangeUsername(username);
        }
      } catch (err: any) {
        clientDefaultErrorHandler({ error: err });
      }
    }
  });

  return (
    <div>
      {/* <Stack className="email-login-back" alignItems="center" gap="8px" onClick={() => router.back()} direction="row">
        <Icon icon="fluent-mdl2:forward" fontSize={15} />
        <Typography className="email-login-back-text">{t("emailLogin.back")}</Typography>
      </Stack> */}
      <div className="flex flex-col items-center">
        <p className="text-center text-v2-content-primary text-h5-bold font-semibold">{t("register.title")}</p>
        <p className="text-center text-body3-medium text-v2-content-tertiary mt-2">{t("register.subtitle")}</p>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <div className="xmd:mt-8 mt-2 flex flex-col ">
          <Box>
            <Input
              label={t("register.firstName")}
              placeholder={t("auth.placeholders.firstName")}
              id="firstName"
              name="firstName"
              value={formik.values.firstName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.firstName && Boolean(formik.errors.firstName)}
              helperText={formik.touched.firstName && formik.errors.firstName}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
          <Box>
            <Input
              label={t("register.lastName")}
              placeholder={t("auth.placeholders.lastName")}
              id="lastName"
              name="lastName"
              value={formik.values.lastName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.lastName && Boolean(formik.errors.lastName)}
              helperText={formik.touched.lastName && formik.errors.lastName}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
          <Box>
            <Input
              onChange={e => formik.setFieldValue("username", e.target.value)}
              onBlur={formik.handleBlur}
              label={t("register.username")}
              placeholder={t("auth.placeholders.email")}
              id="username"
              name="username"
              value={formik.values.username}
              error={formik.touched.username && Boolean(formik.errors.username)}
              helperText={formik.touched.username && formik.errors.username}
              requiredStar={false}
              hasEmptySpace
            />
          </Box>
        </div>
        <Box className="auth-form-submit-button-wrapper">
          <CustomButton color="primary" size="large" fullWidth type="submit" disabled={isLoading}>
            {isLoading ? <CircularProgress color="info" size={26} /> : t("checkUsername.buttonText")}
          </CustomButton>
        </Box>
        <div className="flex items-center gap-1 justify-center mt-8">
          <span className="text-body3-medium text-v2-content-tertiary">{t("register.hasAccount")}</span>
          <Link
            href={process.env.NODE_ENV === "development" ? routes.supplierLogin : routes.login}
            className="text-body3-medium text-v2-content-on-action-2"
          >
            {t("register.enterAccount")}
          </Link>
        </div>
      </form>
    </div>
  );
};

export default AuthRegister;

// const registerAuthError = (error: string[]) => {
//   if (error.at(0) === "duplicated") {
//     return ["duplicatedEmail"];
//   }
//   return error;
// };

// const registerPhoneError = (error: string[]) => {
//   if (error.at(0) === "duplicated") {
//     return ["duplicatedPhone"];
//   }
//   return error;
// };
