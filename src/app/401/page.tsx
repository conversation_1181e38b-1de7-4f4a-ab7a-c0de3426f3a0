"use client";
import { Grid, Typography } from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";

export default function UnauthorizedPage() {
  const { t } = useTranslation();
  return (
    <>
      <Link href="/">
        <Grid container direction="column" p={4} alignItems="center" justifyContent="center">
          <Typography variant="h2">{t("companyTitle")}</Typography>
          <Typography color="error" variant="body1">
            {t("errors.unauthorized")}
          </Typography>
          <Image src="https://dev-logos.s3.amazonaws.com/401.webp" width={500} height={500} alt="unauthorized" />
          {t("subscription.home")}
        </Grid>
      </Link>
    </>
  );
}
