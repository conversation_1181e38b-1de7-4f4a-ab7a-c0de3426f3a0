import { create } from "zustand";
import Cookies from "js-cookie";
import { STORE_ID } from "@/constants/cookies";
import { IntegrationDataItem } from "../../apps/retailer/types";

export interface RetailerStoreState {
  selectedRetailerStoreId: string | null;
  data: IntegrationDataItem | null;
  isError: boolean;
  isLoading: boolean;
}

interface RetailerStoreActions {
  setRetailerStoreId: (id: string) => void;
  getRetailerStoreId: () => string | null;
  setRetailerStoreData: (data: IntegrationDataItem | null) => void;
  setIsError: (isError: boolean) => void;
  setIsLoading: (isLoading: boolean) => void;
  clearRetailerStore: () => void;
}

type RetailerStore = RetailerStoreState & RetailerStoreActions;

export const useRetailerStore = create<RetailerStore>(set => ({
  /* ------------- Initial State (retrieving from cookie) ------------ */
  selectedRetailerStoreId: Cookies.get(STORE_ID) || null,
  data: null,
  isError: false,
  isLoading: false,

  /* -------------------------- Actions -------------------------- */
  setRetailerStoreId: (id: string) => {
    /* ----------------- Set the store ID to cookie and state ----------------- */
    Cookies.set(STORE_ID, id);
    set({ selectedRetailerStoreId: id });
  },

  getRetailerStoreId: () => {
    /* ------------------ Retrieve store ID from cookie ----------------- */
    const id = Cookies.get(STORE_ID);
    if (id) {
      set({ selectedRetailerStoreId: id });
    }
    return id || null;
  },

  setRetailerStoreData: (data: IntegrationDataItem | null) => {
    set({ data });
  },

  setIsError: (isError: boolean) => {
    set({ isError });
  },

  setIsLoading: (isLoading: boolean) => {
    set({ isLoading });
  },

  clearRetailerStore: () => {
    /* -------------- Remove the cookie and reset state -------------- */
    Cookies.remove(STORE_ID);
    set({
      selectedRetailerStoreId: null,
      data: null,
      isError: false,
      isLoading: false
    });
  }
}));
