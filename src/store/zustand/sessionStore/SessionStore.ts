import { create } from "zustand";
import Cookies from "js-cookie";
import { SESSION_DATA } from "@/constants/cookies";
import { SessionData, SessionStore } from "./types";

const useSessionStore = create<SessionStore>(set => ({
  /* ------------- Initial State (retrieving from a single cookie) ------------ */
  ...JSON.parse(Cookies.get(SESSION_DATA) || "{}"),

  setSession: (sessionData: SessionData) => {
    /* ---------------- Merge existing session data with new data --------------- */
    const currentData: SessionData = JSON.parse(Cookies.get(SESSION_DATA) || "{}");
    const updatedData: SessionData = {
      ...currentData,
      ...sessionData,
      initial_date_created: currentData.initial_date_created || new Date().toISOString(),
      date_created: new Date().toISOString()
    };

    /* ----------------- Set the session data to a single cookie ---------------- */
    Cookies.set(SESSION_DATA, JSON.stringify(updatedData));

    // Update Zustand state
    set(updatedData);
  },

  clearSession: () => {
    /* ---------------- Remove the cookie and reset Zustand state --------------- */
    Cookies.remove(SESSION_DATA);
    set({
      user_id: null,
      user_type: null,
      access_token: null,
      refresh_token: null,
      refresh_token_expires_in: null,
      expires_in: null,
      initial_date_created: null,
      date_created: null
    });
  },

  getSession: (): SessionData | null => {
    /* ------------------ Retrieve session data from the cookie ----------------- */
    const sessionData = Cookies.get(SESSION_DATA);
    return sessionData ? JSON.parse(sessionData) : null;
  }
}));

export default useSessionStore;
