import { USERTYPES } from "@/constants/userTypes";

export interface SessionData {
  user_id?: string | null;
  user_type: USERTYPES | null;
  access_token?: string | null;
  refresh_token?: string | null;
  refresh_token_expires_in?: number | null;
  expires_in?: number | null;
  initial_date_created?: string | null;
  date_created?: string | null;
}

/* ------------------- Type for Zustand state and actions ------------------- */
export interface SessionStore extends SessionData {
  setSession: (sessionData: SessionData) => void;
  clearSession: () => void;
  getSession: () => SessionData | null;
}
