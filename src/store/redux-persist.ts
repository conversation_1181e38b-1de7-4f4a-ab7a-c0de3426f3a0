// redux-persist.ts
import storage from "redux-persist/lib/storage";
import { PersistConfig, Transform } from "redux-persist";
import { AppState } from "./store"; // Adjust the import path as needed

// Transform for MetaWithPersist API cache
const metaWithPersistTransform: Transform<any, any, any, any> = {
  in: (inboundState: any, key: string | number | symbol) => {
    if (key === "MetaWithPersist") {
      // Only persist fulfilled query results with their data
      const transformedState = {
        ...inboundState,
        queries: Object.entries(inboundState?.queries || {}).reduce((acc, [queryKey, queryState]: [string, any]) => {
          // Only persist successful queries with data
          if (queryState?.status === "fulfilled" && queryState?.data) {
            acc[queryKey] = {
              status: queryState.status,
              endpointName: queryState.endpointName,
              requestId: queryState.requestId,
              data: queryState.data,
              fulfilledTimeStamp: queryState.fulfilledTimeStamp,
              // Include originalArgs for cache matching
              originalArgs: queryState.originalArgs
            };
          }
          return acc;
        }, {} as any),
        // Don't persist mutations, subscriptions, or provided tags
        mutations: {},
        subscriptions: {},
        provided: {}
      };
      return transformedState;
    }
    return inboundState;
  },
  out: (outboundState: any, key: string | number | symbol) => {
    if (key === "MetaWithPersist") {
      // Restore the persisted state with proper RTK Query structure
      return {
        ...outboundState,
        // Add default RTK Query config
        config: {
          online: true,
          focused: true,
          middlewareRegistered: true,
          refetchOnFocus: false,
          refetchOnReconnect: false,
          refetchOnMountOrArgChange: false,
          keepUnusedDataFor: 60,
          reducerPath: "MetaWithPersist"
        },
        // Initialize empty collections for runtime
        mutations: {},
        provided: {},
        subscriptions: {},
        // Keep the persisted queries
        queries: outboundState?.queries || {}
      };
    }
    return outboundState;
  }
};

// Main persist configuration
export const persistConfig: PersistConfig<AppState> = {
  key: "root",
  storage,
  version: 1, // Increment this when making breaking changes

  // Whitelist - only persist these slices
  whitelist: [
    "chatReducer", // Chat messages
    "config", // App configuration
    "retailerStore", // Retailer store data
    "MetaWithPersist" // Meta data that should persist
  ],

  // Blacklist - never persist these slices (they should refresh on app load)
  blacklist: [
    "Auth", // Auth tokens should not persist for security
    "Supplier", // Real-time supplier data
    "Retailer", // Real-time retailer data
    "Meta", // Non-persistent meta data
    "RetailerProduct", // Product data that should be fresh
    "Product", // Product data that should be fresh
    "Order", // Order data that should be fresh
    "SupplierDashboard", // Dashboard data should be fresh
    "Conversation" // Conversation data might be sensitive
  ],

  // Transforms to handle special cases
  transforms: [metaWithPersistTransform],

  // Migration function for version updates
  migrate: (state: any, currentVersion: number) => {
    if (currentVersion === 0) {
      // Migration from version 0 to 1
      // Add any migration logic here if needed
    }
    return Promise.resolve(state);
  },

  // Debug mode (disable in production)
  debug: process.env.NODE_ENV === "development",

  // Timeout for persist operations
  timeout: 10000,

  // Throttle persist operations
  throttle: 100
};
