import { createApi } from "@reduxjs/toolkit/query/react";

import { rtkBaseQuery } from "@/utils/services";
import {
  TSupplierDocumentsDeleteBody,
  TSupplierDocumentsPostBody,
  TSupplierDocumentsResponse,
  TSupplierIntegrationBody,
  TSupplierIntegrationResponse,
  TSupplierIntegrationStateBody,
  TSupplierProfileBody,
  TSupplierProfileResponse,
  TSupplierReturnBody,
  TSupplierReturnResponse,
  TSupplierShippingBody,
  TSupplierShippingResponse
} from "./types";
import {
  SUPPLIER_CHAT,
  SUPPLIER_CHATS,
  SUPPLIER_DOCUMENTS_KEY,
  SUPPLIER_GENERAL_KEY,
  SUPPLIER_INFO_KEY,
  SUPPLIER_INTEGRATION_KEY,
  SUPPLIER_PROFILE_KEY,
  SUPPLIER_RETURN_KEY,
  SUPPLIER_SHIPPING_KEY
} from "@/constants/queryKeys";
import { profileApiRoutes } from "@/constants/apiRoutes/apiServiceRoutes";
import { convertCamelToSnake } from "@/utils/helpers/objectHelper";
import { method } from "lodash";

export const Supplier = createApi({
  reducerPath: "Supplier",
  tagTypes: [
    SUPPLIER_GENERAL_KEY,
    SUPPLIER_INFO_KEY,
    SUPPLIER_SHIPPING_KEY,
    SUPPLIER_RETURN_KEY,
    SUPPLIER_DOCUMENTS_KEY,
    SUPPLIER_CHATS,
    SUPPLIER_CHAT,
    SUPPLIER_PROFILE_KEY,
    SUPPLIER_INTEGRATION_KEY
  ],
  baseQuery: rtkBaseQuery,
  keepUnusedDataFor: 0,
  endpoints: builder => ({
    /* -------------------------------- supplier -------------------------------- */
    getSupplierProfile: builder.query<TSupplierProfileResponse, void>({
      query: () => ({
        url: profileApiRoutes.supplierProfile
      }),
      providesTags: [SUPPLIER_PROFILE_KEY]
    }),
    postSupplierProfile: builder.mutation<TSupplierProfileResponse, TSupplierProfileBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.supplierProfile,
        method: "post",
        data: {
          ...convertCamelToSnake(body),
          address: {
            ...convertCamelToSnake(body)?.address,
            address1: body?.address?.address1,
            address2: body?.address?.address2
          },
          warehouse_address: {
            ...convertCamelToSnake(body)?.warehouse_address,
            address1: body?.warehouseAddress?.address1,
            address2: body?.warehouseAddress?.address2
          }
        }
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_PROFILE_KEY])
    }),
    putSupplierProfile: builder.mutation<TSupplierProfileResponse, TSupplierProfileBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.supplierProfile,
        method: "put",
        data: {
          ...convertCamelToSnake(body),
          address: {
            ...convertCamelToSnake(body)?.address,
            address1: body?.address?.address1,
            address2: body?.address?.address2
          },
          warehouse_address: {
            ...convertCamelToSnake(body)?.warehouse_address,
            address1: body?.warehouseAddress?.address1,
            address2: body?.warehouseAddress?.address2
          }
        }
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_PROFILE_KEY])
    }),
    /* ---------------------------- supplier shipping --------------------------- */
    postSupplierShipping: builder.mutation<TSupplierShippingResponse, TSupplierShippingBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.shippingPolicy,
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_SHIPPING_KEY])
    }),
    putSupplierShipping: builder.mutation<TSupplierShippingResponse, TSupplierShippingBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.shippingPolicy,
        method: "put",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_SHIPPING_KEY])
    }),
    getSupplierShipping: builder.query<TSupplierShippingResponse, { productId?: string }>({
      query: ({ productId }) => ({
        url: profileApiRoutes.shippingPolicy,
        params: { pid: productId }
      }),
      providesTags: [SUPPLIER_SHIPPING_KEY],
      transformResponse: (responseData: TSupplierShippingResponse) => {
        return {
          ...responseData,
          data: responseData?.data?.map(item => ({
            ...item
          }))
        };
      }
    }),
    /* ----------------------------- supplier return ---------------------------- */
    postSupplierReturn: builder.mutation<TSupplierReturnResponse, TSupplierReturnBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.returnPolicy,
        method: "post",
        data: {
          ...convertCamelToSnake(body),
          address: {
            ...convertCamelToSnake(body)?.address,
            address1: body?.address?.address1,
            address2: body?.address?.address2
          }
        }
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_RETURN_KEY])
    }),
    putSupplierReturn: builder.mutation<TSupplierReturnResponse, TSupplierReturnBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.returnPolicy,
        method: "put",
        data: {
          ...convertCamelToSnake(body),
          address: {
            ...convertCamelToSnake(body)?.address,
            address1: body?.address?.address1,
            address2: body?.address?.address2
          }
        }
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_RETURN_KEY])
    }),
    getSupplierReturn: builder.query<TSupplierReturnResponse, { productId?: string }>({
      query: ({ productId }) => ({
        url: profileApiRoutes.returnPolicy,
        params: { pid: productId }
      }),
      providesTags: [SUPPLIER_RETURN_KEY]
    }),
    /* --------------------------- supplier documents --------------------------- */
    postSupplierDocuments: builder.mutation<TSupplierDocumentsResponse, TSupplierDocumentsPostBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.supplierDocuments,
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_DOCUMENTS_KEY])
    }),
    deleteSupplierDocuments: builder.mutation<TSupplierDocumentsResponse, TSupplierDocumentsDeleteBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.supplierDocuments,
        method: "delete",
        data: convertCamelToSnake(body)
      })
      // invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_DOCUMENTS_KEY])
    }),
    getSupplierDocuments: builder.query<TSupplierDocumentsResponse, void>({
      query: () => ({
        url: profileApiRoutes.supplierDocuments
      }),
      providesTags: [SUPPLIER_DOCUMENTS_KEY]
    }),
    supplierSignContract: builder.mutation<
      {
        data: {};
        status: string;
      },
      void
    >({
      query: () => ({
        url: profileApiRoutes.supplierSignContract,
        method: "get"
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_PROFILE_KEY])
    }),
    getSupplierStore: builder.query<TSupplierIntegrationResponse, void>({
      query: () => ({
        url: profileApiRoutes.supplierStore
      }),
      providesTags: [SUPPLIER_INTEGRATION_KEY]
    }),
    postSupplierStore: builder.mutation<TSupplierIntegrationResponse, TSupplierIntegrationBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.supplierStore,
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_INTEGRATION_KEY])
    }),
    putSupplierStore: builder.mutation<TSupplierIntegrationResponse, TSupplierIntegrationBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.supplierStore,
        method: "put",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_INTEGRATION_KEY])
    }),
    postSupplierStoreState: builder.mutation<TSupplierIntegrationResponse, TSupplierIntegrationStateBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.supplierStoreStateV2,
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [SUPPLIER_INTEGRATION_KEY])
    })
  })
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  usePostSupplierDocumentsMutation,
  useGetSupplierDocumentsQuery,
  useDeleteSupplierDocumentsMutation,
  useGetSupplierProfileQuery,
  usePostSupplierProfileMutation,
  usePutSupplierProfileMutation,
  useGetSupplierReturnQuery,
  usePutSupplierReturnMutation,
  usePostSupplierReturnMutation,
  useGetSupplierShippingQuery,
  usePostSupplierShippingMutation,
  usePutSupplierShippingMutation,
  useSupplierSignContractMutation,
  useGetSupplierStoreQuery,
  usePostSupplierStoreMutation,
  usePutSupplierStoreMutation,
  usePostSupplierStoreStateMutation
} = Supplier;
