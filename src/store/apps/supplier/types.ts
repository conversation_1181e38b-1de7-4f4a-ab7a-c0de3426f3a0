import { Platform } from "../retailer/types";

export type TSupplierData = {
  address: {
    address1: string;
    address2?: string;
    city?: number;
    company: string;
    countryId?: number;
    phoneNumber?: string;
    state?: number;
    zip: string;
  };
  vatNumber?: string;
  contactEmail: string;
  contactNumber: string;
  currency?: string;
  defaultCategory?: number;
  id?: number;
  biography?: string;
  isAllowedCustomizable?: boolean;
  logo?: string;
  name: string;
  website?: string;
  processingTime: "1_2_DAYS" | "2_3_DAYS" | "3_5_DAYS" | "5_7_DAYS" | "7_10_DAYS" | "MORE_7DAYS" | null;
  productSources: number[];
  rejectionNote?: string;
  sellingPlatforms: number[];
  signatories?: string[];
  skuCategories: number[];
  status?: "Pending" | "InReview" | "Approved" | "Rejected" | "Disabled";
  warehouseAddress: {
    address1?: string;
    address2?: string;
    city?: number;
    company?: string;
    countryId?: number;
    phoneNumber?: string;
    state?: number;
    zip?: string;
  };
};

export type TSupplierShippingPayload = {
  excluded?: boolean;
  extra_item_rate?: string;
  prepaid?: boolean;
  rate?: string;
  description?: string;
  rate_type?: TSupplierRate;
  shipping_carrier?: string;
  shipping_time?: {
    max: number;
    min: number;
  };
  shipping_to?: string;
  supplier_id?: string;
};

export type TSupplierReturnPayload = {
  address?: {
    address1?: string;
    address2?: string;
    city?: number;
    company?: string;
    location_id?: string;
    phone_number?: string;
    state?: number;
    zip?: string;
  };
  description?: string;
  is_allowed?: boolean;
  product_id?: string;
  shipping_payer?: "Supplier" | "Customer";
  supplier_id?: string;
  window_time?: {
    max: number;
    min: number;
  };
};

export type TSupplierBody = {
  body: TSupplierData;
};
export type TSupplierLogoBody = {
  body: FormData;
};

export type TSupplierResponse = {
  data: TSupplierData;
  meta: string;
  status: boolean;
};

export type TSupplierInfoData = {
  birthDay?: string;
  companyName?: string;
  companyType?: string;
  economicCode?: string;
  isLegalPerson?: boolean;
  nationalCode?: string;
  registrationNumber?: string;
  sex?: "MALE" | "FEMALE";
};

export type TSupplierInfoBody = {
  body: TSupplierInfoData;
};

export type TSupplierInfoResponse = {
  data: TSupplierInfoData;
  meta: string;
  status: boolean;
};

export type TSupplierRate = "PER_PRODUCT" | "PER_BASKET";

export type TSupplierShippingTime = "1_3_DAYS" | "4_7_DAYS" | "8_14_DAYS" | "5_20_DAYS" | "21_30_DAYS";

export type TSupplierShippingCountry = {
  notAllowed: boolean;
  rate: number;
  rateType: TSupplierRate;
  shippingFrom: number;
  shippingTime: TSupplierShippingTime;
  shippingTo: number;
};

export type TSupplierChatsData = {
  id: number;
  lastMessage: string;
  partner: {
    id: number;
    logo: string;
    name: string;
  };
  unreadCount: 0;
};

export type TSupplierChatsResponse = {
  data: TSupplierChatsData[];
  meta: string;
  status: true;
};

export type TSupplierChatData = {
  chatId: number;
  content: string;
  createdAt: string;
  id: number;
  ours: boolean;
  seenAt: string;
  sendAt: string;
  user: {
    id: number;
    name: string;
    logo: string;
  };
};

export type TSupplierChatResponse = {
  data: TSupplierChatData[];
  meta: string;
  status: true;
};

export type TSupplierChatBody = {
  body: {
    content: string;
    retailerId: number;
  };
};

export type TPostSupplierChatResponse = {
  data: TSupplierChatData;
  meta: string;
  status: true;
};

export type TSupplierChatsParams = {
  filter?: { filters?: { name?: string }; sorts?: { last_update: "asc" | "desc" } };
};

/* --------------------------------- profile -------------------------------- */
export type TSupplierProfileResponse = {
  data: TSupplierProfileData;
  status: string;
};

export type TSupplierProfileBody = {
  body: TSupplierProfileData;
};

export type TSupplierProfileData = {
  address: {
    address1?: string;
    address2?: string;
    city?: number;
    company?: string;
    locationId?: string;
    phoneNumber?: string;
    state?: number;
    zip?: string;
  };
  bankAccount: {
    bic: string;
    holderName: string;
    iban: string;
  };
  brandedInvoicingAllowed: boolean;
  currencyId: string;
  identity: {
    birthDay?: string;
    companyName?: string;
    companyType?: string;
    economicCode?: string;
    isLegalPerson?: boolean;
    nationalCode?: string;
    registrationNumber?: string;
    vatNumber?: string;
    sex?: string;
  };
  map: number;
  processingTime: {
    max: number;
    min: number;
  };
  status?: "InReview" | "Active" | "Rejected" | "Disabled";
  contactEmail: string;
  contactNumber: string;
  currency?: string;
  termsAndConditionsApproved?: boolean;
  id?: string;
  biography?: string;
  logo?: string;
  cover?: string;
  name: string;
  website?: string;
  rejectionNote?: string;
  warehouseAddress?: {
    address1?: string;
    address2?: string;
    city?: number;
    company?: string;
    locationId?: string;
    phoneNumber?: string;
    state?: number;
    zip?: string;
  };
};

export type TSupplierShippingData = {
  id?: string;
  excluded?: boolean;
  description?: string;
  extraItemRate?: string;
  prepaid?: boolean;
  productId?: string;
  rate?: string;
  rateType?: TSupplierRate;
  shippingLocation?: any;
  shippingCarrier?: {
    id: string;
    name: string;
    logo: string;
    trackingUrlPattern: string;
    isActive: boolean;
    position: number;
    createdAt: string;
    updatedAt: string;
  };
  shippingCarrierId?: string;
  shippingTime?: {
    max: number;
    min: number;
  };
  shippingTo: string;
  supplierId?: string;
};

export type TSupplierShippingBody = {
  body: TSupplierShippingData[];
};

export type TSupplierShippingResponse = {
  data: TSupplierShippingData[];
  meta: string;
  status: boolean;
};

export type TSupplierReturnData = {
  address?: {
    address1?: string;
    address2?: string;
    city?: number;
    company?: string;
    locationId?: string;
    phoneNumber?: string;
    state?: number;
    zip?: string;
  };
  productId?: string;
  description?: string;
  isAllowed?: boolean;
  shippingPayer?: "Supplier" | "Customer";
  supplierId?: string;
  windowTime?: {
    max: number;
    min: number;
  };
};

export type TSupplierReturnBody = {
  body: TSupplierReturnData;
};

export type TSupplierReturnResponse = {
  data: TSupplierReturnData;
  meta: string;
  status: boolean;
};

export type TSupplierDocumentsData = {
  media: {
    bucket: string;
    createdAt: string;
    id: string;
    key: string;
    url: string;
  };
  mediaId: string;
  status: "InReview" | "Accepted" | "Rejected";
  supplierId: string;
  tag: string;
};

export type TSupplierDocumentsPostBodyData = {
  mediaId: string;
  tag: string;
};

export type TSupplierDocumentsBodyDeleteData = {
  documentIds: string[];
};

export type TSupplierDocumentsPostBody = {
  body: TSupplierDocumentsPostBodyData[];
};

export type TSupplierDocumentsDeleteBody = {
  body: TSupplierDocumentsBodyDeleteData;
};

export type TSupplierDocumentsResponse = {
  data: TSupplierDocumentsData[];
  status: string;
};

type Config = {
  [key: string]: any;
};

type Preferences = {
  syncOrders: boolean;
  syncProducts: boolean;
};

export type TSupplierIntegrationStateBody = {
  body: {
    isActive: boolean;
    supplierId: string;
  };
};

export type TSupplierIntegrationBody = {
  body: {
    config: Config;
    platform: string;
    isActive: boolean;
    preferences: Preferences;
    adjustmentPercentage: number;
    returnUri: string;
    supplierId: string;
    wholesalePrice: boolean;
  };
};

export type TSupplierIntegrationData = {
  config: Config;
  configurator: Config;
  createdAt: string;
  id: string;
  isActive: boolean;
  isConnected: boolean;
  logo: string;
  name: string;
  integration: {
    createdAt: string;
    credential: { key: string; createdAt: string };
    id: string;
    isActive: boolean;
    isConnected: boolean;
    platform: Platform;
    platformKey: string;
    updatedAt: string;
  };
  supplierOauth?: boolean;
  preferences: Preferences;
  adjustmentPercentage: number;
  updatedAt: string;
  url: string;
  wholesalePrice: boolean;
};

export type TSupplierIntegrationResponse = {
  data: TSupplierIntegrationData;
  status: string;
};
