import { ApiError } from "@/utils/services";
import { TMetaCategoriesData } from "../meta/types";
import {
  TSupplierReturnData,
  TSupplierReturnPayload,
  TSupplierShippingData,
  TSupplierShippingPayload
} from "../supplier/types";

export type TProductStatuses = "Active" | "Inactive" | "InReview" | "Rejected";
export type Authenticity = 'Original' | 'HighCopy' | 'Fake';
export type Condition = 'New' | 'Used' | 'Refurbished';

export type TProductVariantBody = {
  id?: string;
  inventory: number;
  options: {
    [k: string]: string;
  };
  retail_price: number;
  sku?: string;
  created_at?: string;
  updated_at?: string;
  map?: number;
  is_active: boolean;
  authenticity: Authenticity;
  backorder: boolean;
  commission: number; 
  condition: Condition;
};
export type TProductImageType = { id: number; image: string };

export interface IProductBodyMedia {
  alt?: string;
  marked_as_cover: boolean;
  url: string;
}

export type TProductBody = {
  category_id: string;
  description: string;
  id: string;
  images: IProductBodyMedia[];
  status: TProductStatuses;
  tags: string[];
  title: string;
  return_policy?: Omit<TSupplierReturnPayload, "address">;
  shipping_policies?: TSupplierShippingPayload[];
  variants: [TProductVariantBody, ...TProductVariantBody[]];
};

export type TProductRequest = {
  body: Omit<TProductBody, "id"> & { id?: string };
  id?: string;
};

export type TProductImportBody = {
  body: { url: string };
};

export interface IVariantPayload {
  createdAt?: string;
  id?: string;
  inventory: number;
  map?: number;
  isActive: boolean;
  authenticity: Authenticity;
  backorder: boolean;
  commission: number; 
  condition: Condition;
  options: {
    [additionalProp1: string]: string;
  };
  retailPrice: number;
  sku?: string;
  updatedAt?: string;
}

interface IMediaPayload {
  alt: string;
  markedAsCover: boolean;
  url: string;
  sizes?: {
    medium?: string;
    thumbnail?: string;
  };
}

export type IProductSupplier = {
  branded_invoicing_allowed: boolean;
  created_at: string;
  description: string;
  id: string;
  cover?: string;
  logo: string;
  name: string;
  website: string;
  processing_time: {
    max: number;
    min: number;
  };
  return_policy?: Omit<TSupplierReturnPayload, "address">;
  shipping_policies?: TSupplierShippingPayload[];
};
export interface IProductPayLoad {
  category: TMetaCategoriesData;
  categoryTree: TMetaCategoriesData;
  categoryId: string;
  cheapestPrice: number;
  cheapestVariant: IVariantPayload;
  cover: IMediaPayload;
  createdAt: string;
  description: string;
  hasSpecialCondition: boolean;
  hasVariant: boolean;
  id: string;
  images: IMediaPayload[];
  premium: boolean;
  rejectionNote: string;
  returnPolicy?: Omit<TSupplierReturnData, "address" | "shippingPayer">;
  shippingPolicies?: TSupplierShippingData[];
  source: "CSV" | "Manual";
  status: TProductStatuses;
  supplier?: IProductSupplier;
  supplierId: string;
  tags: string[];
  title: string;
  updatedAt: string;
  variants: IVariantPayload[];
  imported: boolean;
}

export type TProductForm = {
  images?: Array<{ url: string; markedAsCover: boolean }>;
  variants: Array<TProductVariantBody>;
} & Omit<
  Omit<TProductBody, "return_policy" | "shipping_policies">,
  "id" | "premium" | "cover" | "images" | "rejection_note" | "supplier_id" | "winning" | "variants"
> &
  Omit<TProductVariantBody, "options" | "id">;

export type TProductResponse = {
  status: string;
  error?: { data?: ApiError };
  data: IProductPayLoad;
};

export type TDeleteProductResponse = {
  meta: string;
  status: boolean;
  data: string;
};

export type TBulkUpdatePriceProductResponse = {
  status: string;
  data: {};
};

export type TProductAttributeData = {
  id: number;
  name: string;
  values:
    | null
    | {
        id: number;
        value: string;
      }[];
};

export type TProductAttributeResponse = {
  meta: string;
  status: boolean;
  data: TProductAttributeData[];
};

export type TProductSearchMeta = {
  totalCount: number;
  filters: string[];
  sorts: string[];
};

export type TProductData = {
  categoryId: string;
  hasSpecialCondition: boolean;
  returnPolicy?: TProductBody["return_policy"];
  isAllowed: boolean;
  shippingPolicies?: TSupplierShippingData[];
} & Omit<TProductBody, "category_id" | "return_policy" | "shipping_policies">;

export type TProductListData = {
  source?: string;
  createdAt: string;
  category: {
    ID: number;
    image: string;
    parentCategory: number;
    slug: string;
    title: string;
  };
} & Omit<TProductBody, "category">;

export interface IFilterState {
  id?: string;
  title?: string;
  tags?: string[];
  sku?: string;
  premium?: boolean;
  winning?: boolean;
  category?: string;
  // If request is not in Retailer context following filter is available:
  status?: TProductStatuses;
  created_from?: string;
  created_to?: string;
  updated_from?: string;
  updated_to?: string;
  // If request is not in Supplier context following filter is available
  supplier?: string[];
  shipping_from?: string;
  shipping_to?: string;
  shipping_time?: string;
  top_suppliers?: boolean;
}

export type ISortState = {
  created_at?: "asc" | "desc";
  updated_at?: "asc" | "desc";
};

export type TProductListMeta = {
  filter?: IFilterState;
  sort?: ISortState;
  page_size: number;
  page: number;
  totalCount?: number;
};

export type TProductListResponse = {
  data: IProductPayLoad[];
  meta: TProductListMeta;
  status: string;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
};

export type TProductCategoryMapperCount = {
  data: {
    count: number;
  };
  status: string;
};

export type TProductCategoryUnmapped = {
  data: {
    adjustmentPercentage: number;
    id: string;
    mappedId: string;
    name: string;
  }[];
  status: string;
};

export type TMapCategoriesResponse = {
  data: any;
  status: string;
};

type MapCategoryPayload = {
  id: string;
  mapped_id: string;
  adjustment_percentage: number | null;
};
export type TMapCategoriesRequest = MapCategoryPayload[];
