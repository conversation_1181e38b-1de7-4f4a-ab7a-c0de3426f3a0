import {
  TSupplierReturnData,
  TSupplierReturnPayload,
  TSupplierShippingData,
  TSupplierShippingPayload
} from "../supplier/types";
import { IVariantPayload, TProductVariantBody } from "./types";

export const variantTransFormer = (variant: IVariantPayload): TProductVariantBody => {
  const { isActive, retailPrice, map, commission, ...rest } = variant;
  return {
    retail_price: retailPrice ? Number(retailPrice) : retailPrice,
    map: map ? Number(map) : map,
    commission: commission ? Number(commission) : commission,

    is_active: isActive,
    ...rest
  };
};

export const shippingTransFormer = (shipping: TSupplierShippingData): TSupplierShippingPayload => {
  const { extraItemRate, rateType, shippingCarrier, shippingTime, ...rest } = shipping;
  return {
    extra_item_rate: extraItemRate,
    rate_type: rateType,
    shipping_carrier: shippingCarrier?.name,
    shipping_time: shippingTime,
    ...rest
  };
};

export const returnTransFormer = (
  returnPolicy: Omit<TSupplierReturnData, "address" | "shippingPayer">
): Omit<TSupplierReturnPayload, "address" | "shipping_payer"> => {
  const { windowTime, isAllowed, ...rest } = returnPolicy;
  return { window_time: windowTime, is_allowed: isAllowed, ...rest };
};
