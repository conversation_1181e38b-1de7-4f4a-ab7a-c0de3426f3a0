import { productApiRoues } from "@/constants/apiRoutes/apiServiceRoutes";
import {
  PRODUCT,
  PRODUCT_ATTRIBUTE,
  PRODUCT_CATEGORY_MAPPER_COUNT,
  PRODUCT_CATEGORY_UNMAPPED,
  PRODUCT_LIST,
  PRODUCT_SEARCH
} from "@/constants/queryKeys";
import { TBulkUpdatePriceProductResponse, TProductImportBody } from "@/store/apps/product/types";
import { convertCamelToSnake } from "@/utils/helpers/objectHelper";
import { rtkBaseQuery } from "@/utils/services";
import { createApi } from "@reduxjs/toolkit/query/react";
import {
  TDeleteProductResponse,
  TMapCategoriesRequest,
  TMapCategoriesResponse,
  TProductAttributeResponse,
  TProductCategoryMapperCount,
  TProductCategoryUnmapped,
  TProductListResponse,
  TProductRequest,
  TProductResponse
} from "./types";

export const Product = createApi({
  reducerPath: "Product",
  tagTypes: [
    PRODUCT_LIST,
    PRODUCT,
    PRODUCT_ATTRIBUTE,
    PRODUCT_SEARCH,
    PRODUCT_CATEGORY_MAPPER_COUNT,
    PRODUCT_CATEGORY_UNMAPPED
  ],
  baseQuery: rtkBaseQuery,
  keepUnusedDataFor: 0,
  endpoints: builder => ({
    postProduct: builder.mutation<TProductResponse, TProductRequest>({
      query: ({ body }) => {
        return {
          url: productApiRoues.create,
          method: "post",
          data: convertCamelToSnake(body)
        };
      },
      invalidatesTags: [PRODUCT_LIST]
    }),
    productImport: builder.mutation<{}, TProductImportBody>({
      query: ({ body }) => {
        return {
          url: productApiRoues.productImport,
          method: "post",
          data: convertCamelToSnake(body)
        };
      },
      invalidatesTags: [PRODUCT_LIST]
    }),

    putProduct: builder.mutation<TProductResponse, TProductRequest>({
      query: ({ body, id }) => ({
        url: productApiRoues.productDetail({ id }),
        method: "put",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error, { id }) => [PRODUCT_LIST, { type: PRODUCT, id }] // Invalidate specific product
    }),
    deleteProduct: builder.mutation<TDeleteProductResponse, { ids: string[] }>({
      query: ({ ids }) => ({
        url: productApiRoues.productDetail({}),
        method: "delete",
        data: { ids }
      }),
      invalidatesTags: [PRODUCT_LIST]
    }),
    bulkUpdatePrice: builder.mutation<
      TBulkUpdatePriceProductResponse,
      { ids: string[]; is_fixed: boolean; is_incremental: boolean; value: number }
    >({
      query: ({ ids, is_fixed, is_incremental, value }) => ({
        url: productApiRoues.productBulkPrice,
        method: "patch",
        data: { ids, is_fixed, is_incremental, value }
      }),
      invalidatesTags: [PRODUCT_LIST]
    }),
    getProduct: builder.query<TProductResponse, { id: string }>({
      query: ({ id }) => ({
        url: productApiRoues.productDetail({ id })
      }),
      providesTags: (result, error, { id }) => [{ type: PRODUCT, id }] // Provide tags for the product
    }),
    /* ---------------------------- product attribute --------------------------- */
    getProductAttribute: builder.query<TProductAttributeResponse, void>({
      query: () => ({
        url: productApiRoues.attributs
      }),
      providesTags: [PRODUCT_ATTRIBUTE]
    }),
    /* ----------------------------- product list ----------------------------- */
    getProductList: builder.query<TProductListResponse, string>({
      query: params => ({
        url: `${productApiRoues.productList}?${params}`
      }),
      providesTags: [PRODUCT_LIST]
    }),
    getProductCategoryMapperCount: builder.query<TProductCategoryMapperCount, void>({
      query: () => ({
        url: `${productApiRoues.categoryMapperCount}`
      }),
      providesTags: [PRODUCT_CATEGORY_MAPPER_COUNT]
    }),
    getProductCategoryUnmapped: builder.query<TProductCategoryUnmapped, void>({
      query: () => ({
        url: `${productApiRoues.categoryUnmapped}`
      }),
      providesTags: [PRODUCT_CATEGORY_UNMAPPED]
    }),
    mapCategories: builder.mutation<TMapCategoriesResponse, TMapCategoriesRequest>({
      query: body => ({
        url: productApiRoues.mapCategories,
        method: "put",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: [PRODUCT_CATEGORY_MAPPER_COUNT, PRODUCT_CATEGORY_UNMAPPED]
    })
  })
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useDeleteProductMutation,
  useGetProductQuery,
  usePostProductMutation,
  usePutProductMutation,
  useGetProductAttributeQuery,
  useGetProductListQuery,
  useBulkUpdatePriceMutation,
  useGetProductCategoryMapperCountQuery,
  useGetProductCategoryUnmappedQuery,
  useMapCategoriesMutation,
  useProductImportMutation
} = Product;
