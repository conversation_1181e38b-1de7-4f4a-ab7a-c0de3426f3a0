import { createApi } from "@reduxjs/toolkit/query/react";
import { rtkBaseQuery } from "@/utils/services";

import {
  TRetailerStoreResponse,
  TRetailerProfileBody,
  TRetailerProfileResponse,
  TRetailerResponse,
  IntegrationsDataResponse,
  TRetilerPushBody,
  TRetilerPushBodyResponce,
  TRetailerPlanBody,
  TRetailerDocumentsResponse,
  TRetailerDocumentsPostBody,
  TRetailerDocumentsDeleteBody,
  TRetailerOnboardingResponse,
  IntegrationPostBody,
  IntegrationPutBody,
  IStoreInvoiceDataResponse,
  IStoreInvoiceDataBody,
  IntegrationStatePostBody
} from "./types";
import {
  RETAILER_STORE_KEY,
  RETAILER_INFO_KEY,
  RETAILER_PLAN_KEY,
  RETAILER_CHATS,
  RETAILER_CHAT,
  RETAILER_DOCUMENTS_KEY,
  RETAILER_ONBOARDING_KEY,
  RETAILER_STORES_KEY,
  META_INTEGRATIONS
} from "@/constants/queryKeys";
import { productApiRoues, profileApiRoutes } from "@/constants/apiRoutes/apiServiceRoutes";
import { convertCamelToSnake } from "@/utils/helpers/objectHelper";

export const Retailer = createApi({
  reducerPath: "Retailer",
  tagTypes: [
    RETAILER_INFO_KEY,
    RETAILER_STORE_KEY,
    RETAILER_PLAN_KEY,
    RETAILER_CHATS,
    RETAILER_CHAT,
    RETAILER_STORES_KEY,
    RETAILER_DOCUMENTS_KEY,
    RETAILER_ONBOARDING_KEY
  ],
  baseQuery: rtkBaseQuery,
  keepUnusedDataFor: 0,
  endpoints: builder => ({
    /* ---------------------------- retailer profile ---------------------------- */
    getRetailerProfile: builder.query<TRetailerResponse, void>({
      query: () => ({
        url: profileApiRoutes.retailerProfile
      }),
      providesTags: [RETAILER_INFO_KEY]
    }),
    postRetailerProfile: builder.mutation<TRetailerProfileResponse, TRetailerProfileBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.retailerProfile,
        method: "post",
        data: {
          ...convertCamelToSnake(body),
          address: {
            ...convertCamelToSnake(body)?.address,
            address1: body?.address?.address1,
            address2: body?.address?.address2
          }
        }
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_INFO_KEY])
    }),
    putRetailerProfile: builder.mutation<TRetailerProfileResponse, TRetailerProfileBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.retailerProfile,
        method: "put",
        data: {
          ...convertCamelToSnake(body),
          address: {
            ...convertCamelToSnake(body)?.address,
            address1: body?.address?.address1,
            address2: body?.address?.address2
          }
        }
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_INFO_KEY])
    }),
    /* --------------------------- retailer documents --------------------------- */
    postRetailerDocuments: builder.mutation<TRetailerDocumentsResponse, TRetailerDocumentsPostBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.retailerDocuments,
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_DOCUMENTS_KEY])
    }),
    deleteRetailerDocuments: builder.mutation<TRetailerDocumentsResponse, TRetailerDocumentsDeleteBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.retailerDocuments,
        method: "delete",
        data: convertCamelToSnake(body)
      })
      // invalidatesTags: (result, error) => (error ? [] : [Retailer_DOCUMENTS_KEY])
    }),
    getRetailerDocuments: builder.query<TRetailerDocumentsResponse, void>({
      query: () => ({
        url: profileApiRoutes.retailerDocuments
      }),
      providesTags: [RETAILER_DOCUMENTS_KEY]
    }),
    getRetailerOnboarding: builder.query<TRetailerOnboardingResponse, void>({
      query: () => ({
        url: profileApiRoutes.retailerOnboarding
      }),
      providesTags: [RETAILER_ONBOARDING_KEY]
    }),
    /* ------------------------------ retailer fee ----------------------------- */
    postPushToStore: builder.mutation<TRetilerPushBodyResponce, TRetilerPushBody>({
      query: body => ({
        url: productApiRoues.retailerPush,
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_STORE_KEY])
    }),
    postPushAllToStore: builder.mutation<TRetilerPushBodyResponce, Omit<TRetilerPushBody, "product_ids">>({
      query: body => ({
        url: productApiRoues.retailerPushAll,
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_STORE_KEY, RETAILER_STORE_KEY])
    }),
    postRetailerStore: builder.mutation<IntegrationsDataResponse, IntegrationPostBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.retailerStoreV2,
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_STORES_KEY, RETAILER_STORE_KEY])
    }),
    putRetailerStore: builder.mutation<IntegrationsDataResponse, IntegrationPutBody>({
      query: ({ body, id }) => ({
        url: profileApiRoutes.retailerStoreDetailV2({ id }),
        method: "put",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_STORES_KEY])
    }),
    putRetailerStoreInvoice: builder.mutation<IStoreInvoiceDataResponse, IStoreInvoiceDataBody>({
      query: ({ body, id }) => ({
        url: profileApiRoutes.retailerStoreInvoice({ id }),
        method: "put",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_STORES_KEY])
    }),
    getRetailerStores: builder.query<IntegrationsDataResponse, void>({
      query: () => ({
        url: profileApiRoutes.retailerStore
      }),
      providesTags: [RETAILER_STORES_KEY]
    }),
    getRetailerStore: builder.query<TRetailerStoreResponse, { id: string }>({
      query: ({ id }) => ({
        url: profileApiRoutes.retailerStoreDetail({ id })
      }),
      providesTags: (result, error, { id }) => [{ type: RETAILER_STORE_KEY, id }]
    }),
    getRetailerPlan: builder.query<any, void>({
      query: () => ({
        url: profileApiRoutes.retailerPlan
      }),
      providesTags: [RETAILER_PLAN_KEY]
    }),
    postRetailerPlan: builder.mutation<any, TRetailerPlanBody>({
      query: ({ body }) => ({
        url: profileApiRoutes.retailerPlan,
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_PLAN_KEY])
    }),
    retailerSignContract: builder.mutation<
      {
        data: {};
        status: string;
      },
      void
    >({
      query: () => ({
        url: profileApiRoutes.retailerSignContract,
        method: "get"
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_INFO_KEY])
    }),
    postRetailerStoreState: builder.mutation<IntegrationsDataResponse, IntegrationStatePostBody>({
      query: ({ sid, body }) => ({
        url: profileApiRoutes.retailerStoreStateV2({ sid }),
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [RETAILER_STORES_KEY, RETAILER_STORE_KEY])
    })
  })
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetRetailerOnboardingQuery,
  useGetRetailerProfileQuery,
  usePostRetailerProfileMutation,
  usePutRetailerProfileMutation,
  useGetRetailerStoresQuery,
  useGetRetailerStoreQuery,
  usePostRetailerStoreMutation,
  usePutRetailerStoreMutation,
  useGetRetailerPlanQuery,
  usePostPushAllToStoreMutation,
  usePostPushToStoreMutation,
  usePostRetailerPlanMutation,
  usePostRetailerDocumentsMutation,
  useDeleteRetailerDocumentsMutation,
  useGetRetailerDocumentsQuery,
  useRetailerSignContractMutation,
  usePutRetailerStoreInvoiceMutation,
  usePostRetailerStoreStateMutation,
  util: retailerDispatchUtil
} = Retailer;
