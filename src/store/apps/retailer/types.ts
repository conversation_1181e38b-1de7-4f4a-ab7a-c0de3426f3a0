import { FormElementData, I18N } from "../meta/types";

export type TRetailerProfileData = {
  address: {
    address1?: string;
    address2?: string;
    city?: number;
    company?: string;
    locationId?: string;
    phoneNumber?: string;
    state?: number;
    zip?: string;
  };
  bankAccount: {
    bic?: string;
    holderName?: string;
    iban?: string;
  };
  brandedInvoicing: boolean;
  currencyId: string;
  contactEmail: string;
  contactNumber: string;
  createdAt: string;
  hasFreeTrial: boolean;
  id?: string;
  isInFreeTrial: boolean;
  termsAndConditionsApproved: boolean;
  markup: number;
  markupType: string;
  name: string;
  rejectionNote?: string;
  staffs?: {
    retailerId: string;
    role: string;
    userId: string;
  }[];
  status?: "InReview" | "Active" | "Rejected" | "Disabled";
  updatedAt: string;
  identity: {
    birthDay?: string;
    companyName?: string;
    companyType?: string;
    economicCode?: string;
    isLegalPerson?: boolean;
    nationalCode?: string;
    registrationNumber?: string;
    vatNumber?: string;
  };
};

export type TRetailerBody = {
  body: TRetailerProfileData;
};

export type TRetailerResponse = {
  data: TRetailerProfileData;
  meta: string;
  status: boolean;
};

export type TRetailerProfileBody = {
  body: TRetailerProfileData;
};

export type TRetailerProfileResponse = {
  data: TRetailerProfileData;
  meta: string;
  status: boolean;
};

export type TRetailerData = {
  address: {
    address1: string;
    address2?: string;
    city?: number;
    company: string;
    countryId?: number;
    phoneNumber?: string;
    state?: number;
    zip: string;
  };
  feeType?: "Percentage" | "FixedAmount";
  fee?: number;
  vatNumber?: string;
  contactEmail: string;
  contactNumber: string;
  currency?: string;
  isAllowedCustomizable?: boolean;
  logo?: string;
  name: string;
};

export type TRetailerStoreData = {
  createdAt?: string;
  id?: number;
  title?: string;
  name?: string;
  description?: string;
  handle?: string;
  domain?: string;
  instagramId?: string;
  telegramId?: string;
  whatsappNumber?: string;
  isActive?: boolean;
};

export type TRetailerStoreBody = {
  body: TRetailerStoreData;
  id?: number | string;
};

export type TRetailerStoreResponse = {
  data: IntegrationDataItem;
  meta: string;
  status: boolean;
};

export type TRetilerPushBody = {
  product_ids: Array<string | number>;
  store_ids: Array<string | number>;
};

export type TRetilerPushBodyResponce = {
  data: string;
  meta: string;
  status: boolean;
};

export type TRetailerPlanData = {
  planId: number;
};

export type TRetailerPlanBody = {
  body: TRetailerPlanData;
};

export type TRetailerChatsData = {
  id: number;
  lastMessage: string;
  partner: {
    id: number;
    logo: string;
    name: string;
  };
  unreadCount: number;
};

export type TRetailerChatsResponse = {
  data: TRetailerChatsData[];
  meta: string;
  status: true;
};

export type TChat = {
  id: number;
  name: string;
  logo: string;
};

export type TRetailerChatData = {
  chatId: number;
  content: string;
  createdAt: string;
  id: number;
  ours: boolean;
  seenAt: string;
  sendAt: string;
  user: TChat;
};

export type TRetailerChatResponse = {
  data: TRetailerChatData[];
  meta: string;
  status: true;
};

export type TReatilerChatsParams = {
  filter?: {
    filters?: { name?: string };
    sorts: { last_update: "asc" | "desc" };
  };
};

export type TRetailerChatBody = {
  body: {
    content: string;
    supplier_id: number;
  };
};

export type TPostRetailerChatResponse = {
  data: TRetailerChatData;
  meta: string;
  status: true;
};

export type TReatilerSupplierChatsParams = {
  filter?: {
    filters?: { name: string };
    sort?: {
      name?: "asc" | "desc";
      created_at?: "asc" | "desc";
    };
  };
};

export type TPostRetailerSupplierResponse = {
  data: TChat[];
} & Omit<TPostRetailerChatResponse, "data">;

export type TRetailerDocumentsData = {
  media: {
    bucket: string;
    createdAt: string;
    id: string;
    key: string;
    url: string;
  };
  mediaId: string;
  status: "InReview" | "Accepted" | "Rejected";
  retailerId: string;
  tag: string;
};

export type TRetailerDocumentsPostBodyData = {
  mediaId: string;
  tag: string;
};

export type TRetailerDocumentsBodyDeleteData = {
  documentIds: string[];
};

export type TRetailerDocumentsPostBody = {
  body: TRetailerDocumentsPostBodyData[];
};

export type TRetailerDocumentsDeleteBody = {
  body: TRetailerDocumentsBodyDeleteData;
};

export type TRetailerDocumentsResponse = {
  data: TRetailerDocumentsData[];
  status: string;
};

export type TRetailerOnboardingData = {
  hasStore: boolean;
  profile: boolean;
};
export type TRetailerOnboardingStatus = "InReview" | "Active" | "Rejected" | "Disabled";
export type TRetailerOnboardingResponse = {
  data: TRetailerOnboardingData;
  status: TRetailerOnboardingStatus;
};

export type Platform = {
  appLink: string;
  color: string;
  logo?: string;
  configForm: { [key: string]: FormElementData };
  guideLink: string;
  hasApp: boolean;
  id: string;
  key: string;
  isActive: boolean;
  name: I18N[];
  platform: "shopBuilder";
  position: number;
  subtitle: I18N[];
  config: { [key: string]: any };
  hasPreferences: boolean;
};

export type Integration = {
  appLink: string;
  color: string;
  configForm: { [key: string]: FormElementData };
  guideLink: string;
  hasApp: boolean;
  id: string;
  isActive: boolean;
  logo: string;
  name: I18N[];
  platform: Platform;
  position: number;
  subtitle: I18N[];
  config: { [key: string]: any };
  preferences: { syncProducts: boolean; syncOrders: boolean };
  wholesalePrice: boolean;
  adjustmentPercentage: number;
};

export type SynchronizerConfig = {
  clientId: string;
  workflowId: string;
};

export type BrandedInvoicing = {
  contactNumber?: string;
  description?: string;
  email?: string;
  isBranded?: boolean;
  logo?: string;
};

export type IntegrationDataItem = {
  createdAt: string;
  id: string;
  name: string;
  logo?: string;
  integration: Integration;
  platform: string;
  isActive: boolean;
  isConnected: boolean;
  log: string[];
  retailerId: string;
  synchronizerConfig: SynchronizerConfig;
  updatedAt: string;
  url: string;
  brandedInvoicing: BrandedInvoicing;
};

export type IntegrationsDataResponse = {
  data: IntegrationDataItem[];
  status: string;
};

export type IntegrationsPostDataResponse = {
  data: { shouldRedirect?: boolean; redirectTo?: string };
  status: string;
};

export type IntegrationsPutDataResponse = {
  data: { shouldRedirect?: boolean; redirectTo?: string };
  status: string;
};

export type ConfigFormData = {
  [key: string]: any;
};

export type IntegrationPostData = {
  config: ConfigFormData;
  platform: string;
  isActive: boolean;
  returnUri?: string;
};

export type IntegrationPostBody = {
  body: IntegrationPostData;
};

export type IntegrationStatePostData = {
  isActive: boolean;
  retailerId: string;
};

export type IntegrationStatePostBody = {
  body: IntegrationStatePostData;
  sid: string;
};

export type IntegrationPutBody = {
  body: IntegrationPostData;
  id: string;
};

export type IStoreInvoiceData = {
  contactNumber: string;
  description: string;
  email: string;
  isBranded: boolean;
  logo: string;
  retailerId: string;
};

export type IStoreInvoiceDataBody = {
  body: IStoreInvoiceData;
  id: string;
};

export type IStoreInvoiceDataResponse = {
  data: {};
  status: string;
};
