import { createApi } from "@reduxjs/toolkit/query/react";

import { rtkBaseQuery } from "@/utils/services";
import { CONVERSATION, CONVERSATION_MESSAGE } from "@/constants/queryKeys";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import { conversationRoutes } from "@/constants/apiRoutes/apiServiceRoutes";
import { convertCamelToSnake } from "@/utils/helpers/objectHelper";

import { ConversationMessageBody, ConversationPayloadResponse, ConversationMessageResponse } from "./types";

export const Conversation = createApi({
  reducerPath: "Conversation",
  tagTypes: [CONVERSATION, CONVERSATION_MESSAGE],
  baseQuery: rtkBaseQuery,
  keepUnusedDataFor: 0,
  endpoints: builder => ({
    postConversationMessage: builder.mutation<
      snakeCaseToCamelCase<ConversationMessageResponse>,
      ConversationMessageBody
    >({
      query: ({ body, id }) => ({
        url: conversationRoutes.conversationMessage({ cid: id || "" }),
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [CONVERSATION_MESSAGE])
    }),
    postConversationMessageStart: builder.mutation<
      snakeCaseToCamelCase<ConversationMessageResponse>,
      ConversationMessageBody
    >({
      query: ({ body }) => ({
        url: conversationRoutes.conversationStart,
        method: "post",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [CONVERSATION_MESSAGE])
    }),
    getConversation: builder.query<snakeCaseToCamelCase<ConversationPayloadResponse>, void>({
      query: () => ({
        url: conversationRoutes.conversation
      }),
      providesTags: [CONVERSATION]
    }),
    getConversationMessage: builder.query<snakeCaseToCamelCase<ConversationMessageResponse>, { id: string }>({
      query: ({ id }) => ({
        url: conversationRoutes.conversationMessage({ cid: id })
      }),
      providesTags: [CONVERSATION_MESSAGE]
    })
  })
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetConversationQuery,
  useGetConversationMessageQuery,
  usePostConversationMessageMutation,
  usePostConversationMessageStartMutation
} = Conversation;
