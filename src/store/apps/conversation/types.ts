export interface ConversationMessageBody {
  body: {
    content: string;
    contentType: "Text" | "File" | "Link" | "Image";
    partnerId: string;
  };
  id?: string;
}

export interface Message {
  content: string;
  content_type: ContentType;
  conversation_id: string;
  created_at: string;
  id: string;
  seen_at: string;
  send_at: string;
  sender_id: string;
  user: User;
  ours?: boolean;
}

export interface ConversationMessageBody {
  body: {
    content: string;
    contentType: "Text" | "File" | "Link" | "Image";
    partnerId: string;
  };
  id?: string;
}

export type TChat = {
  id: number;
  name: string;
  logo: string;
};

export enum ContentType {
  Text = "Text",
  File = "File",
  Link = "Link",
  Image = "Image"
}

export enum UserStatus {
  Active = "Active",
  Inactive = "Inactive",
  Suspended = "Suspended"
}

export enum UserType {
  Admin = "Admin",
  Supplier = "Supplier",
  Retailer = "Retailer"
}

export interface Partner {
  avatar: string;
  name: string;
}

export interface Participant {
  avatar: string;
  id: string;
  name: string;
}

export interface User {
  created_at: string;
  email: string;
  first_name: string;
  id: string;
  is_email_verified: boolean;
  is_phone_number_verified: boolean;
  last_name: string;
  phone_number: string;
  status: UserStatus;
  type: UserType;
  updated_at: string;
}

export interface ConversationPayload {
  created_at: string;
  id: string;
  messages: Message[];
  last_message: Message;
  participants: Participant[];
  partner: Partner;
  partner_id: string;
  starter_id: string;
}

export interface ConversationPayloadResponse {
  data: ConversationPayload[];
  status: string;
}

export type TChatData = {
  chatId: number;
  content: string;
  createdAt: string;
  id: number;
  ours: boolean;
  seenAt: string;
  sendAt: string;
  user: TChat;
};

export type ConversationMessagePayload = {
  content: string;
  content_type: "Text" | "File" | "Link" | "Image";
  conversation_id: string;
  created_at: string;
  id: string;
  seen_at: string;
  send_at: string;
  sender_id: string;
  ours?: boolean;
  user: { name: string; avatar: string };
};

export type ConversationMessageResponse = {
  data: ConversationMessagePayload[];
  status: string;
};
