import { createApi } from "@reduxjs/toolkit/query/react";

import { apiService, rtkBaseQuery } from "@/utils/services";

import { ORDER, ORDER_LIST, ORDER_PAY } from "@/constants/queryKeys";
import {
  TOrderPayBody,
  TOrderPayResponse,
  TOrderResponse,
  TOrdersData,
  TOrderShipmentBody,
  TOrderShippingAddressBody,
  TOrderShippingAddressResponse,
  TOrderStateBody,
  TOrderStateResponse,
  TRemoveOrderStateBody,
  TRemoveOrderStateResponse
} from "./types";
import { orderApiRoutes } from "@/constants/apiRoutes/apiServiceRoutes";
import { convertCamelToSnake } from "@/utils/helpers/objectHelper";

export const Order = createApi({
  reducerPath: "Order",
  tagTypes: [ORDER_PAY, ORDER, ORDER_LIST],
  baseQuery: rtkBaseQuery,
  keepUnusedDataFor: 0,
  endpoints: builder => ({
    PostOrderPayment: builder.mutation<TOrderPayResponse, TOrderPayBody>({
      query: ({ body, id }) => ({
        url: orderApiRoutes.pay({ oid: id }),
        method: "post",
        data: convertCamelToSnake(body)
      })
    }),
    PutOrderShipment: builder.mutation<TOrderPayResponse, TOrderShipmentBody>({
      query: ({ body, id }) => ({
        url: orderApiRoutes.shipment({ oid: id || "" }),
        method: "put",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [ORDER])
    }),
    getOrderList: builder.query<TOrdersData, string>({
      query: params => ({
        url: `${orderApiRoutes.order}?${params}`
      }),
      providesTags: [ORDER_LIST]
    }),
    getOrder: builder.query<TOrderResponse, { id: string }>({
      query: ({ id }) => ({
        url: orderApiRoutes.orderDetail({ id })
      }),
      providesTags: [ORDER]
    }),
    putOrderState: builder.mutation<TOrderStateResponse, TOrderStateBody>({
      query: ({ body, oid, vid }) => ({
        url: orderApiRoutes.orderState({ oid, vid }),
        method: "put",
        data: convertCamelToSnake(body)
      }),
      invalidatesTags: (result, error) => (error ? [] : [ORDER])
    }),
    removeOrderState: builder.mutation<TRemoveOrderStateResponse, TRemoveOrderStateBody>({
      query: ({ oid, vid }) => ({
        url: orderApiRoutes.removeOrder({ oid, vid }),
        method: "delete"
      }),
      invalidatesTags: (result, error) => (error ? [] : [ORDER_LIST])
    }),
    putOrderShippingAddress: builder.mutation<TOrderShippingAddressResponse, TOrderShippingAddressBody>({
      query: ({ body: { address1, address2, ...restBody }, oid }) => ({
        url: orderApiRoutes.shippingAddress({ oid }),
        method: "put",
        data: {
          ...convertCamelToSnake(restBody),
          address1,
          address2
        }
      }),
      invalidatesTags: (result, error) => (error ? [] : [ORDER])
    }),
    getConfirmOrderPayment: builder.mutation<any, { id: string }>({
      query: ({ id }) => ({
        url: orderApiRoutes.verifyPayment({ oid: id }),
        method: "get"
      })
    }),
    getDownloadInvoice: builder.mutation<Blob, { id: string; type?: "customer" }>({
      queryFn: async ({ id, type }, _queryApi, _extraOptions) => {
        try {
          const response = await apiService.get(`${orderApiRoutes.downloadInvoice({ oid: id, type })}`, {
            responseType: "blob"
          });

          return response;
        } catch (error) {
          return { error: { status: "FETCH_ERROR", error: String(error) } };
        }
      }
    })
  })
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  usePutOrderShippingAddressMutation,
  usePutOrderShipmentMutation,
  usePostOrderPaymentMutation,
  useGetOrderListQuery,
  useGetOrderQuery,
  usePutOrderStateMutation,
  useRemoveOrderStateMutation,
  useGetConfirmOrderPaymentMutation,
  useGetDownloadInvoiceMutation
} = Order;
