import { TOrdersFilters } from "@/app/supplier/(dashboard)/order/components/types";

type User = {
  id: string;
  username: string;
  email: string;
  createdAt?: string;
  updatedAt?: string;
};

type Carrier = {
  id: string;
  name: string;
  logo: string;
  isActive: boolean;
  position: number;
  trackingUrlPattern: string;
  createdAt: string;
  updatedAt: string;
};

type Media = {
  id: string;
  bucket: string;
  key: string;
  url: string;
  userId: string;
  createdAt: string;
  user: User;
};

export type ShipmentDetail = {
  carrier: Carrier;
  carrierId: string;
  createdAt: string;
  document: Media;
  documentId: string;
  note: string;
  orderId: string;
  supplierId: string;
  trackingCode: string;
  trackingUrl: string;
  updatedAt: string;
};

export type TOrderPayData = {
  amount: number;
  createdAt: string;
  currency: string;
  data: {
    orderId: string;
    paymentUrl: string;
    redirectUrl: string;
  };
  detail: string;
  email: string;
  id: number;
  kind: "Payment" | "Refund" | "Void";
  mobile: string;
  reference: string;
  status: "Success" | "Pending" | "Failed";
};

export type TOrderPayBody = {
  body: {
    paymentMethod: "no_cash" | "card_to_card" | "zarinpal";
    callbackUrl: string;
  };
  id: string;
};

export type TOrderShipmentBody = {
  body: {
    carrierId?: string;
    documentId?: string;
    note?: string;
    trackingCode?: string;
  };
  id?: string;
};

export type TOrderResponse = {
  data: TOrderData;
  meta: string;
  status: boolean;
};

export type TOrderPayResponse = {
  data: {
    url: string;
  };
  meta: string;
  status: boolean;
};

export type TOrderRequest = {
  filters: TOrdersFilters;
  page?: number;
  page_size?: number;
  sorts?: {
    created_at?: "asc" | "desc";
  };
};

export type TOrderStatus = "Pending" | "InProgress" | "Done" | "Refunded" | "Canceled";
export type TPaymentStatus =
  | "Pending"
  | "Authorized"
  | "Captured"
  | "Paid"
  | "Refunded"
  | "RefundedPartially"
  | "Voided"
  | "Failed";

export type TDeilveryStatus = "Pending" | "Shipped" | "ShippedPartially" | "Cancelled";

export type TOrderProductImage = {
  alt: string;
  marked_as_cover: boolean;
  url: string;
};

type TProductType = {
  cover: TOrderProductImage;
  createdAt: string;
  description: string;
  id: number;
  images: TOrderProductImage[];
  inventory: number;
  isImported: boolean;
  minimumRetailPrice: number;
  premium: boolean;
  price: number;
  sku: string;
  source: string;
  status: TOrderStatus;
  tags: string[];
  title: string;
  winning: boolean;
  supplier: {
    id: number;
    name: string;
  };
};

export type TOrderLineItem = {
  options: {
    [key: string]: string;
  };
  listPrice: number;
  discount: number;
  id: string;
  orderId: string;
  quantity: number;
  product: TProductType;
  salePrice: number;
  supplierId?: string;
  shippingCarrier: string;
  shippingCost: number;
  shippingTime: {
    min: number;
    max: number;
  };
  sku: string;
  state:
    | "Pending"
    | "InProgress"
    | "Fulfilled"
    | "ReturnRequested"
    | "ReturnApproved"
    | "ReturnRejected"
    | "Returned"
    | "Canceled";
  title: string;
  availableStates: string[];
  trackingCode: string;
  updatedAt: string;
  variantId: string;
};

export type TOrderData = {
  shipmentDetails?: ShipmentDetail[];
  hasUncalculatedShipping: boolean;
  billingAddress?: {
    address1: string;
    address2?: string;
    city?: number;
    company: string;
    locationId?: string;
    phoneNumber?: string;
    state?: number;
    zip: string;
  };
  createdAt?: string;
  orderAt: string;
  customer: {
    company: string;
    email: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    title: string;
  };
  id: string;
  lineItems?: TOrderLineItem[];
  note?: string;
  orderNumber?: string;
  paymentState: TPaymentStatus;
  shippingAddress?: {
    address1: string;
    address2?: string;
    city?: number;
    company: string;
    locationId?: string;
    phoneNumber?: string;
    state?: number;
    zip: string;
  };
  retailerId: string;
  shippingState: "Pending" | "Shipped" | "ShippedPartially" | "Cancelled";
  sourceOrderNumber: string;
  state: "Pending" | "InProgress" | "Done" | "Refunded" | "Canceled";
  stateHistories: {
    comment: string;
    createdAt: string;
    orderId: string;
    state: string;
    type: "Order" | "Payment" | "Delivery";
    userId: string;
  };
  storeId: string;
  subtotalPrice: number;
  totalPrice: number;
  transactions: {
    orderId: string;
    transactionId: string;
    userId: string;
  };
  updatedAt: string;
};

export type TOrdersData = {
  data: TOrderData[];
  pagination: { total: number };
  status: boolean;
};

export type TOrderState =
  | "Pending"
  | "InProgress"
  | "Fulfilled"
  | "ReturnRequested"
  | "ReturnApproved"
  | "ReturnRejected"
  | "Returned"
  | "Canceled";

export type TOrderStateBody = {
  body: {
    comment?: string;
    state: TOrderState;
    shippingCarrier?: string;
    trackingCode?: string;
  };
  oid: string;
  vid: string;
};

export type TOrderStateResponse = {
  data: {};
  status: boolean;
};

export type TRemoveOrderStateBody = {
  oid: string;
  vid: string;
};

export type TRemoveOrderStateResponse = {
  data: {};
  status: boolean;
};

export type TOrderShippingAddressResponse = {
  data: {};
  status: boolean;
};

export type TOrderShippingAddressBody = {
  oid: string;
  body: {
    address1?: string;
    address2?: string;
    city?: string;
    company?: string;
    locationId?: string;
    phoneNumber?: string;
    state?: string;
    zip?: string;
  };
};
