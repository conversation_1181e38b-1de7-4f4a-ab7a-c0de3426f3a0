import { createApi } from "@reduxjs/toolkit/query/react";
import { rtkBaseQuery } from "@/utils/services";
import { SUPPLER_ONBOARDING } from "@/constants/queryKeys";
import { profileApiRoutes } from "@/constants/apiRoutes/apiServiceRoutes";

import { TSupplierOnboardingResponse } from "./types";

export const SupplierDashboard = createApi({
  reducerPath: "SupplierDashboard",
  tagTypes: [SUPPLER_ONBOARDING],
  baseQuery: rtkBaseQuery,
  keepUnusedDataFor: 0,
  endpoints: builder => {
    return {
      getSupplierOnboarding: builder.query<TSupplierOnboardingResponse, void>({
        query: () => ({
          url: profileApiRoutes.supplierOnboarding
        }),
        providesTags: [SUPPLER_ONBOARDING]
      })
    };
  }
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints

export const { useGetSupplierOnboardingQuery } = SupplierDashboard;
