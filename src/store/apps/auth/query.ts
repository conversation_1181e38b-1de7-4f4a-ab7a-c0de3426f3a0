import { createApi } from "@reduxjs/toolkit/query/react";
import { AUTH_KEY, AUTH_ME } from "@/constants/queryKeys";
import { authApiRoutes } from "@/constants/apiRoutes/apiServiceRoutes";

import {
  TEmailLoginResponse,
  TMeResponse,
  TPhoneLoginArg,
  TRequestOtpArg,
  TRegisterResponse,
  TRequestOtpResponse,
  TRegisterArg,
  TUpdatePasswordResponse,
  TUpdatePasswordArg,
  TChangePasswordResponse,
  TChangePasswordArg,
  TLoginByPasswordResponse,
  TLoginByPasswordArg,
  TLoginByOtpArg,
  TLoginByOtpResponse
} from "./types";
import { authRtkBaseQuery } from "@/utils/services/authRtkBaseQuery";
import { convertSnakeToCamel } from "@/utils/helpers/objectHelper";

export const Auth = createApi({
  reducerPath: "Auth",
  tagTypes: [AUTH_KEY, AUTH_ME],
  baseQuery: authRtkBaseQuery,
  keepUnusedDataFor: 0,
  endpoints: builder => ({
    phoneLogin: builder.mutation<TEmailLoginResponse, TPhoneLoginArg>({
      query: ({ body }) => ({
        url: authApiRoutes.phoneLogin,
        method: "post",
        data: body
      })
    }),
    register: builder.mutation<TRegisterResponse, TRegisterArg>({
      query: ({ body }) => ({
        url: authApiRoutes.register,
        method: "post",
        data: body
      })
    }),
    requestOtp: builder.mutation<TRequestOtpResponse, TRequestOtpArg>({
      query: ({ body }) => ({
        url: authApiRoutes.requestOtp,
        method: "post",
        data: body
      })
    }),
    changePassword: builder.mutation<TChangePasswordResponse, TChangePasswordArg>({
      query: ({ body }) => ({
        url: authApiRoutes.changePassword,
        method: "post",
        data: body
      })
    }),
    updatePassword: builder.mutation<TUpdatePasswordResponse, TUpdatePasswordArg>({
      query: ({ body }) => ({
        url: authApiRoutes.updatePassword,
        method: "put",
        data: body
      })
    }),
    getMe: builder.query<TMeResponse, void>({
      query: () => ({
        url: authApiRoutes.me
      }),
      providesTags: [AUTH_ME],
      transformResponse: (responseData: TMeResponse) => convertSnakeToCamel(responseData) as any
    }),
    postLoginByPassword: builder.mutation<TLoginByPasswordResponse, TLoginByPasswordArg>({
      query: ({ body }) => ({
        url: authApiRoutes.loginByPassword,
        method: "post",
        data: body
      })
    }),
    postLoginByOtp: builder.mutation<TLoginByOtpResponse, TLoginByOtpArg>({
      query: ({ body }) => ({
        url: authApiRoutes.loginByOtp,
        method: "post",
        data: body
      })
    })
  })
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  usePhoneLoginMutation,
  useRegisterMutation,
  useRequestOtpMutation,
  useChangePasswordMutation,
  useUpdatePasswordMutation,
  useGetMeQuery,
  usePostLoginByOtpMutation,
  usePostLoginByPasswordMutation
} = Auth;
