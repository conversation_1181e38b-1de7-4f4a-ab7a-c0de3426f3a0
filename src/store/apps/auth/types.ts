import { USER_TYPES } from "@/constants/userTypes";

export interface TEmailLoginArg {
  body: {
    email: string;
    password: string;
    user_type: USER_TYPES;
  };
}

export interface TPhoneLoginArg {
  body: {
    phone_number: string;
    user_type: USER_TYPES;
  };
}

export interface TPhoneLoginVerifyArg {
  body: {
    phone_number: string;
    token: string;
    user_type: USER_TYPES;
  };
}

export type TLoginUser = {
  email: string;
  first_name: string;
  is_email_verified: boolean;
  is_phone_number_verified: boolean;
  last_name: string;
  phone_number: string;
  status: "ACTIVE";
  user_type: "ADMIN";
};

export type TEmailLoginResponse = {
  status: boolean;
  token: string;
  user: TLoginUser;
};

export type TMeData = {
  createdAt: string;
  email: string;
  firstName: string;
  id: string;
  isEmailVerified: boolean;
  isPhoneNumberVerified: boolean;
  lastName: string;
  phoneNumber: string;
  status: "Active" | "Inactive" | "Suspended";
  type: "Admin" | "Supplier" | "Retailer";
};

export type TMeResponse = {
  status: boolean;
  data: TMeData;
};

export type TRegisterResponse = {
  status: boolean;
  data: string;
  error: string;
};

export type TRegisterArg = {
  body: {
    phone_number?: string;
    email?: string;
    first_name: string;
    last_name: string;
  };
};

export type TRequestOtpResponse = {
  status: boolean;
  data: {
    expires_in: number;
    is_new: boolean;
  };
};

export type TRequestOtpArg = {
  body: {
    phone_number?: string;
    email?: string;
  };
};

export type TChangePasswordResponse = {
  status: boolean;
  data: string;
};

export type TChangePasswordArg = {
  body: {
    phone_number?: string;
    email?: string;
    new_password: string;
    otp: string;
  };
};

export type TUpdatePasswordResponse = {
  status: boolean;
  data: string;
};

export type TUpdatePasswordArg = {
  body: {
    new_password?: string;
    old_password?: string;
  };
};

export type TLoginByPasswordArg = {
  body: {
    email?: string;
    password: string;
    phone_number?: string;
  };
};

export type TLoginByOtpArg = {
  body: {
    email?: string;
    otp: string;
    phone_number?: string;
  };
};

export type TLoginData = {
  expires_in: number;
  refresh_token: string;
  refresh_token_expires_in: number;
  token: string;
  user_id: string;
};

export type TLoginByOtpResponse = {
  status: boolean;
  data: TLoginData;
};

export type TLoginByPasswordResponse = {
  status: boolean;
  data: TLoginData;
};
