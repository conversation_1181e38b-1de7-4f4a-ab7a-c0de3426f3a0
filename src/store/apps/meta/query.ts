import { createApi } from "@reduxjs/toolkit/query/react";
import { rtkBaseQuery } from "@/utils/services";
import {
  META_CATEGORIES_KEY,
  META_INTEGRATIONS,
  META_LOCATIONS_KEY,
  META_PLATFORMS_KEY,
  META_PRODUCT_SOURCES_KEY,
  META_TAGS
} from "@/constants/queryKeys";

import {
  IntegrationDataResponse,
  CarrierResponse,
  TMetaCategoriesResponse,
  TMetaLocationsResponseQuery,
  TPlansReponse,
  ICurrencyClient
} from "./types";
import { metaApiRoutes } from "@/constants/apiRoutes/apiServiceRoutes";

export const Meta = createApi({
  reducerPath: "Meta",
  tagTypes: [META_PLATFORMS_KEY, META_LOCATIONS_KEY, META_PRODUCT_SOURCES_KEY, META_TAGS, META_INTEGRATIONS],
  baseQuery: rtkBaseQuery,
  endpoints: builder => ({
    getMetaPlans: builder.query<TPlansReponse, void>({
      query: () => ({
        url: metaApiRoutes.plans
      })
      // providesTags: [META_TAGS]
    }),
    getMetaIntegration: builder.query<IntegrationDataResponse, void>({
      query: () => ({
        url: metaApiRoutes.integration
      }),
      providesTags: [META_INTEGRATIONS]
    })
  })
});

export const MetaWithPersist = createApi({
  reducerPath: "MetaWithPersist",
  tagTypes: [META_LOCATIONS_KEY, META_CATEGORIES_KEY],
  baseQuery: rtkBaseQuery,
  endpoints: builder => ({
    getMetaLocations: builder.query<TMetaLocationsResponseQuery, void>({
      query: () => ({
        url: metaApiRoutes.locations
      }),
      providesTags: [META_LOCATIONS_KEY]
    }),
    getMetaCarrier: builder.query<CarrierResponse, void>({
      query: () => ({
        url: metaApiRoutes.carrier
      })
      // providesTags: [META_TAGS]
    }),
    getMetaCurrencies: builder.query<ICurrencyClient, void>({
      query: () => ({
        url: metaApiRoutes.currencies
      })
      // providesTags: [META_TAGS]
    }),
    getMetaCategories: builder.query<TMetaCategoriesResponse, { type?: string } | void>({
      query: params => ({
        url: metaApiRoutes.categories,
        params: params
      }),
      providesTags: [META_CATEGORIES_KEY]
    })
  })
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints

export const { useGetMetaPlansQuery, useGetMetaIntegrationQuery } = Meta;

export const {
  useGetMetaCarrierQuery,
  useGetMetaLocationsQuery,
  useGetMetaCurrenciesQuery,
  useGetMetaCategoriesQuery
} = MetaWithPersist;
