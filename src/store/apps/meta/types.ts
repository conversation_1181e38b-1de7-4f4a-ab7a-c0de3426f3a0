import { ICurrency } from "@/app/retailer/(dashboard)/accountInfo/AccountCurrency";

export enum InputType {
  Text = "text",
  Email = "email",
  Tel = "tel",
  Password = "password",
  File = "file",
  Number = "number",
  Radio = "radio",
  Checkbox = "checkbox",
  URL = "url",
  Currency = "currency",
  Locale = "locale"
}

export interface I18N {
  iso: string; // URL: https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes
  text: string;
}

export type TMetaPlan = {
  description: string;
  fee: number;
  id: number;
  name: string;
  price: number;
};

export interface ICurrencyClient {
  data: ICurrency[];
  status: string;
}

export type TMetaPlansData = {
  annualPaymentDiscount: number;
  importedProduct: number;
  orderNumber: number;
  premiumProduct: number;
  siteNumber: number;
  trialDays: number;
};

export type TMetaTagsData = {
  id: number | string;
  isPremium?: boolean;
  tag: string;
};

export type TMetaPaymentMethodsData = {
  id: number;
  name: string;
  type: string;
};

export type TMetaPaymentMethodsResponse = {
  meta: string;
  status: boolean;
  data: TMetaPaymentMethodsData[];
};

export type TMetaTagsResponse = {
  meta: string;
  status: boolean;
  data: TMetaTagsData[];
};

export type TMetaPlatformsData = {
  id: number;
  name: string;
};

export type TMetaPlatformsResponse = {
  data: TMetaPlatformsData[];
  meta: string;
  status: boolean;
};

export type TMetaCategoriesData = {
  createdAt: string;
  icon: string;
  id: string;
  image: string;
  isActive: true;
  name: string;
  order: number;
  parentId: string;
  position?: number;
  slug: string;
  updatedAt: string;
  rootPath?: string[];
  subCategories: TMetaCategoriesData[];
};

export type TMetaCategoriesResponse = {
  data: TMetaCategoriesData[];
  meta: string;
  status: boolean;
};

export interface TMetaLocationsData {
  type: "Country" | "State" | "City";
  updated_at: string;
  created_at: string;
  id: string | number;
  is_active: boolean;
  iso2: string;
  iso3: string;
  latitude: number;
  longitude: number;
  name: string;
  sub_locations: TMetaLocationsData[];
}

export type TMetaLocations = {
  updatedAt: string;
  createdAt: string;
  isActive: boolean;
  parent?: TMetaLocations | null;
  subLocations: TMetaLocations[];
} & Omit<TMetaLocationsData, "updated_at" | "created_at" | "is_active" | "sub_locations">;

export type TMetaLocationsResponse = {
  data: TMetaLocationsData[];
  meta: string;
  status: boolean;
};
export type TMetaLocationsResponseQuery = {
  data: TMetaLocations[];
  meta: string;
  status: boolean;
};

export type TMetaProductSourcesData = {
  createdAt: string;
  id: number;
  isActive: boolean;
  name: string;
};

export type TMetaProductSourcesResponse = {
  data: TMetaProductSourcesData[];
  meta: string;
  status: boolean;
};

export type TPlansReponse = {
  data: Array<TMetaPlansData>;
  meta: string;
  status: true;
};

enum Platform {
  ShopBuilder = "shopBuilder",
  Shopify = "shopify",
  WooCommerce = "woocommerce",
  Prestashop17 = "prestashop17",
  Shopware = "shopware",
  BigCommerce = "bigcommerce"
}

export interface FormElementData {
  addon: string;
  addonSide?: string;
  addonType?: "text" | "icon";
  direction: "ltr" | "rtl";
  placeholder?: I18N[];
  position?: number;
  width?: number;
  hint: I18N[];
  label: I18N[];
  max?: number;
  min?: number;
  options?: string;
  required: boolean;
  requiredIfNotPresent?: string;
  type: InputType;
  validationRegex?: string;
  prepend?: string;
  append?: string;
}

export interface IntegrationData {
  appLink: string;
  color: string;
  key: string;
  configForm: { [key: string]: FormElementData };
  guideLink: string;
  hasApp: boolean;
  isActive: boolean;
  logo: string;
  name: I18N[];
  platform: Platform;
  position: number;
  subtitle: I18N[];
  supplierOauth?: boolean;
}

export interface IntegrationDataResponse {
  data: IntegrationData[];
  status: string;
}
export type CarrierData = {
  createdAt: string;
  id: string;
  isActive: boolean;
  logo: string;
  name: string;
  position: number;
  trackingUrlPattern: string;
  updatedAt: string;
};

export type CarrierResponse = {
  data: CarrierData[];
  status: string;
};
