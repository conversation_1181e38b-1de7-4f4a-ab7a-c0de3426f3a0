import { createSlice } from "@reduxjs/toolkit";

interface StateType {
  subscriptionModal: boolean;
  storeModal: boolean;
}

const initialState = {
  subscriptionModal: false,
  storeModal: false
};

export const ConfigSlice = createSlice({
  name: "config",
  initialState,
  reducers: {
    setSubscriptionModal: (state: StateType, action) => {
      state.subscriptionModal = action.payload;
    },
    setStoreModal: (state: StateType, action) => {
      state.storeModal = action.payload;
    }
  }
});

export const { setSubscriptionModal, setStoreModal } = ConfigSlice.actions;

export default ConfigSlice.reducer;
