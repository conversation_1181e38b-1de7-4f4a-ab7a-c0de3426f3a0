export enum USER_TYPES {
  SUPPLIER = "SUPPLIER",
  RETAILER = "R<PERSON><PERSON><PERSON><PERSON>"
}

export const SUPPLIER_USER_CONTEXT: string | undefined = process.env.NEXT_PUBLIC_SUPPLIER_USER_CONTEXT;
export const RETAILER_USER_CONTEXT: string | undefined = process.env.NEXT_PUBLIC_RETAILER_USER_CONTEXT;

export const USER_CONTEXT_KEYS = {
  SUPPLIER: SUPPLIER_USER_CONTEXT,
  RETAILER: RETAILER_USER_CONTEXT
};

export type USERTYPES = "SUPPLIER" | "RET<PERSON><PERSON><PERSON>";
