export const mediaApiRoutes = {
  upload: "/v1/media",
  supplierDoc: "/supplier/doc",
  retailerDoc: "/retailer/doc"
};

export const authApiRoutes = {
  loginByPassword: "/v1/auth/login-by-password",
  changePassword: "/v1/auth/change-password",
  loginByOtp: "/v1/auth/login-by-otp",
  refreshToken: "/v1/auth/refresh-token",
  requestOtp: "/v1/auth/request-otp",
  updatePassword: "/v1/auth/update-password",

  register: "/v1/auth/register",
  emailLogin: "/v1/auth/login-by-password",
  me: "/v1/auth/me",
  phoneLogin: "/auth/phone-number/login",
  verfiyPhone: "/auth/phone-number/login/verify",
  oldRegister: "/auth/register"
};

export const metaApiRoutes = {
  locations: "/v1/meta/location",
  carrier: "/v1/meta/carrier",
  plans: "/meta/plans",
  platforms: "/meta/platforms",
  sources: "/meta/product-sources",
  tags: "/meta/tags",
  paymentMethods: "/meta/payment-methods",
  integration: "/v2/meta/integration/platform",
  currencies: "/v1/meta/currency",
  categories: "/v1/catalog/category"
};

export const orderApiRoutes = {
  pay: ({ oid }: { oid: string }) => `/v1/order/${oid}/payment`,
  verifyPayment: ({ oid }: { oid: string }) => `/v1/order/${oid}/verify-payment`,
  downloadInvoice: ({ oid, type }: { oid: string; type?: "customer" }) =>
    `/v1/order/${oid}/invoice/download${type ? `?type=${type}` : ""}`,
  shippingAddress: ({ oid }: { oid: string }) => `/v1/order/${oid}/shipping-address`,
  shipment: ({ oid }: { oid: string }) => `/v1/order/${oid}/shipment`,
  order: "/v1/order",
  orderDetail: ({ id }: { id: string }) => `/v1/order/${id}`,
  orderState: ({ oid, vid }: { oid: string; vid: string }) => `/v1/order/${oid}/line-item/${vid}/state`,
  removeOrder: ({ oid, vid }: { oid: string; vid: string }) => `/v1/order/${oid}/line-item/${vid}`
};

export const productApiRoues = {
  product: "/product",
  productDetail: ({ id }: { id?: string }) => `/v2/catalog/product${id ? `/${id}` : ""}`,
  productBulkPrice: `/v1/catalog/product/bulk/price`,
  productImport: "/v1/catalog/product/import",
  productList: "/v2/catalog/product",
  create: "/v2/catalog/product",
  attributs: "/v1/product/attribute",
  retailerPush: "/v1/retailer/product/push",
  retailerPushAll: "/v1/retailer/product/push-all",
  categoryMapperCount: "/v1/catalog/category-mapper/count",
  categoryUnmapped: "/v1/catalog/category-mapper/unmapped",
  mapCategories: "/v1/catalog/category-mapper"
};

export const profileApiRoutes = {
  retailer: "/retailer",
  retailerProfile: "/v1/retailer/profile",
  retailerSignContract: "/v1/retailer/sign-contract",
  retailerProduct: "/v1/retailer/product",
  retailerProductDetail: ({ id }: { id?: string }) => `/v1/retailer/product/${id}`,
  retailerProductPush: "/v1/retailer/product/push",
  retailerProductUnPush: "/v1/retailer/product/un-push",
  retailerOnboarding: "/v1/retailer/onboarding",
  retailerPlan: "/retailer/plan",
  retailerStore: "/v1/retailer/store",
  retailerStoreV2: "/v2/retailer/store",
  retailerStoreStateV2: ({ sid }: { sid: string }) => `/v2/retailer/store/${sid}/state`,
  retailerStoreDetail: ({ id }: { id: string }) => `/v1/retailer/store/${id}`,
  retailerStoreDetailV2: ({ id }: { id: string }) => `/v2/retailer/store/${id}`,
  retailerStoreInvoice: ({ id }: { id: string }) => `/v1/retailer/store/${id}/invoice`,
  retailerDocuments: "/v1/retailer/document",
  supplier: "/supplier",
  supplierInfo: "/supplier/info",
  supplierProfile: "/v1/supplier/profile",
  supplierSignContract: "/v1/supplier/sign-contract",
  returnPolicy: "/v1/supplier/return-policy",
  shippingPolicy: "/v1/supplier/shipping-policy",
  supplierDocuments: "/v1/supplier/document",
  supplierOnboarding: "/v1/supplier/onboarding",
  supplierStore: "/v1/supplier/integration",
  supplierStoreStateV2: "/v2/supplier/integration/state",
  retailerCategory: "/v1/retailer/category"
};

export const chatApiRoutes = {
  retailer: "/retailer/chat",
  supplierContacts: "/retailer/supplier",
  supplier: "/supplier/chat"
};

export const conversationRoutes = {
  conversation: "/v1/conversation",
  conversationStart: "/v1/conversation/start",
  conversationMessage: ({ cid }: { cid: string }) => `/v1/conversation/${cid}/message`
};
