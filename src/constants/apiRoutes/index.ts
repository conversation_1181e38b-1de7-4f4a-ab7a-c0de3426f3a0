export const apiRoutes = {
  posts: () => "/api/test",
  post: (id: number) => `/posts/${id}`,
  /* ---------------------------------- auth ---------------------------------- */
  emailLogin: () => "/api/auth/emailLogin",
  phoneLogin: () => "/api/auth/loginByOtp",
  phoneLoginVerify: () => "/api/auth/phoneLoginVerify",
  me: () => "/api/auth/me",
  register: () => "/api/auth/register",
  requestOtp: () => "/api/auth/requestOtp",
  changePassword: () => "/api/auth/changePassword",
  updatePassword: () => "/api/auth/updatePassword",
  loginByPassword: () => "/api/auth/loginByPassword",
  loginByOtp: () => "/api/auth/loginByOtp",
  refreshToken: () => "/api/auth/refreshToken",

  /* --------------------------------- product -------------------------------- */
  product: ({ id }: { id?: string | number }) => `/api/product${id ? `/${id}` : ""}`,
  productList: () => `/api/product/list`,
  productAttribute: () => "/api/product/attribute",
  productSearch: () => "/api/product/search",

  /* --------------------------------- profile -------------------------------- */
  supplierProfile: () => "/api/supplier/profile",
  supplierShipping: () => "/api/supplier/profile/shipping",
  supplierReturn: () => "/api/supplier/profile/return",

  /* -------------------------------- dashboard ------------------------------- */
  supplierOnboarding: () => "/api/supplier/onboarding",

  /* -------------------------------- documents ------------------------------- */
  supplierDocuments: () => "/api/supplier/documents",
  retailerDocuments: () => "/api/retailer/documents",

  /* -------------------------------- supplier -------------------------------- */

  supplier: () => "/api/supplier",
  supplierInfo: () => "/api/supplier/info",
  supplierLogo: () => "/api/supplier/logo",
  supplierOrderList: () => "/api/supplier/orders",
  supplierOrder: ({ id }: { id?: string | number }) => `/api/supplier/orders/${id}`,
  supplierOrderStates: ({ id }: { id?: string | number }) => `/api/supplier/orderStates/${id}`,
  supplierOrderFulfillment: ({ id }: { id?: string | number }) => `/api/supplier/orderFulfillment/${id}`,
  supplierChat: ({ id }: { id?: string | number }) => `/api/supplier/chat${id ? `/${id}` : ""}`,

  /* -------------------------------- retailer -------------------------------- */
  retailerPlan: () => "/api/retailer/plan",
  retailerProfile: () => "/api/retailer/profile",
  retailerOnboarding: () => "/api/retailer/onboarding",
  retailerStore: ({ id }: { id?: string | number }) => `/api/retailer/store${id ? `/${id}` : ""}`,
  retailerImportProduct: ({ id }: { id: number | string }) => `/api/retailer/product/${id}`,
  retailerImportList: () => "/api/retailer/product",
  retailerProductPush: () => "/api/retailer/product/push",
  retailerProductUnPush: () => "/api/retailer/product/un-push",
  removeRetailerImport: () => "/api/retailer/product/delete",
  ratilerPush: () => "/api/retailer/push",
  ratilerPushAll: () => "/api/retailer/pushall",
  retailerImportProductPush: () => "/api/retailer/product/pushEdit",
  retailerChat: ({ id }: { id?: string | number }) => `/api/retailer/chat${id ? `/${id}` : ""}`,
  retailersupplier: () => `/api/retailer/chat/supplier`,
  /* -------------------------------- countries ------------------------------- */
  countries: () => "/api/data/countries",
  city: (city: string) => `/api/data/${city}`,

  /* ---------------------------------- meta ---------------------------------- */
  metaTags: () => "/api/meta/tags",
  metaPlans: () => "/api/meta/plans",
  metaCarrier: () => "/api/meta/carrier",
  metaCurrencies: () => "/api/meta/currency",
  metaPlatforms: () => "/api/meta/platforms",
  metaCategories: () => "/api/meta/categories",
  metaLocations: () => "/api/meta/locations",
  metaIntegration: () => "/api/meta/integration",
  metaProductSources: () => "/api/meta/productSources",
  metaPaymentMethods: () => "/api/meta/paymentMethods",
  retailerOrderList: () => "/api/retailer/orders",
  retailerOrder: ({ id }: { id?: string | number }) => `/api/retailer/orders/${id}`,
  retailerOrderStates: ({ id }: { id?: string | number }) => `/api/retailer/orderStates/${id}`,

  uploadMedia: "/api/media/upload",
  supplierDocUpload: "/api/doc/supplier",
  retailerDocUpload: "/api/doc/retailer",

  /* ---------------------------------- order --------------------------------- */
  orderPay: ({ id }: { id: string }) => `/api/order/${id}/payment`,
  orderShipment: ({ id }: { id?: string }) => `/api/order/${id}/shipment`,
  order: ({ id }: { id?: string | number }) => `/api/order${id ? `/${id}` : ""}`,
  orderState: ({ oid, vid }: { oid?: string; vid?: string }) => `/api/order/${oid}/state/${vid}`,
  orderShippingAddress: ({ oid }: { oid?: string }) => `/api/order/${oid}/shippingAddress`,

  /* ------------------------------ conversation ------------------------------ */
  getConversation: "/api/conversation",
  conversationStart: "/api/conversation/start",
  conversationMessage: ({ id }: { id?: string }) => `/api/conversation/${id}/message`
};
