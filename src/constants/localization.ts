import dayjs from "dayjs";
import jalali from "jalaliday";
import hijri from "dayjs-calendar-hijri";
import { arNumber, enNumber, faNumber } from "@/utils/helpers";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import relativeTime from "dayjs/plugin/relativeTime";
import { DAYS, HOURS, MONTHS, SECONDS, YEARS } from "@/constants";
import jalaliPlugin from "jalali-plugin-dayjs";
import "dayjs/locale/fa";
import { dateConverter } from "@/utils/helpers/dateHelpers";

dayjs.extend(jalaliPlugin);
dayjs.extend(hijri);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);

export const IRN_ISO = "IRN";
export const DEFUALT_LANG_LOCALE = process.env.NEXT_PUBLIC_LANG_LOCALE || "fa";
export const LANGUAGES_LOCALES = process.env.NEXT_PUBLIC_DASHBOARD_LANG_LOCALES || "en|fa|ar|fr";
export type LanguageType = {
  flagname: string;
  timeDistance: (d: Date | number) => string;
  icon: string;
  value: "en" | "ar" | "fr" | "fa";
  renderDate: (d: string, f: string) => string;
  disabled?: boolean;
  subTitle?: string;
};

export const LANGUAGES_REGEX_STRING = `\/(${LANGUAGES_LOCALES})(?:\/\w)?`;

function timeDistance(date: Date | number, locale: "en" | "fa"): string {
  // Set the locale for dayjs
  dayjs.locale(locale);

  // Convert the input date to Tehran timezone
  const tehranDate = dayjs(date).tz("Asia/Tehran");

  // Get the current time in Tehran timezone
  const now = dayjs().tz("Asia/Tehran");

  // Calculate the difference in seconds
  const diffInSeconds = now.diff(tehranDate, "second");

  // Customize output based on the time difference and locale
  if (diffInSeconds < SECONDS) {
    return locale === "fa" ? "چند ثانیه پیش" : "a few seconds ago"; // "Now"
  } else if (diffInSeconds < HOURS) {
    const minutes = Math.floor(diffInSeconds / SECONDS);
    return locale === "fa" ? `${minutes} دقیقه پیش` : `${minutes} minutes ago`; // "X minutes ago"
  } else if (diffInSeconds < DAYS) {
    const hours = Math.floor(diffInSeconds / HOURS);
    return locale === "fa" ? `${hours} ساعت پیش` : `${hours} hours ago`; // "X hours ago"
  } else if (diffInSeconds < MONTHS) {
    // 30 days in seconds
    const days = Math.floor(diffInSeconds / DAYS);
    return locale === "fa" ? `${days} روز پیش` : `${days} days ago`; // "X days ago"
  } else if (diffInSeconds < YEARS) {
    // 365 days in seconds
    const months = Math.floor(diffInSeconds / MONTHS); // 30 days in seconds
    return locale === "fa" ? `${months} ماه پیش` : `${months} months ago`; // "X months ago"
  } else {
    const years = Math.floor(diffInSeconds / YEARS); // 365 days in seconds
    return locale === "fa" ? `${years} سال پیش` : `${years} years ago`; // "X years ago"
  }
}

export const LANGUAGES: LanguageType[] = [
  {
    flagname: "فارسی",
    subTitle: "Persian",
    icon: "/images/flag/icon-flag-fa.svg",
    value: "fa",
    renderDate(date, format = "YYYY/MM/DD") {
      return dateConverter(date).format(format);
    },
    timeDistance(date: Date | number) {
      return faNumber(timeDistance(date, "fa"));
    }
  },
  {
    flagname: "انگلیسی",
    subTitle: "English",
    icon: "/images/flag/icon-flag-en.svg",
    value: "en",
    disabled: true,
    renderDate(date, format = "YYY/MM/DD") {
      return dayjs(date)
        .calendar("gregory")
        .locale("en")
        .format(format)
        .replace(/\d/gm, v => Number(v).toLocaleString("en"));
    },
    timeDistance(date: Date | number) {
      return enNumber(timeDistance(date, "fa"));
    }
  },
  {
    flagname: "عربی",
    subTitle: "Arabic",
    icon: "/images/flag/icon-flag-sa.svg",
    value: "ar",
    disabled: true,
    renderDate(date, format = "YYY/MM/DD") {
      return dayjs(date)
        .calendar("hijri")
        .locale("ar")
        .format(format)
        .replace(/\d/gm, v => Number(v).toLocaleString("ar"));
    },
    timeDistance(date: Date | number) {
      return arNumber(timeDistance(date, "fa"));
    }
  }
];

export class Currency {
  iso: string = "";
  symbol: string = "";
  locale: string = "";
  icon: string = "";

  constructor(iso: string, symbol: string, locale: string, icon?: string, render?: (v: number | string) => string) {
    this.iso = iso;
    this.symbol = symbol;
    this.locale = locale;
    if (icon) this.icon = icon;
    if (render) this.render = render.bind(this);
  }

  render: (v: number | string) => string = price => {
    const p = typeof price === "number" ? price : Number(price);
    return p.toLocaleString(this.locale) + " " + this.symbol;
  };
}

export const DEFUALT_CURRENCY_ID =
  process.env.NEXT_PUBLIC_DASHBOARD_DEFAULT_CURRENCY ?? "413876e3-ea28-44f8-8208-16f01eac015c";
export type TCurrenciesType = string;

export const CURRENCIES = {
  en: ["USD", "EUR"],
  fa: ["IRR", "USD"],
  ar: ["AED", "SAR", "USD"],
  fr: ["EUR", "USD"]
};
