export const IS_IRAN_SERVED = Boolean(process.env.NEXT_PUBLIC_IRAN_SERVED_ONLY) || true;
export const MALE_GENDER = "Male";
export const FEMALE_GENDER = "Female";

export const SUPPLIER_USER_TYPE = "SUPPLIER";
export const RETAILER_USER_TYPE = "RETAILER";
export const ADMIN_USER_TYPE = "ADMIN";
export const SUPPLIER_STATUS_PENDING = "Pending";
export const SUPPLIER_STATUS_INREVIEW = "InReview";
export const SUPPLIER_STATUS_APPROVED = "Approved";
export const SUPPLIER_STATUS_REJECTED = "Rejected";
export const SUPPLIER_STATUS_DISABLED = "Disabled";
export const SUPPLIER_PROCESSING_TIME_1_2_DAYS = "1_2_DAYS";
export const SUPPLIER_PROCESSING_TIME_2_3_DAYS = "2_3_DAYS";
export const SUPPLIER_PROCESSING_TIME_3_5_DAYS = "3_5_DAYS";
export const SUPPLIER_PROCESSING_TIME_5_7_DAYS = "5_7_DAYS";
export const SUPPLIER_PROCESSING_TIME_7_10_DAYS = "7_10_DAYS";
export const SUPPLIER_PROCESSING_TIME_MORE_7DAYS = "MORE_7DAYS";
export const SUPPLIER_RATE_TYPE_PER_PRODUCT = "PerProduct";
export const SUPPLIER_RATE_TYPE_PER_BASKET = "PerBasket";
export const SUPPLIER_PROCESSING_TIME_1_3_DAYS = "1To3Days";
export const SUPPLIER_PROCESSING_TIME_4_7_DAYS = "4To7Days";
export const SUPPLIER_PROCESSING_TIME_8_14_DAYS = "8To14Days";
export const SUPPLIER_PROCESSING_TIME_15_20_DAYS = "15To20Days";
export const SUPPLIER_PROCESSING_TIME_21_30_DAYS = "21To30Days";
export const SUPPLIER_SHIPPING_PAYER_CUSTOMER = "Customer";
export const SUPPLIER_SHIPPING_PAYER_SUPPLIER = "Supplier";
export const SUPPLIER_WINDOW_TIME_14_DAYS = "14Days";
export const SUPPLIER_WINDOW_TIME_30_DAYS = "30Days";
export const SUPPLIER_WINDOW_TIME_60_DAYS = "60Days";
export const RETAILER_FEE_TYPE_PERCENTAGE = "Percentage";
export const RETAILER_FEE_TYPE_FIXED_AMOUNT = "FixedAmount";

export const SECONDS = 60;
export const HOURS = 3600;
export const DAYS = 86400;
export const MONTHS = 2592000;
export const YEARS = 31536000;

export const BASE_URL = process.env.NEXT_PUBLIC_DATA_BASE_URL?.trim();

export const ISDEV = process.env.NODE_ENV === "development";
export const DEFAULT_CACHE_MAX_AGE =
  process.env["DEFAULT_CACHE_MAX_AGE"] && new RegExp(/^\d{5,}$/).test(process.env["DEFAULT_CACHE_MAX_AGE"])
    ? Number(process.env["DEFAULT_CACHE_MAX_AGE"])
    : 3 * 24 * 3600 * 1000;
