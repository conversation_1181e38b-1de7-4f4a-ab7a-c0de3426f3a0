export const routes = {
  supplierHome: "/supplier",
  home: "/",
  profile: "/profile",
  accountInfo: "/accountInfo",
  profileChangePassword: "/profile/changePassword",

  // Todo: #multySite it has used only for development mode
  supplierLogin: "/supplier/auth/login",
  supplierRegister: "/supplier/auth/register",
  supplierChangePassword: "/supplier/auth/changePassword",

  supplierProfileChangePassword: "/supplier/profile/changePassword",

  retailerLogin: "/retailer/auth/login",
  retailerRegister: "/retailer/auth/register",
  retailerChangePassword: "/retailer/auth/changePassword",
  retailerOnBoarding: "/onboarding",

  login: "/auth/login",
  register: "/auth/register",
  changePassword: "/auth/changePassword",
  order: "/order",
  product: "/product",
  createProductStore: "/product/store/create",
  editProductStore: "/product/store/edit",
  productCategoryMapper: "/product/category-mapper",
  supplierProducts: (supplierId: string) => `/supplier-products/${supplierId}`,
  subscription: "/subscription",
  createProduct: "/product/create",
  editRetailerProduct: "/product/edit",
  retailerListCategories: "/category",
  retailerProductsDrafts: "/importedsProducts/drafts",
  retailerProductsImports: "/importedsProducts/imports",
  chat: "/notifAndChat/chat",
  notifications: "/notifAndChat/notification"
};
