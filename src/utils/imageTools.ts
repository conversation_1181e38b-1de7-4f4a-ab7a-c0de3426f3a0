import imageCompression from "browser-image-compression";

export async function compressImage(file: File, { maxFileSizeMB = 1 }) {
  // Check if the file size is already below the maximum allowed size
  if (file.size <= maxFileSizeMB * 1024 * 1024) {
    return file; // Return the original file if it's already small enough
  }

  const options = {
    maxSizeMB: maxFileSizeMB, // Max size in MB
    // maxWidthOrHeight: 1920, // Optional, max width/height to maintain aspect ratio
    useWebWorker: true, // Use multi-threading for faster processing
    maxIteration: 10 // Set max iterations to refine the compression
  };

  try {
    const compressedFile = await imageCompression(file, options);

    // Check if the compressed image is now below the specified size limit
    if (compressedFile.size <= maxFileSizeMB * 1024 * 1024) {
      return compressedFile;
    } else {
      throw new Error("Unable to reduce file size below specified limit");
    }
  } catch (error: any) {
    throw new Error(error?.message || "Error during image processing");
  }
}
