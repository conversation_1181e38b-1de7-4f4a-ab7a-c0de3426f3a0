import { STORE_ID } from "./../constants/cookies";
import Cookies, { CookieAttributes } from "js-cookie";
import omitBy from "lodash/omitBy";
import isEmptyLoadash from "lodash/isEmpty";
import isArray from "lodash/isArray";
import isObject from "lodash/isObject";
import { v4 as uuidv4 } from "uuid";
import { routes } from "@/constants/routes";
import useSessionStore from "@/store/zustand/sessionStore";
import { batch } from "react-redux";
import { SetHookFormError } from "./services/utils";
import { useRetailerStore } from "@/store/zustand/retailerStore/RetailerStore";

export const setCookie = (name: string, value: string, options?: CookieAttributes): string | undefined =>
  Cookies.set(name, value, options);

export const getCookie = (name: string): string | undefined => Cookies.get(name);

export const removeCookie = (name: string, options?: CookieAttributes): void => Cookies.remove(name, options);

export const readFile = (
  file: Blob,
  as: "arraybuffer" | "dataurl" | "text" | "binarystring" = "dataurl"
): Promise<string | ArrayBuffer | null | undefined> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = event => {
      resolve(event.target?.result);
    };
    reader.onerror = event => {
      reject(event);
    };
    if (as === "dataurl") reader.readAsDataURL(file);
    if (as === "arraybuffer") reader.readAsArrayBuffer(file);
    if (as === "text") reader.readAsText(file);
    if (as === "binarystring") reader.readAsBinaryString(file);
  });
};

export function isEmpty<T>(value: T) {
  return (
    value === undefined ||
    value === null ||
    (typeof value === "object" && Object.keys(value).length === 0) ||
    (typeof value === "string" && value.trim().length === 0)
  );
}

export function enNumber(value: string | number): string {
  if (!value) return "";

  const strValue = (value + "").trim();

  const convertedValue = strValue
    .replace(/[٠١٢٣٤٥٦٧٨٩]/gm, v => String.fromCharCode(v.charCodeAt(0) - 1584))
    .replace(/[۰۱۲۳۴۵۶۷۸۹]/gm, v => String.fromCharCode(v.charCodeAt(0) - 1728));

  return convertedValue; // Return the modified string with all characters retained
}

export function faNumber(value: string | number): string {
  if (!value && typeof value !== "number") return value as string;
  return String(value).replace(/\d/gm, v => Number(v).toLocaleString("fa"));
}

export function arNumber(value: string | number): string {
  if (!value && typeof value !== "number") return value as string;
  return (value + "")
    .replace(/[۰۱۲۳۴۵۶۷۸۹]/gm, v => String.fromCharCode(v.charCodeAt(0) - 1728))
    .replace(/\d/gm, v => Number(v).toLocaleString("ar"));
}

export const numberTransformers = {
  fa: faNumber,
  ar: arNumber,
  en: enNumber,
  fr: enNumber
};

export function toCommas(value?: number) {
  return value?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export const convertToJson = (params: string) => {
  return JSON.parse('{"' + params.replace(/&/g, '","').replace(/=/g, '":"') + '"}', function (key, value) {
    return key === "" ? value : decodeURIComponent(value);
  });
};

export function toQueryString(o: { [k: string | number]: string | number | Array<number | string> }) {
  return Object.keys(o)
    .filter(key => o[key] !== undefined && o[key] !== null)
    .map(function (key) {
      let ret = [];
      if (Array.isArray(o[key])) {
        (o[key] as Array<string>).forEach((item: string) => {
          ret.push(`${key}=${encodeURIComponent(item)}`);
        });
      } else {
        ret.push(`${key}=${encodeURIComponent(o[key] as string)}`);
      }
      return ret.join("&");
    })
    .join("&");
}

export function calculateTotalCount({ pageSize, totalCount }: { pageSize: number; totalCount: number }) {
  return Math.ceil(totalCount / pageSize);
}

export function omitEmptyValues(obj: any) {
  return omitBy(obj, v =>
    isObject(v) || isArray(v) || typeof v === "string" ? isEmptyLoadash(v) : v === undefined || v === null
  );
}

export const generateSkuHash = () => {
  return uuidv4().slice(0, 8);
};

export const logOut = (dispatch?: any, shouldRedirect = true) => {
  const path = process.env.NODE_ENV === "development" ? routes.supplierLogin : routes.login;

  useSessionStore?.getState()?.clearSession();
  useRetailerStore?.getState()?.clearRetailerStore();

  if (dispatch) {
    batch(() => {
      dispatch({ type: "RESET" });
      if (shouldRedirect) window.location.assign(path);
    });
  } else {
    if (shouldRedirect) window.location.assign(path);
    window.location.reload();
  }

  [STORE_ID].forEach(item => {
    if (Cookies.get(item)) Cookies.remove(item);
  });
};

export const checkExpireToken = (dateCreated: string, expiresInSeconds: number) => {
  const createdDate = new Date(dateCreated).getTime();
  const expiryDate = createdDate + expiresInSeconds * 1000;
  const currentDate = new Date().getTime();
  return currentDate > expiryDate;
};

export function snakeToCamel(snakeCase: string): string {
  return snakeCase.replace(/(_\w)/g, matches => matches[1].toUpperCase());
}

export type SetFieldErrorFunction = (field: string, message: string | undefined) => void;

export function SnakeToCamelFieldErrorWrapper(setFieldError?: SetFieldErrorFunction): SetFieldErrorFunction {
  return (field: string, message: string | undefined) => {
    const camelCaseField = snakeToCamel(field);
    setFieldError?.(camelCaseField, message);
  };
}

export const snakeToCamelCaseHookFormWrapper = (setHookFormFieldError?: SetHookFormError) => {
  return (field: string, error: { message: string; type: "validation" | "required" | "custom" }) => {
    setHookFormFieldError?.(snakeToCamel(field), error);
  };
};

export const ensureUrlScheme = (url: string) => {
  if (!url) return "";
  if (!/^https?:\/\//i.test(url)) {
    return `http://${url}`;
  }
  return url;
};

export function isValidUUID(uuid: string): boolean {
  /* ----------- Regular expression to check for a valid UUID format ---------- */
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

  /* -------- Check if the UUID matches the regex and is not all zeros -------- */
  if (uuidRegex.test(uuid)) {
    /* --------------------- Check if the UUID is all zeros --------------------- */
    const allZerosUUID = "00000000-0000-0000-0000-000000000000";
    return uuid !== allZerosUUID;
  }

  /* ----------- If it doesn't match the UUID pattern, return false ----------- */
  return false;
}

type HandleSetFilterArgs = {
  key: string;
  value: any;
  page?: number;
  setFilters: (filters: any, options?: { history?: "push" | "replace" }) => void;
};

export const handleSetFilter = ({ key, value, page, setFilters }: HandleSetFilterArgs) => {
  setFilters({ [key]: value, page: page || null }, { history: "push" });
};
