import i18n from "@/utils/i18n";
import * as yup from "yup";
import { subYears, parseISO } from "date-fns";

import "yup-phone";

const phoneSchema = yup.string().phone().required(i18n.t("supplier.profile.validations.requiredField"));

yup.addMethod(yup.string, "phone", function (errorMessage: string) {
  return this.test("phone", errorMessage, value => (!value ? true : phoneSchema.isValidSync(value)));
});

const validateIranianNationalId = (value: string) => {
  const pattern = /^\d{10}$/;

  if (!pattern.test(value)) {
    return false; // ID code must be 10 digits long
  }

  const check = parseInt(value[9]);
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(value[i]) * (10 - i);
  }
  sum %= 11;

  return (sum < 2 && check === sum) || (sum >= 2 && check + sum === 11);
};

export const supplierStoreValidation = new yup.ObjectSchema({
  contactEmail: yup
    .string()
    .email(i18n.t("supplier.profile.validations.invalidEmail"))
    .required(i18n.t("supplier.profile.validations.requiredField")),
  contactNumber: yup.string().required(i18n.t("supplier.profile.validations.requiredField")),
  logo: yup.string(),
  cover: yup.string(),
  name: yup
    .string()
    .required(i18n.t("supplier.profile.validations.requiredField"))
    .min(3, i18n.t("supplier.profile.validations.min3"))
    .max(32, i18n.t("supplier.profile.validations.max32")),
  website: yup.string(),
  biography: yup
    .string()
    .required(i18n.t("supplier.profile.validations.requiredField"))
    .max(4098, i18n.t("supplier.profile.validations.max4098")),
  processingTime: yup
    .object({
      min: yup
        .number()
        .typeError(i18n.t("supplier.profile.validations.requiredField"))
        .min(1, i18n.t("validations.min", { number: 1 }))
        .required(i18n.t("supplier.profile.validations.requiredField")),
      max: yup
        .number()
        .typeError(i18n.t("supplier.profile.validations.requiredField"))
        .min(1, i18n.t("validations.min", { number: 1 }))
        .test(
          "bigger-than-min",
          i18n.t("supplier.profile.validations.maxGreaterThanMin", {
            fallback: i18n.t("supplier.profile.validations.maxGreaterThanMin")
          }),
          function (max) {
            const { min } = this.parent;
            return !min || !max || max > min;
          }
        )
        .required(i18n.t("supplier.profile.validations.requiredField"))
    })
    .required(i18n.t("supplier.profile.validations.requiredField"))
});

export const supplierBankValidation = new yup.ObjectSchema({
  bankAccount: yup.object({
    bic: yup.string(),
    holderName: yup
      .string()
      .required(i18n.t("supplier.profile.validations.requiredField"))
      .min(3, i18n.t("supplier.profile.validations.min3"))
      .max(32, i18n.t("supplier.profile.validations.max32")),
    iban: yup
      .string()
      .required(i18n.t("supplier.profile.validations.requiredField"))
      .matches(/^[0-9]{24}$/, i18n.t("supplier.profile.validations.iban"))
  })
});

export const supplierInfoValidationSchema = ({ isEdit }: { isEdit: boolean }) =>
  new yup.ObjectSchema({
    vatNumber: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup.string().required(i18n.t("supplier.profile.validations.requiredField"));
      }
      return schema;
    }),
    isLegalPerson: yup.boolean(),
    nationalCode: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (!isLegalPerson) {
        return yup
          .string()
          .required(i18n.t("supplier.profile.validations.requiredField"))
          .matches(/^\d{10}$/, i18n.t("supplier.profile.validations.nationalCode"))
          .test("nationalCode", i18n.t("supplier.profile.validations.nationalCode"), value =>
            validateIranianNationalId(value as string)
          );
      }
      return schema;
    }),
    birthDay: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (!isLegalPerson) {
        return yup.string().test("is-18", i18n.t("supplier.profile.validations.birthDay"), value => {
          if (!value) return false;
          const date = parseISO(value);
          return subYears(new Date(), 18) >= date;
        });
      }
      return schema;
    }),
    companyName: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup
          .string()
          .required(i18n.t("supplier.profile.validations.requiredField"))
          .min(3, i18n.t("supplier.profile.validations.min3"))
          .max(64, i18n.t("supplier.profile.validations.max64"));
      }
      return schema;
    }),
    companyType: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup
          .string()
          .required(i18n.t("supplier.profile.validations.requiredField"))
          .min(3, i18n.t("validations.min", { number: 3 }))
          .max(32, i18n.t("validations.max", { number: 32 }));
      }
      return schema;
    }),
    economicCode: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup
          .string()
          .min(3, i18n.t("validations.min", { number: 3 }))
          .max(32, i18n.t("validations.max", { number: 32 }))
          .required(i18n.t("supplier.profile.validations.requiredField"));
      }
      return schema;
    }),
    registrationNumber: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (isLegalPerson) {
        return yup
          .string()
          .min(3, i18n.t("validations.min", { number: 3 }))
          .max(32, i18n.t("validations.max", { number: 32 }))
          .required(i18n.t("supplier.profile.validations.requiredField"));
      }
      return schema;
    }),
    sex: yup.string().when("isLegalPerson", (isLegalPerson, schema) => {
      if (!isLegalPerson) {
        return yup.string().required(i18n.t("supplier.profile.validations.requiredField"));
      }
      return schema;
    }),
    documents: yup
      .object()
      .shape({
        "legal-national-front": yup.array().nullable(),
        "legal-national-back": yup.array().nullable(),
        "legal-newspaper": yup.array().nullable(),
        "legal-certificate-of-added-value": yup.array().nullable(),
        "national-front": yup.array().nullable(),
        "national-back": yup.array().nullable()
      })
      .when([], (documents, { parent: context }) => {
        if (context?.isLegalPerson) {
          return yup.object().shape({
            "legal-national-front": yup.array().required(i18n.t("supplier.profile.validations.requiredField")),
            "legal-national-back": yup.array().required(i18n.t("supplier.profile.validations.requiredField")),
            "legal-newspaper": yup.array().required(i18n.t("supplier.profile.validations.requiredField")),
            "legal-certificate-of-added-value": yup
              .array()
              .required(i18n.t("supplier.profile.validations.requiredField"))
          });
        } else {
          return yup.object().shape({
            "national-front": yup.array().required(i18n.t("supplier.profile.validations.requiredField")),
            "national-back": yup.array().required(i18n.t("supplier.profile.validations.requiredField"))
          });
        }
      })
  });

export const supplierAddressValidation = yup.object({
  address: yup.object({
    address1: yup
      .string()
      .required(i18n.t("supplier.profile.validations.requiredField"))
      .min(5, i18n.t("supplier.profile.validations.min5")),
    locationId: yup.string().required(i18n.t("supplier.profile.validations.requiredField")),
    zip: yup
      .string()
      .matches(/^\d{10}$/gm, i18n.t("errors.zip"))
      .required(i18n.t("supplier.profile.validations.requiredField"))
  }),
  warehouseAddress: yup.object({
    address1: yup
      .string()
      .required(i18n.t("supplier.profile.validations.requiredField"))
      .min(5, i18n.t("supplier.profile.validations.min5")),
    locationId: yup.string().required(i18n.t("supplier.profile.validations.requiredField")),
    zip: yup
      .string()
      .matches(/^\d{10}$/gm, i18n.t("errors.zip"))
      .required(i18n.t("supplier.profile.validations.requiredField")),
    phoneNumber: yup.string().required(i18n.t("supplier.profile.validations.requiredField"))
  })
});

export const orderShippingAddressValidation = yup.object({
  address1: yup
    .string()
    .required(i18n.t("supplier.profile.validations.requiredField"))
    .min(5, i18n.t("supplier.profile.validations.min5")),
  locationId: yup.string().required(i18n.t("supplier.profile.validations.requiredField")),
  zip: yup
    .string()
    .matches(/^\d{10}$/gm, i18n.t("errors.zip"))
    .required(i18n.t("supplier.profile.validations.requiredField"))
});

export const supplierReturnValidationSchema = yup.lazy(values => {
  // Check the isAllowed value
  const isAllowed = values.isAllowed;

  return yup.object().shape({
    address: yup.object().shape({
      address1: isAllowed
        ? yup
            .string()
            .required(i18n.t("supplier.profile.validations.requiredField"))
            .min(5, i18n.t("supplier.profile.validations.min5"))
        : yup.string().optional(),

      locationId: isAllowed
        ? yup.string().required(i18n.t("supplier.profile.validations.requiredField"))
        : yup.string().optional(),

      zip: isAllowed
        ? yup
            .string()
            .matches(/^\d{10}$/gm, i18n.t("errors.zip"))
            .required(i18n.t("supplier.profile.validations.requiredField"))
        : yup.string().optional(),

      phoneNumber: isAllowed
        ? yup.string().required(i18n.t("supplier.profile.validations.requiredField"))
        : yup.string().optional()
    }),

    isAllowed: yup.boolean(),

    description: isAllowed
      ? yup
          .string()
          .required(i18n.t("supplier.profile.validations.requiredField"))
          .max(2048, i18n.t("supplier.profile.validations.max2048"))
      : yup.string().optional().max(2048, i18n.t("supplier.profile.validations.max2048")),

    shippingPayer: isAllowed
      ? yup.string().required(i18n.t("supplier.profile.validations.requiredField"))
      : yup.string().optional(),

    windowTime: isAllowed
      ? yup
          .object()
          .shape({
            max: yup
              .number()
              .min(1, i18n.t("validations.min", { number: 1 }))
              .required(i18n.t("supplier.profile.validations.requiredField"))
          })
          .required(i18n.t("supplier.profile.validations.requiredField"))
      : yup.object().optional().shape({
          max: yup.number().optional()
        })
  });
});

export const supplierOrderStateValidationSchema = new yup.ObjectSchema({
  carrierId: yup.string().required(i18n.t("supplier.profile.validations.requiredField")),
  trackingCode: yup.string().required(i18n.t("supplier.profile.validations.requiredField")),
  note: yup.string()
});

export const supplierOrderRejectedValidationSchema = new yup.ObjectSchema({
  comment: yup.string().required(i18n.t("supplier.profile.validations.requiredField"))
});

export const supplierShippingSchema = (isFree: boolean) =>
  new yup.ObjectSchema({
    excluded: yup.boolean(),
    extraItemRate: yup.string(),
    prepaid: yup.string().when(["excluded"], excluded => {
      if (!excluded) {
        return yup.boolean().required(i18n.t("supplier.profile.validations.requiredField"));
      }
      return yup.boolean();
    }),
    rate: yup.string().when(["excluded"], excluded => {
      if (!excluded && !isFree) {
        return yup.string().required(i18n.t("supplier.profile.validations.requiredField"));
      }
      return yup.string();
    }),
    shippingCarrierId: yup.string().when(["excluded"], excluded => {
      if (!excluded) {
        return yup.string().required(i18n.t("supplier.profile.validations.requiredField"));
      }
      return yup.string();
    }),
    shippingTime: yup.string().when(["excluded"], excluded => {
      if (!excluded) {
        return yup.object({}).required(i18n.t("supplier.profile.validations.requiredField"));
      }
      return yup.object({});
    }),
    shippingTo: yup.string().required(i18n.t("supplier.profile.validations.requiredField"))
    // description: yup.string().max(2048, i18n.t("supplier.profile.validations.max2048"))
  });

export const profileSetupRetailerSchema = new yup.ObjectSchema({
  address: supplierStoreValidation,
  contact_email: yup.string().email().required(i18n.t("supplier.profile.validations.requiredField")),
  // contact_number: yup.string().phone().required(i18n.t("supplier.profile.validations.requiredField")),
  currency: yup.string().length(3),
  is_allowed_customizable: yup.boolean(),
  logo: yup.string(),
  name: yup.string(),
  vat_number: yup.string()
});
