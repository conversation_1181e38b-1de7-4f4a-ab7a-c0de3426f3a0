import i18n from "@/utils/i18n";
import * as yup from "yup";
import { subYears, parseISO } from "date-fns";

export const invoiceBrandValidation = new yup.ObjectSchema({
  email: yup
    .string()
    .email(i18n.t("supplier.profile.validations.invalidEmail"))
    .required(i18n.t("supplier.profile.validations.requiredField")),
  contactNumber: yup.string().required(i18n.t("supplier.profile.validations.requiredField")),
  logo: yup.string().required(i18n.t("supplier.profile.validations.requiredField")),
  description: yup.string().max(300, i18n.t("validations.max", { number: 300 }))
});
