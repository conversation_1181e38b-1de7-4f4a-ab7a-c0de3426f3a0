{"Dashboard 1": "Dashboard 1", "Dashboard 2": "Dashboard 2", "Contacts": "Contacts", "searchCategory": "Search Category", "selectCategory": "Select Category", "Blog": "Blog", "companyTitle": "Drop Parse", "Ecommerce": "Online Store", "retailerProductWarning": "Welcome, you need to complete your profile first", "title": "Title", "inventory": "Inventory", "price": "Price", "minimum_retail_price": "Minimum Retail Price", "sku": "Unique Product Identifier", "Chats": "Chats", "filterList": "Filters", "products": "Products", "orders": "Orders", "home": "Home", "supplierProductWarning": "Please register first to create your profile", "selected": "Selected", "profileMenu": {"profile": "Profile", "support": "Support", "logout": "Logout"}, "realUser": {"title": "Real Provider", "subTitle": "Every Human Individual"}, "legalUser": {"title": "Legal Provider", "subTitle": "Companies, Organizations, etc."}, "auth": {"footer": "© All rights reserved by Savvy Tech.", "placeholders": {"email": "Email or Mobile Number", "password": "Enter your password", "firstName": "First Name", "lastName": "Last Name"}, "changedPassword": {"text": "Your password has been successfully changed", "buttonText": "Log in to the login panel"}}, "overview": {"weeks": {"mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun"}, "sWeeks": {"mon": "M", "tue": "T", "wed": "W", "thu": "T", "fri": "F", "sat": "S", "sun": "S"}, "payments": "Payments", "lastPayments": "Last 7 days", "profitMore": "$18k Profit more than month", "products": "Products", "LatestDeal": "Latest Deal", "couponUsed": "Coupon Used", "customers": "Customers", "weeklyOrder": "Weekly order updates", "totalOrders": "Total Orders", "earnMony": "You have done 38% more sales", "congratulations": "Congratulations", "stateItems": {"newOrders": "64 new orders", "processing": "Processing", "orders": "Orders", "onHold": "On Hold", "delivered": "Delivered"}}, "validations": {"requiredField": "This field is required."}, "product": {"productNotFound": "This roduct is not found", "list": "List", "importeds": "My Products", "moreCategory": "More Categories", "optionName": "Option Name", "created": "Product created", "filters": "Filters", "uploadIamges": "Upload Images", "optionValues": "Option Values", "save": "Save", "cancel": "Cancel", "cover": "Cover", "newOption": "New Option", "filterTitle": "Search by Title", "onlyActive": "Only Active Products", "onlyInActive": "Only Inactive Products", "hasVariant": "<PERSON>", "noVariant": "No Variant", "createdAtFrom": "Created From", "createdAtTo": "Created To", "filtersTag": "Filter by Tag", "filtersList": "Filters List", "statusItems": {"active": "Active", "inactive": "Inactive", "inreview": "In Review"}, "sourceItems": {"manual": "Manual", "csv": "Excel"}, "rowPerPage": "Rows Per Page", "sku": "Unique Product Identifier", "title": "Title", "category": "Category", "filterSku": "Search by Unique Product Identifier", "description": "Description", "images": "Images", "inventory": "Inventory", "minimumRetailPrice": "Minimum Price", "price": "Price", "status": "Status", "tags": "Tags", "submit": "Submit", "edit": "Edit", "hasVariants": "This product has different types", "validations": {"minLengthTitle": "Minimum character count is 5 characters", "price": "Price must be greater than minimum retail price"}, "alert": {"subtitle": "Are you sure you want to permanently delete this product? By clicking the delete button, the product will be permanently deleted", "verify": "Delete", "cancel": "Cancel"}, "emptyList": "No products available", "productTitle": "Products", "createProduct": "Add Product", "variations": "Variations", "variationsHeaderItems": {"id": "ID", "inventory": "Inventory", "minimumPrice": "Minimum Price", "price": "Price", "sku": "Unique Product Identifier", "options": "Options"}, "tableHeaderItems": {"id": "ID", "cover": "Cover", "title": "Title", "category": "Category", "status": "Status", "variationsCount": "Variation Count", "source": "Source", "sku": "Unique Product Identifier", "price": "Price", "inventory": "Inventory", "createdAt": "Created At", "detail": "Details"}, "breadCrumbs": {"home": "Home", "product": "Products"}, "noVariants": "No product variants available", "importedFrom": "Imported From", "importedTo": "Imported To"}, "validation": {"uniqField": "Do not enter duplicate values", "uniqueName": "Name must be unique", "required": "This information is required"}, "Users": "Users", "Users v2": "Users v2", "imageUpload": "Upload Image", "uploadFailed": "Upload failed", "Notes": "Notes", "Calendar": "Calendar", "Email": "Email", "Tickets": "Tickets", "Starter": "Starter", "Menu Level": "Menu Level", "Disabled": "Disabled", "SubCaption": "Sub Caption", "Chip": "Chip", "Outlined": "Outlined", "External Link": "External Link", "search": "Search", "searchPlaceholder": "Search...", "searchLinks": "Page Links", "welcome": "Welcome to Drop Parse", "notFoundMessage": "The page you are looking for could not be found", "notFoundRedirect": "Return to Homepage", "settings": {"settings": "Settings", "themeOption": "Theme", "themeDirection": "Theme Direction", "themeColors": "Theme Colors", "layoutType": "Layout Type", "containerOption": "Container Option", "sidebarType": "Sidebar Type", "cardWith": "<PERSON>", "themeBorderRadius": "Theme Border Radius", "dark": "Dark", "rtl": "Right to Left", "ltr": "Left to Right", "light": "Light", "border": "Border", "shadow": "Shadow", "full": "Full", "collapse": "Collapse", "boxed": "Boxed", "vertical": "Vertical", "horizontal": "Horizontal"}, "addNewCity": "Add New City", "addNewTag": "Add New Tag", "errors": {"unauthorized": "Oops, You are apear to be lost.", "passwordWrong": "Your Password is wrong", "somethingWentWrong": "Something went wrong", "productNotPushable": "This product cannot be added to the store", "existNationalId": "This national ID is already registered", "zip": "Invalid zip code", "haveNoStores": "No stores available"}, "retailerProduct": {"search": "Search", "tags": "Tags", "title": "Title", "shippingFrom": "Shipping From", "shippingTo": "Shipping To", "category": "Category", "emptyList": "No products available", "productDescription": "Product Description", "inventory": "Inventory", "description": "Description", "by": "By", "price": "Price", "minPrice": "Starting Price", "shippingTime": "Shipping Time", "return": "Return Policies", "city": "City"}, "currency": "<PERSON><PERSON><PERSON><PERSON>", "emailLogin": {"enterPass": "Enter the password", "title": "Welcome to Drop Parse", "subtext": "Your management dashboard", "subTitle": "Not registered yet?", "subTitle2": "Create an account", "validation": {"validPassword": "Must contain 8 characters, one uppercase letter, one lowercase letter, one digit, and one special character", "validEmail": "Enter a valid email", "requiredEmail": "Email is required", "requiredPassword": "Password is required"}, "notFound": "You don't have an account, please register first", "back": "Back", "email": "Email", "password": "Password", "confirmPassword": "Confirm password", "remember": "Remember me", "loginWithCode": "Enter with otp code", "forgetPassword": "Forgot password", "buttonText": "<PERSON><PERSON>"}, "otpLogin": {"retryCode": "Retrieve code again", "enterCode": "Enter the code", "subtitlePhone": "Verification code sent via SMS", "subtitleEmail": "Verification code sent via Email", "title": "Welcome to Drop Parse", "subtext": "Your management dashboard", "validation": {"requiredPhoneNumber": "Phone number is required", "requiredOtpCode": "OTP code is required", "minOtpCode": "OTP code must be at least 6 characters"}, "back": "Back", "phoneNumber": "Phone Number", "otpCode": "OTP Code", "buttonText": "<PERSON><PERSON>", "confirm": "Confirm"}, "checkUsername": {"title": "Login to user panel", "subTitle": "Enter your information to log into the system", "loginTitle": "Don't have an account?", "subTitle2": "Create an account", "subtext": "Your management dashboard", "validation": {"identifyUsername": "Invalid email or phone number", "requiredUsername": "This field is required"}, "username": "Email / Phone Number", "buttonText": "Continue"}, "register": {"title": "Sign Up at Drop Hub", "subtitle": "Fill in the information below to register in the system", "otpCode": {"title": "Enter your one-time password"}, "changePassword": {"title": "Create a password to log in to your account"}, "hasAccount": "Already have an account?", "enterAccount": "Log in to Drop Hub", "subTitle": "Have you registered before?", "subTitle2": "<PERSON><PERSON>", "validation": {"validPassword": "Must include 8 characters, one uppercase letter, one lowercase letter, one number, and one special character", "validEmail": "Enter a valid email", "requiredEmail": "Email is required", "requiredPassword": "Password is required", "identifyUsername": "Invalid email or phone number", "requiredUsername": "This field is required", "passwordsMustMatch": "Passwords do not match"}, "lastName": "Last Name", "firstName": "First Name", "username": "Email / Phone Number", "back": "Back", "email": "Email", "password": "Password", "remember": "Remember Me", "forgetPassword": "Forgot Password", "buttonText": "Register"}, "changePassword": {"title": "Change Password", "subtitle": "Enter your mobile number or email to change the password", "otpCode": {"title": "Enter the verification code"}, "form": {"title": "Change password for account login", "subtitle": "Password must be at least 8 characters"}}, "myProfile": "My Profile", "profileMenuSubtitle": "Profile Settings", "profile": "Profile", "logOut": "Log Out", "supplier": {"profile": {"sectionInfo": {"accountType": "Account Type", "uploadDoc": "Upload Document"}, "shippingTypeItems": {"prePaid": "Pre Paid", "nextPaid": "Next Paid"}, "addressTitle": "Company Address", "warehouseAddressTitle": "Warehouse Address", "anotherAddressTitle": "Another Address", "anotherAddressValue": "Add another new address", "changeProfile": "Change Photo", "returnAddress": "Return Address", "returnPolicy": "Return Policy", "whoPayer": "Who pays for shipping?*", "customer": "seller", "active": "active", "return": "we have returns", "me": "supplier (myself)", "save": "Save", "extraItemRate": "Extra Item Rate", "shippingCarrier": "Shipping Carrier", "buttonNext": "Next Stage", "buttonPrev": "Previous Stage", "success": "Your profile registration has been successfully completed", "name": "seller name", "logo": "Logo", "profileLogo": "Profile Photo", "storeInfo": "Store Info", "holderName": "Holder Name", "sameAddress": "The warehouse address is the same as the company's address", "shippingType": "Shipping Type", "iban": "<PERSON><PERSON>", "bankInfo": "Bank Info", "email": "Email", "country": "Country", "city": "City", "state": "State", "company": "Company", "phoneNumber": "Phone Number", "address": "Address", "address1": "Address 1", "address2": "Address 2", "zip": "Zip Code", "merchantName": "Merchant Name", "merchantLogo": "Merchant Logo", "website": "Website", "description": "Description", "isAllowedCustomizable": "Brand Customization Allowed (Yes/No)", "numberOfSku": "Number of Unique Product Identifiers", "productSources": "Product Sources", "sellingPlatforms": "Selling Platforms", "processingTime": "Processing Time", "processingTimeItems": {"1_2_DAYS": "1 to 2 days", "3_4_DAYS": "3 to 4 days", "5_7_DAYS": "5 to 7 days", "MORE_7DAYS": "More than 7 days"}, "contactNumber": "Contact Number", "contactEmail": "Email", "submit": "Submit", "update": "Update", "skip": "<PERSON><PERSON>", "contactInfo": "Contact Information", "addressInfo": "Company Address Information", "WarehouseAddressInfo": "Warehouse Address Information", "merchantInfo": "Merchant Information", "shopInfo": "Shop Information", "step": {"shopInfo": "Shop Information", "personalInfo": "Personal Information", "addressInfo": "Address Information", "shipping": "Shipping", "policy": "Terms and Conditions"}, "registerAsTitle": "Register As", "registerAs": {"legal": "Legal Entity", "personal": "Individual"}, "companyName": "Registered Company Name", "companyType": "Company Type", "economicCode": "Economic Code", "registrationNumber": "Registration Number", "birthDay": "Date of Birth", "nationalCode": "National Code", "economicCodeError": "Invalid Economic Code", "numberFieldError": "Must be a number", "sex": "Gender", "male": "Male", "female": "Female", "shippingFrom": "Shipping From", "shippingTo": "Shipping To", "shippingTime": "Delivery Time", "allowed": "Active", "rateType": "Rate Type", "rate": "Rate", "remove": "Remove", "addShipping": "Add New Item", "shippingTimeItems": {"1_2_DAYS": "1 to 2 Days"}, "rateTypeItems": {"PerProduct": "Per Product", "PerBasket": "Per Basket"}, "allCities": "All Cities", "shippingPayer": "Who Pays for Shipping?", "windowTime": "Return Window", "isAllowed": "Allowed", "returnDescription": "Return Policy Description", "shippingPayerItems": {"customer": "Customer", "supplier": "Supplier"}, "windowTimeItems": {"14Days": "14 Days", "30Days": "30 Days", "60Days": "60 Days"}, "vatNumber": "VAT Number", "personalInfo": "Personal Information", "categories": "Product Categories", "defaultCategory": "Default Category", "stepperTooltip": "First, complete this form", "validations": {"requiredField": "This field is required.", "phoneNumber": "Invalid phone number", "email": "Invalid email", "nationalCode": "Invalid national code", "sex": "Invalid gender field", "shippingTime": "Invalid shipping time", "rateType": "Invalid rate type", "shippingPayer": "Invalid shipping payer type", "windowTime": "Invalid window time", "positive": "Number must be greater than 0", "number": "Value must be a number", "min5": "Minimum characters is 5", "min3": "Minimum characters is 3", "max64": "Maximum characters is 64", "max32": "Maximum characters is 32", "max2048": "Maximum characters is 2048", "iban": "The entered Iban is not correct", "invalidEmail": "The specified email is not correct", "invalidPhoneNumber": "The specified mobile number is not correct"}}, "printOrder": {"title": "Ecommerce", "orderNumber": "Order Number", "orderDate": "Order Date", "fullName": "Full Name", "email": "Email Address", "phoneNumber": "Phone Number", "zipCode": "Recipient's Zip Code", "address": "Recipient's Address", "paymentMethod": "Payment Method", "shippingMethod": "Shipping Method", "shippingPrice": "Shipping Cost", "netExpenses": "Net Expenses", "vat": "Tax", "totalPrice": "Total Cost", "printFactor": "Print Factor"}}, "retailer": {"retailer": "Distributor", "profile": {"success": "Your profile registration has been successfully completed", "isActive": "Active", "storeName": "Store Name", "description": "Description", "handle": "<PERSON><PERSON>", "domain": "Domain", "instagramId": "Instagram Id", "telegramId": "Telegram Id", "whatsappNumber": "Whatsapp Number", "logo": "Logo", "country": "Country", "city": "City", "state": "State", "company": "Company", "phoneNumber": "Phone Number", "address": "Address", "address1": "Address 1", "address2": "Address 2", "zip": "Zip Code", "name": "Name", "merchantLogo": "Merchant Logo", "addressInfo": "Address Information", "contactNumber": "Contact Number", "contactEmail": "Email", "submit": "Submit", "isAllowedCustomizable": "Customizable Brand (Yes/No)", "update": "Update", "skip": "Next", "contactInfo": "Contact Information", "step": {"generalInfo": "General Information", "personalInfo": "Personal Information", "store": "Store"}, "registerAsTitle": "Register As", "registerAs": {"legal": "Legal Entity", "personal": "Individual"}, "companyName": "Company Name", "companyType": "Company Type", "economicCode": "Economic Code", "registrationNumber": "Registration Number", "birthDay": "Date of Birth", "nationalCode": "National Code", "sex": "Gender", "male": "Male", "female": "Female", "vatNumber": "VAT Number", "personalInfo": "Personal Information", "stepperTooltip": "First, complete the manufacturer form", "fee": "Price", "feeType": "Price Type", "feeTypeItems": {"percentage": "Percentage", "fixedAmount": "Fixed Amount"}, "title": "Title", "url": "Website Address", "validations": {"requiredField": "This field is required.", "phoneNumber": "Invalid phone number", "email": "Invalid email", "nationalCode": "Invalid national code", "sex": "Invalid gender field", "positive": "Number must be greater than 0", "number": "Value must be a number", "feeType": "Invalid price type field", "feePercentage": "Price cannot be more than 100%", "minTitle": "Minimum characters is 3", "maxTitle": "Maximum characters is 100", "minHandle": "Minimum characters is 3", "maxHandle": "Maximum characters is 32"}}}, "productItem": {"by": "By", "minPrice": "Starting Price From", "price": "Price", "shipping": "Shipping", "addToWhishlist": "Add to Wishlist", "shippingTo": "Destination", "rate": "Rate", "shippingTime": "Delivery Time", "batchImport": "Batch Import", "addedToWhishlist": "Added to Wishlist", "product": "Product", "description": "Description", "variants": "Variants", "images": "Images", "openVariations": "View Product Variants", "variationDetail": {"headerTitle": "Product Variants", "image": "Image", "inventory": "Inventory", "price": "Price"}, "actionBar": {"pushToStore": "Add to Store", "save": "Save", "remove": "Remove"}, "viewProduct": "View Product", "variantHeaderItems": {"salesPrice": "Sales Price", "compareAtPrice": "Compare At Price", "image": "Image", "sku": "SKU", "inventory": "Inventory", "minimumPrice": "Minimum Price", "price": "Price", "profit": "Profit"}, "alert": {"variants": {"subtitle": "Are you sure you want to permanently delete this product variant? By clicking the delete button, the product variant will be permanently removed.", "verify": "Delete", "cancel": "Cancel"}}}, "retailerImport": {"successfullyPushedToStore": "Product successfully added to store", "disableSave": "Once a product is pushed to the store, it cannot be edited anymore.", "importedSuccessfully": "Your product has been successfully added! You can check it on My Products page.", "active": "Active", "titles": "Titles", "draft": "Draft", "products": "Products", "supplier": "Supplier", "status": "Status", "action": "Details", "importedAt": "Imported Date", "salesPrice": "Sales Price", "latestImports": "Latest Imports", "latestImportsDesc": "Products you've considered to publish on your store", "pushAll": "Push All", "push": "<PERSON><PERSON>", "product": "Product", "to": "To Store", "stores": "Stores", "storesDescription": "Select destination stores", "erroPushing": "There was a problem pushing the product.", "inPushables": "The product '{{name}}' is not pushable due to inventory depletion or supplier inactivity."}, "subscription": {"premiumProduct": "Premium Product", "importedProduct": "Product Imported from Other Platforms", "planRequired": "You need an active plan. Please choose from these.", "annualPaymentDiscount": "% Annual Payment Discount", "orderNumber": "Order Number", "siteNumber": "Site Number", "trialDays": "Trial Days", "price": "Price", "choosePlan": "Choose {{name}} Plan", "storeRequired": "You need to create a new store for this.", "home": "Home", "pricing": "Pricing", "title": "Flexible Plans Tailored to Your Needs!", "monthly": "Monthly", "yearly": "Yearly", "free": "Free"}, "retailerOrder": {"id": "ID", "customerName": "Customer Name", "destination": "Destination", "quantity": "Quantity", "orderNum": "Order Number", "byId": "Search by ID", "source": "Store", "shippingBillingId": "Shipping Destination", "shippingLocationId": "Billing Destination", "totalPrice": "Total Price", "orderContent": "Order Content", "orderState": "Order State", "paymentState": "Payment State", "deliveryState": "Delivery State", "orderAtFrom": "Order From", "orderAtTo": "Order To", "orderAt": "Order Date", "action": "Details", "filterList": "Filters", "paymentStateItems": {"pending": "Pending", "authorized": "Authorized", "captured": "Captured", "paid": "Paid", "refunded": "Refunded", "refundedpartially": "Partially Refunded", "voided": "Voided", "failed": "Failed"}, "orderStateItems": {"pending": "Pending", "inprogress": "In Progress", "done": "Done", "refunded": "Refunded", "canceled": "Canceled"}, "deliveryStateItems": {"pending": "Pending", "shipped": "Shipped", "shippedpartially": "Partially Shipped", "cancelled": "Canceled"}, "noOrders": "No orders available", "tabItems": {"orderSummary": "Order Summary", "orderHistory": "Order History"}, "pay": "Pay", "orderSummary": {"city": "City", "country": "Country", "addressState": "State", "address": "Address", "orderDetails": "Order Details", "dateAdded": "Date Added", "paymentMethod": "Payment Method", "shippingMethod": "Shipping Method", "customerDetails": "Customer Details", "customer": "Customer", "email": "Email", "phone": "Phone", "states": "States", "orderState": "Order State", "deliveryState": "Delivery State", "paymentState": "Payment State", "billingAddress": "Billing Address", "shippingAddress": "Shipping Address", "order": "Order", "product": "Product", "state": "State", "sku": "SKU", "qty": "Quantity", "unitPrice": "Unit Price", "total": "Total", "deliveryDate": "Delivery Date", "supplier": "Supplier", "subTotal": "Subtotal", "vat": "VAT", "shippingCost": "Shipping Cost", "grandTotal": "Grand Total", "paymentMethodSelect": "Select Payment Method"}}, "supplierOrder": {"id": "ID", "customerName": "Customer Name", "destination": "Destination", "quantity": "Quantity", "orderNum": "Order Number", "byId": "Search by ID", "source": "Store", "shippingBillingId": "Shipping Destination", "shippingLocationId": "Billing Destination", "totalPrice": "Total Price", "orderContent": "Order Content", "orderState": "Order State", "paymentState": "Payment State", "deliveryState": "Delivery State", "orderAtFrom": "Order From", "orderAtTo": "Order To", "orderAt": "Order Date", "action": "Details", "filterList": "Filters", "paymentStateItems": {"pending": "Pending", "authorized": "Authorized", "captured": "Captured", "paid": "Paid", "refunded": "Refunded", "refundedpartially": "Partially Refunded", "voided": "Voided", "failed": "Failed"}, "orderStateItems": {"pending": "Pending", "inprogress": "In Progress", "done": "Done", "refunded": "Refunded", "canceled": "Canceled"}, "deliveryStateItems": {"pending": "Pending", "shipped": "Shipped", "shippedpartially": "Partially Shipped", "cancelled": "Canceled"}, "lineItemsOrderStateItems": {"pending": "Pending", "fulfilled": "Fulfilled", "returnrequested": "Return Requested", "returnapproved": "Return Approved", "returnrejected": "Return Rejected", "returned": "Returned", "canceled": "Canceled"}, "noOrders": "No orders available", "tabItems": {"orderSummary": "Order Summary", "orderHistory": "Order History"}, "pay": "Pay", "orderSummary": {"city": "City", "country": "Country", "addressState": "State", "address": "Address", "orderDetails": "Order Details", "dateAdded": "Date Added", "paymentMethod": "Payment Method", "shippingMethod": "Shipping Method", "customerDetails": "Customer Details", "customer": "Customer", "email": "Email", "phone": "Phone", "states": "States", "orderState": "Order State", "deliveryState": "Delivery State", "paymentState": "Payment State", "billingAddress": "Billing Address", "shippingAddress": "Shipping Address", "order": "Order", "product": "Product", "state": "State", "sku": "SKU", "qty": "Quantity", "unitPrice": "Unit Price", "total": "Total", "deliveryDate": "Delivery Date", "supplier": "Supplier", "subTotal": "Subtotal", "vat": "VAT", "shippingCost": "Shipping Cost", "grandTotal": "Grand Total", "paymentMethodSelect": "Select Payment Method", "trackingCode": "Tracking Code"}, "confirm": "Confirm"}, "chats": {"chats": "Chats", "ago": "Ago", "typeMessage": "Type a message", "searchSuppliers": "Search Supplier", "empty": "No chats to show", "selectChat": "Select a Chat", "noMessage": "No Message", "lastUpdateAsc": "Last Update (Assending)", "lastUpdateDesc": "Last update (Descending)", "sortNameAsc": "By Name (Assending)", "sortcreatedAtAsc": "By Create Time (Asscending)", "sortNameDesc": "By Name Descending", "sortcreatedAtDesc": "By Create Time Descending", "sortBy": "Sort By", "typeNewMessage": "Type A Message", "newMessage": "New Message", "searchQuery": "Search ..."}, "serverErrors": {"ErrBadBodyRequest": "Request body have problems", "internal_error": "Internal Server Error", "user_notfound": "User Not found", "entity_not_found": "Entity Not found", "product_notfound": "Product Not Found", "retailer_notfound": "Retailer Not Found", "document_notfound": "Document Not Found", "retailer_info_notfound": "Retailer Info Not Found", "retailer_plan_notfound": "Retailer Plan Not Found", "supplier_notfound": "Supplier Not Found", "supplier_info_notfound": "Supplier Info Not Found", "user_type_mismatch": "User Type Doesn't Match", "return_policy_not_found": "Return Policy Not Found", "wrong_password": "Password is Wrong", "cant_upload_file": "Cant Upload The File", "retailer_not_found": "Retailer Not Found", "retailer_already_exist": "Retailer Already Exists", "retailer_info_already_exist": "Retailer Info Already Exists", "retailer_store_notfound": "Retailer Store Not Found", "supplier_already_exist": "Supplier Already Exists", "supplier_not_found": "Supplier Not Found", "supplier_info_already_exist": "Supplier Info Already Exists.", "return_policy_already_exist": "Return Policy Already Exists", "shipping_policy_not_found": "Shipping Policy Not Found"}, "productForm": {"gallery": "Gallery", "cover": "Cover Image", "pricing": "Priceing", "listingPrice": "Bulk Price", "retailPrice": "Retail Price", "coverFormat": "Format: JPG, PNG or PDF", "coverSize": "Max 2Mb", "imagSizeExceeded": "Image Size Exceeds Acceptable Amount.", "specialState": "This Product Has independent Shipping or Return Policy", "publish": "Publish", "specialShipping": "Special Shipping Policy", "newWithEnter": "َPress Enter Key to add ad New Value", "specialReturn": "Special Return Policy", "publishStatus": "Publish Status", "steps": {"general": "General Information", "categories": "Category", "gallery": "Gallery", "pricing": "Pricing"}}}