import {
  DEFUALT_LANG_LOCALE,
  LANGUAGES_REGEX_STRING,
  DEFUALT_CURRENCY_ID,
  LanguageType,
  TCurrenciesType
} from "@/constants/localization";
import React, { ReactNode, useContext, useMemo, useState } from "react";
import cookies from "js-cookie";
import Cookies from "js-cookie";

const getLang = () => {
  if (typeof window === "undefined") return undefined;
  const lang = new RegExp(LANGUAGES_REGEX_STRING, "gm").exec(window.location.href);
  if (lang && lang[1]) return lang[1] as LanguageType["value"];
};

type TContextDiapatchInput = {
  lang?: LanguageType["value"];
  currency?: TCurrenciesType;
  isDark?: boolean;
};

type TLocalizationContext = {
  lang: LanguageType["value"];
  currency: TCurrenciesType;
  isDark: boolean;
  setLocalization: (v: TContextDiapatchInput) => void;
};

const getIsDakrMode = () => {
  const browserValue = typeof window !== "undefined" && window?.matchMedia?.("(prefers-color-scheme:dark)")?.matches;
  return Boolean(Cookies.get("dark")) ?? (browserValue || false);
};

const getDefaulContext = (): Omit<TLocalizationContext, "setLocalization"> => ({
  lang: (getLang() ?? DEFUALT_LANG_LOCALE) as LanguageType["value"],
  currency: (cookies.get("currency") ? cookies.get("currency") : DEFUALT_CURRENCY_ID) as TCurrenciesType,
  isDark: getIsDakrMode()
});

export const InternationaliztionContext = React.createContext<TLocalizationContext>({
  ...getDefaulContext(),
  setLocalization: _v => {}
});

const LocalizationWrappper = ({ children }: { children: ReactNode }) => {
  const [localization, setLocaliz] = useState<Omit<TLocalizationContext, "setLocalization">>(getDefaulContext());

  const value: TLocalizationContext = useMemo(
    () => ({
      ...localization,
      setLocalization: (v: TContextDiapatchInput) => {
        setLocaliz(prev => ({ ...prev, ...v }));
      }
    }),
    [localization]
  );

  return <InternationaliztionContext.Provider value={value}>{children}</InternationaliztionContext.Provider>;
};

export function useLocalication() {
  const context = useContext(InternationaliztionContext);
  return context;
}

export default LocalizationWrappper;
