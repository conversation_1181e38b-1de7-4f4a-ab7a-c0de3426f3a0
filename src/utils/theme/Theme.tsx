import merge from "lodash/merge";
import { createTheme } from "@mui/material/styles";
import components from "./Components";
import typography from "./Typography";
import { shadows, darkshadows } from "./Shadows";
import { baseDarkTheme, baselightTheme } from "./DefaultColors";
import * as locales from "@mui/material/locale";
import { themeCustomizer } from "../theme";
import { useLocalication } from "@/utils/Internationalization";
import useDirection from "../hooks/useDirection";
import { PaletteMode } from "@mui/material";

export const BuildTheme = (config: any = {}) => {
  const { isDark } = useLocalication();
  const defaultTheme = isDark ? baseDarkTheme : baselightTheme;
  const defaultShadow = isDark ? darkshadows : shadows;

  const baseMode = {
    palette: {
      mode: (isDark ? "dark" : "light") as PaletteMode
    },
    shape: {
      borderRadius: themeCustomizer.borderRadius
    },
    shadows: defaultShadow as [
      "none",
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string,
      string
    ],
    typography: typography
  };
  const merged = merge({}, baseMode, defaultTheme, locales, {
    direction: config.direction
  });

  const theme = createTheme(merged);

  theme.components = components(theme);

  return theme;
};
const ThemeSettings = () => {
  const [activDir] = useDirection();
  const activeTheme = themeCustomizer.activeTheme;
  const theme = BuildTheme({
    direction: activDir,
    theme: activeTheme
  });

  theme.typography.h1 = {
    ...theme.typography.h1,
    fontSize: 56,
    fontFamily: "Iran yekanExtraBold",
    [theme.breakpoints.between("sm", "lg")]: {
      fontFamily: "Iran yekanExtraBold",
      fontSize: 48
    },
    [theme.breakpoints.down("sm")]: {
      fontFamily: "Iran yekanExtraBold",
      fontSize: 28
    }
  };

  theme.typography.h2 = {
    ...theme.typography.h2,
    fontSize: 36,
    fontFamily: "Iran yekanBold",
    [theme.breakpoints.between("sm", "lg")]: {
      fontFamily: "Iran yekanBold",
      fontSize: 40
    },
    [theme.breakpoints.down("sm")]: {
      fontFamily: "Iran yekanBold",
      fontSize: 24
    }
  };

  theme.typography.h3 = {
    ...theme.typography.h3,
    fontSize: 28,
    fontFamily: "Iran yekanBold",
    [theme.breakpoints.between("sm", "lg")]: {
      fontFamily: "Iran yekanBold",
      fontSize: 32
    },
    [theme.breakpoints.down("sm")]: {
      fontFamily: "Iran yekanBold",
      fontSize: 20
    }
  };

  theme.typography.h4 = {
    ...theme.typography.h4,
    fontSize: 24,
    fontFamily: "Iran yekanBold",
    [theme.breakpoints.between("sm", "lg")]: {
      fontFamily: "Iran yekanBold",
      fontSize: 24
    },
    [theme.breakpoints.down("sm")]: {
      fontFamily: "Iran yekanBold",
      fontSize: 18
    }
  };

  theme.typography.h5 = {
    ...theme.typography.h5,
    fontSize: 18,
    fontFamily: "Iran yekanBold",
    [theme.breakpoints.between("sm", "lg")]: {
      fontFamily: "Iran yekanBold",
      fontSize: 18
    },
    [theme.breakpoints.down("sm")]: {
      fontFamily: "Iran yekanBold",
      fontSize: 16
    }
  };

  theme.typography.subtitle1 = {
    ...theme.typography.subtitle1,
    fontSize: 16,
    fontFamily: "Iran yekan",
    [theme.breakpoints.between("sm", "lg")]: {
      fontFamily: "Iran yekan",
      fontSize: 16
    },
    [theme.breakpoints.down("sm")]: {
      fontFamily: "Iran yekan",
      fontSize: 14
    }
  };

  theme.typography.body1 = {
    ...theme.typography.body1,
    fontSize: 16,
    fontFamily: "Iran yekan",
    [theme.breakpoints.between("sm", "lg")]: {
      fontFamily: "Iran yekan",
      fontSize: 15
    },
    [theme.breakpoints.down("sm")]: {
      fontFamily: "Iran yekan",
      fontSize: 13
    }
  };
  theme.typography.body2 = {
    ...theme.typography.body2,
    fontSize: 15,
    fontFamily: "Iran yekan",
    [theme.breakpoints.between("sm", "lg")]: {
      fontFamily: "Iran yekan",
      fontSize: 15
    },
    [theme.breakpoints.down("sm")]: {
      fontFamily: "Iran yekan",
      fontSize: 13
    }
  };

  return theme;
};

export { ThemeSettings };
