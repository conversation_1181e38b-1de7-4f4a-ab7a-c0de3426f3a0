import { omit } from "lodash";

const colorPalette = {
  primary: {
    main: "#604FDC",
    light: "#E5EAFB",
    dark: "#604FDC"
  },
  success: {
    main: "#4BD08B",
    light: "#DFFFF3",
    dark: "#4BD08B"
  },
  error: {
    main: "#FB977D",
    light: "#FFEDE9",
    dark: "#FB977D"
  },
  info: {
    main: "#46A6EB",
    light: "#E1F5FA",
    dark: "#E1F5FA"
  },
  secondary: {
    main: "#00BEEC",
    light: "#E3F2F5",
    dark: "#00BEEC"
  },
  warning: {
    main: "#F8C076",
    light: "#FFF6EA",
    dark: "#F8C076"
  },
  grey: {
    "50": "#FAFAFA",
    "100": "#F2F6FA",
    "200": "#F0F5F9",
    "300": "#DFE5EF",
    "400": "#7C8FAC",
    "500": "#5A6A85",
    "600": "#111C2D",
    "700": "#616161",
    "800": "#424242",
    "900": "#212121",
    A100: "#F5F5F5",
    A200: "#EEEEEE",
    A400: "#BDBDBD",
    A700: "#616161"
  }
};
const baselightTheme = {
  direction: "ltr",
  palette: {
    purple: {
      A50: "#f1ebff",
      A100: "#8763da",
      A200: "#557fb9",
      A300: "#604FDC"
    },

    text: {
      primary: "#111c2d",
      secondary: "#111c2d"
    },
    action: {
      disabledBackground: "rgba(73,82,88,0.12)",
      hoverOpacity: 0.02,
      hover: "#f6f9fc"
    },
    divider: "#e5eaef",
    background: {
      default: "#F0F5F9",
      dark: "#F0F5F9",
      paper: "#ffffff"
    },
    ...colorPalette
  }
};

const baseDarkTheme = {
  direction: "ltr",
  palette: {
    purple: {
      A50: "#EBF3FE",
      A100: "#6610f2",
      A200: "#557fb9"
    },
    grey: {
      "50": "#050505",
      "100": "#0D0905",
      "200": "#0F0A06",
      "300": "#201A10",
      "400": "#837053",
      "500": "#A5957A",
      "600": "#EEE3D2",
      "700": "#9E9E9E",
      "800": "#BDBDBD",
      "900": "#DEDEDE",
      A100: "#0A0A0A",
      A200: "#111111",
      A400: "#424242",
      A700: "#9E9E9E"
    },
    text: {
      primary: "#EAEFF4",
      secondary: "#c6d1e9"
    },
    action: {
      disabledBackground: "rgba(73,82,88,0.12)",
      hoverOpacity: 0.02,
      hover: "#333F55"
    },
    divider: "#333F55",
    background: {
      default: "#15263A",
      dark: "#15263A",
      paper: "#121216"
    },
    ...omit(colorPalette, "grey")
  }
};

export { baseDarkTheme, baselightTheme };
