import { toCamel, toSnake } from "ts-case-convert";
import { camelCaseToSnakeCase, snakeCaseToCamelCase } from "../typescript/snakeCaseToCamelCase";

type AnyObject = { [key: string]: any };

/**
 * Recursively converts snake_case to camelCase for objects and arrays.
 * @param data - The input data (object or array) to convert.
 * @returns The converted data with camelCase keys. (typescript support)
 */
export const convertSnakeToCamel = <T>(data: T): snakeCaseToCamelCase<T> => {
  if (Array.isArray(data)) {
    return data.map(item => convertSnakeToCamel(item)) as unknown as snakeCaseToCamelCase<T>;
  } else if (data !== null && typeof data === "object") {
    return Object.keys(data).reduce((acc, key) => {
      const camelKey = toCamel(key);
      acc[camelKey] = convertSnakeToCamel((data as any)[key]);
      return acc;
    }, {} as AnyObject) as snake<PERSON>aseToCamelCase<T>;
  }
  // Return the data as is if it's neither an object nor an array
  return data as snakeCaseToCamelCase<T>;
};

/**
 * Recursively converts camelCase to snake_case for objects and arrays.
 * @param data - The input data (object or array) to convert.
 * @returns The converted data with snake_case keys. (typescript support)
 */
export const convertCamelToSnake = <T>(data: T): camelCaseToSnakeCase<T> => {
  if (Array.isArray(data)) {
    return data.map(item => convertCamelToSnake(item)) as unknown as camelCaseToSnakeCase<T>;
  } else if (data !== null && typeof data === "object") {
    return Object.keys(data).reduce((acc, key) => {
      const camelKey = toSnake(key);
      acc[camelKey] = convertCamelToSnake((data as any)[key]);
      return acc;
    }, {} as AnyObject) as camelCaseToSnakeCase<T>;
  }
  // Return the data as is if it's neither an object nor an array
  return data as camelCaseToSnakeCase<T>;
};

export const ObjectToQueryString = (obj?: any): string | null => {
  if (typeof obj !== "object") return null;
  return Object.entries(obj)
    ?.map(item => `${item?.[0]}=${item?.[1]}`)
    .join("&");
};
