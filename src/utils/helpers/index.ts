import Cookies, { CookieAttributes } from "js-cookie";
import omitBy from "lodash/omitBy";
import isEmptyLoadash from "lodash/isEmpty";
import isArray from "lodash/isArray";
import isObject from "lodash/isObject";

export const setCookie = (name: string, value: string, options?: CookieAttributes): string | undefined =>
  Cookies.set(name, value, options);

export const getCookie = (name: string): string | undefined => Cookies.get(name);

export const removeCookie = (name: string, options?: CookieAttributes): void => Cookies.remove(name, options);

export const readFile = (
  file: Blob,
  as: "arraybuffer" | "dataurl" | "text" | "binarystring" = "dataurl"
): Promise<string | ArrayBuffer | null | undefined> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = event => {
      resolve(event.target?.result);
    };
    reader.onerror = event => {
      reject(event);
    };
    if (as === "dataurl") reader.readAsDataURL(file);
    if (as === "arraybuffer") reader.readAsArrayBuffer(file);
    if (as === "text") reader.readAsText(file);
    if (as === "binarystring") reader.readAsBinaryString(file);
  });
};

export function isEmpty<T>(value: T) {
  return (
    value === undefined ||
    value === null ||
    (typeof value === "object" && Object.keys(value).length === 0) ||
    (typeof value === "string" && value.trim().length === 0)
  );
}

export function enNumber(value: string | number): string {
  if (!value) return value as string;
  return (value + "")
    .replace(/[٠١٢٣٤٥٦٧٨٩]/gm, v => String.fromCharCode(v.charCodeAt(0) - 1584))
    .replace(/[۰۱۲۳۴۵۶۷۸۹]/gm, v => String.fromCharCode(v.charCodeAt(0) - 1728));
}

export function faNumber(value: string | number): string {
  if (!value) return value as string;
  return (value + "")
    .replace(/[٠١٢٣٤٥٦٧٨٩]/gm, v => String.fromCharCode(v.charCodeAt(0) - 1584))
    .replace(/\d/gm, v => String.fromCharCode(v.charCodeAt(0) + 1728));
}

export function arNumber(value: string | number): string {
  if (!value) return value as string;
  return (value + "")
    .replace(/[۰۱۲۳۴۵۶۷۸۹]/gm, v => String.fromCharCode(v.charCodeAt(0) - 1728))
    .replace(/\d/gm, v => String.fromCharCode(v.charCodeAt(0) + 1584));
}

export function toCommas(value: number) {
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export const convertToJson = (params: string) => {
  return JSON.parse('{"' + params.replace(/&/g, '","').replace(/=/g, '":"') + '"}', function (key, value) {
    return key === "" ? value : decodeURIComponent(value);
  });
};

export function toQueryString(o: { [k: string | number]: string | number | Array<number | string> }) {
  return Object.keys(o)
    .filter(key => o[key] !== undefined && o[key] !== null)
    .map(function (key) {
      let ret = [];
      if (Array.isArray(o[key])) {
        (o[key] as Array<string>).forEach((item: string) => {
          ret.push(`${key}=${encodeURIComponent(item)}`);
        });
      } else {
        ret.push(`${key}=${encodeURIComponent(o[key] as string)}`);
      }
      return ret.join("&");
    })
    .join("&");
}

export function calculateTotalCount({ pageSize, totalCount }: { pageSize: number; totalCount: number }) {
  return Math.ceil(totalCount / pageSize);
}

export function omitEmptyValues(obj: any) {
  return omitBy(obj, v => (isObject(v) || isArray(v) || typeof v === "string" ? isEmptyLoadash(v) : v === undefined));
}

/** checks if param is string */
export const isString = (val: any) => typeof val === "string";
/** checks if param is number */
export const isNumber = (val: any) => typeof val === "number";

/** checks if param is number string */
export const isNumberString = (val: any) => isString(val) && !Number.isNaN(Number(val));
/** checks if param is number in value */
export const isNumberOrNumberStr = (val: any) => isNumber(val) || isNumberString(val);
