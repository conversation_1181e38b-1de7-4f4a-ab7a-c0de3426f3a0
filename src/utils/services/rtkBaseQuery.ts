import { AxiosRequestConfig } from "axios";
import { apiService } from "./apiService";
import { IFieldError } from "../../../types/generalTypes";
import i18n from "../i18n";
import { toast } from "react-toastify";
import { logOut } from "../helpers";
import { convertSnakeToCamel } from "../helpers/objectHelper";

function alertFieldsError(fields: IFieldError["fields"]) {
  return fields.forEach(item => toast.error(i18n.t("product.validations.required") + ": " + item.field));
}

export const rtkBaseQuery: any = (RTKParams: AxiosRequestConfig) =>
  apiService
    .request(RTKParams)
    .then(response => ({
      data: convertSnakeToCamel(response)
    }))
    .catch(error => {
      const unauthorized = error?.response?.status === 401;
      if (unauthorized) logOut();

      return {
        error: {
          status: error.response?.status,
          data: error.response?.data || error.message
        }
      };
    });
