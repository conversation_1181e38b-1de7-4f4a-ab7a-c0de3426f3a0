"use client";
import axios, { CreateAxiosDefaults } from "axios";
import { BASE_URL } from "@/constants";
import { USERTYPES, USER_CONTEXT_KEYS } from "@/constants/userTypes";
import useSessionStore from "@/store/zustand/sessionStore";

export function axiosService(conf: CreateAxiosDefaults) {
  const axiosInstance = axios.create(conf);

  return axiosInstance;
}

export const authApiService = axiosService({
  baseURL: BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json"
  }
});

authApiService.interceptors.request.use(
  async req => {
    const session = useSessionStore.getState()?.getSession();
    const token = session?.access_token;
    const userType = session?.user_type || (req?.data?.userType as USERTYPES);

    if (token) {
      req.headers.setAuthorization(`Bearer ${token}`);
    }
    if (userType) req.headers.set("X-User-Context", USER_CONTEXT_KEYS[userType]);

    return req;
  },
  err => Promise.reject(err)
);

authApiService.interceptors.response.use(res => {
  return res.data;
}, null);
