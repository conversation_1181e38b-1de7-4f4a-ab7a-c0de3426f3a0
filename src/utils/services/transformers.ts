export type TGenerateBackendFiltersProps = {
  [key in string]?: string | string[] | number | number[] | boolean | Array<any>;
};

/**
 * Make filters for our backend
 * @param filters is objects which key's is string and values can string | string[] | boolean | array<any>
 * @returns stringified array of object
 * @example
 * [{"field":"top_suppliers","operator":"=","value":true}]
 */
export const generateBackendFilters = (filters?: TGenerateBackendFiltersProps) => {
  const filtersArray =
    !!filters && Object.values(filters)?.length
      ? Object.entries(filters)
          .filter(([_, value]) =>
            typeof value === "string" || Array.isArray(value)
              ? value?.length && value
              : value !== undefined && value !== null
          )
          .map(([key, value]) => {
            const formattedValue = Array.isArray(value)
              ? `[${value.map(v => `"${v}"`).join(",")}]` // Keep arrays intact
              : typeof value === "string"
                ? `"${value}"`
                : value; // Keep strings as strings

            return `{"field":"${key}","operator":"=","value":${formattedValue}}`;
          })
      : [];

  const filtersStr = filtersArray.length ? `[${filtersArray.join(",")}]` : "";

  return filtersStr;
};

export type TGenerateBackendSortsProps = {
  [key in string]?: "asc" | "desc";
};

/**
 * Make sorts for our backend
 * @param sorts object
 * @example
 * {"created_at":asc}
 */
export const generateBackendSorts = (sorts?: TGenerateBackendSortsProps) => {
  const finalValue = sorts
    ? `${Object.entries(sorts)
        .map(([key, value]) => `{"${key}":"${value}"}`)
        .join(",")}`
    : "";

  return finalValue;
};
