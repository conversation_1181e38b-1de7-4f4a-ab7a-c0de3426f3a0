import { authApiRoutes } from "@/constants/apiRoutes/apiServiceRoutes";
import { SessionData, SessionStore } from "@/store/zustand/sessionStore";
import { checkExpireToken, logOut } from "../helpers";
import { apiService } from "./apiService";

type TRefreshToken = {
  sessionData: SessionData | null;
  setSession: SessionStore["setSession"];
};

/* ------------ Flag to track if a refresh request is in progress ----------- */
let isRefreshing = false;
/* --------------------- Array to hold pending requests --------------------- */
let pendingRequests: Function[] = [];

const onRefreshed = (newSessionData: SessionData) => {
  pendingRequests.forEach(callback => callback(newSessionData));
  pendingRequests = [];
};

export const refreshToken = async ({ sessionData, setSession }: TRefreshToken) => {
  if (!sessionData) return;

  const {
    access_token,
    refresh_token,
    user_type,
    initial_date_created,
    refresh_token_expires_in,
    date_created,
    expires_in
  } = sessionData;

  /* ---- Check if both access and refresh tokens are expired, then log out --- */
  if (access_token && checkExpireToken(initial_date_created as string, refresh_token_expires_in as number)) {
    logOut();
    return;
  }

  /* -- Check if the access token is expired but refresh token is still valid - */
  if (access_token && checkExpireToken(date_created as string, expires_in as number)) {
    if (isRefreshing) {
      return new Promise(resolve => pendingRequests.push(resolve));
    }

    isRefreshing = true;

    try {
      const body = {
        token: refresh_token
      };

      const response = await apiService.post(authApiRoutes.refreshToken, body);
      const refreshedTokens = response?.data;

      const newSessionData = {
        ...sessionData,
        access_token: refreshedTokens.token,
        refresh_token: refreshedTokens.refresh_token,
        expires_in: refreshedTokens.expires_in,
        date_created: new Date().toISOString()
      };

      /* --------------------- Update session with new tokens --------------------- */
      setSession(newSessionData);
      /* ----------------------- Notify all pending requests ---------------------- */
      onRefreshed(newSessionData);
    } catch (error) {
      console.error("Error refreshing the access token:", error);
      logOut();
    } finally {
      /* ----------------------------- Reset the flag ----------------------------- */
      isRefreshing = false;
    }
  }
};
