import { AxiosRequestConfig } from "axios";
import { IFieldError } from "../../../types/generalTypes";
import i18n from "../i18n";
import { toast } from "react-toastify";
import { authApiService } from "./authApiService";

function alertFieldsError(fields: IFieldError["fields"]) {
  return fields.forEach(item => toast.error(i18n.t("product.validations.required") + ": " + item.field));
}

export const authRtkBaseQuery: any = (RTKParams: AxiosRequestConfig) =>
  authApiService
    .request(RTKParams)
    .then(response => ({
      data: response
    }))
    .catch(error => {
      return {
        error: {
          status: error.response?.status,
          data: error.response?.data || error.message
        }
      };
    });
