import axios, { AxiosError, CreateAxiosDefaults } from "axios";
import { BASE_URL, RETAILER_USER_TYPE } from "@/constants";
import { USER_CONTEXT_KEYS } from "@/constants/userTypes";
import useSessionStore from "@/store/zustand/sessionStore";
import { STORE_ID } from "@/constants/cookies";
import Cookies from "js-cookie";
import * as Sentry from "@sentry/nextjs";
import { refreshToken } from "./refreshToken";
import { store } from "@/store/store";
import { useRetailerStore } from "@/store/zustand/retailerStore/RetailerStore";

export type ApiError = {
  error: string;
  message?: string;
  error_detail:
    | {
        [k: string]: [string, string];
      }
    | Array<string>;
};

export function axiosService(conf: CreateAxiosDefaults) {
  const axiosInstance = axios.create(conf);

  return axiosInstance;
}

export const apiService = axiosService({
  baseURL: BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json"
  }
});

apiService.interceptors.response.use(
  res => {
    const session = useSessionStore.getState()?.getSession();

    refreshToken({ sessionData: session, setSession: useSessionStore.getState()?.setSession });

    // Check if the response type is blob
    if (res.config.responseType === "blob") {
      return res;
    }

    return res.data;
  },
  async (error: AxiosError<ApiError>) => {
    Sentry.captureException(error);

    try {
      return Promise.reject(error);
    } catch (err) {
      return Promise.reject(err);
    }
  }
);

apiService.interceptors.request.use(
  async req => {
    const session = useSessionStore.getState()?.getSession();
    const token = session?.access_token;
    const userType = session?.user_type;
    const selectedStoreId = useRetailerStore?.getState()?.selectedRetailerStoreId;
    const storeValue = Cookies.get(STORE_ID) || selectedStoreId;
    const isRetailer = userType === RETAILER_USER_TYPE;

    if (token) {
      req.headers.setAuthorization(`Bearer ${token}`);
    }
    if (userType) req.headers.set("X-User-Context", USER_CONTEXT_KEYS[userType]);
    if (storeValue && isRetailer) req.headers.set("X-Store-ID", storeValue);

    return req;
  },
  err => {
    console.error("Request error:", err); // Log any request errors
    return Promise.reject(err);
  }
);
