import { useState } from "react";

function useClipboard(timeout: number = 2000) {
  const [isCopied, setIsCopied] = useState<boolean>(false);
  const [copiedText, setCopiedText] = useState<string | null>(null);

  const copyToClipboard = async (text: string) => {
    if (!navigator.clipboard) {
      console.warn("Clipboard API is not available");
      return false;
    }

    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(text);
      setIsCopied(true);

      // Set timeout to reset the isCopied state
      setTimeout(() => setIsCopied(false), timeout);
      return true;
    } catch (error) {
      console.error("Failed to copy text to clipboard", error);
      return false;
    }
  };

  return { isCopied, copiedText, copyToClipboard };
}

export default useClipboard;
