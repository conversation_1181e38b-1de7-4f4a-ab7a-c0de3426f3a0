import { useSearchParams, useRouter, ReadonlyURLSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import useWithRef from "./useWithRef";
import isEqual from "lodash/isEqual";
import { ActionCreatorWithPayload } from "@reduxjs/toolkit";
import useDebounce from "./useDebounce";
import { toQueryString } from "../helpers";
import { RootState } from "@/store/store";

export default function useRouteEmbedFilters<T>(
  selector: (state: RootState) => T,
  formater: (s: ReadonlyURLSearchParams) => T,
  dispatcher: ActionCreatorWithPayload<T, string>
): [T, (k: keyof T, v: any) => void, T] {
  const storeFilters = useSelector(selector, shallowEqual);
  const [filterState, setFiltersSate] = useState<T>({ ...storeFilters });
  const dispatch = useDispatch();
  const router = useRouter();

  const searchParams = useSearchParams();

  const filterRef = useWithRef<T>(filterState);
  useEffect(() => {
    const newPrams: T = formater(searchParams);

    if (!isEqual(newPrams, filterRef.current)) {
      setFiltersSate(newPrams);
      dispatch(dispatcher(newPrams));
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams, filterRef, dispatch]);

  const debouncedFilterChange = useDebounce((filterValue: T) => {
    if (isEqual(storeFilters, filterValue)) return;
    if (filterValue) {
      const newValue = Object.entries(filterValue).reduce((newVal, [key, value]) => {
        if ((value && !Array.isArray(value)) || (Array.isArray(value) && value.length)) {
          return { ...newVal, [key]: value };
        }
        return newVal;
      }, {} as T);

      dispatch(dispatcher(filterValue));
      const params = toQueryString(newValue as any);
      router.push(window.location.pathname + "?" + params, { shallow: true, scroll: false });
    }
  }, 850);

  const handleChange = (key: keyof T, value: any) => {
    setFiltersSate(prev => {
      const newVal = { ...prev, [key]: value };
      debouncedFilterChange(newVal);
      return newVal;
    });
  };
  return [filterState, handleChange, storeFilters];
}
