import { RefObject, useEffect, useRef, useState } from "react";
import useWithRef from "./useWithRef";

export default function useResizeObserver<T>(callback?: (s: number) => void): [RefObject<T>, number] {
  const [state, setState] = useState<number>(0);
  const stateRef = useWithRef(state);
  const ref: RefObject<T | Element> = useRef<T | Element>(null);

  useEffect(() => {
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        if (entry.contentBoxSize) {
          const size = entry.contentBoxSize.reduce((total, { inlineSize }) => total + inlineSize, 0);
          if (stateRef.current !== size) {
            setState(size);
            callback && callback(size);
          }
        }
      }
    });

    if (ref.current) {
      resizeObserver.observe(ref.current as Element);
      return () => resizeObserver.disconnect();
    }
  });
  return [ref as RefObject<T>, state];
}
