import { usePostConversationMessageStartMutation } from "@/store/apps/conversation";
import { useRouter } from "next/navigation";
import useRoleBasePath from "./useRoleBasePath";
import { routes } from "@/constants/routes";
import { clientDefaultErrorHandler } from "../services/utils";

interface IConversationStart {
  content: string;
  partnerId?: string;
}

export const useConversationStart = () => {
  const router = useRouter();
  const makePath = useRoleBasePath();
  const [postConversationStart, { isLoading: isConversationLoading }] = usePostConversationMessageStartMutation();

  const onChatStart = async ({ content, partnerId }: IConversationStart) => {
    if (!partnerId) return;

    try {
      await postConversationStart({
        body: {
          content,
          contentType: "Text",
          partnerId
        }
      }).then(res => {
        // const error = (res as any)?.error?.data;

        if ((res as any)?.error) {
          clientDefaultErrorHandler({ error: (res as any)?.error });
        }

        // if (error) {
        //   clientDefaultErrorHandler(error as ApiError, SnakeToCamelFieldErrorWrapper(setFieldError));
        // }

        if ("data" in res && res?.data) {
          router.push(makePath(routes.chat));
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  return { onChatStart, isConversationLoading };
};
