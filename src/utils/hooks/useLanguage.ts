/* eslint-disable react-hooks/exhaustive-deps */
import { usePathname } from "next/navigation";
import { useEffect, useMemo } from "react";
import { LANGUAGES, LANGUAGES_REGEX_STRING, LanguageType } from "../../constants/localization";
import { useTranslation } from "react-i18next";
import { IS_IRAN_SERVED } from "@/constants";
import { useLocalication } from "@/utils/Internationalization";

export default function useLanguage(): [LanguageType, () => null | string, string] {
  const pathname = usePathname();
  const { setLocalization, ...localization } = useLocalication();
  const { i18n } = useTranslation();

  const getPathLang: () => null | LanguageType["value"] = () => {
    const lang = new RegExp(LANGUAGES_REGEX_STRING, "gm").exec(pathname);
    if (lang && lang[1]) return lang[1] as LanguageType["value"];
    return null;
  };

  useEffect(() => {
    if (IS_IRAN_SERVED) return;
    const lang = getPathLang();

    if (lang && i18n.language !== lang) {
      i18n.changeLanguage(lang);
    }
    if (lang && localization.lang !== lang) {
      setLocalization({ lang });
    }
  }, [pathname, localization.lang]);

  const currentLang = useMemo(() => {
    return LANGUAGES.find(_lang => _lang.value === localization.lang) || LANGUAGES[0];
  }, [localization.lang]);

  return [currentLang, getPathLang, pathname];
}
