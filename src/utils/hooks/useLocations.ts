import { useGetMetaLocationsQuery } from "@/store/apps/meta";
import { TMetaLocations } from "@/store/apps/meta/types";

export default function useLocations() {
  const { data: countries, isLoading } = useGetMetaLocationsQuery();

  const getLocation = (cityId: string | number): TMetaLocations | null => {
    if (countries?.data) {
      const attachParent = (
        locations: TMetaLocations[],
        parent: TMetaLocations | null = null
      ): TMetaLocations | null => {
        for (let index = 0; index < locations.length; index++) {
          const location = locations[index];
          if (location.id === cityId) {
            return { ...location, parent }; // Attach the parent to the location
          }
          if (location.subLocations) {
            const subLocation = attachParent(location.subLocations, location);
            if (subLocation) {
              return subLocation;
            }
          }
        }
        return null;
      };

      return attachParent(countries.data);
    }
    return null;
  };

  return { isLoading, countries, getLocation };
}
