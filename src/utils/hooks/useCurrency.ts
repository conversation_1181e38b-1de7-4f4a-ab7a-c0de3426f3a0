import { useEffect, useMemo } from "react";
import cookies from "js-cookie";
import { DEFUALT_CURRENCY_ID, TCurrenciesType } from "@/constants/localization";
import { useLocalication } from "@/utils/Internationalization";
import { useGetMetaCurrenciesQuery } from "@/store/apps/meta";
import { isString } from "lodash";
import { arNumber, enNumber, faNumber, isValidUUID } from "../helpers";
import { shallowEqual, useSelector } from "react-redux";
import { TSupplierProfileData } from "@/store/apps/supplier/types";
import { TRetailerProfileData } from "@/store/apps/retailer/types";
import { ICurrency } from "@/app/retailer/(dashboard)/accountInfo/AccountCurrency";

export interface IUiCurrency extends ICurrency {
  render: (v?: number | string, { showSymbol }?: { showSymbol?: boolean }) => string;
}

const numConverter = {
  IRR: faNumber,
  USD: enNumber,
  EUR: enNumber,
  AED: arNumber,
  SAR: arNumber
};

const fallBackData: ICurrency = {
  iso: "IRR",
  name: "ریال",
  symbol: "﷼",
  precision: 0,
  symbolPosition: "After",
  id: "413876e3-ea28-44f8-8208-16f01eac015c",
  isActive: true,
  createdAt: "0001-01-01T00:00:00Z",
  updatedAt: "0001-01-01T00:00:00Z"
};

export default function useCurrency(): [c: IUiCurrency, s: (cc: TCurrenciesType) => void, Array<ICurrency>, string] {
  const { data: allCurrencies } = useGetMetaCurrenciesQuery();
  const { currency: currID, setLocalization } = useLocalication();

  /* -------------------------------- supplier -------------------------------- */
  const supplierData = useSelector(
    (state: any) => state?.Supplier?.queries[`getSupplierProfile(undefined)`]?.data,
    shallowEqual
  ) as { data: TSupplierProfileData };

  /* -------------------------------- retailer -------------------------------- */
  const retailerData = useSelector(
    (state: any) => state?.Supplier?.queries[`getRetailerProfile(undefined)`]?.data,
    shallowEqual
  ) as { data: TRetailerProfileData };

  /* ----------------------------- get currency id ---------------------------- */
  const getCurrencyId = () => {
    if (supplierData?.data?.currencyId && isValidUUID(supplierData?.data?.currencyId))
      return supplierData?.data?.currencyId;
    if (retailerData?.data?.currencyId && isValidUUID(retailerData?.data?.currencyId))
      return retailerData?.data?.currencyId;

    return currID;
  };
  const currencyId = getCurrencyId();

  const selectCurrency = (c: string) => {
    cookies.set("currency", c);
    setLocalization({ currency: c });
  };

  useEffect(() => {
    const currency = currencyId || cookies.get("currency") || DEFUALT_CURRENCY_ID;
    if (currency !== currID) selectCurrency(currency);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currencyId]);

  const currencyObj: IUiCurrency = useMemo(() => {
    const res = allCurrencies?.data.find(curr => curr?.id === currencyId) || fallBackData;

    return {
      ...res,
      render: (val, { showSymbol } = { showSymbol: true }) => {
        const formated = val !== undefined ? (isString(val) ? Number(val) : val).toLocaleString() : "";
        const converted =
          res?.iso in numConverter ? numConverter[res?.iso as keyof typeof numConverter](formated) : formated;

        if (!showSymbol) {
          return converted;
        }

        return res.symbolPosition === "After" ? `${converted} ${res.symbol}` : `${res.symbol} ${converted}`;
      }
    };
  }, [currencyId, allCurrencies?.data]);

  const curencyOptions = allCurrencies?.data || ([] as ICurrency[]);

  const selectedCurrencyId = currencyObj?.id || currencyId || cookies.get("currency") || DEFUALT_CURRENCY_ID;

  return [currencyObj, selectCurrency, curencyOptions, selectedCurrencyId];
}
