import { IS_IRAN_SERVED } from "@/constants";
import { useGetMetaLocationsQuery } from "@/store/apps/meta";
import { TMetaLocations } from "@/store/apps/meta/types";
import { useCallback, useMemo } from "react";

export default function useShippingLocation() {
  const { data: countries } = useGetMetaLocationsQuery();

  const map = useMemo(() => {
    if (!countries?.data) return null;
    const countriesMap = new Map(countries?.data?.map(item => [item.id, item]));
    if (IS_IRAN_SERVED) {
      const iranData = countriesMap.get(1);
      const cities = new Map(
        iranData?.subLocations?.flatMap(state =>
          state.subLocations ? state.subLocations?.map(city => [city.id, city]) : []
        )
      );
      return cities;
    }
    return countriesMap;
  }, [countries?.data]);

  const findLocation = useCallback(
    (id: number): TMetaLocations | undefined => {
      return map?.get(id);
    },
    [map]
  );

  return findLocation;
}
