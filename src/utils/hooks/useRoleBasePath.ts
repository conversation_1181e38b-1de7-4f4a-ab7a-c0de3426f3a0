import useRole from "./useRole";

export default function useRoleBasePath() {
  const role = useRole();
  const makePath = (href: string): string => {
    if (href && process.env.NODE_ENV === "development") {
      if (href.startsWith("/")) {
        return `/${role?.toLocaleLowerCase()}${href}`;
      }
      const routs = window.location.pathname.split("/");
      return routs
        .slice(0, routs.length - 1)
        .concat(href.split("/"))
        .join("/");
    }
    return href;
  };
  return makePath;
}
