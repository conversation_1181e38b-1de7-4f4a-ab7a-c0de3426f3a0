import { useGetMetaCategoriesQuery } from "@/store/apps/meta";
import { TMetaCategoriesData } from "@/store/apps/meta/types";

export default function useCategories() {
  const { data: categories, isLoading } = useGetMetaCategoriesQuery();
  const getCategory = (catId: string | number) => {
    if (categories?.data) {
      const getter = (categories: TMetaCategoriesData[]): TMetaCategoriesData | null => {
        for (let index = 0; index < categories.length; index++) {
          const category = categories[index];
          if (category.id === catId) return category;
          if (category.subCategories) return getter(category.subCategories);
        }
        return null;
      };
      return getter(categories.data);
    }
    return null;
  };

  return { isLoading, categories, getCategory };
}
