import useModalStore from "@/store/zustand/modalStore";
import { TOpenModalProps } from "@/store/zustand/modalStore/ModalStore";

export type TShowModal = (props: TOpenModalProps) => void;

const useModal = () => {
  const { openModal, closeModal } = useModalStore();

  const showModal: TShowModal = props => {
    openModal(props);
  };

  const hideModal = () => {
    closeModal();
  };

  return { showModal, hideModal };
};

export default useModal;
