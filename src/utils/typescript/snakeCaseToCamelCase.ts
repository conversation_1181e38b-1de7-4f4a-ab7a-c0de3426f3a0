type CamelCase<S extends string> = S extends `${infer P1}_${infer P2}${infer P3}`
  ? `${Lowercase<P1>}${Uppercase<P2>}${CamelCase<P3>}`
  : Lowercase<S>;

export type snakeCaseToCamelCase<T> = T extends object
  ? {
      [K in keyof T as CamelCase<string & K>]: T[K] extends Array<infer U>
        ? Array<snakeCaseToCamelCase<U>>
        : snakeCaseToCamelCase<T[K]>;
    }
  : T;

type SnakeCase<S extends string> = S extends `${infer C}${infer T}`
  ? T extends Uncapitalize<T>
    ? `${Lowercase<C>}${SnakeCase<T>}`
    : `${Lowercase<C>}_${SnakeCase<Uncapitalize<T>>}`
  : S;

export type camelCaseToSnakeCase<T> = T extends object
  ? {
      [K in keyof T as SnakeCase<string & K>]: T[K] extends Array<infer U>
        ? Array<camelCaseToSnakeCase<U>>
        : camelCaseToSnakeCase<T[K]>;
    }
  : T;
