import * as yup from "yup";

import "yup-phone";

const phoneSchema = yup.string().phone().required();
const emailSchema = yup.string().email().required();

export const identifyInput = (input: string): "email" | "phone" | "unknown" => {
  const isEmail = emailSchema.isValidSync(input);
  const isPhone = phoneSchema.isValidSync(input);

  if (isEmail) {
    return "email";
  } else if (isPhone) {
    return "phone";
  } else {
    return "unknown";
  }
};
