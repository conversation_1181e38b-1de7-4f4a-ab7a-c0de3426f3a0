import { ensureUrlScheme } from "./helpers";

export const popupManager = (function () {
  // Private variable to store popup reference
  let popupWindow: Window | null = null;

  // Return public methods
  return {
    open: function (url: string | undefined | null, width: number, height: number): Window | null {
      /* ---------------------- Close existing popup if open ---------------------- */
      if (popupWindow && !popupWindow.closed) {
        popupWindow.close();
      }

      /* ---- Get the URL with proper scheme - assuming ensureUrlScheme exists ---- */
      const targetUrl = ensureUrlScheme(url ?? "");

      /* ----------------------- Calculate centered position ---------------------- */
      const left = (window.screen.width - width) / 2;
      const top = (window.screen.height - height) / 2;

      /* --------------------- Open the popup with positioning -------------------- */
      popupWindow = window.open(
        targetUrl,
        "popup",
        `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
      );

      /* -------------- Add an interval to check if window was closed ------------- */
      const checkClosed = setInterval(() => {
        if (popupWindow && popupWindow.closed) {
          clearInterval(checkClosed);
          popupWindow = null;
        }
      }, 500);

      return popupWindow;
    },

    close: function (): void {
      if (popupWindow && !popupWindow.closed) {
        popupWindow.close();
        popupWindow = null;
      }
    },

    isOpen: function (): boolean {
      return !!popupWindow && !popupWindow.closed;
    }
  };
})();
