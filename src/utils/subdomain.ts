export const getValidSubdomain = (host?: string | null) => {
  const retailerSubdomain = process.env.NEXT_PUBLIC_RETAILER_SUBDOMAIN?.trim() || "";
  const supplierSubdomain = process.env.NEXT_PUBLIC_SUPPLIER_SUBDOMAIN?.trim() || "";

  if (!host && typeof window !== "undefined") {
    host = window.location.host;
  }

  if (host) {
    if (host.includes("localhost") || host.includes("/")) {
      return null;
    }

    if (retailerSubdomain && host.includes(retailerSubdomain)) {
      return "retailer";
    }
    if (supplierSubdomain && host.includes(supplierSubdomain)) {
      return "supplier";
    }
  }

  return null;
};
