import { useTheme } from "@mui/material";
import React, { forwardRef } from "react";

export default function withClassname<P extends object>(
  WrappedComponent: React.ComponentType<P & { className?: string }> | string,
  mainClassName: string
): React.ForwardRefExoticComponent<
  React.PropsWithoutRef<P> & React.RefAttributes<HTMLElement> & { className?: string }
> {
  const Component = forwardRef<HTMLElement, P & { className?: string }>(({ className, ...props }, ref) => {
    const theme = useTheme();
    const combinedProps = {
      ...props,
      className: mainClassName + " " + (className ?? "") + " " + theme.palette.mode,
      ref
    } as unknown as P & { className?: string };

    return <WrappedComponent {...combinedProps} />;
  });

  Component.displayName = `withClassname(${(WrappedComponent as any).displayName || (WrappedComponent as any).name || "Component"})`;

  return Component;
}
