@tailwind base;

/* 
https://tailwindcss.com/docs/customizing-colors#using-css-variables
*/
@layer base {
  :root {
    --color-background: 240 242 245;
    --color-cards: 255 255 255;
    --color-info-50: 227 234 253;
    --color-gray-20: 247 249 252;
    --color-gray-40: 245 245 245;
    --color-gray-50: 233 233 233;
    --color-gray-100: 212 212 212;
    --color-gray-200: 192 192 192;
    --color-gray-400: 145 145 145;
    --color-gray-500: 117 117 117;
    --color-gray-600: 106 106 106;
    --color-gray-999: 5 5 5;
    --color-purple-40: 248 247 255;
    --color-purple-50: 239 237 252;
    --color-purple-500: 96 79 220;
    --color-purple-700: 77 63 176;
    --color-cyan-50: 230 249 253;
    --color-cyan-500: 0 190 236;
    --color-error-50: 253 232 230;
    --color-error-500: 230 27 6;
    --color-error-600: 209 25 5;
    --color-error-800: 127 15 3;
    --color-warning-50: 254 250 230;
    --color-warning-75: 253 234 216;
    --color-warning-500: 245 204 0;
    --color-warning-800: 174 89 10;
    --color-success-50: 230 251 244;
    --color-success-500: 15 150 78;
    --box-shadow-product-card: 4px 4px 15px 0px rgba(0, 0, 0, 0.15);
    --box-shadow-header: 0px 12px 32px 0px rgba(87, 111, 133, 0.08);
    --color-foreground-neutral-tertiary: 136 151 174;
    --color-foreground-neutral-secondary: 69 84 104;

    /** v2 */
    --global-color-base-black: 14 15 16;
    --global-color-base-white: 255 255 255;
    --global-color-blue-100: 209 224 255;
    --global-color-blue-200: 178 204 255;
    --global-color-blue-25: 242 246 255;
    --global-color-blue-300: 132 173 255;
    --global-color-blue-400: 82 139 255;
    --global-color-blue-50: 245 248 255;
    --global-color-blue-500: 41 112 255;
    --global-color-blue-600: 21 94 239;
    --global-color-blue-700: 0 78 235;
    --global-color-blue-800: 0 64 193;
    --global-color-blue-900: 0 53 158;
    --global-color-gray-100: 243 244 246;
    --global-color-gray-200: 229 231 235;
    --global-color-gray-25: 252 252 253;
    --global-color-gray-300: 185 191 198;
    --global-color-gray-400: 157 164 174;
    --global-color-gray-50: 249 250 251;
    --global-color-gray-500: 108 115 127;
    --global-color-gray-600: 77 87 97;
    --global-color-gray-700: 56 66 80;
    --global-color-gray-800: 31 42 55;
    --global-color-gray-900: 17 25 39;
    --global-color-green-100: 123 225 175;
    --global-color-green-200: 95 212 154;
    --global-color-green-25: 237 253 244;
    --global-color-green-300: 67 199 133;
    --global-color-green-400: 38 186 112;
    --global-color-green-50: 209 250 228;
    --global-color-green-500: 24 180 102;
    --global-color-green-600: 20 148 84;
    --global-color-green-700: 16 117 66;
    --global-color-green-800: 11 85 49;
    --global-color-green-900: 7 34 19;
    --global-color-lime-100: 177 237 251;
    --global-color-lime-200: 129 225 248;
    --global-color-lime-25: 245 253 254;
    --global-color-lime-300: 90 216 246;
    --global-color-lime-400: 30 203 246;
    --global-color-lime-50: 226 248 254;
    --global-color-lime-500: 0 173 215;
    --global-color-lime-600: 0 173 215;
    --global-color-lime-700: 0 173 215;
    --global-color-lime-800: 0 173 215;
    --global-color-lime-900: 0 173 215;
    --global-color-orange-100: 255 216 180;
    --global-color-orange-200: 255 199 147;
    --global-color-orange-25: 255 246 237;
    --global-color-orange-300: 255 173 98;
    --global-color-orange-400: 252 142 40;
    --global-color-orange-50: 255 235 216;
    --global-color-orange-500: 252 132 21;
    --global-color-orange-600: 235 113 1;
    --global-color-orange-700: 210 101 0;
    --global-color-orange-800: 180 86 0;
    --global-color-orange-900: 136 66 2;
    --global-color-purple-100: 206 200 244;
    --global-color-purple-200: 182 174 239;
    --global-color-purple-25: 247 241 253;
    --global-color-purple-300: 148 137 232;
    --global-color-purple-400: 128 114 227;
    --global-color-purple-50: 239 237 252;
    --global-color-purple-500: 96 79 220;
    --global-color-purple-600: 87 72 200;
    --global-color-purple-700: 68 56 156;
    --global-color-purple-800: 53 43 121;
    --global-color-purple-900: 40 33 92;
    --global-color-red-100: 254 228 226;
    --global-color-red-200: 254 205 202;
    --global-color-red-25: 255 251 250;
    --global-color-red-300: 253 162 155;
    --global-color-red-400: 249 112 102;
    --global-color-red-50: 254 243 242;
    --global-color-red-500: 240 68 56;
    --global-color-red-600: 217 45 32;
    --global-color-red-700: 180 35 24;
    --global-color-red-800: 145 32 24;
    --global-color-red-900: 122 39 26;
    --global-color-yellow-100: 255 233 157;
    --global-color-yellow-200: 247 220 124;
    --global-color-yellow-25: 255 249 223;
    --global-color-yellow-300: 248 211 79;
    --global-color-yellow-400: 245 198 30;
    --global-color-yellow-50: 255 242 196;
    --global-color-yellow-500: 233 185 11;
    --global-color-yellow-600: 216 168 0;
    --global-color-yellow-700: 177 138 0;
    --global-color-yellow-800: 137 107 0;
    --global-color-yellow-900: 98 77 0;

    /** v2 semantics */
    --base-black: var(--global-color-base-black);
    --base-white: var(--global-color-base-white);
    --brand-brand: var(--global-color-purple-500);
    --brand-brand-dark: var(--global-color-purple-700);
    --brand-brand-darker: var(--global-color-purple-800);
    --brand-brand-darkest: var(--global-color-purple-900);
    --brand-brand-light: var(--global-color-purple-100);
    --brand-brand-lighter: var(--global-color-purple-50);
    --brand-brand-lightest: var(--global-color-purple-25);
    --error-error: var(--global-color-red-500);
    --error-error-dark: var(--global-color-red-700);
    --error-error-darker: var(--global-color-red-800);
    --error-error-darkest: var(--global-color-red-900);
    --error-error-light: var(--global-color-red-200);
    --error-error-lighter: var(--global-color-red-50);
    --error-error-lightest: var(--global-color-red-25);
    --info-info: var(--global-color-blue-500);
    --info-info-dark: var(--global-color-blue-700);
    --info-info-darker: var(--global-color-blue-800);
    --info-info-darkest: var(--global-color-blue-900);
    --info-info-light: var(--global-color-blue-100);
    --info-info-lighter: var(--global-color-blue-50);
    --info-info-lightest: var(--global-color-blue-25);
    --neutral-neutral: var(--global-color-gray-500);
    --neutral-neutral-dark: var(--global-color-gray-600);
    --neutral-neutral-darker: var(--global-color-gray-800);
    --neutral-neutral-darkest: var(--global-color-gray-900);
    --neutral-neutral-light: var(--global-color-gray-200);
    --neutral-neutral-light-2: var(--global-color-gray-300);
    --neutral-neutral-lighter: var(--global-color-gray-100);
    --neutral-neutral-lightest: var(--global-color-gray-50);
    --purple-purple: var(--global-color-purple-500);
    --purple-purple-dark: var(--global-color-purple-700);
    --purple-purple-darker: var(--global-color-purple-800);
    --purple-purple-darkest: var(--global-color-purple-900);
    --purple-purple-light: var(--global-color-purple-300);
    --purple-purple-lighter: var(--global-color-purple-50);
    --purple-purple-lightest: var(--global-color-purple-25);
    --success-success: var(--global-color-green-500);
    --success-success-dark: var(--global-color-green-700);
    --success-success-darker: var(--global-color-green-800);
    --success-success-darkest: var(--global-color-green-900);
    --success-success-light: var(--global-color-green-100);
    --success-success-lighter: var(--global-color-green-50);
    --success-success-lightest: var(--global-color-green-25);
    --warning-warning: var(--global-color-orange-500);
    --warning-warning-dark: var(--global-color-orange-700);
    --warning-warning-darker: var(--global-color-orange-800);
    --warning-warning-darkest: var(--global-color-orange-900);
    --warning-warning-light: var(--global-color-orange-100);
    --warning-warning-lighter: var(--global-color-orange-50);
    --warning-warning-lightest: var(--global-color-orange-25);
  }

  html.dark {
    --color-background: 12 9 15;
    --color-cards: 21 20 24;
    --color-info-50: 227 234 253;
    --color-gray-20: 34 34 34;
    --color-gray-40: 245 245 245;
    --color-gray-50: 49 49 49;
    --color-gray-100: 212 212 212;
    --color-gray-200: 83 83 83;
    --color-gray-400: 83 83 83;
    --color-gray-500: 163 163 163;
    --color-gray-600: 192 192 192;
    --color-gray-999: 252 252 252;
    --color-purple-40: 248 247 255;
    --color-purple-50: 26 19 72;
    --color-purple-500: 128 114 227;
    --color-purple-700: 77 63 176;
    --color-cyan-50: 3 37 45;
    --color-cyan-500: 51 203 240;
    --color-error-50: 63 7 2;
    --color-error-500: 245 38 17;
    --color-warning-50: 66 56 3;
    --color-warning-75: 253 234 216;
    --color-warning-500: 251 210 7;
    --color-warning-800: 174 89 10;
    --color-success-50: 1 46 33;
    --color-success-500: 15 150 78;
    --box-shadow-product-card: 0px 11px 20px 0px rgba(255, 255, 255, 0.15);
    --box-shadow-header: 0px 12px 32px 0px rgba(87, 111, 133, 0.08);
  }
}
