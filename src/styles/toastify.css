@keyframes radial-progress {
  from {
    stroke-dashoffset: 565px;
  }
  to {
    stroke-dashoffset: 0px;
  }
}

.animate {
  animation: radial-progress linear forwards;
}

:root {
  --toastify-toast-padding: 13px 16px 13px 64px;
  --toastify-toast-min-height: 16px;
  --toastify-toast-bd-radius: 12px;
  --toastify-toast-shadow: 0px 10px 15px -3px rgba(18, 18, 23, 0.08);
}

.Toastify__toast {
  border: 1px solid rgb(var(--neutral-neutral-lighter)) !important;
}
.Toastify__close-button {
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin-top: -1px !important;
}
.Toastify__close-button > svg {
  height: 20px !important;
  width: 20px !important;
}
.Toastify__toast--rtl .Toastify__close-button {
  left: 24px !important;
  right: unset !important;
}
