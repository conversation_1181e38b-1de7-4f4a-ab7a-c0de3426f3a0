@tailwind base;
@tailwind components;
@tailwind utilities;
@import "react-perfect-scrollbar/dist/css/styles.css";
@import "react-quill/dist/quill.snow.css";
@import "swiper/css";
@import "swiper/css/navigation";
@import "swiper/css/pagination";
@import "colors.css";
@import "muiTable.css";
@import "toastify.css";

/* English Font */
@font-face {
  font-family: mainFont;
  src: url("../assets/fonts/en/Roboto-Medium.ttf");
  font-weight: 500;
  unicode-range: U+0020-007E;
}

.Toastify__toast {
  direction: rtl;
  font-family: mainFont;
}

@font-face {
  font-family: "Iran yekan";
  font-weight: 100 1000;
  font-display: fallback;
  src:
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff-variations"),
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff");
}

@font-face {
  font-family: "Iran yekanLight";
  font-weight: 300;
  font-display: fallback;
  src:
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff-variations"),
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff");
  font-variation-settings: "wght" 300;
}

@font-face {
  font-family: "Iran yekanSemiBold";
  font-weight: 600;
  font-display: fallback;
  src:
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff-variations"),
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff");
  font-variation-settings: "wght" 600;
}

@font-face {
  font-family: "Iran yekanBold";
  font-weight: 700;
  font-display: fallback;
  src:
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff-variations"),
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff");
  font-variation-settings: "wght" 700;
}

@font-face {
  font-family: "Iran yekanExtraBold";
  font-weight: 800;
  font-display: fallback;
  src:
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff-variations"),
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff");
  font-variation-settings: "wght" 800;
}

@font-face {
  font-family: "Iran yekanExtraBlack";
  font-weight: 900;
  font-display: fallback;
  src:
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff-variations"),
    url("../assets/fonts/fa/IRANYekanXVFaNumVF.woff") format("woff");
  font-variation-settings: "wght" 900;
}

html {
  touch-action: pan-y;
}

/* Apply fonts to elements */
body {
  font-family:
    Iran yekan,
    Arial,
    sans-serif;
  background-color: rgb(var(--color-background)) !important;
  /* Fallback font */
}

.MuiChip-root {
  border-radius: var(--mui-theme-spacing-1);
}

.MuiChip-outlinedSecondary {
  background-color: var(--mui-palette-secondary-light);
}

.MuiChip-outlinedPrimary {
  background-color: rgb(var(--color-purple-50));
}

.MuiInputBase-root .MuiOutlinedInput-notchedOutline {
  border-color: transparent;
}

.MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgb(var(--color-purple-500));
}

:root {
  --drop-hub-sidebar-size: 264px;
}

.MuiButton-text {
  background-color: transparent;
}

a {
  text-decoration: none;
}

input:-webkit-autofill,
input:-webkit-autofill:focus {
  transition:
    background-color 600000s 0s,
    color 600000s 0s;
}

input[data-autocompleted] {
  background-color: transparent !important;
}

@media only screen and (max-width: 480px) {
  .Toastify__toast-container {
    width: 90vw;
    padding: 0;
    margin: 0 18px;
    right: 0px;
    top: 16px;
  }
}

.swiper-pagination-bullet-active {
  width: 12px;
  border-radius: 7px;
}

@layer base {
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"] {
    -webkit-appearance: none;
    margin: 0;
    -moz-appearance: textfield !important;
  }
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Firefox */
.scrollbar-hide {
  scrollbar-width: none;
}

/* IE and Edge */
.scrollbar-hide {
  -ms-overflow-style: none;
}