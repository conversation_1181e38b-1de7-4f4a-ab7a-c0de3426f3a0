/* whole table container */
.MuiTableContainer-root {
  @apply rounded-md;
}

.MuiTableContainer-root .MuiTable-root {
  @apply border-2 border-v2-border-primary rounded-lg border-separate;
}

/* table head */
.MuiTableHead-root,
.MuiTableCell-root.MuiTableCell-head {
  @apply bg-v2-surface-thertiary border-b border-v2-border-primary;
}

/* texts in table head */
.MuiTableHead-root .MuiTableRow-root .MuiTableCell-root {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;

  @apply text-v2-content-tertiary;
}

/* table head checkbox */
.MuiTableHead-root .MuiTableRow-root .MuiTableCell-root .MuiCheckbox-root .styled-bp-icon {
  width: 16px;
  height: 16px;
}
.MuiTableHead-root .MuiTableRow-root .MuiTableCell-root .MuiCheckbox-root .styled-bp-icon:before {
  width: 16px;
  height: 16px;
}

/* table body checkboxes */
.MuiTableBody-root .MuiTableRow-root .MuiTableCell-root .MuiCheckbox-root .styled-bp-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}
.MuiTableBody-root .MuiTableRow-root .MuiTableCell-root .MuiCheckbox-root .styled-bp-icon:before {
  width: 16px;
  height: 16px;
}

/* table rows */
.MuiTableBody-root .MuiTableRow-root:hover {
  @apply bg-v2-surface-secondary;
}

.MuiTableBody-root .MuiTableRow-root .MuiTableCell-root {
  padding: 8px;
}

@media screen and (max-width: 768px) {
  .MuiTable-root {
    white-space: nowrap;
  }
}

@media screen and (min-width: 768px) and (max-width: 900px) {
  .MuiTable-root {
    white-space: unset;
  }
}
