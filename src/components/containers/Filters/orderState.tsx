import React from "react";
import { twMerge } from "tailwind-merge";
import { Swiper, SwiperSlide } from "swiper/react";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import "swiper/swiper-bundle.css";
import OrderStateItem from "./orderStateItem";

interface IOrderStateFilterProps {
  totalCount: number;
  state: string;
  onChangeState: (val: string | null) => void;
  stateItems: {
    id: string;
    label: string;
  }[];
}

const OrderStateFilter = ({ totalCount, onChangeState, state, stateItems }: IOrderStateFilterProps) => {
  return (
    <div>
      <Swiper
        dir="rtl"
        slidesPerView={"auto"}
        keyboard
        grabCursor
        spaceBetween={24}
        modules={[Navigation, Keyboard, Mousewheel]}
      >
        {stateItems?.map((item, index) => (
          <SwiperSlide key={item?.id} className="max-w-fit">
            <div
              key={item?.id}
              onClick={() => {
                if (item?.id === "All") {
                  onChangeState(null);
                  return;
                }
                onChangeState(item?.id);
              }}
              className={twMerge(
                "whitespace-nowrap border-b-transparent border-b-2 pb-2.5 flex items-center gap-1 cursor-pointer",
                (state || stateItems[0]?.id) === item?.id ? "text-purple-500 !border-b-v2-content-on-action-2" : ""
              )}
            >
              <span className="text-body3-medium text-v2-content-primary">{item?.label}</span>
              <div className="bg-v2-surface-action-light text-body3-medium min-w-[22px] min-h-[22px] text-v2-content-on-action-hover-2 rounded-[4px] shrink-0 flex items-center justify-center  ">
                {item?.id === "All" ? totalCount : <OrderStateItem state={item?.id} />}
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default OrderStateFilter;
