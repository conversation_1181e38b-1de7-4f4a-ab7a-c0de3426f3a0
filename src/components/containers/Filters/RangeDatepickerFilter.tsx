import ChipsWithPopover from "@/components/containers/Filters/ChipsWithPopover";
import React from "react";
import { TFiltersType } from "./types";
import { DatePicker } from "@/components/ui/CustomDatePicker";
import i18n from "@/utils/i18n";
import useLanguage from "@/utils/hooks/useLanguage";

function RangeDatepickerFilter<T extends string[]>({
  title,
  filterKey,
  initialValue,
  setFilterValue,
  onReset
}: TFiltersType<T>) {
  const [{ renderDate }] = useLanguage();

  const renderDateValue = (from?: string, to?: string) => {
    if (from && to) {
      return `${renderDate(from, "YYYY/MM/DD")}, ${renderDate(to, "YYYY/MM/DD")}`;
    } else if (from) {
      return renderDate(from, "YYYY/MM/DD");
    } else if (to) {
      return renderDate(to, "YYYY/MM/DD");
    }
  };

  return (
    <ChipsWithPopover<T>
      title={renderDateValue(initialValue?.[0], initialValue?.[1]) || title}
      filterKey={filterKey}
      initialValue={initialValue || undefined}
      setFilterValue={setFilterValue}
      onReset={onReset}
    >
      {({ setValue, value, filterKey }) => (
        <div className="flex flex-col gap-4">
          <DatePicker
            inputProps={{
              placeholder: i18n.t("product.filterItems.createdFrom"),
              requiredStar: false
            }}
            hasTime
            label={i18n.t("product.filterItems.createdFrom")}
            value={(value as string[])?.[0] || ""}
            onChange={(v: string) => setValue((prev: any) => [v, prev?.[1]])}
          />

          <DatePicker
            inputProps={{
              placeholder: i18n.t("product.filterItems.createdTo"),
              requiredStar: false
            }}
            hasTime
            label={i18n.t("product.filterItems.createdTo")}
            value={(value as string[])?.[1] || ""}
            onChange={v => setValue((prev: any) => [prev?.[0], v])}
          />
        </div>
      )}
    </ChipsWithPopover>
  );
}

export default RangeDatepickerFilter;
