import { Icon } from "@iconify/react";
import React, { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

function Chips({
  onClick,
  icon,
  title,
  id,
  onReset,
  isSelected = false
}: {
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  icon?: ReactNode;
  title?: ReactNode;
  onReset?: VoidFunction;
  isSelected?: Boolean;
  id?: string;
}) {
  return (
    <button
      onClick={onClick}
      id={id}
      className={twMerge(
        "px-3 py-1.5 border rounded-full border-v2-border-primary flex items-center whitespace-nowrap gap-1.5 outline-none text-[13px] text-v2-content-primary font-medium",
        isSelected ? "bg-v2-surface-action-light text-v2-content-on-action-hover-2" : "bg-v2-surface-primary"
      )}
    >
      {icon}

      {title}

      {isSelected && (
        <Icon
          icon="mdi:close"
          className="flex-shrink-0"
          width={18}
          height={18}
          onClick={e => {
            e.stopPropagation();

            onReset?.();
          }}
        />
      )}
    </button>
  );
}

export default Chips;
