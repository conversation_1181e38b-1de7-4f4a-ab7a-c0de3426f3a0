import React from "react";
import { TFiltersType } from "./types";
import Chips from "@/components/containers/Filters/Chips";

function ToggleFilter<T extends boolean>({
  title,
  icon,
  filterKey,
  initialValue,
  setFilterValue,
  onReset
}: TFiltersType<T>) {
  return (
    <Chips
      {...{
        title,
        icon,
        isSelected: !!initialValue,
        onClick: () => setFilterValue?.(filterKey, initialValue === true ? false : true),
        onReset: () => onReset?.(filterKey)
      }}
    />
  );
}

export default ToggleFilter;
