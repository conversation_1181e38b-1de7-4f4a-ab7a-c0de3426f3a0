import useModal from "@/utils/hooks/useModal";
import { Menu, MenuItem, useMediaQuery } from "@mui/material";
import { Theme } from "@mui/system";
import React, { useState } from "react";
import Chips from "./Chips";
import { TFiltersType } from "./types";

function DropdownFilter<T extends string>({
  title,
  filterKey,
  initialValue,
  setFilterValue,
  onReset,
  options,
  icon
}: TFiltersType<T> & { options: { id: string; label: string }[] }) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const { hideModal, showModal } = useModal();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const statusValue = options?.find(item => item?.id === initialValue)?.label || title;

  const RenderMenus = () => {
    return options?.map(item => (
      <MenuItem
        key={item?.id}
        value={item?.id}
        className="py-3 text-[13px]"
        onClick={() => {
          setFilterValue?.(filterKey, item?.id);

          if (isMobile) {
            hideModal();
          } else {
            setAnchorEl(null);
          }
        }}
      >
        {item?.label}
      </MenuItem>
    ));
  };
  const handleClickChips = (event: React.MouseEvent<HTMLElement>) => {
    if (isMobile) {
      showModal({
        body: (
          <div className="pt-4">
            <div className="text-sm text-v2-content-primary mb-4">{title}</div>
            {<RenderMenus />}
          </div>
        )
      });
      return;
    }

    setAnchorEl(event.currentTarget);
  };

  const handleCloseChipsMenu = () => {
    if (isMobile) {
      hideModal();
    } else {
      setAnchorEl(null);
    }
  };

  return (
    <>
      <Chips
        {...{
          title: <div className="flex items-center gap-1.5 ">{statusValue}</div>,
          onClick: handleClickChips,
          icon,
          isSelected: !!initialValue,
          onReset: () => onReset?.(filterKey)
        }}
      />
      <Menu
        anchorEl={anchorEl}
        open={open}
        elevation={0}
        onClose={handleCloseChipsMenu}
        MenuListProps={{
          className: "p-0"
        }}
        PaperProps={{
          className:
            "bg-v2-surface-primary overflow-visible rounded-[8px] w-[230px] shadow-[3px 7px 11px 0px #0000000F]",
          style: { boxShadow: "0px 0px 8px 0px rgba(0, 0, 0, 0.08)" }
        }}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
      >
        <RenderMenus />
      </Menu>
    </>
  );
}

export default DropdownFilter;
