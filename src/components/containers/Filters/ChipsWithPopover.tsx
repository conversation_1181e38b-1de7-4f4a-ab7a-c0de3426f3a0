import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Divider, <PERSON>row, <PERSON>u, Paper, Popper, useMediaQuery } from "@mui/material";
import React, { Dispatch, ReactNode, SetStateAction, useCallback, useState } from "react";
import Button from "../../ui/Button";
import { Theme } from "@mui/system";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import i18n from "@/utils/i18n";
import Chips from "./Chips";

type TValueType = string | string[] | boolean;
type TFilterKey = string | string[];

type TChipsWithPopover<T> = {
  filterKey: TFilterKey;
  setFilterValue?: (k: TFilter<PERSON>ey, v: any) => void;
  title?: string;
  icon?: ReactNode;
  hasValue?: boolean;
  initialValue?: TValueType;
  children: (props: {
    value: TValueType;
    setValue: Dispatch<SetStateAction<TValueType>>;
    filterKey: TFilterKey;
  }) => React.ReactNode;
  showApplyButton?: boolean;
  onReset?: (key: TFilterKey) => void;
};

function ChipsWithPopover<T>({
  title,
  icon,
  children,
  initialValue,
  filterKey,
  showApplyButton = true,
  setFilterValue,
  onReset
}: TChipsWithPopover<T>) {
  const [popoverAnchorEl, setPopoverAnchorEl] = React.useState<HTMLButtonElement | null>(null);
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const { hideModal, showModal } = useModal();

  const hasInitialValue = !!(
    (typeof initialValue === "string" && initialValue?.length) ||
    (typeof initialValue === "boolean" && initialValue === true) ||
    (Array.isArray(initialValue) && initialValue?.some(a => a?.length) && initialValue?.length)
  );

  const handleClosePopover = () => {
    if (isMobile) {
      hideModal();
      return;
    }

    setPopoverAnchorEl(null);
  };

  const RenderBody = useCallback(() => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<TValueType>(initialValue as TValueType);

    const handleFilter = () => {
      /* --------------------- once we have two filter's field -------------------- */
      if (Array.isArray(filterKey) && filterKey?.length) {
        filterKey?.forEach((item, index) => {
          setFilterValue?.(item as string, (value as string[])[index]);
        });
        /* ---------------------- once we have a filter's field --------------------- */
      } else {
        setFilterValue?.(filterKey, value);
      }

      setTimeout(() => {
        handleClosePopover();
      }, 0);
    };

    const arrayVal = Array?.isArray(filterKey) ? (value as string[])?.filter(Boolean) : [];
    const arrayFieldDisabled = !arrayVal?.length;
    const generalFieldDisabled = typeof value === "string" && !value?.length;
    const disabledFilter = Array?.isArray(filterKey) ? arrayFieldDisabled : generalFieldDisabled;

    return (
      <div>
        {children({ setValue, value, filterKey })}

        {showApplyButton && (
          <>
            {/* <Divider orientation="horizontal" flexItem className="bg-gray-50 my-2" /> */}

            <Button disabled={disabledFilter} onClick={handleFilter} className="w-full mt-16">
              {i18n.t("handleFilter")}
            </Button>
          </>
        )}
      </div>
    );
  }, [filterKey, showApplyButton, initialValue]);

  const handleOpenPopover = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (isMobile) {
      showModal({
        body: (
          <div className="pt-4">
            <RenderBody />
          </div>
        ),
        title: (
          <div className="flex items-center gap-2">
            <Icon icon="solar:tuning-2-linear" className="size-6 text-v2-content-on-action-2" />
            <span className="text-v2-content-primary text-body4-medium">{i18n.t("filter")}</span>
          </div>
        )
      });
      return;
    }
    setPopoverAnchorEl(event.currentTarget);
  };

  const open = Boolean(popoverAnchorEl);
  const id = open ? `simple-popover-${filterKey}` : undefined;

  return (
    <>
      <Chips
        {...{
          title,
          id,
          icon,
          isSelected: hasInitialValue,
          onClick: handleOpenPopover,
          onReset: () => onReset?.(filterKey)
        }}
      />

      <Popper
        open={open}
        anchorEl={popoverAnchorEl}
        className="z-[9999999] mt-5"
        placement="bottom"
        transition
        disablePortal
      >
        {({ TransitionProps }) => (
          <Grow {...TransitionProps} style={{ transformOrigin: "right top" }}>
            <Paper
              className="bg-white border border-solid border-gray-50 overflow-visible rounded-[8px] w-[230px] shadow-[3px 7px 11px 0px #0000000F]"
              style={{ boxShadow: "3px 7px 11px 0px #0000000F" }}
            >
              <ClickAwayListener onClickAway={handleClosePopover}>
                <div className="p-4">
                  <RenderBody />
                </div>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </>
  );
}

export default ChipsWithPopover;
