import ChipsWithPopover from "@/components/containers/Filters/ChipsWithPopover";
import Input from "@/components/ui/inputs/Input";
import React from "react";
import { TFiltersType } from "./types";

function InputFilter<T extends string>({ title, filterKey, initialValue, setFilterValue, onReset }: TFiltersType<T>) {
  const finalTitle =
    typeof initialValue === "string" && initialValue?.length
      ? initialValue
      : Array.isArray(initialValue) && initialValue?.some(a => a.length)
        ? initialValue?.join(", ")
        : title;

  return (
    <ChipsWithPopover<T>
      title={finalTitle}
      filterKey={filterKey}
      initialValue={initialValue || undefined}
      setFilterValue={setFilterValue}
      onReset={onReset}
    >
      {({ setValue, value, filterKey }) => (
        <Input
          placeholder={title}
          label={title}
          type="text"
          value={(value as string) || undefined}
          onChange={({ target: { value } }) => setValue(value)}
          requiredStar={false}
        />
      )}
    </ChipsWithPopover>
  );
}

export default InputFilter;
