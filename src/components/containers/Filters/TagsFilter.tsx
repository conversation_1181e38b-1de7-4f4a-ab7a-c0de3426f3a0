import { CustomTags } from "@/components/ui/CustomTags";
import ChipsWithPopover from "@/components/containers/Filters/ChipsWithPopover";
import InputLabel from "@/components/ui/inputs/Input/InputLabel";
import React from "react";
import { TFiltersType } from "./types";

function TagsFilter<T extends string[]>({ title, filterKey, initialValue, setFilterValue, onReset }: TFiltersType<T>) {
  const finalTitle =
    Array.isArray(initialValue) && initialValue?.some(a => a.length) ? initialValue?.join(", ") : title;

  return (
    <ChipsWithPopover<T>
      title={finalTitle}
      filterKey={filterKey}
      initialValue={initialValue || undefined}
      setFilterValue={setFilterValue}
      onReset={onReset}
    >
      {({ setValue, value, filterKey }) => (
        <>
          <InputLabel requiredStar={false}>{title}</InputLabel>

          <CustomTags
            value={(value as string[]) ?? []}
            onChange={({ tagValues }) => setValue(tagValues as string[])}
            name={filterKey as string}
            placeholder={title}
          />
        </>
      )}
    </ChipsWithPopover>
  );
}

export default TagsFilter;
