import ChipsWithPopover from "@/components/containers/Filters/ChipsWithPopover";
import Input from "@/components/ui/inputs/Input";
import React from "react";
import { TFiltersType } from "./types";
import LocationsSelect from "@/components/ui/CustomCountrySelect/LocationsSelect";
import useLocations from "@/utils/hooks/useLocations";

function LocationSelectFilter<T extends string>({
  title,
  placeholder,
  filterKey,
  initialValue,
  setFilterValue,
  onReset
}: TFiltersType<T>) {
  const { getLocation } = useLocations();

  const finalTitle =
    typeof initialValue === "string" && initialValue?.length ? getLocation(initialValue)?.name || "" : title;

  return (
    <ChipsWithPopover<T>
      title={finalTitle}
      filterKey={filterKey}
      initialValue={initialValue || undefined}
      setFilterValue={setFilterValue}
      onReset={onReset}
    >
      {({ setValue, value, filterKey }) => (
        <LocationsSelect
          multiple={false}
          label={title}
          placeholder={placeholder || title}
          helperText=" "
          value={value as string}
          onChange={location => setValue(location as string)}
          requiredStar={false}
        />
      )}
    </ChipsWithPopover>
  );
}

export default LocationSelectFilter;
