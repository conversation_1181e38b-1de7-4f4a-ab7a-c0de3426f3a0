import ChipsWithPopover from "@/components/containers/Filters/ChipsWithPopover";
import React from "react";
import { TFiltersType } from "./types";
import { DatePicker } from "@/components/ui/CustomDatePicker";
import useLanguage from "@/utils/hooks/useLanguage";

function SingleDatepickerFilter<T extends string>({
  title,
  filterKey,
  initialValue,
  setFilterValue,
  onReset
}: TFiltersType<T>) {
  const [{ renderDate }] = useLanguage();

  const renderDateValue = (date?: string | null) => {
    if (date) {
      return renderDate(date, "YYYY/MM/DD");
    }
  };

  return (
    <ChipsWithPopover<T>
      title={renderDateValue(initialValue) || title}
      filterKey={filterKey}
      initialValue={initialValue || undefined}
      setFilterValue={setFilterValue}
      onReset={onReset}
    >
      {({ setValue, value, filterKey }) => (
        <div>
          <DatePicker
            inputProps={{
              placeholder: title
            }}
            hasTime
            label={title}
            value={(value as string) || ""}
            onChange={(v: string) => setValue(v)}
          />
        </div>
      )}
    </ChipsWithPopover>
  );
}

export default SingleDatepickerFilter;
