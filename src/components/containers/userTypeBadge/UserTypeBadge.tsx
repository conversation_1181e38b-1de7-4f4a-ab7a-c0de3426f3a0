import { USER_TYPES } from "@/constants/userTypes";
import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import "./userTypeBadge.css";

interface IUserTypeBadgeProps {
  userType: USER_TYPES;
  prefix?: string;
}

export default function UserTypeBadge({ userType, prefix }: IUserTypeBadgeProps) {
  const { t } = useTranslation();

  return (
    <Box className="user-type-badge">
      {!!prefix && <Typography className="user-type-badge-title">{prefix}</Typography>}
      <Typography className="user-type-badge-title">
        {userType === USER_TYPES.SUPPLIER ? t("userTypes.supplier") : t("userTypes.retailer")}
      </Typography>
    </Box>
  );
}
