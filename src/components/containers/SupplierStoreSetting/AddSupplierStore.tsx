import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";

import { useGetSupplierStoreQuery } from "@/store/apps/supplier";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import Button from "@/components/ui/Button";
import { routes } from "@/constants/routes";
import { CircularProgress } from "@mui/material";

function AddSupplierStore() {
  const { t } = useTranslation();
  const { showModal } = useModal();
  const makePath = useRoleBasePath();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const step = searchParams?.get("step");

  const { data: supplierStore, isLoading: isStoreSingleLoading } = useGetSupplierStoreQuery();

  const isEdit = supplierStore?.data?.id;

  const onCancel = () => {
    router.replace(pathname);
  };

  const handleImport = () => {
    const path = isEdit ? routes.createProductStore : routes.editProductStore;
    router.replace(makePath(path));
  };

  return (
    <Button
      variant="secondaryGray"
      size="xl"
      startAdornment={<Icon icon="solar:import-broken" className="size-5" />}
      onClick={handleImport}
    >
      {isStoreSingleLoading ? <CircularProgress size={16} /> : t("supplier.import")}
    </Button>
  );
}

export default AddSupplierStore;
