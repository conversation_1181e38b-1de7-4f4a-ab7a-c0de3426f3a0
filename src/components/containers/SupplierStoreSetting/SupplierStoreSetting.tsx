import SelectedStore from "./SelectedStore";
import { useGetSupplierStoreQuery } from "@/store/apps/supplier";
import StoreStepper from "./StoreStepper/StoreStepper";
import { CircularProgress } from "@mui/material";
import { useState } from "react";
import { useSearchParams } from "next/navigation";

function SupplierStoreSetting() {
  const { data: supplierStore, isLoading: isStoreSingleLoading } = useGetSupplierStoreQuery();
  const isEdit = supplierStore?.data?.id;
  const isActive = supplierStore?.data?.isActive;
  const searchParams = useSearchParams();
  const isConnected = searchParams?.get("isConnected");
  const [inProgress, setInProgress] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  if (isStoreSingleLoading || isLoading) {
    return (
      <div className="flex items-center justify-center bg-cards h-screen">
        <CircularProgress />
      </div>
    );
  }

  if (isEdit && !inProgress && !isConnected) {
    return <SelectedStore isStoreSingleLoading={isStoreSingleLoading} supplierStore={supplierStore} />;
  }

  return (
    <StoreStepper
      isCreate
      setIsLoading={setIsLoading}
      supplierStore={supplierStore}
      setInProgress={setInProgress}
      isStoreSingleLoading={isStoreSingleLoading}
    />
  );
}

export default SupplierStoreSetting;
