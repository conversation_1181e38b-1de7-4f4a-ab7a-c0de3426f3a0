import { Icon } from "@iconify/react";
import React from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

type Step = {
  id: number;
  label: string;
};

type ProgressBarProps = {
  currentStep: number;
};

const ProgressBar: React.FC<ProgressBarProps> = ({ currentStep }) => {
  const { t } = useTranslation();
  const steps: Step[] = [
    { id: 1, label: t("store.progressbar.websiteType") },
    { id: 2, label: t("store.progressbar.sync") },
    { id: 3, label: t("store.progressbar.pricing") },
    { id: 4, label: t("store.progressbar.view") }
  ];

  const lastStepId = steps?.at(-1)?.id;

  const getStepStyles = (stepId: number) => {
    if (currentStep === lastStepId || stepId < currentStep)
      return "bg-success-500 text-white border border-solid border-success-500";
    if (stepId === currentStep) return "bg-purple-50 text-white border border-dashed border-purple-500";
    return "bg-white text-gray-500 border-gray-300";
  };

  const getConnectorStyles = (stepId: number) => {
    if (currentStep === lastStepId || stepId < currentStep) return "border-success-500 !border-solid";
    if (stepId === currentStep) return " border-purple-500";

    return "border-gray-500";
  };

  return (
    <div className="flex items-center justify-center gap-2">
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <div className="flex flex-col items-center relative">
            <div
              className={`size-6 flex items-center justify-center rounded-full border-[1.5px] ${getStepStyles(step.id)}`}
            >
              <span className="text-caption-regular font-medium">
                {currentStep === lastStepId || step.id < currentStep ? (
                  <Icon icon="mdi:tick" className="text-[white] size-4 " />
                ) : (
                  step.id
                )}
              </span>
            </div>
            <span
              className={twMerge(
                `mt-8 text-xs absolute text-caption-regular z-50 font-medium whitespace-nowrap ${step.id <= currentStep ? "text-gray-600" : "text-gray-500"} `,
                step.id === currentStep && "text-gray-999"
              )}
            >
              {step.label}
            </span>
          </div>
          {index < steps.length - 1 && (
            <div className={`flex-1 h-0 border-t-[1.5px] border-dashed ${getConnectorStyles(step.id)}`}></div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default ProgressBar;
