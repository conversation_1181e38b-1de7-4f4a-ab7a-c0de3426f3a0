import { useGetMetaIntegrationQuery } from "@/store/apps/meta";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import { TSupplierIntegrationResponse } from "@/store/apps/supplier/types";
import DisconnectSupplierStoreButton from "./DisconnectStore";
import { ensureUrlScheme } from "@/utils/helpers";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import ProfileContentContainer from "@/components/containers/ProfileStepper/ProfileContentContainer";
import Pricing from "./StoreStepper/Pricing";
import ApiKey from "./StoreStepper/ApiKey";

interface ISelectWebsiteProps {
  supplierStore?: TSupplierIntegrationResponse;
  isStoreSingleLoading?: boolean;
}

export default function SelectedStore({ isStoreSingleLoading, supplierStore }: ISelectWebsiteProps) {
  const { t } = useTranslation();
  const isActive = supplierStore?.data?.isActive;

  const { data: integrations, isLoading } = useGetMetaIntegrationQuery();

  const integration = integrations?.data?.find(item => item?.key === supplierStore?.data?.integration?.platform?.key);

  const configureForm = integration?.configForm || {};

  return (
    <div>
      <MobileAppBar title={t("storeInfo")} hasBack />
      <ProfileContentContainer>
        <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 xmd:pr-4 xmd:pt-4 xmd:h-full relative">
          {" "}
          <div className="flex items-center justify-between mb-4">
            <span className="text-body1-medium text-v2-content-primary">{t("supplier.profile.storeDetails")}</span>
            <DisconnectSupplierStoreButton supplierStore={supplierStore} />
          </div>
          <div className={isActive ? "" : "pointer-events-none opacity-50"}>
            <div>
              <div className="flex items-center gap-x-16 gap-y-4 flex-wrap">
                {/* website type */}
                <div className="flex flex-col gap-3">
                  <span className="text-body4-regular text-v2-content-tertiary">
                    {t("supplier.profile.websiteType")}
                  </span>

                  <div className="flex items-center gap-2">
                    {!!supplierStore?.data?.logo && (
                      <div
                        dangerouslySetInnerHTML={{ __html: supplierStore?.data?.logo }}
                        className="size-9 [&>svg]:size-9 rounded-sm flex items-center justify-center"
                      />
                    )}
                    <span className="text-body4-medium text-v2-content-primary">
                      {supplierStore?.data?.integration?.platform?.key}
                    </span>
                  </div>
                </div>
                {/* website address */}
                {supplierStore?.data?.integration?.platform?.key !== "API" && (
                  <div className="flex flex-col gap-3">
                    <span className="text-body4-regular text-v2-content-tertiary">
                      {t("supplier.profile.websiteAddress")}
                    </span>

                    <Link
                      target="_blank"
                      rel="noopener noreferrer"
                      href={ensureUrlScheme(supplierStore?.data?.url ?? "")}
                    >
                      <span className="text-body3-medium !font-semibold text-v2-content-on-info">
                        {supplierStore?.data?.url}
                      </span>
                    </Link>
                  </div>
                )}

                {/* status */}
                <div className="flex flex-col gap-1">
                  <span className="text-body4-regular text-v2-content-tertiary">{t("supplier.profile.status")}</span>
                  <span
                    className={twMerge(
                      "!text-body3-medium !font-semibold",
                      supplierStore?.data?.isActive ? "text-v2-content-on-success-2" : "text-v2-content-on-error-2"
                    )}
                  >
                    {supplierStore?.data?.isActive ? t("active") : t("deActive")}
                  </span>
                </div>
              </div>
            </div>
            {supplierStore?.data?.integration?.platform?.key === "API" && (
              <div className="border border-v2-border-primary mt-6 rounded-lg p-4">
                <div className="flex justify-between items-center border-b border-b-v2-border-primary pb-4">
                  <ApiKey
                    apiTitle={t("store.view.apiKey")}
                    apiValue={supplierStore?.data?.integration?.credential?.key}
                  />
                </div>
                <div className="flex justify-between items-center pt-4 ">
                  <ApiKey apiTitle={t("store.view.integrationKey")} apiValue={supplierStore?.data?.integration?.id} />
                </div>
              </div>
            )}

            <Pricing
              isLoading={isLoading}
              isStoreSingleLoading={isStoreSingleLoading}
              supplierStore={supplierStore}
              id={supplierStore?.data?.id}
              onContinue={() => {}}
              isEdit
              buttonContainerClassName="absolute -bottom-16 left-0"
              isApi={supplierStore?.data?.integration?.platform?.key === "API"}
            />
            {/* <SupplierDynamicForm
            isEdit
            id={integration?.id}
            isLoading={isLoading}
            configForm={configureForm}
            supplierStore={supplierStore}
            isStoreSingleLoading={isStoreSingleLoading}
          /> */}
          </div>
        </div>
      </ProfileContentContainer>
    </div>
  );
}
