import React from "react";
import { CircularProgress, Theme, useMediaQuery } from "@mui/material";
import { usePutRetailerStoreMutation } from "@/store/apps/retailer";
import useModal from "@/utils/hooks/useModal";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { usePostSupplierStoreStateMutation, usePutSupplierStoreMutation } from "@/store/apps/supplier";
import { TSupplierIntegrationResponse } from "@/store/apps/supplier/types";
import StoreModalBody from "@/app/retailer/(dashboard)/store/StoreWarningModalBody";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import Button from "@/components/ui/Button";
import { toast } from "@/components/ui/toast";

type RetailerStoreProps = {
  supplierStore?: TSupplierIntegrationResponse;
  disabled?: boolean;
  handleNotActiveUser?: () => void;
};

const DisconnectSupplierStoreButton: React.FC<RetailerStoreProps> = ({
  supplierStore,
  disabled,
  handleNotActiveUser
}) => {
  const { t } = useTranslation();
  const { showModal, hideModal } = useModal();
  const [postSupplierStoreState, { isLoading: isPutSupplierStoreLoading }] = usePostSupplierStoreStateMutation();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const makePath = useRoleBasePath();

  const handleActiveAndDeActive = async (isActive: boolean) => {
    if (handleNotActiveUser && disabled) {
      return handleNotActiveUser();
    } else {
      const body = {
        supplierId: supplierStore?.data?.id ?? "",
        isActive: isActive
      };

      try {
        const retailerStoreApi = postSupplierStoreState({ body });

        await retailerStoreApi.then((res: any) => {
          if ("data" in res && res?.data) {
            // showModal({
            //   body: (
            //     <StoreModalBody
            //       title={t("store.disconnected.title")}
            //       subtitle={t("store.disconnected.subtitle")}
            //       buttonText={t("confirm")}
            //     />
            //   ),
            //   width: isMobile ? undefined : 428
            // });

            hideModal();
            toast(t("updateStore"), { type: "success" });
          }

          if (res?.error) {
            clientDefaultErrorHandler({ error: res.error });
            return;
          }
        });
      } catch (err: any) {
        clientDefaultErrorHandler({ error: err });
      }
    }
  };

  const onConfirmDisconnect = (isActive: boolean) => {
    showModal({
      icon: "/images/svgs/redWarning.svg",
      title: t("disconnectTitle"),
      subTitle: t("disconnectSubtitle"),
      actions: [
        {
          label: t("cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("disconnectConnection"),
          variant: "accentPrimary",
          onClick: () => handleActiveAndDeActive(isActive)
        }
      ]
    });
  };

  const onConfirmConnect = (isActive: boolean) => {
    showModal({
      icon: "/images/svgs/successConnection.svg",
      title: t("connectTitle"),
      subTitle: t("connectSubtitle"),
      actions: [
        {
          label: t("cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("connect"),
          className: "!bg-success-500",
          variant: "accentPrimary",
          onClick: () => handleActiveAndDeActive(isActive)
        }
      ]
    });
  };

  if (supplierStore?.data?.isActive) {
    return (
      <Button
        variant="secondaryGray"
        color="info"
        onClick={() => onConfirmDisconnect(false)}
        startAdornment={<Icon icon="solar:clound-cross-outline" className="size-4" />}
      >
        {isPutSupplierStoreLoading ? <CircularProgress size={21} /> : t("disconnect")}
      </Button>
    );
  }

  return (
    <Button
      variant="secondaryGray"
      color="info"
      onClick={() => onConfirmConnect(true)}
      startAdornment={<Icon icon="solar:clound-cross-outline" className="size-4" />}
    >
      {isPutSupplierStoreLoading ? <CircularProgress size={21} /> : t("active")}
    </Button>
  );
};

export default DisconnectSupplierStoreButton;
