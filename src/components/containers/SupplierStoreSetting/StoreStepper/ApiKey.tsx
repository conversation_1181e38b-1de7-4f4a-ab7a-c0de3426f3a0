import useClipboard from "@/utils/hooks/useClipboard";
import { Icon } from "@iconify/react";

interface IApiKeyProps {
  apiTitle: string;
  apiValue: string;
}

function ApiKey({ apiTitle, apiValue }: IApiKeyProps) {
  const { copyToClipboard, isCopied } = useClipboard();

  return (
    <>
      <span className="text-body4-medium text-v2-content-tertiary">{apiTitle}</span>

      <div className="px-2 py-1.5 bg-v2-surface-info rounded flex items-center gap-2">
        <Icon
          icon={isCopied ? "charm:tick" : "solar:copy-outline"}
          className="size-4 cursor-pointer"
          onClick={() => copyToClipboard(apiValue)}
        />
        {apiValue}
      </div>
    </>
  );
}

export default ApiKey;
