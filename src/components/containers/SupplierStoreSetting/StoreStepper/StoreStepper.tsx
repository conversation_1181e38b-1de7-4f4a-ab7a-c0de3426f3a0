import { useGetMetaIntegrationQuery } from "@/store/apps/meta";
import { Dispatch, SetStateAction, useEffect, useLayoutEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Website from "./Website";
import Sync from "./Sync";
import Pricing from "./Pricing";
import View from "./View";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Icon } from "@iconify/react";
import { useGetSupplierStoreQuery } from "@/store/apps/supplier";
import Image from "next/image";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import StoreModalBody from "@/app/retailer/(dashboard)/store/StoreWarningModalBody";
import { useSelector } from "@/store/hooks";
import { TSupplierIntegrationResponse, TSupplierProfileData } from "@/store/apps/supplier/types";
import useModal from "@/utils/hooks/useModal";
import { useMediaQuery } from "@mui/system";
import { Theme } from "@mui/material";
import MobileAppBar from "../../mobileAppBar/MobileAppBar";
import ProfileContentContainer from "../../ProfileStepper/ProfileContentContainer";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";

interface ISelectWebsiteProps {
  supplierStore?: TSupplierIntegrationResponse;
  isStoreSingleLoading?: boolean;
  isCreate?: boolean;
  setInProgress: Dispatch<SetStateAction<boolean>>;
  setIsLoading?: (val: boolean) => void;
}

export default function StoreStepper({
  isStoreSingleLoading,
  supplierStore,
  setInProgress,
  setIsLoading,
  isCreate
}: ISelectWebsiteProps) {
  const { t } = useTranslation();
  const [isConnecting, setIsConnecting] = useState(false);
  const [currentStep, setCurrentStep] = useState<number | undefined>(undefined);
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { showModal } = useModal();
  const step = isNaN(Number(searchParams?.get("storeStep"))) ? "" : Number(searchParams?.get("storeStep"));
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const makePath = useRoleBasePath();

  const { data: integrations, isLoading } = useGetMetaIntegrationQuery();

  const supplierProfile = useSelector((state: any) => state?.Supplier?.queries["getSupplierProfile(undefined)"] || {});
  const supplierProfileData = supplierProfile?.data?.data as TSupplierProfileData;
  const activeProfile = supplierProfileData?.status === "Active";

  const [integrationKey, setIntegrationKey] = useState(supplierStore?.data?.integration?.platform?.key ?? "");

  const handleNotActive = () => {
    showModal({
      body: (
        <StoreModalBody
          title={t("userNotActive.title")}
          subtitle={t("userNotActive.subtitle")}
          buttonText={t("understand")}
        />
      ),
      width: isMobile ? undefined : 428
    });
  };
  const handleSelectWebsite = () => {
    if (!activeProfile) {
      handleNotActive();
      return;
    } else setCurrentStep(1);
  };

  useEffect(() => {
    if (step === 2) setCurrentStep(2);
  }, [step]);

  const generalSteps = [
    {
      step: 2,
      component: (
        <Sync
          isConnecting={isConnecting}
          isStoreSingleLoading={isStoreSingleLoading}
          supplierStore={supplierStore}
          onContinue={() => {
            setCurrentStep(3);
            router.replace(`${pathname}?settingType=store`);
          }}
          onBack={() => {
            setCurrentStep(1);
            router.replace(`${pathname}?settingType=store`);
          }}
        />
      )
    },
    {
      step: 3,
      component: (
        <Pricing
          isLoading={isLoading}
          isStoreSingleLoading={isStoreSingleLoading}
          supplierStore={supplierStore}
          id={supplierStore?.data?.id}
          onContinue={() => setCurrentStep(4)}
          onBack={() => setCurrentStep(2)}
        />
      )
    },
    {
      step: 4,
      component: (
        <View
          isStoreSingleLoading={isStoreSingleLoading}
          supplierStore={supplierStore}
          onBack={() => setCurrentStep(3)}
          onContinue={() => setInProgress(false)}
        />
      )
    }
  ];

  const apiSteps = [
    {
      step: 2,
      component: (
        <Pricing
          isLoading={isLoading}
          isStoreSingleLoading={isStoreSingleLoading}
          supplierStore={supplierStore}
          id={supplierStore?.data?.id}
          onContinue={() => setCurrentStep(3)}
          onBack={() => setCurrentStep(1)}
          isApi={integrationKey === "API"}
        />
      )
    },
    {
      step: 3,
      component: (
        <View
          isStoreSingleLoading={isStoreSingleLoading}
          supplierStore={supplierStore}
          onBack={() => setCurrentStep(2)}
          hasWebsiteLink={false}
          hasSyncData={false}
          hasWebsiteDocument
          hasWebsiteType
          hasApiKeyData
          onContinue={() => setInProgress(false)}
        />
      )
    }
  ];

  const getFlowSteps = (integrationKey: string) => {
    return integrationKey === "API" ? apiSteps : generalSteps;
  };

  const steps = [
    {
      step: 1,
      component: (
        <Website
          isLoading={isLoading}
          integrations={integrations?.data}
          supplierStore={supplierStore}
          isStoreSingleLoading={isStoreSingleLoading}
          setIsConnecting={setIsConnecting}
          setIntegrationKey={setIntegrationKey}
          isCreate={isCreate}
          setIsLoading={setIsLoading}
          onBack={() => setCurrentStep(undefined)}
          setInProgress={() => setInProgress(true)}
        />
      )
    },
    ...getFlowSteps(integrationKey)
  ];

  const activeStep = steps?.find(item => item?.step === currentStep);

  return (
    <>
      {!currentStep ? (
        <>
          <MobileAppBar title={t("storeInfo")} hasBack onBack={() => router.push(makePath(routes.profile))} />
          <ProfileContentContainer>
            <div className="flex flex-col gap-4 items-center h-full justify-center xmd:min-h-[auto] min-h-[70vh] xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] py-20  ">
              <div>
                <Image src="/images/profile/noStore.png" alt="noStore" width={120} height={120} />
              </div>
              <div>
                <p className="text-body1-medium text-v2-content-primary text-center">
                  {t("supplier.profile.noStoreTitle")}
                </p>
                <p className="text-body4-medium text-v2-content-secondary text-center mt-1">
                  {t("supplier.profile.noStoreSubtitle")}
                </p>
              </div>
              <CustomButton
                className="!bg-v2-surface-info !border-v2-surface-info text-v2-content-on-info !text-body2-medium"
                onClick={handleSelectWebsite}
                startIcon={<Icon icon="ic:sharp-plus" className="size-5 text-v2-content-on-info" />}
              >
                {t("supplier.profile.addStore")}
              </CustomButton>
            </div>
          </ProfileContentContainer>
        </>
      ) : (
        <div>{activeStep?.component}</div>
      )}
    </>
  );
}
