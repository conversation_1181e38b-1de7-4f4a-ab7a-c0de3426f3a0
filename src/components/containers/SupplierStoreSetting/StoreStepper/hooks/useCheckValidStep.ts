import { TSupplierIntegrationResponse } from "@/store/apps/supplier/types";
import useModal from "@/utils/hooks/useModal";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";

interface ICheckValidStep {
  supplierStore?: TSupplierIntegrationResponse;
  isStoreSingleLoading?: boolean;
}

export const useCheckValidStep = ({ supplierStore, isStoreSingleLoading }: ICheckValidStep) => {
  const router = useRouter();
  const { hideModal } = useModal();
  const pathname = usePathname();

  const hasData = !!supplierStore?.data?.id;

  useEffect(() => {
    if (isStoreSingleLoading) return;
    if (!hasData) {
      router.replace(pathname);
      hideModal();
      // router?.replace(`${pathname}?step=${1}`);
    }
  }, [hasData, isStoreSingleLoading]);
};
