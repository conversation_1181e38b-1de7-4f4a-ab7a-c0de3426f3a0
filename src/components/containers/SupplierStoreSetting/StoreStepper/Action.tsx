import BottomAction from "@/components/ui/bottomAction/BottomAction";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { CircularProgress } from "@mui/material";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

interface IActionProps {
  isLoading?: boolean;
  hasBack?: boolean;
  onContinue?: () => void;
  onBack?: () => void;
  disabledContinue?: boolean;
  disabledBack?: boolean;
  continueText?: string;
  buttonContainerClassName?: string;
  buttonType?: "submit" | "button";
}

function Action({
  disabledBack,
  disabledContinue,
  continueText,
  hasBack = true,
  isLoading,
  onBack,
  onContinue,
  buttonContainerClassName,
  buttonType = "button"
}: IActionProps) {
  const { t } = useTranslation();

  return (
    <>
      <BottomAction
        saveButtonText={isLoading ? <CircularProgress color="info" size={26} /> : continueText || t("confirm&continue")}
        saveButtonProps={{
          type: buttonType,
          onClick: () => onContinue?.(),
          disabled: disabledContinue
        }}
        cancelButtonText={t("cancelButton")}
        cancelButtonProps={{
          onClick: () => {
            onBack?.();
          },
          className: "hidden"
        }}
      />

      <div className={twMerge("xmd:flex hidden items-center justify-between w-full", buttonContainerClassName)}>
        {/* {hasBack && (
          <CustomButton color="secondary" disabled={disabledBack} onClick={onBack}>
            {t("cancelButton")}
          </CustomButton>
        )} */}
        <div />
        <CustomButton disabled={disabledContinue} type={buttonType} onClick={onContinue} className="mr-auto">
          {isLoading ? <CircularProgress color="info" size={26} /> : continueText || t("confirm&continue")}
        </CustomButton>
      </div>
    </>
  );
}

export default Action;
