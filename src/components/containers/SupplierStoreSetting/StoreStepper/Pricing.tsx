import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import NumberInput from "@/components/ui/inputs/NumberInput";
import { SUPPLIER_INTEGRATION_KEY } from "@/constants/queryKeys";
import { IntegrationsPutDataResponse } from "@/store/apps/retailer/types";
import { Supplier, usePutSupplierStoreMutation } from "@/store/apps/supplier";
import { TSupplierIntegrationResponse } from "@/store/apps/supplier/types";
import { useDispatch } from "@/store/hooks";
import useCurrency from "@/utils/hooks/useCurrency";
import useModal from "@/utils/hooks/useModal";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { Icon } from "@iconify/react";
import { Box, CircularProgress, FormControl, Grid } from "@mui/material";
import { Form, Formik, FormikHelpers } from "formik";
import { isNaN } from "lodash";
import { useParams, usePathname, useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import Action from "./Action";
import { validationSchema } from "./utils";
import ProfileContentContainer from "../../ProfileStepper/ProfileContentContainer";
import MobileAppBar from "../../mobileAppBar/MobileAppBar";
import { twMerge } from "tailwind-merge";
import { toast } from "@/components/ui/toast";

interface IPricingProps {
  id?: string;
  buttonContainerClassName?: string;
  isLoading: boolean;
  isEdit?: boolean;
  supplierStore?: TSupplierIntegrationResponse;
  isStoreSingleLoading?: boolean;
  isApi?: boolean;
  onContinue: () => void;
  onBack?: () => void;
}

const Pricing = ({
  isLoading,
  id,
  supplierStore,
  isStoreSingleLoading,
  onBack,
  isApi = false,
  onContinue,
  buttonContainerClassName,
  isEdit = false
}: IPricingProps) => {
  const [_selected, _selectCurrency] = useCurrency();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  // useCheckValidStep({ isStoreSingleLoading, supplierStore });

  const [putSupplierStore, { isLoading: isPutSupplierStoreLoading }] = usePutSupplierStoreMutation();

  const isSubmitting = isPutSupplierStoreLoading;

  const initialValues = {
    preferences: {
      syncOrders: supplierStore?.data?.preferences?.syncOrders || false,
      syncProducts: supplierStore?.data?.preferences?.syncProducts || false
    },
    wholesalePrice: supplierStore?.data?.wholesalePrice,
    adjustmentPercentage: supplierStore?.data?.adjustmentPercentage
  };

  const onSubmit = async (
    { preferences, adjustmentPercentage, wholesalePrice, ...values }: { [key: string]: any },
    { setFieldError }: FormikHelpers<typeof initialValues>
  ) => {
    const apiModalBody = {
      config: supplierStore?.data?.config,
      platform: supplierStore?.data?.integration?.platform?.key,
      adjustmentPercentage,
      wholesalePrice: wholesalePrice === "false" ? false : true,
      isActive: true
    };

    const generalBody = {
      config: supplierStore?.data?.config,
      preferences,
      platform: supplierStore?.data?.integration?.platform?.key,
      adjustmentPercentage,
      wholesalePrice: wholesalePrice === "false" ? false : true,
      isActive: true
      // returnUri: "/product"
    } as any;

    const body = isApi ? apiModalBody : generalBody;

    try {
      const supplierStoreApi = putSupplierStore({ body });

      await supplierStoreApi.then(res => {
        const error = (res as any)?.error?.data;

        const resData = (res as any)?.data?.data as IntegrationsPutDataResponse["data"];

        if (error) {
          if (error.error_detail.handle?.includes("already exists")) {
            const bodyError = {
              ...error,
              error_detail: { handle: ["userDuplicate"] }
            };

            clientDefaultErrorHandler({ error: (res as any)?.error, bodyError, setFieldError });
          } else clientDefaultErrorHandler({ bodyError: error, error: (res as any)?.error, setFieldError });
        } else if ("data" in res && res?.data) {
          dispatch(Supplier.util.invalidateTags([{ type: SUPPLIER_INTEGRATION_KEY }]));
          onContinue();

          toast(t("updateStore"), { type: "success" });
          // router.replace(`${pathname}?step=${4}`);
          // hideModal();
          // if (resData?.shouldRedirect && resData?.redirectTo) {
          //   router.push(ensureUrlScheme(resData?.redirectTo));
          // } else router.push(makePath(routes.product));
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  if (isLoading || isStoreSingleLoading) {
    return (
      <Box className="flex items-center justify-center p-10">
        <CircularProgress />
      </Box>
    );
  }

  if (!supplierStore?.data?.id) {
    return null;
  }

  return (
    <>
      {!isEdit && <MobileAppBar title={t("connectStore")} hasBack onBack={onBack} />}
      <ProfileContentContainer
        containerClassName={twMerge("!py-4", isEdit ? "!p-0" : "")}
        wrapperClassName={isEdit ? "!mt-0" : undefined}
        topWrapperClassName={isEdit ? "xmd:px-4 px-0" : ""}
      >
        {!isEdit && (
          <div className="xmd:flex hidden  items-center w-fit gap-2 cursor-pointer mb-4" onClick={onBack}>
            <Icon icon="solar:arrow-right-outline" className="size-5 text-v2-content-tertiary " />

            <p className="text-center text-v2-content-tertiary text-body2-medium  font-semibold flex-1">{t("back")}</p>
          </div>
        )}

        <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={onSubmit}>
          {({ setFieldValue, handleBlur, handleChange, errors, touched, values }) => (
            <Form>
              <>
                <div
                  className={twMerge(
                    "xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4  relative mt-4 w-full",
                    isEdit && "!border-transparent"
                  )}
                >
                  {!isEdit && (
                    <div className="xmd:p-6 p-0 pb-4 pt-0 border-b border-b-v2-border-primary flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <Icon icon="solar:folder-path-connect-outline" className="size-5" />
                        <span className="text-body1-medium text-v2-content-primary !font-semibold">
                          {t("store.productStore.title")}
                        </span>
                      </div>

                      <span className="text-body3-medium text-v2-content-tertiary">
                        {t("store.productStore.subtitle")}
                      </span>
                    </div>
                  )}

                  <div className={twMerge("xmd:p-6 p-0 pt-4", isEdit && "xmd:p-0 p-0")}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={12}>
                        <p className="text-body2-bold text-gray-999 mb-4">{t("store.priceType")}</p>
                        <FormControl id="sx-supplierinfo-17762">
                          <div className="flex items-center gap-x-8 gap-y-4  flex-wrap">
                            <CustomRadio
                              checked={values?.wholesalePrice === false || (values?.wholesalePrice as any) === "false"}
                              onChange={handleChange}
                              onBlur={handleBlur}
                              value={false}
                              size="small"
                              label={t("store.partPrice")}
                              name="wholesalePrice"
                            />
                            <CustomRadio
                              checked={values?.wholesalePrice === true || (values?.wholesalePrice as any) === "true"}
                              onChange={handleChange}
                              onBlur={handleBlur}
                              value={true}
                              size="small"
                              label={t("store.wholePrice")}
                              name="wholesalePrice"
                            />
                          </div>
                        </FormControl>
                      </Grid>

                      {/* {!values?.wholesalePrice && ( */}
                      <Grid item xs={12} md={12}>
                        <NumberInput
                          id="adjustmentPercentage"
                          name="adjustmentPercentage"
                          autoComplete="off"
                          onBlur={handleBlur}
                          label={t("store.adjustmentPercentage")}
                          value={values?.adjustmentPercentage || ""}
                          placeholder={t("store.adjustmentPercentage")}
                          endAdornment={<span className="text-caption-regular text-gray-400">{t("percent")}</span>}
                          onChange={e => {
                            if (e?.target?.value === undefined || isNaN(e?.target?.value)) return;

                            setFieldValue("adjustmentPercentage", Number(e?.target?.value));
                          }}
                          error={touched.adjustmentPercentage && Boolean(errors.adjustmentPercentage)}
                          helperText={
                            (!!touched?.adjustmentPercentage && (errors?.adjustmentPercentage as string)) || undefined
                          }
                        />
                      </Grid>
                      {/* )} */}
                      <Grid item xs={12} md={12} marginTop={0}>
                        <div className="bg-v2-surface-info flex items-center gap-1.5 p-2.5 rounded-md">
                          <Icon icon="solar:info-circle-outline" className="size-5 shrink-0" />
                          <span className="text-body4-medium text-gray-999">
                            {values?.wholesalePrice === true || (values?.wholesalePrice as any) === "true"
                              ? t("store.alertWholePrice")
                              : t("store.alertPrice")}
                          </span>
                        </div>
                      </Grid>
                      {supplierStore?.data?.integration?.platform?.hasPreferences && (
                        <>
                          <Grid item xs={12} md={12} marginTop={1}>
                            <h2 className="text-body2-bold !font-semibold text-gray-999">{t("synchronization")}</h2>
                          </Grid>

                          <Grid item xs={12} md={12}>
                            <div className="flex items-start gap-2">
                              <CustomSwitch
                                id="preferences.syncOrders"
                                name="preferences.syncOrders"
                                checked={values?.preferences?.syncOrders}
                                label=""
                                labelClassName="!mr-0 !ml-0"
                                onChange={(e, checked) => setFieldValue("preferences.syncOrders", checked)}
                              />
                              <div className="flex flex-col ">
                                <span className="text-body4-medium text-gray-999">{t("store.syncOrdersTitle")}</span>
                                <span className="text-caption-medium text-gray-600">
                                  {t("store.syncOrdersSubtitle")}
                                </span>
                              </div>
                            </div>
                          </Grid>
                          <Grid item xs={12} md={12}>
                            <div className="flex items-start gap-2">
                              <CustomSwitch
                                id="preferences.syncProducts"
                                name="preferences.syncProducts"
                                checked={values?.preferences?.syncProducts}
                                label=""
                                labelClassName="!mr-0 !ml-0"
                                onChange={(e, checked) => setFieldValue("preferences.syncProducts", checked)}
                              />
                              <div className="flex flex-col ">
                                <span className="text-body4-medium text-gray-999">{t("store.syncProductsTitle")}</span>
                                <span className="text-caption-medium text-gray-600">
                                  {t("store.syncProductsSubtitle")}
                                </span>
                              </div>{" "}
                            </div>
                          </Grid>
                        </>
                      )}
                    </Grid>
                  </div>
                </div>

                <Action
                  buttonType="submit"
                  onBack={onBack}
                  isLoading={isSubmitting}
                  hasBack={!isEdit}
                  continueText={t("saveChanges")}
                  buttonContainerClassName={buttonContainerClassName}
                />
              </>
              {/* </Grid> */}
            </Form>
          )}
        </Formik>
      </ProfileContentContainer>
    </>
  );
};

export default Pricing;
