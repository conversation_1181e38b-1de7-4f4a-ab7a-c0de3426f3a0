"use client";

import { useTranslation } from "react-i18next";
import RetailerProductsLoading from "./components/RetailerProductsLoading";
import { useEffect, useMemo, useState } from "react";
import { CircularProgress, Grid, SelectChangeEvent, Theme, useMediaQuery } from "@mui/material";
import { useGetProductListQuery } from "@/store/apps/product";
import ProductCard from "@/components/containers/ProductCard/ProductCard";
import CustomTablePagination from "@/components/ui/CustomTablePagination/CustomTablePagination";
import useLocations from "@/utils/hooks/useLocations";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import EmptyPage from "./EmptyPage";
import RetailerProductMobile from "./RetailerProductMobile";
import { calcCheapestPrice, desktopGridSx } from "./utils";
import Filters from "./components/Filters/Filters";
import {
  generateBackendFilters,
  generateBackendSorts,
  TGenerateBackendFiltersProps
} from "@/utils/services/transformers";
import { merge } from "lodash";
import { IProductSupplier } from "@/store/apps/product/types";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";
import { handleSetFilter, omitEmptyValues } from "@/utils/helpers";
import { TProductFilters, useProductFilters } from "./components/Filters/useProductFilters";
import Searchbar from "./components/searchbar/searchbar";
import Categories from "./components/categories/categories";
import Breadcrumb from "./components/breadcrumb/breadcrumb";
import Header from "./components/header/header";
import { useParams } from "next/navigation";
import SupplierCard from "@/components/containers/SupplierCard/SupplierCard";
// import SupplierCard from "../SupplierCard/SupplierCard";

const rowsPerPageOptions = {
  twoCards: [10, 20, 50, 100],
  threeCards: [21, 42, 72, 99],
  fourCards: [20, 40, 80, 100]
};

export default function RetailerProduct({
  showFilters = true,
  initialFilters,
  setSupplierData,
  isSupplierView
}: {
  showFilters?: boolean;
  initialFilters?: Partial<TProductFilters>;
  setSupplierData?: (data: IProductSupplier) => void;
  isSupplierView?: boolean;
}) {
  const { t } = useTranslation();
  const params = useParams();

  const { filters, pagination, sorts: sortsStates, setFilters, viewType, resetAll } = useProductFilters();

  // useEffect(() => {
  //   resetAll();
  // }, [params]);

  const { page, pageSize } = pagination || {};
  const { created_at, updated_at } = sortsStates || {};
  const nonEmptyFilters = useMemo(() => omitEmptyValues(filters), [filters]);

  const [rowsPerPage, setRowsPerPage] = useState(rowsPerPageOptions["twoCards"]);
  const { getLocation: findLocation } = useLocations();
  const makePath = useRoleBasePath();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const isPageWidthIsSm = useMediaQuery((theme: Theme) => theme.breakpoints.up("sm"));
  const isPageWidthIsMd = useMediaQuery((theme: Theme) => theme.breakpoints.up("md"));
  const isPageWidthIsLg = useMediaQuery((theme: Theme) => theme.breakpoints.up("lg"));

  useEffect(() => {
    // sm: 2 cards
    if (isPageWidthIsSm) {
      setRowsPerPage(rowsPerPageOptions["twoCards"]);
      if (pageSize === 10) setFilters({ pageSize: rowsPerPageOptions["twoCards"][0] }, { history: "push" });
    }
    // md: 3 cards
    if (isPageWidthIsMd) {
      setRowsPerPage(rowsPerPageOptions["threeCards"]);
      if (pageSize === 10) setFilters({ pageSize: rowsPerPageOptions["threeCards"][0] }, { history: "push" });
    }
    // lg: 4 cards
    if (isPageWidthIsLg) {
      setRowsPerPage(rowsPerPageOptions["fourCards"]);
      if (pageSize === 10) setFilters({ pageSize: rowsPerPageOptions["fourCards"][0] }, { history: "push" });
    }
  }, [isPageWidthIsSm, isPageWidthIsMd, isPageWidthIsLg]);

  const finalFilters = generateBackendFilters(merge(initialFilters, nonEmptyFilters) as TGenerateBackendFiltersProps);
  const sorts = generateBackendSorts(
    omitEmptyValues({
      created_at: (created_at as any) || undefined,
      updated_at: (updated_at as any) || undefined
    })
  );

  const queryString = [
    pageSize ? `page_size=${pageSize}` : "",
    page ? `page=${page}` : "",
    finalFilters ? `filters=${finalFilters}` : "",
    sorts ? `sorts=${sorts}` : ""
  ]
    ?.filter(part => part !== "")
    ?.join("&");

  const {
    data: productData,
    isLoading: isProductLoading,
    isFetching: isProductFetching,
    isError: isProductError
  } = useGetProductListQuery(queryString, { skip: !page, refetchOnMountOrArgChange: true });

  useEffect(() => {
    // set first items'supplier data for parent
    // useful when in specific supplier page
    if (productData?.data?.[0]?.supplier) {
      setSupplierData?.(productData?.data?.[0]?.supplier);
    }
  }, [JSON.stringify(productData?.data)]);

  const totalCount = productData?.pagination?.total ?? 0;

  const handleChangePage = (event: unknown, newPage: number) => {
    setFilters({ page: newPage }, { history: "push" });
  };

  const handleChangeRowsPerPage = (event: SelectChangeEvent<number>) => {
    setFilters({ page: 1, pageSize: event.target.value as number }, { history: "push" });
  };

  if (isMobile) {
    return (
      <RetailerProductMobile
        {...{
          productData,
          findLocation,
          totalCount,
          pageSize,
          page: page,
          handleChangePage,
          handleChangeRowsPerPage,
          isError: isProductError,
          isLoading: isProductLoading || isProductFetching,
          showFilters,
          isSupplierView
        }}
      />
    );
  }

  if (isProductLoading) {
    return (
      <div className="h-full w-full flex flex-col items-center justify-center min-h-[calc(100vh_-_120px)] rounded-[10px] bg-cards">
        <CircularProgress />
      </div>
    );
  }

  return (
    <CustomCardContent className="h-full flex flex-col">
      <div className="h-full flex flex-col">
        {showFilters && (
          <div className="mb-6 flex flex-col gap-6">
            <div className="flex flex-col gap-6">
              <Searchbar />
              <Categories
                onSelectCategory={id => {
                  // setFilters({ category: id }, { history: "push" });
                  handleSetFilter({ key: "category", value: id as any, setFilters });
                }}
              />
            </div>
            <div className="flex flex-col divide-y divide-v2-border-primary gap-4">
              {!isSupplierView && (
                <div className="flex flex-col gap-9">
                  <Breadcrumb />
                  <Header totalDataCount={totalCount} />
                </div>
              )}
              <div className="pt-4">
                <Filters />
              </div>
            </div>
          </div>
        )}

        {isProductFetching && <RetailerProductsLoading count={6} />}

        {(isProductError || (!isProductFetching && !isProductLoading && !productData?.data?.length)) && <EmptyPage />}

        {viewType === "suppliers" && (
          <div>
            <>
              <Grid container spacing={0} sx={desktopGridSx}>
                {[...Array.from({ length: 10 })].map((_, index) => {
                  return (
                    <Grid
                      key={index}
                      item
                      xs={12}
                      sm={6}
                      md={4}
                      lg={3}
                      position={"relative"}
                      boxSizing="border-box"
                      alignItems="stretch"
                      className="relative"
                    >
                      <SupplierCard
                        key={index}
                        cover="/images/products/bproduct-5.jpg"
                        logo="/images/products/bproduct-11.jpg"
                        title="تکنواستایل"
                        categoryLabel="مد و پوشاک"
                      />
                    </Grid>
                  );
                })}
              </Grid>
              <div className="mt-20">
                <CustomTablePagination
                  rowsPerPageOptions={rowsPerPage}
                  count={totalCount}
                  rowsPerPage={pageSize}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  labelRowsPerPage={t("product.rowPerPage")}
                />
              </div>
            </>
          </div>
        )}

        {viewType === "products" &&
          !isProductError &&
          !isProductFetching &&
          productData?.data &&
          productData?.data?.length > 0 && (
            <>
              <Grid container spacing={0} sx={desktopGridSx}>
                {productData?.data?.map((product, index) => {
                  const cheapestShippingItem = calcCheapestPrice(product?.shippingPolicies);
                  const { id, cover, title, supplier, cheapestPrice, hasVariant, cheapestVariant } = product;

                  return (
                    <Grid
                      key={product.id}
                      item
                      xs={12}
                      sm={6}
                      md={4}
                      lg={3}
                      position={"relative"}
                      boxSizing="border-box"
                      alignItems="stretch"
                      className="relative"
                    >
                      <ProductCard
                        key={index}
                        productId={id}
                        cover={cover}
                        title={title}
                        imported={product?.imported}
                        supplier={{ id: supplier?.id, name: supplier?.name }}
                        price={cheapestVariant?.retailPrice}
                        authenticity={cheapestVariant?.authenticity}
                        condition={cheapestVariant?.condition}
                        commission={cheapestVariant?.commission}
                        retailPrice={cheapestVariant?.retailPrice}
                        shippingPrice={cheapestShippingItem?.rate}
                        shippingTime={cheapestShippingItem?.shippingTime}
                        shippingPolicies={product?.shippingPolicies}
                        findLocation={findLocation}
                        href={`${makePath(routes.product)}/${product.id}`}
                        isSupplierView={isSupplierView}
                      />
                    </Grid>
                  );
                })}
              </Grid>
              <div className="mt-20">
                <CustomTablePagination
                  rowsPerPageOptions={rowsPerPage}
                  count={totalCount}
                  rowsPerPage={pageSize}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  labelRowsPerPage={t("product.rowPerPage")}
                />
              </div>
            </>
          )}
      </div>
    </CustomCardContent>
  );
}
