import { TSupplierShippingData } from "@/store/apps/supplier/types";

export const desktopGridSx = {
  "--Grid-borderWidth": "1px",
  borderTop: "1px solid",
  borderLeft: "1px solid",
  borderColor: "rgb(var(--neutral-neutral-light))",
  "& > div": {
    borderRight: "1px solid",
    borderBottom: "1px solid",
    borderColor: "rgb(var(--neutral-neutral-light))"
  },
  "& > div:hover": {
    borderColor: "rgb(var(--neutral-neutral-light))"
  }
};

export function calcCheapestPrice(shippingPolicies?: TSupplierShippingData[]) {
  const cheapestShippingItem =
    shippingPolicies && shippingPolicies?.length > 0
      ? [...shippingPolicies].sort(
          (a, b) => parseFloat(a.rate || "") || Infinity - parseFloat(b.rate || "") || Infinity
        )?.[0]
      : undefined;

  return cheapestShippingItem;
}
