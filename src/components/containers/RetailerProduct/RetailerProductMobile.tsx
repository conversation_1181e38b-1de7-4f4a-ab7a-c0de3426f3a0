import MobileProductCard from "@/components/containers/ProductCard/MobileProductCard";
import { TProductListResponse } from "@/store/apps/product/types";
import React from "react";
import { calcCheapestPrice } from "./utils";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import { CircularProgress, SelectChangeEvent } from "@mui/material";
import { TMetaLocations } from "@/store/apps/meta/types";
import CustomTablePagination from "@/components/ui/CustomTablePagination/CustomTablePagination";
import { useTranslation } from "react-i18next";
import EmptyPage from "./EmptyPage";
import Filters from "./components/Filters/Filters";
import Searchbar from "./components/searchbar/searchbar";
import MobileCategories from "./components/categories/mobileCategories";
import Header from "./components/header/header";

type TRetailerProductMobileProps = {
  productData?: TProductListResponse;
  isLoading?: boolean;
  isError?: boolean;
  findLocation: (cityId: string | number) => TMetaLocations | null;
  totalCount: number;
  pageSize: number;
  page: number;
  handleChangePage: (event: unknown, newPage: number) => void;
  handleChangeRowsPerPage: (event: SelectChangeEvent<number>) => void;
  showFilters?: boolean;
  isSupplierView?: boolean;
};

function RetailerProductMobile(props: TRetailerProductMobileProps) {
  const {
    productData,
    isLoading,
    isError,
    findLocation,
    totalCount,
    pageSize,
    page,
    handleChangePage,
    handleChangeRowsPerPage,
    showFilters = true,
    isSupplierView
  } = props || {};
  const makePath = useRoleBasePath();
  const { t } = useTranslation();

  return (
    <div className="bg-v2-background-secondary h-full flex flex-col">
      {showFilters && (
        <div className="flex flex-col">
          <div className="bg-v2-surface-primary p-4 gap-4 flex flex-col">
            <Searchbar />
            <MobileCategories />
            {!isSupplierView && <Header totalDataCount={totalCount} />}
          </div>

          <div className="p-4">
            <Filters />
          </div>
        </div>
      )}
      <div className="px-4 pb-4 flex-1">
        {(isError || (!isLoading && !productData?.data?.length)) && <EmptyPage />}

        {isLoading && (
          <div className="h-full w-full flex-1 flex items-center justify-center">
            <CircularProgress />
          </div>
        )}

        {!isError && !isLoading && productData?.data && productData?.data?.length > 0 && (
          <div>
            <div className="flex-1 flex flex-col gap-2">
              {productData?.data?.map((product, index) => {
                const cheapestShippingItem = calcCheapestPrice(product?.shippingPolicies);
                const { id, cover, title, supplier, cheapestPrice, cheapestVariant } = product;

                return (
                  <MobileProductCard
                    key={index}
                    productId={id}
                    cover={cover}
                    title={title}
                    imported={product?.imported}
                    supplier={{ id: supplier?.id, name: supplier?.name }}
                    price={cheapestPrice}
                    authenticity={cheapestVariant?.authenticity}
                    condition={cheapestVariant?.condition}
                    commission={cheapestVariant?.commission}
                    retailPrice={cheapestVariant?.retailPrice}
                    shippingPrice={cheapestShippingItem?.rate}
                    shippingTime={cheapestShippingItem?.shippingTime}
                    shippingPolicies={product?.shippingPolicies}
                    findLocation={findLocation}
                    href={`${makePath(routes.product)}/${product.id}`}
                    isSupplierView={isSupplierView}
                  />
                );
              })}
            </div>

            <div className="mt-4">
              <CustomTablePagination
                rowsPerPageOptions={[10, 50, 100, 200]}
                count={totalCount}
                rowsPerPage={pageSize}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage={t("product.rowPerPage")}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default RetailerProductMobile;
