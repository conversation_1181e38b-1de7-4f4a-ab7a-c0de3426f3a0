import Button from "@/components/ui/Button";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Typography } from "@mui/material";
import { IconPlus } from "@tabler/icons-react";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";

interface IProductEmptyListProps {
  hasFilters: boolean;
  isDraft: boolean;
}

function ProductEmptyList({ hasFilters, isDraft }: IProductEmptyListProps) {
  const makePath = useRoleBasePath();
  const { t } = useTranslation();

  return (
    <div className="flex flex-col items-center justify-center h-full max-h-fullMinesHeader gap-5 min-h-[60dvh]">
      <div className="">
        {hasFilters ? (
          <Image src="/images/product-search-not-found-1.svg" width={196} height={230} alt="empty list palceholder" />
        ) : (
          <Image src="/images/product-list-empty-2.svg" width={196} height={230} alt="empty list palceholder" />
        )}
      </div>

      <div className="flex gap-2 flex-col text-center">
        <Typography variant="h5">
          {hasFilters
            ? t("product.noProducts")
            : isDraft
            ? t("product.emptyList")
            : t("retailer.categoryPage.emptyPublishedList")}
        </Typography>
        <Typography variant="caption">
          {hasFilters
            ? t("product.noProductsSubtitle")
            : isDraft
            ? t("retailer.categoryPage.emptyListSubTitle")
            : t("retailer.categoryPage.emptyPublishedListSubTitle")}
        </Typography>
      </div>
      {!hasFilters && isDraft && (
        <Link href={makePath(routes.product)}>
          <Button size="lg" variant="secondaryColor">
            <IconPlus />
            {t("product.createProduct")}
          </Button>
        </Link>
      )}
    </div>
  );
}

export default ProductEmptyList;
