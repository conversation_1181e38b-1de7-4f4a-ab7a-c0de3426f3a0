import { useGetMetaCategoriesQuery } from "@/store/apps/meta";
import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import RootMenuItem from "./ui/rootMenuItem";
import { ClickAwayListener, Menu, Popover } from "@mui/material";
import { Icon } from "@iconify/react";
import InnerMenuItem from "./ui/innerMenuItem";
import { getCategoryAndItsChildrenById } from "@/components/containers/SelectCategoryModal/utils";
import useBreadCrumbData from "../breadcrumb/useBreadCrumbData";
import { uniq } from "lodash";
import { twMerge } from "tailwind-merge";
import InnerCategoryTitleItem from "@/components/containers/RetailerProduct/components/categories/ui/innerCategoryTitleItem";

const rootId = "00000000-0000-0000-0000-000000000000";

function Categories({ onSelectCategory }: { onSelectCategory: (id: string | null) => void }) {
  const { t } = useTranslation();
  const [isPopoverOpen, setPopoverOpen] = React.useState<HTMLDivElement | null>(null);
  const breadCrumbData = useBreadCrumbData();
  const [activeMenuId, setActiveMenuId] = useState("");
  const [isMouseOverPopover, setIsMouseOverPopover] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const [visibleCount, setVisibleCount] = useState(0);
  const [selectedItemsState, setSelectedItems] = useState<Array<string | null> | null>(
    breadCrumbData?.slice(0, -1)?.map(i => i.id) || null
  );
  const selectedItems = uniq(selectedItemsState);

  useEffect(() => {
    setSelectedItems(breadCrumbData?.slice(0, -1)?.map(i => i.id) || null);
  }, [breadCrumbData]);

  const { data, isLoading } = useGetMetaCategoriesQuery();
  const rootCategories = data?.data?.filter(a => a.parentId === rootId);

  useEffect(() => {
    if (containerRef.current) {
      const containerWidth = containerRef.current.offsetWidth;
      const itemWidth = 176; // Assuming each item has a fixed width of 100px
      const count = Math.floor(containerWidth / itemWidth);
      setVisibleCount(count);
    }
  }, [rootCategories]);

  const handleClickRootMenu = (event: React.MouseEvent<HTMLDivElement, MouseEvent>, id: string | null) => {
    event?.preventDefault();

    if (breadCrumbData?.[0]?.id !== id) {
      setSelectedItems([id]);
    } else {
      setSelectedItems(breadCrumbData?.map(i => i.id) || null);
    }

    if (containerRef?.current && !isPopoverOpen) {
      setPopoverOpen(containerRef?.current);
    }
  };

  const handleClosePopOver = () => {
    setPopoverOpen(null);
    setActiveMenuId("");
  };

  const handleSelectCategoryTree = (categoryId: string, lvl: number) => {
    setSelectedItems(prev => [...(prev?.slice(0, lvl + 1) || []), categoryId]);
  };

  const handleSelectCategory = (categoryId: string) => {
    onSelectCategory(categoryId);
    handleClosePopOver();
  };

  const handleContainerMouseLeave = (e: any) => {
    // Check if the mouse is moving to the popover
    const toElement = e.relatedTarget;
    const isMovingToPopover = toElement?.closest?.("#category-popover");

    if (!isMovingToPopover && !isMouseOverPopover) {
      handleClosePopOver();
    }
  };

  const RenderPopoverBody = () => {
    return (
      <div
        className={twMerge(
          "py-4 grid divide-x divide-x-reverse divide-v2-border-primary",
          selectedItems?.length <= 3
            ? "grid-cols-3"
            : selectedItems?.length === 4
              ? "grid-cols-4"
              : selectedItems?.length === 5
                ? "grid-cols-5"
                : ""
        )}
      >
        {selectedItems?.map((item, index) => {
          const isOtherRootCategories = item === null;
          const fountMenuItems =
            item === rootId
              ? rootCategories
              : isOtherRootCategories
                ? rootCategories?.slice(visibleCount)
                : index === 0
                  ? rootCategories?.filter(a => a.id === item)
                  : data?.data && getCategoryAndItsChildrenById(data?.data, item);

          if (!fountMenuItems?.length) {
            return;
          }

          return (
            <div key={index} className="min-h-56 max-h-[50vh] overflow-y-auto flex flex-col">
              {fountMenuItems?.map(fundMenuItem => {
                if (!fundMenuItem?.subCategories?.length) {
                  return;
                }

                return (
                  <div key={fundMenuItem?.id} className="flex flex-col">
                    <InnerCategoryTitleItem
                      onClick={() => {
                        if (isOtherRootCategories) {
                          handleSelectCategoryTree(fundMenuItem?.id, index);
                        } else {
                          handleSelectCategory(fundMenuItem?.id);
                        }
                      }}
                      showIcon={isOtherRootCategories}
                    >
                      {isOtherRootCategories ? "" : t("retailerProduct.allInCategoryThe")} {fundMenuItem?.name}
                    </InnerCategoryTitleItem>
                    {!isOtherRootCategories && (
                      <div className="flex flex-col w-full flex-1">
                        {fundMenuItem?.subCategories?.map(item => (
                          <InnerMenuItem
                            key={item?.id}
                            onClick={() => {
                              if (item?.subCategories?.length) {
                                handleSelectCategoryTree(item?.id, index);
                              } else {
                                handleSelectCategory(item?.id);
                              }
                            }}
                            showIcon={!!item?.subCategories?.length}
                          >
                            {item?.name}
                          </InnerMenuItem>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    );
  };

  if (isLoading) return null;

  return (
    <>
      <div
        ref={containerRef}
        className="flex gap-1 items-start rounded-lg bg-v2-surface-secondary border-b border-b-v2-border-primary"
        onMouseLeave={handleContainerMouseLeave}
      >
        <RootMenuItem
          isActive={!activeMenuId}
          onClick={e => {
            onSelectCategory(null);
            handleClosePopOver();
          }}
        >
          {t("retailerProduct.allCategories")}
        </RootMenuItem>

        {rootCategories?.slice(0, visibleCount).map(item => (
          <RootMenuItem
            key={item?.id}
            onClick={e => {
              handleClickRootMenu(e, item?.id);
              setActiveMenuId(item?.id);
            }}
            isActive={!!breadCrumbData?.find(a => a.id === item?.id) || activeMenuId === item?.id}
          >
            {item?.name}
          </RootMenuItem>
        ))}
        {rootCategories && rootCategories?.length > visibleCount && (
          <RootMenuItem
            isActive={activeMenuId === "other"}
            onClick={e => {
              handleClickRootMenu(e, null);
              setActiveMenuId("other");
            }}
          >
            {t("retailerProduct.otherCategories")}{" "}
            <Icon icon="solar:alt-arrow-down-outline" width={20} height={20} className="text-v2-content-subtle" />
          </RootMenuItem>
        )}
      </div>

      <Popover
        id="category-popover"
        open={Boolean(isPopoverOpen)}
        anchorEl={isPopoverOpen}
        onClose={handleClosePopOver}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left"
        }}
        marginThreshold={0}
        slotProps={{
          paper: {
            className: "w-full",
            style: {
              boxShadow: "0px 4px 20px 1px rgba(0, 0, 0, 0.10)",
              borderRadius: "8px",
              width: containerRef?.current?.clientWidth + "px",
              left: 0,
              pointerEvents: "auto"
            },
            onBlur: () => {
              handleClosePopOver();
            },
            onMouseEnter: () => {
              setIsMouseOverPopover(true);
            },
            onMouseLeave: () => {
              setIsMouseOverPopover(false);
              handleClosePopOver();
            }
          },
          root: {
            style: {
              pointerEvents: "none"
            }
          }
        }}
        transitionDuration={1}
      >
        <RenderPopoverBody />
      </Popover>
    </>
  );
}

export default Categories;
