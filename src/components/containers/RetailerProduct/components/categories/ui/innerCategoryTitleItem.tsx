import { Icon } from "@iconify/react";
import React, { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

function InnerCategoryTitleItem({
  children,
  onClick,
  showIcon = true
}: {
  children: ReactNode;
  onClick?: React.MouseEventHandler<HTMLDivElement> | undefined;
  showIcon?: boolean;
}) {
  return (
    <div
      onClick={onClick}
      className={twMerge(
        "text-sm font-medium text-v2-content-primary px-3 py-2 cursor-pointer flex items-center justify-between",
        showIcon ? "hover:bg-v2-surface-thertiary" : ""
      )}
    >
      {children}
      {showIcon && (
        <Icon icon="solar:alt-arrow-left-outline" width={16} height={16} className="text-v2-content-subtle" />
      )}
    </div>
  );
}

export default InnerCategoryTitleItem;
