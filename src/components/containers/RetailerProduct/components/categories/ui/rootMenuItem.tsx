import React, { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

function RootMenuItem({
  children,
  onClick,
  isActive,
  onMouseEnter,
  onMouseLeave
}: {
  children: ReactNode;
  isActive?: boolean;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
  onMouseEnter?: React.MouseEventHandler<HTMLDivElement>;
  onMouseLeave?: React.MouseEventHandler<HTMLDivElement>;
}) {
  return (
    <div
      aria-describedby="category-popover"
      className={twMerge(
        "p-3 max-w-44 truncate text-sm font-medium cursor-pointer text-v2-content-secondary flex items-center gap-2 relative before:content-none before:absolute before:bottom-0 before:h-0.5 before:bg-v2-content-on-action-2 before:left-0 before:right-0 before:rounded-t-md",
        isActive
          ? "text-v2-content-on-action-2 before:content-['']"
          : "hover:text-v2-content-on-action-2 hover:before:content-['']"
      )}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {children}
    </div>
  );
}

export default RootMenuItem;
