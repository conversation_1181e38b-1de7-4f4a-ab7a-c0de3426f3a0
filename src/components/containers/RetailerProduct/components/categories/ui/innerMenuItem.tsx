import { Icon } from "@iconify/react";
import React, { ReactNode } from "react";

function InnerMenuItem({
  children,
  onClick,
  showIcon = true
}: {
  children: ReactNode;
  onClick?: React.MouseEventHandler<HTMLDivElement> | undefined;
  showIcon?: boolean;
}) {
  return (
    <div
      onClick={onClick}
      className="px-3 py-2 text-v2-content-primary hover:bg-v2-surface-thertiary text-[13px] flex items-center justify-between cursor-pointer"
    >
      {children}
      {showIcon && (
        <Icon icon="solar:alt-arrow-left-outline" width={16} height={16} className="text-v2-content-subtle" />
      )}
    </div>
  );
}

export default InnerMenuItem;
