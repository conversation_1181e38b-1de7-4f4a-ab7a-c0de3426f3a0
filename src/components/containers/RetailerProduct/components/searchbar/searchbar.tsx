import Input from "@/components/ui/inputs/Input";
import { Icon } from "@iconify/react";
import React, { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { useProductFilters } from "../Filters/useProductFilters";
import { useMediaQuery } from "@mui/system";
import { Theme } from "@mui/material";
import Button from "@/components/ui/Button";
import { debounce } from "lodash";
import { twMerge } from "tailwind-merge";
import Link from "next/link";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { handleSetFilter } from "@/utils/helpers";

function Searchbar() {
  const { t } = useTranslation();
  const { filters, setFilters } = useProductFilters();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const [inputValue, setInputValue] = useState(filters?.title || "");
  const makePath = useRoleBasePath();

  const handleOnChange = useCallback(
    debounce((value: string) => {
      // setFilters({ title: value, page: 1 });
      handleSetFilter({ key: "title", value: value, setFilters });
    }, 2000),
    []
  );

  if (isMobile) {
    return (
      <div className={twMerge("flex-1 flex items-center gap-2", isMobile ? "" : "")}>
        <Link href={`${makePath(routes.product)}`}>
          <div className="flex items-center gap-2">
            <Icon icon="solar:shop-bold-duotone" width={24} height={24} className="text-v2-content-on-action-2" />
            <div className="text-v2-content-primary font-semibold text-[13px]">{t("retailerProduct.marketplace")}</div>
          </div>
        </Link>

        <Input
          startAdornment={
            <div className="border-l border-v2-border-primary pl-1.5">
              <Icon
                icon="solar:magnifer-linear"
                width="1.1rem"
                color="#ADADAD"
                height="1.1rem"
                className="size-4 text-v2-content-tertiary"
              />
            </div>
          }
          value={inputValue}
          className="max-h-10"
          rootClassName="shrink-0 flex-1 h-12"
          placeholder={`${t("retailerProduct.searchQuery")}`}
          onChange={e => {
            handleOnChange(e.target.value);
            setInputValue(e.target.value);
          }}
          requiredStar={false}
          variant="filled"
          inputSize="lg"
        />
      </div>
    );
  }

  return (
    <div className="flex gap-4 items-stretch">
      <Input
        startAdornment={
          <div className="border-l border-v2-border-primary pl-1.5">
            <Icon
              icon="solar:magnifer-linear"
              width="1.1rem"
              color="#ADADAD"
              height="1.1rem"
              className="size-4 text-v2-content-tertiary"
            />
          </div>
        }
        value={inputValue}
        className="max-h-10"
        rootClassName="shrink-0 flex-1 h-12"
        placeholder={`${t("retailerProduct.searchQuery")}`}
        onChange={e => {
          handleOnChange(e.target.value);
          setInputValue(e.target.value);
        }}
        requiredStar={false}
        variant="filled"
        inputSize="lg"
      />
      <Button
        variant="secondaryGray"
        size="xl"
        className="text-v2-content-on-action-hover-2 !h-12"
        startAdornment={<Icon icon="solar:fire-minimalistic-outline" className="size-5" />}
      >
        {t("retailerProduct.topSells")}
      </Button>
      <Button
        variant="secondaryGray"
        size="lg"
        className="text-v2-content-on-action-hover-2 !h-12"
        startAdornment={<Icon icon="solar:sale-outline" className="size-5" />}
      >
        {t("retailerProduct.mostProfits")}
      </Button>
    </div>
  );
}

export default Searchbar;
