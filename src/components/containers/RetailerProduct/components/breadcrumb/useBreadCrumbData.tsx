import { generateBreadcrumb } from "@/components/containers/SelectCategoryModal/utils";
import { useGetMetaCategoriesQuery } from "@/store/apps/meta";
import { useMemo } from "react";
import { useProductFilters } from "../Filters/useProductFilters";

function useBreadCrumbData() {
  const { data: categories } = useGetMetaCategoriesQuery();
  const { filters } = useProductFilters();

  const breadCrumbData = useMemo(
    () => (filters?.category && categories?.data ? generateBreadcrumb(categories?.data, filters?.category) : undefined),
    [filters?.category, JSON.stringify(categories?.data)]
  );

  return breadCrumbData;
}

export default useBreadCrumbData;
