import { Grid, Skeleton } from "@mui/material";
import React from "react";

function RetailerProductsLoading({ count = 6 }: { count?: number }) {
  return (
    <Grid container spacing={0.5} minHeight={300}>
      {new Array(count).fill(0).map((_, index) => (
        <Grid
          item
          key={index}
          xs={12}
          sm={6}
          md={4}
          lg={3}
          position={"relative"}
          boxSizing="border-box"
          alignItems="stretch"
          minHeight={363}
        >
          <Skeleton key={index} variant="rectangular" width={"100%"} height={"100%"} />
        </Grid>
      ))}
    </Grid>
  );
}

export default RetailerProductsLoading;
