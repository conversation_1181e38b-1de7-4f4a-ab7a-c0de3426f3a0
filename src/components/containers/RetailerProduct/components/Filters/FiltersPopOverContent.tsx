import { Icon } from "@iconify/react";
import { FormControl } from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import Close from "@mui/icons-material/Close";
import LocationsSelect from "@/components/ui/CustomCountrySelect/LocationsSelect";
import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { TProductFilters } from "./useProductFilters";

export type FiltersPopOverContentProps = {
  filterState?: TProductFilters;
  setFilterState: (k: keyof TProductFilters, v: any) => void;
  onResetAll: () => void;
  closePopOver: () => void;
};

function FiltersPopOverContent({ filterState, setFilterState, onResetAll, closePopOver }: FiltersPopOverContentProps) {
  const { t } = useTranslation();
  const [shippingFrom, setShippingFrom] = useState(filterState?.shipping_from || undefined);
  const [shippingTo, setShippingTo] = useState(filterState?.shipping_to || undefined);
  const [shippingTime, setShippingTime] = useState<"1-3" | "4-7" | "8-12" | "14-0" | undefined>(
    (filterState?.shipping_time || undefined) as any
  );

  const hasFilterApplied = filterState?.shipping_from || filterState?.shipping_to || filterState?.shipping_time;

  const handleSubmit = () => {
    if (shippingFrom) {
      setFilterState("shipping_from", shippingFrom);
    }
    if (shippingTo) {
      setFilterState("shipping_to", shippingTo);
    }
    if (shippingTime) {
      setFilterState("shipping_time", shippingTime);
    }

    closePopOver();
  };

  const handleCancel = () => {
    setShippingFrom(undefined);
    setShippingTo(undefined);
    setShippingTime(undefined);
    closePopOver();
  };

  const handleClearFilters = () => {
    handleCancel();
    onResetAll();
  };

  const handleChangeRadio = (event: React.ChangeEvent<any>) => {
    setShippingTime(event?.target?.value);
  };

  return (
    <div className="flex flex-col gap-4 w-[740px]">
      <div className="flex justify-between">
        <div className="text-gray-500 text-sm font-medium">{t("retailerProduct.filters.filters")}</div>
        <div>
          <Close className="w-5 h-5 text-gray-500 cursor-pointer" onClick={closePopOver} />
        </div>
      </div>

      <div className="border border-gray-50 rounded-lg p-4 flex flex-col gap-4">
        <div className="text-base font-bold text-gray-999">{t("retailerProduct.filters.shipping")}</div>
        <div className="flex justify-between gap-4">
          <div className="flex-1 flex flex-col">
            <LocationsSelect
              multiple={false}
              label={t("retailerProduct.filters.shippingFrom")}
              // handleBlur={handleBlur}
              value={shippingFrom || undefined}
              onChange={value => {
                setShippingFrom(value as string);
              }}
              size="small"
              placeholder={t("retailerProduct.filters.search")}
              className="-mt-3"
              inputProps={{
                inputProps: {
                  className: "!py-[2.5px]"
                },
                InputProps: {
                  endAdornment: <Icon icon="solar:magnifer-outline" width={24} height={24} className="text-gray-400" />
                }
              }}
              // name={`policies[${index}].shippingTo`}
              // error={touched?.shippingTo && Boolean(errors?.shippingTo)}
              // helperText={touched?.shippingTo && errors?.shippingTo}
            />
          </div>
          <div className="h-auto w-px bg-gray-50 shrink-0" />
          <div className="flex-1">
            <LocationsSelect
              multiple={false}
              label={t("retailerProduct.filters.shippingTo")}
              // handleBlur={handleBlur}
              value={shippingTo || undefined}
              onChange={value => {
                setShippingTo(value as string);
              }}
              size="small"
              placeholder={t("retailerProduct.filters.search")}
              className="-mt-3"
              inputProps={{
                inputProps: {
                  className: "!py-[2.5px]"
                },
                InputProps: {
                  endAdornment: <Icon icon="solar:magnifer-outline" width={24} height={24} className="text-gray-400" />
                }
              }}
              // name={`policies[${index}].shippingTo`}
              // error={touched?.shippingTo && Boolean(errors?.shippingTo)}
              // helperText={touched?.shippingTo && errors?.shippingTo}
            />
          </div>
          <div className="flex-1 flex flex-col gap-0.5">
            <div className="text-gray-600 text-sm font-normal">{t("retailerProduct.filters.shippingTime")}</div>
            <div className="text-gray-500 text-xs font-normal">{t("retailerProduct.filters.shippingTimeDesc")}</div>
            <FormControl id="shippingTime">
              <div className="flex flex-col">
                <CustomRadio
                  checked={shippingTime === "1-3"}
                  onChange={handleChangeRadio}
                  value="1-3"
                  label={t("retailerProduct.filters.1_3_days")}
                  name="sex"
                  className="font-medium"
                />
                <CustomRadio
                  checked={shippingTime === "4-7"}
                  onChange={handleChangeRadio}
                  value="4-7"
                  label={t("retailerProduct.filters.4_7_days")}
                  name="sex"
                  className="font-medium"
                />
                <CustomRadio
                  checked={shippingTime === "8-12"}
                  onChange={handleChangeRadio}
                  value="8-12"
                  label={t("retailerProduct.filters.8_12_days")}
                  name="sex"
                  className="font-medium"
                />
                <CustomRadio
                  checked={shippingTime === "14-0"}
                  onChange={handleChangeRadio}
                  value="14-0"
                  label={t("retailerProduct.filters.plus_14_days")}
                  name="sex"
                  className="font-medium"
                />
              </div>
            </FormControl>
          </div>
        </div>
      </div>

      {/* -------------------------------------------------------------------------- */
      /*                                  supplier                                  */
      /* -------------------------------------------------------------------------- */}
      {/* <div className="border border-gray-50 rounded-lg p-4 flex flex-col gap-4">
        <div className="text-base font-bold text-gray-999">{t("retailerProduct.filters.supplier")}</div>
        <div className="flex justify-between gap-4">
          <div className="flex-1">
            <CustomAutocomplete
              multiple={false}
              onChange={value => {}}
              size="small"
              placeholder={t("retailerProduct.filters.supplier")}
              options={[]}
            />
          </div>
          <div className="h-auto w-px bg-gray-50 shrink-0" />
          <div className="flex-1 flex flex-col gap-2">
            <CustomCheckbox className="" label={t("retailerProduct.filters.topSuppliers")} />
            <div className="text-gray-500 text-xs font-normal">{t("retailerProduct.filters.topSuppliersDesc")}</div>
          </div>
        </div>
      </div> */}

      <div className="flex items-center gap-2.5 justify-end">
        {hasFilterApplied ? (
          <CustomButton size="small" color="secondary" onClick={handleClearFilters}>
            {t("removeFilters")}
          </CustomButton>
        ) : (
          <CustomButton size="small" color="secondary" onClick={handleCancel}>
            {t("retailerProduct.filters.cancel")}
          </CustomButton>
        )}
        <CustomButton size="small" onClick={handleSubmit}>
          {t("retailerProduct.filters.applyChanges")}
        </CustomButton>
      </div>
    </div>
  );
}

export default FiltersPopOverContent;
