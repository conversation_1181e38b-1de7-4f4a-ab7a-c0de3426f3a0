import { useGetMetaCategoriesQuery } from "@/store/apps/meta";
import { TMetaCategoriesData } from "@/store/apps/meta/types";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import Image from "next/image";
import React, { ReactNode, useState } from "react";
import { twMerge } from "tailwind-merge";
import { TProductFilters } from "./useProductFilters";

function RootMenuItem({
  id,
  children,
  icon,
  onHover,
  hasChildren,
  isActive,
  onClick
}: {
  id: string;
  children: ReactNode;
  icon?: string;
  onHover?: (id: string) => void;
  onClick?: (id: string) => void;
  hasChildren?: boolean;
  isActive?: boolean;
}) {
  return (
    <div
      className={twMerge(
        "p-2 text-gray-999 text-sm font-medium hover:bg-purple-50 cursor-pointer rounded-md flex items-center justify-between gap-1.5",
        isActive ? "bg-purple-50" : ""
      )}
      onMouseEnter={() => onHover?.(id)}
      onClick={() => onClick?.(id)}
    >
      <div className="shrink-0 flex-1 flex items-center gap-1.5">
        {icon && (
          <div className="shrink-0 relative h-[18px] w-[18px]">
            <Image src={icon} fill alt={icon} />
          </div>
        )}
        <div className="shrink-0">{children}</div>
      </div>
      {hasChildren && <Icon icon="solar:alt-arrow-left-outline" width={18} height={18} className="text-gray-400" />}
    </div>
  );
}

function Level2MenuItem({
  item,
  children,
  onClick,
  isActive
}: {
  item: TMetaCategoriesData;
  children: ReactNode;
  onClick: (id: string) => void;
  isActive?: boolean;
}) {
  return (
    <>
      <div
        className={twMerge(
          "w-[calc(_20%_-_0.875rem)] text-sm font-semibold text-gray-999 cursor-pointer hover:text-purple-500 pb-3 border-b border-gray-40",
          isActive ? "text-purple-500 cursor-default" : ""
        )}
        onClick={() => (!isActive ? onClick(item?.id) : undefined)}
      >
        {children}
      </div>

      {item?.subCategories?.map(subItem => (
        <SingleMenuItem key={subItem?.id} id={subItem?.id} onClick={id => onClick(id)} isActive={isActive}>
          {subItem?.name}
        </SingleMenuItem>
      ))}
    </>
  );
}

function SingleMenuItem({
  id,
  children,
  onClick,
  isActive
}: {
  id: string;
  children: ReactNode;
  onClick: (id: string) => void;
  isActive?: boolean;
}) {
  return (
    <div
      className={twMerge(
        "w-[calc(_20%_-_0.875rem)] text-xs font-medium text-gray-600 cursor-pointer hover:text-purple-500",
        isActive ? "text-purple-500 cursor-default" : ""
      )}
      onClick={() => (!isActive ? onClick(id) : undefined)}
    >
      {children}
    </div>
  );
}

function Categories({
  closePopOver,
  setFilterState,
  defaultValue
}: {
  closePopOver: () => void;
  setFilterState: (k: keyof TProductFilters, v: any) => void;
  defaultValue?: string;
}) {
  const { data, isLoading } = useGetMetaCategoriesQuery();
  const rootCategories = data?.data?.filter(a => a.parentId === "00000000-0000-0000-0000-000000000000");
  const [hoverItem, setHoverItem] = useState<string | undefined>(
    rootCategories?.find(a => a.id === defaultValue || a.subCategories?.some(b => b.id === defaultValue))?.id ||
      undefined
  );
  const hoveredCategory = rootCategories?.find(a => a.id === hoverItem) || rootCategories?.[0];

  if (isLoading) {
    return (
      <div className="items-center justify-center flex w-[90vw] xl:w-[50vw] px-16 py-56">
        <CircularProgress />
      </div>
    );
  }

  const handleSelectCategory = (id: string) => {
    closePopOver();
    setFilterState("category", id);
  };

  return (
    <div className="flex gap-[22px] w-[90vw] xl:w-[70vw] xl:max-w-7xl">
      <div className="shrink-0 flex flex-col gap-1 max-h-[80vh] overflow-x-hidden overflow-y-auto">
        {rootCategories?.map(item => (
          <RootMenuItem
            key={item?.id}
            id={item?.id}
            icon={item?.icon}
            isActive={item?.id === hoverItem}
            onHover={id => setHoverItem(id)}
            onClick={id => handleSelectCategory(id)}
            hasChildren={item?.subCategories?.length > 0}
          >
            {item?.name}
          </RootMenuItem>
        ))}
      </div>

      <div className="flex-1 flex flex-col gap-3.5 pt-2.5 flex-wrap max-h-[75vh] overflow-y-auto overflow-x-hidden max-w-full">
        {hoveredCategory?.subCategories?.map(item =>
          item?.subCategories?.length ? (
            <Level2MenuItem
              key={item?.id}
              item={item}
              onClick={id => handleSelectCategory(id)}
              isActive={item?.id === hoverItem || item?.id === defaultValue}
            >
              {item?.name}
            </Level2MenuItem>
          ) : (
            <SingleMenuItem
              key={item?.id}
              id={item?.id}
              onClick={id => handleSelectCategory(id)}
              isActive={item?.id === hoverItem || item?.id === defaultValue}
            >
              {item?.name}
            </SingleMenuItem>
          )
        )}
      </div>
    </div>
  );
}

export default Categories;
