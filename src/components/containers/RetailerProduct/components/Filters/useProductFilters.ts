import {
  inferParserType,
  parseAsArrayOf,
  parseAsBoolean,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
  useQueryStates
} from "nuqs";

const productFiltersParsers = {
  title: parseAsString,
  page: parseAsInteger.withDefault(1),
  pageSize: parseAsInteger.withDefault(10),
  tags: parseAsArrayOf(parseAsString),
  sku: parseAsString,
  status: parseAsString,
  created_from: parseAsString,
  created_to: parseAsString,
  updated_from: parseAsString,
  updated_to: parseAsString,
  shipping_from: parseAsString,
  shipping_to: parseAsString,
  shipping_time: parseAsString,
  category: parseAsString,
  top_suppliers: parseAsBoolean,
  premium: parseAsBoolean,
  winning: parseAsBoolean,
  created_at: parseAsString,
  updated_at: parseAsString,
  supplier: parseAsArrayOf(parseAsString),
  viewType: parseAsStringEnum(["products", "suppliers"]).withDefault("products")
};

export const useProductFilters = () => {
  const [filters, setFilters] = useQueryStates(productFiltersParsers, { history: "push", shallow: false });

  const { page, pageSize, created_at, updated_at, viewType, ...restFilters } = filters || {};

  const handleResetAll = () => {
    const keys = !!filters ? Object.keys(filters) : [];

    keys?.forEach(item => setFilters({ [item]: null }));
  };

  return {
    filters: restFilters,
    setFilters,
    pagination: { page, pageSize },
    sorts: { created_at, updated_at },
    viewType,
    resetAll: handleResetAll
  };
};

export type TProductFilters = inferParserType<typeof productFiltersParsers>;
