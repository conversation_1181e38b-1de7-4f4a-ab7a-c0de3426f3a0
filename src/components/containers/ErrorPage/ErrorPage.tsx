import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { routes } from "@/constants/routes";
import { Icon } from "@iconify/react";
import i18next from "i18next";
import Image from "next/image";
import React from "react";

function ErrorPage() {
  return (
    <div className="bg-cards h-screen w-full flex items-center justify-center">
      <div className="flex flex-col items-center">
        <Image src="/images/illustrations/somethingwentwrong.png" alt="somethingwentwrong" width={200} height={200} />

        <h5 className="mt-8 text-h5-bold text-gray-999">{i18next.t("somethingwentrong.title")}</h5>
        <p className="mt-2 text-body3-medium text-gray-600">{i18next.t("somethingwentrong.subtitle")}</p>
        <CustomButton
          color="secondary"
          className=" mt-4"
          onClick={() => {
            window.location.assign(routes.home);
          }}
          endIcon={<Icon icon="fluent:chevron-left-24-regular" className="size-5 !text-purple-500" />}
        >
          <span className="!text-purple-500">{i18next.t("somethingwentrong.button")}</span>
        </CustomButton>
      </div>
    </div>
  );
}

export default ErrorPage;
