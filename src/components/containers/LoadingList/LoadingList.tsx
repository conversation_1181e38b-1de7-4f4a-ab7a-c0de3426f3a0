import { Skeleton, TableBody, TableCell, TableRow } from "@mui/material";
import React from "react";

const LoadingList = ({ rowCount, columnCount }: { rowCount: number; columnCount: number | Array<number> }) => {
  return (
    <TableBody>
      {new Array(rowCount).fill(0).map((_, rowIndex) => (
        <TableRow key={rowIndex}>
          {(Array.isArray(columnCount) ? columnCount : new Array(columnCount).fill(0)).map((column, columnIndex) => (
            <TableCell key={columnIndex} width={column || 200} height={70}>
              <Skeleton variant="text" width={column || 50 + Math.random() * 45 + "%"} height={18} />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </TableBody>
  );
};

export default LoadingList;
