import { Box, Stack, Typography } from "@mui/material";
import Image from "next/image";
import { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

interface IOnBoardingAlertProps {
  status: "rejected" | "inReview" | "contract";
  title: string;
  subtile: string;
  endAdornment?: ReactNode;
  imgSrc: string;
  titleClassName?: string;
  subtitleClassName?: string;
}
3;
function OnBoardingAlert({
  status,
  imgSrc,
  subtile,
  title,
  endAdornment,
  subtitleClassName,
  titleClassName
}: IOnBoardingAlertProps) {
  const variant = {
    rejected: {
      borderColor: "#EE6658",
      bgColor: "rgb(var(--color-error-50))",
      color: ""
    },
    inReview: {
      borderColor: "#54D3F2",
      bgColor: "rgb(var(--color-cyan-50))",
      color: ""
    },
    contract: {
      borderColor: "#133A9A",
      bgColor: "rgb(var(--info-info-lightest))",
      color: ""
    }
  };

  return (
    <Box
      className="p-4 rounded-lg mb-4"
      sx={{
        border: `1px solid ${variant[status].borderColor}`,
        background: variant[status].bgColor
      }}
    >
      <Stack flexDirection="row" alignItems="center" className={status === "rejected" ? "items-start" : ""}>
        <Image src={imgSrc} alt="alert" width={40} height={40} />

        <Stack
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          marginInlineStart={1}
          flexWrap="wrap"
          width="100%"
        >
          <Stack gap={0.5}>
            <Typography className={twMerge("text-sm font-bold text-gray-999", titleClassName)}>{title}</Typography>
            <Typography
              className={twMerge(
                "font-normal text-gray-400 dark:text-gray-600 text-xs md:text-[13px]",
                subtitleClassName
              )}
            >
              {subtile}
            </Typography>
          </Stack>

          {!!endAdornment && endAdornment}
        </Stack>
      </Stack>
    </Box>
  );
}

export default OnBoardingAlert;
