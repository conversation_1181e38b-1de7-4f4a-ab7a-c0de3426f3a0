"use client";
import { ReactNode } from "react";
import i18n from "@/utils/i18n";
import useRole from "@/utils/hooks/useRole";
import useModal from "@/utils/hooks/useModal";
import useSessionStore from "@/store/zustand/sessionStore";

export type Role = "SUPPLIER" | "RETAILER" | "ADMIN";
interface AclWrapperPropsType {
  for: Role | Array<Role>;
  children: ReactNode;
}

export function useAcl() {
  const role = useRole();
  const { showModal, hideModal } = useModal();
  const canRun = (r: Role | Array<Role>, func: Function) => {
    if (role && ((role && Array.isArray(r) && r.includes(role)) || role === r)) {
      return func;
    }
    return () => {
      showModal({
        icon: "/images/svgs/danger.svg",
        title: i18n.t("errorTitle"),
        subTitle: i18n.t("unauthorized"),
        actions: [
          {
            label: i18n.t("confirm"),
            onClick: () => hideModal()
          }
        ]
      });
    };
  };
  return canRun;
}

export default function AclWrapper(props: AclWrapperPropsType) {
  const { user_type } = useSessionStore();

  const { for: accessableFor, children } = props;

  const userRole = user_type;
  if (
    // status === "authenticated" &&
    userRole &&
    ((Array.isArray(accessableFor) && accessableFor.includes(userRole)) || accessableFor === userRole)
  ) {
    return children;
  }

  return null;
}
