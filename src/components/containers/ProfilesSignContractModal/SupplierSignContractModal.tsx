import CustomButton from "@/components/ui/CustomButton/CustomButton";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import { Close } from "@mui/icons-material";
import { CircularProgress, IconButton } from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useSupplierSignContractMutation } from "@/store/apps/supplier";
import { generateContractHtml } from "./supplierContent";
import useLanguage from "@/utils/hooks/useLanguage";

interface ISupplierSignContractModalProps {
  address?: string;
  fullName?: string;
  nationalId?: string;
  isLegal?: boolean;
  economicCode?: string;
  registrationNumber?: string;
  companyName?: string;
  iban?: string;

  close: () => void;
  onSuccess: () => void;
}

function SupplierSignContractModal({
  close,
  onSuccess,
  fullName,
  nationalId,
  isLegal,
  companyName,
  economicCode,
  iban,
  registrationNumber
}: ISupplierSignContractModalProps) {
  const { t } = useTranslation();
  const [checked, setChecked] = useState(false);
  const [{ renderDate }] = useLanguage();

  const [mutate, { isLoading, isSuccess }] = useSupplierSignContractMutation();

  const nowDate = new Date().toISOString();

  const contractData = {
    companyAName: "شرکت نمونه الف",
    companyANationalId: "1234567890",
    companyAManagerName: "آقای مدیر عامل",
    companyAMemberName: "خانم عضو هیئت مدیره",
    companyAEconomicNumber: "**********",
    companyBManagerName: "آقای مدیر عامل ب",
    companyBChairmanName: "خانم رئیس هیئت مدیره ب",
    contractDate: "۱۴۰۳/۰۳/۱۱",
    contractDurationMonths: 12,
    commissionFeeFormulaP: "قیمت آمر",
    commissionFeeFormulaS: "ارزش افزوده",
    commissionFeeFormulaR: "قیمت فروش - هزینه ارسال و ارزش افزوده",
    companyABankAccountSheba: "**************************"
  };

  const contractHtml = isLegal
    ? generateContractHtml({
        contractDate: renderDate(nowDate, "YYYY/MM/DD"),
        contractDurationMonths: 12,
        bankAccountSheba: iban,
        economicCode: economicCode,
        id: registrationNumber,
        name: companyName
      })
    : generateContractHtml({
        contractDate: renderDate(nowDate, "YYYY/MM/DD"),
        contractDurationMonths: 12,
        bankAccountSheba: iban,
        economicCode: "",
        id: nationalId,
        name: fullName
      });

  const handleClickConfirm = () => {
    mutate().then(() => {
      onSuccess();
    });
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-999 font-medium">{t("supplier.profile.signContractTitle")}</div>
        <IconButton onClick={close} className="block p-0 w-5 h-5">
          <Close className="w-5 h-5" />
        </IconButton>
      </div>
      <div className="max-h-[50vh] overflow-auto">
        <div
          dangerouslySetInnerHTML={{
            __html: contractHtml
          }}
          className="text-gray-999 text-[13px]"
        />
      </div>
      <div>
        <CustomCheckbox
          label={t("supplier.profile.signContractCheckbox")}
          checked={checked}
          onChange={e => setChecked(e.target.checked)}
        />
      </div>

      <div className="flex justify-between items-center">
        <CustomButton color="secondary" onClick={close}>
          {t("supplier.profile.signContractCancelBtn")}
        </CustomButton>
        <CustomButton disabled={!checked || isLoading || isSuccess} onClick={handleClickConfirm}>
          {isLoading && <CircularProgress color="info" size={26} />} {t("supplier.profile.signContractApproveBtn")}
        </CustomButton>
      </div>
    </div>
  );
}

export default SupplierSignContractModal;
