import Input from "@/components/ui/inputs/Input";
import { TMetaCategoriesData } from "@/store/apps/meta/types";
import useDebounce from "@/utils/hooks/useDebounce";
import { Icon } from "@iconify/react";
import Image from "next/image";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export const generateBreadcrumb = (
  data: TMetaCategoriesData[],
  selectedCategoryId: string
): { id: string; name: string }[] => {
  if (!selectedCategoryId) {
    return data;
  }

  // should recursively find the category and its parents for n levels
  const findCategory = (categories: TMetaCategoriesData[], selectedCategoryId: string): any => {
    for (let i = 0; i < categories.length; i++) {
      if (categories[i]?.id === selectedCategoryId) {
        return [
          {
            id: categories[i]?.id,
            name: categories[i]?.name
          }
        ];
      } else if (categories[i].subCategories) {
        const found = findCategory(categories[i]?.subCategories, selectedCategoryId);
        if (found) {
          return [
            {
              id: categories[i]?.id,
              name: categories[i]?.name
            },
            ...found
          ];
        }
      }
    }
    return null;
  };

  const foundCategory = findCategory(data, selectedCategoryId);
  return foundCategory;
};

export const getCategoryAndItsChildrenById = (
  data: TMetaCategoriesData[],
  selectedCategoryId?: string | null
): TMetaCategoriesData[] | undefined => {
  if (!selectedCategoryId) {
    return data;
  }

  // recursive function to get the category by id
  const getCategoryById = (
    data: TMetaCategoriesData[],
    selectedCategoryId: string
  ): TMetaCategoriesData[] | undefined => {
    for (let i = 0; i < data?.length; i++) {
      if (data[i].id === selectedCategoryId) {
        return [data[i]];
      }
      if (data[i].subCategories) {
        const result = getCategoryById(data[i].subCategories, selectedCategoryId);
        if (result) {
          return result;
        }
      }
    }
  };

  // get the category by id
  const category = getCategoryById(data, selectedCategoryId);

  // return the category and its children
  return category;
};

export const filterCategoryByQStr = (
  data: TMetaCategoriesData[],
  filterQstr: string
): TMetaCategoriesData[] | undefined => {
  // should return a flat list of categories that match the filter query string in their name, recursively including their subcategories

  // recursive function to filter the categories
  const filterCategories = (data: TMetaCategoriesData[], filterQstr: string): TMetaCategoriesData[] | undefined => {
    let result: TMetaCategoriesData[] | undefined = [];
    for (let i = 0; i < data?.length; i++) {
      if (data[i]?.name?.toLowerCase()?.includes(filterQstr?.toLowerCase())) {
        result.push(data[i]);
      }
      if (data[i].subCategories) {
        const subCategories = filterCategories(data[i].subCategories, filterQstr);
        if (subCategories?.length) {
          result = result.concat(subCategories);
        }
      }
    }
    return result;
  };

  // filter the categories
  const filteredCategories = filterCategories(data, filterQstr);

  // return the filtered categories
  return filteredCategories;
};

export const RenderBreadCrumb = ({
  data,
  handleOnClickCategory
}: {
  data:
    | {
        id: string;
        name: string;
      }[]
    | undefined;
  handleOnClickCategory: (categoryId: string | null) => void;
}) => {
  const { t } = useTranslation();

  return (
    <div className="text-gray-500 text-xs font-medium">
      <span className="cursor-pointer" onClick={() => handleOnClickCategory(null)}>
        {t("product.categorySelect.breadCrumbAll")} /{" "}
      </span>
      {data?.map((item, index) => (
        <>
          <span className="cursor-pointer" onClick={() => handleOnClickCategory(item.id)}>
            {item?.name}
          </span>
          {index + 1 !== data?.length && <span> / </span>}
        </>
      ))}
    </div>
  );
};

const RootCategoryItem = ({ title, onClick }: { title: string; onClick?: () => void }) => {
  return (
    <div
      className="border-b border-gray-50 mb-4 pb-4 flex items-center justify-between cursor-pointer"
      onClick={onClick}
    >
      <div className="text-sm font-medium text-gray-999">{title}</div>
      <Icon icon="solar:alt-arrow-left-outline" className="size-4 text-gray-999" />
    </div>
  );
};

const SelectableCategoryItem = ({
  title,
  onClick,
  isSelected = false
}: {
  title: string;
  onClick?: () => void;
  isSelected?: boolean;
}) => {
  return (
    <div
      className="border-b border-gray-50 mb-4 pb-4 gap-1 flex items-center justify-start cursor-pointer"
      onClick={onClick}
    >
      <div>
        {isSelected ? (
          <Image src="/images/svgs/user-checked.svg" alt="realUser" width={20} height={20} />
        ) : (
          <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={20} height={20} />
        )}
      </div>
      <div className="text-sm font-medium text-gray-999">{title}</div>
    </div>
  );
};

export const RenderRootCategory = ({
  data,
  handleOnClickCategory
}: {
  data: TMetaCategoriesData[];
  handleOnClickCategory: (categoryId: string) => void;
}) => {
  return (
    <div className="grid grid-cols-2 gap-x-4">
      {data?.map((item, index) => (
        <RootCategoryItem key={index} title={item?.name} onClick={() => handleOnClickCategory(item?.id)} />
      ))}
    </div>
  );
};

export const RenderSubCategory = ({
  data,
  selectedCategoryValue,
  handleOnClickCategory,
  handleSelectCategory
}: {
  data: TMetaCategoriesData[];
  selectedCategoryValue?: string;
  handleOnClickCategory: (categoryId: string) => void;
  handleSelectCategory: (categoryId: string) => void;
}) => {
  const rootCategory = data?.[0];
  return (
    <div className="grid grid-cols-2 gap-x-4">
      <SelectableCategoryItem
        title={rootCategory?.name}
        onClick={() => handleSelectCategory(rootCategory?.id)}
        isSelected={selectedCategoryValue === rootCategory?.id}
      />
      {rootCategory?.subCategories?.map((item, index) =>
        !item?.subCategories?.length ? (
          <SelectableCategoryItem
            key={item?.id}
            title={item?.name}
            onClick={() => handleSelectCategory(item?.id)}
            isSelected={selectedCategoryValue === item?.id}
          />
        ) : (
          <RootCategoryItem key={index} title={item?.name} onClick={() => handleOnClickCategory(item?.id)} />
        )
      )}
    </div>
  );
};

export const RenderCategory = ({
  data,
  handleSelectCategory,
  selectedCategoryValue
}: {
  data: TMetaCategoriesData[];
  handleSelectCategory: (categoryId: string) => void;
  selectedCategoryValue?: string;
}) => {
  return (
    <div className="grid grid-cols-2 gap-x-4">
      {data?.map((item, index) => (
        <SelectableCategoryItem
          key={index}
          title={item?.name}
          onClick={() => handleSelectCategory(item?.id)}
          isSelected={selectedCategoryValue === item?.id}
        />
      ))}
    </div>
  );
};

export const RenderSearchInputDebounced = ({ onChange }: { onChange: (v: string) => void }) => {
  const { t } = useTranslation();
  const [filterQstr, setFilterQstr] = useState<string | null>(null);

  const handleDebouncedOnSearch = useDebounce((v: string) => {
    onChange(v);
  }, 500);

  return (
    <Input
      id="title"
      name="title"
      placeholder={t("product.categorySelect.search")}
      value={filterQstr || undefined}
      onChange={e => {
        handleDebouncedOnSearch(e.target?.value);
        setFilterQstr(e.target?.value);
      }}
    />
  );
};
