import useModal from "@/utils/hooks/useModal";
import React from "react";
import SelectCategoryModalContent from "./ModalContent";
import { Icon } from "@iconify/react";
import { useGetMetaCategoriesQuery } from "@/store/apps/meta";
import { CircularProgress } from "@mui/material";
import { generateBreadcrumb } from "./utils";
import { twMerge } from "tailwind-merge";

function SelectCategoryWithModal({
  initialValue,
  placeholder,
  onChange,
  error
}: {
  initialValue?: string;
  placeholder?: string;
  onChange: (categoryId: string) => void;
  error?: boolean;
}) {
  const { showModal } = useModal();
  const { data, isLoading } = useGetMetaCategoriesQuery();

  const handleOpenModal = () => {
    showModal({
      body: <SelectCategoryModalContent initialValue={initialValue} onChange={onChange} />,
      width: 685,
      modalProps: { showCloseIcon: false }
    });
  };

  const valueTree = data?.data && initialValue ? generateBreadcrumb(data?.data, initialValue) : undefined;
  const valueStr =
    data?.data && initialValue
      ? valueTree?.map((item, index) => `${item?.name}${index + 1 !== valueTree?.length ? " / " : ""} `)
      : undefined;

  return (
    <div
      onClick={handleOpenModal}
      className={twMerge(
        "py-[13px] px-3 rounded-lg border-[1.5px] flex items-center justify-between cursor-pointer",
        error ? "border-v2-content-on-error-2" : "border-v2-border-primary"
      )}
    >
      <div className="text-v2-content-primary text-sm font-normal">
        {isLoading ? <CircularProgress size={16} className="mt-0.5 -mb-0.5" /> : valueStr ? valueStr : placeholder}
      </div>
      <Icon icon="solar:alt-arrow-down-outline" className="size-4 text-v2-content-subtle" />
    </div>
  );
}

export default SelectCategoryWithModal;
