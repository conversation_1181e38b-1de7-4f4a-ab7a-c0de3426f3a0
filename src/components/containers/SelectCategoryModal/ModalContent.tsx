import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { useGetMetaCategoriesQuery } from "@/store/apps/meta";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import React, { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  filterCategoryByQStr,
  generateBreadcrumb,
  getCategoryAndItsChildrenById,
  RenderBreadCrumb,
  RenderCategory,
  RenderRootCategory,
  RenderSearchInputDebounced,
  RenderSubCategory
} from "./utils";
import Button from "@/components/ui/Button";

function SelectCategoryModalContent({
  initialValue,
  onChange
}: {
  initialValue?: string;
  onChange: (categoryId: string) => void;
}) {
  const { t } = useTranslation();
  const { hideModal } = useModal();
  const { data, isLoading } = useGetMetaCategoriesQuery();

  const [selectedCategoryId, setSelectedCategoryId] = useState<string | undefined>(initialValue);
  const [selectedTreeCategoryId, setSelectedTreeCategory] = useState<string | null>(null);
  const [filterQstr, setFilterQstr] = useState<string | null>(null);

  const filteredByCategory = useMemo(
    () =>
      data?.data && selectedTreeCategoryId
        ? getCategoryAndItsChildrenById(data?.data, selectedTreeCategoryId)
        : undefined,
    [selectedTreeCategoryId, JSON.stringify(data?.data)]
  );

  const searchedResult = useMemo(
    () => (filterQstr && data?.data ? filterCategoryByQStr(data?.data, filterQstr) : undefined),
    [selectedTreeCategoryId, filterQstr, JSON.stringify(data?.data)]
  );

  const breadCrumbData = useMemo(
    () => (selectedTreeCategoryId && data?.data ? generateBreadcrumb(data?.data, selectedTreeCategoryId) : undefined),
    [selectedTreeCategoryId, JSON.stringify(data?.data)]
  );

  const hasBack = !!breadCrumbData;

  const handleGoBack = () => {
    if (breadCrumbData?.length === 1) {
      setSelectedTreeCategory(null);
      return;
    }

    const lastBreadCrumbItem = breadCrumbData?.at(-2);

    if (lastBreadCrumbItem) {
      setSelectedTreeCategory(lastBreadCrumbItem?.id);
    }
  };

  const handleOnClickCategory = (categoryId: string | null) => {
    setSelectedTreeCategory(categoryId);
  };

  const handleSelectCategory = (categoryId: string) => {
    setSelectedCategoryId(categoryId);
  };

  const handleConfirmModal = () => {
    if (!selectedCategoryId) {
      return;
    }
    onChange(selectedCategoryId);
    hideModal();
  };

  const handleOnSearchChange = (v: string) => {
    setFilterQstr(v);

    if (selectedTreeCategoryId) {
      setSelectedTreeCategory(null);
    }
  };

  return (
    <div className="flex flex-col gap-2.5">
      {/* ------------------------------ header navbar ----------------------------- */}
      <div className="flex items-center justify-between">
        <div className="text-gray-999 text-sm font-medium">{t("product.categorySelect.title")}</div>
        <Icon icon="mdi:close" onClick={hideModal} className="cursor-pointer size-6" />
      </div>

      {/* ----------------------------------- box ---------------------------------- */}
      {isLoading && (
        <div className="h-[485px] w-full flex items-center justify-center">
          <CircularProgress />
        </div>
      )}
      {!isLoading && !!data?.data?.length && (
        <div className="flex flex-col gap-7 self-stretch items-start h-[485px]">
          {/* --------------------------------- top box -------------------------------- */}
          <div className="flex flex-col gap-4 items-start self-stretch w-full h-[calc(100%_-_76px)]">
            {/* --------------------------------- search --------------------------------- */}
            <div className="w-full">
              <RenderSearchInputDebounced onChange={handleOnSearchChange} />
            </div>

            {/* ------------------------------- breadcrumb ------------------------------- */}
            {!searchedResult && filteredByCategory && (
              <RenderBreadCrumb data={breadCrumbData} handleOnClickCategory={handleOnClickCategory} />
            )}

            {/* ---------------------------------- lists --------------------------------- */}
            <div className="overflow-y-auto flex-1 h-full w-full">
              {!searchedResult && !filteredByCategory && (
                <RenderRootCategory data={data?.data} handleOnClickCategory={handleOnClickCategory} />
              )}
              {!searchedResult && filteredByCategory && (
                <RenderSubCategory
                  data={filteredByCategory}
                  handleOnClickCategory={handleOnClickCategory}
                  handleSelectCategory={handleSelectCategory}
                  selectedCategoryValue={selectedCategoryId}
                />
              )}
              {searchedResult && (
                <RenderCategory
                  data={searchedResult}
                  handleSelectCategory={handleSelectCategory}
                  selectedCategoryValue={selectedCategoryId}
                />
              )}
            </div>
          </div>

          {/* --------------------------------- footer --------------------------------- */}
          <div className="flex items-start self-stretch">
            {hasBack && (
              <Button variant="secondaryGray" onClick={handleGoBack}>
                {t("product.categorySelect.backBtn")}
              </Button>
            )}
            <Button variant="primary" className="mr-auto" disabled={!selectedCategoryId} onClick={handleConfirmModal}>
              {t("product.categorySelect.confirmBtn")}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export default SelectCategoryModalContent;
