import React from "react";
import { TProductCard } from "./types";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import { useTranslation } from "react-i18next";
import useCurrency from "@/utils/hooks/useCurrency";
import { Icon } from "@iconify/react";

function ShippingTableContent({
  shippingPolicies,
  findLocation
}: {
  shippingPolicies?: TProductCard["shippingPolicies"];
  findLocation: TProductCard["findLocation"];
}) {
  const { t } = useTranslation();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };

  const shippingPoliciesData = shippingPolicies?.filter(a => !a.excluded);
  const shippingPoliciesExcludedCities =
    shippingPolicies
      ?.filter(a => a.shippingTo && a.excluded)
      ?.map(item =>
        item.shippingTo === "00000000-0000-0000-0000-000000000000"
          ? t("productItem.allCities")
          : item.shippingTo
          ? findLocation(item.shippingTo)?.name
          : "-"
      ) || [];

  return (
    <TableContainer className="bg-transparent !border-none">
      <div className="flex items-center gap-2 mb-2.5">
        <Icon icon="solar:pin-broken" width={12} height={12} />
        <div className="text-gray-999 text-sm font-medium">{t("productItem.shippingDetail")}</div>
      </div>
      <Table className="bg-transparent !border-none">
        <TableHead sx={{ backgroundColor: "transparent !important" }}>
          <TableRow>
            <TableCell align="right" className="text-right text-[13px] text-gray-400 py-1">
              {t("productItem.shippingTo")}
            </TableCell>
            <TableCell align="right" className="text-right text-[13px] text-gray-400 py-1">
              {t("productItem.rate")}
            </TableCell>
            <TableCell align="right" className="text-right text-[13px] text-gray-400 py-1">
              {t("productItem.shippingTime")}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {shippingPoliciesData?.map(row => (
            <TableRow key={row.shippingTo}>
              <TableCell align="right" className="text-right text-[13px] text-gray-999 py-2">
                {row.shippingTo === "00000000-0000-0000-0000-000000000000"
                  ? t("productItem.allCities")
                  : row.shippingTo
                  ? findLocation(row.shippingTo)?.name
                  : "-"}
              </TableCell>
              <TableCell align="right" className="text-right text-[13px] text-gray-999 py-2">
                {renderPrice(Number(row.rate))}
              </TableCell>
              <TableCell align="right" className="text-right text-[13px] text-gray-999 py-2">
                {row?.shippingTime?.min ? row?.shippingTime?.min : ""} {t("productItem.shippingTimeTo")}{" "}
                {row?.shippingTime?.max ? row?.shippingTime?.max : ""} {t("productItem.shippingTimeDays")}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {!!shippingPoliciesExcludedCities?.length && (
        <div className="p-1.5 rounded bg-v2-surface-info text-xs text-v2-content-on-info font-medium">
          <span className="">{t("productItem.dontHaveShippingTo")}</span>{" "}
          {shippingPoliciesExcludedCities?.map((item, index) => (
            <span key={index}>
              {item}
              {index < shippingPoliciesExcludedCities?.length - 1 ? "،" : ""}{" "}
            </span>
          ))}
        </div>
      )}
    </TableContainer>
  );
}

export default ShippingTableContent;
