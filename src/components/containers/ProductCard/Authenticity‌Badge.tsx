import React from "react";
import { useTranslation } from "react-i18next";
import { Authenticity } from "@/store/apps/product/types";
import { twMerge } from "tailwind-merge";

interface IAuthenticityProps {
  authenticity: Authenticity;
}

function AuthenticityBadge({ authenticity }: IAuthenticityProps) {
  const { t } = useTranslation();

  const authenticityBadge = {
    HighCopy: {
      bg: "#F2F6FF",
      color: "#00359E",
      title: t("product.highCopy")
    },
    Fake: {
      bg: "#FFEBD8",
      color: "#D26500",
      title: t("product.fake")
    }
  };

  return (
    <>
      {authenticity && authenticity !== "Original" && (
        <div
          className={twMerge("rounded px-1.5 py-0.5 text-caption-medium w-fit ")}
          style={{
            background: authenticityBadge?.[authenticity]?.bg,
            color: authenticityBadge?.[authenticity]?.color
          }}
        >
          {authenticityBadge?.[authenticity]?.title}
        </div>
      )}
    </>
  );
}

export default AuthenticityBadge;
