import { TMetaLocations } from "@/store/apps/meta/types";
import { Authenticity, Condition, IProductPayLoad } from "@/store/apps/product/types";
import { TSupplierShippingData } from "@/store/apps/supplier/types";

export type TProductCard = {
  className?: string;
  commission: number;
  productId?: string;
  cover?: IProductPayLoad["cover"];
  title?: string;
  supplier?: {
    id?: string;
    name?: string;
  };
  listingPrice?: number;
  retailPrice?: number;
  price?: number;
  authenticity: Authenticity;
  condition: Condition;
  shippingPrice?: number | string;
  shippingTime?: {
    max: number;
    min: number;
  };
  imported: boolean;
  shippingPolicies?: Pick<TSupplierShippingData, "shippingTime" | "shippingTo" | "rate" | "excluded">[];
  href?: string;
  findLocation: (cityId: string | number) => TMetaLocations | null;
  isSupplierView?: boolean;
};

export type TRetailerImportedProductCardProps = {
  productId: string;
  title?: string;
  cover?: IProductPayLoad["cover"];
  supplier?: {
    id?: string;
    name?: string;
  };
  onClickMenu: (id: string) => void;
};
