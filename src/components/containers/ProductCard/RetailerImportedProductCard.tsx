import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { useTranslation } from "react-i18next";
import { TRetailerImportedProductCardProps } from "./types";

function RetailerImportedProductCard(props: TRetailerImportedProductCardProps) {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();

  const { productId, cover, title, supplier, onClickMenu } = props || {};

  return (
    <div className="bg-v2-surface-primary p-4 rounded-lg flex gap-2">
      {/* -------------------------------- right img ------------------------------- */}
      <div className="relative items-center h-16 w-16 shrink-0 justify-center overflow-hidden flex bg-v2-surface-secondary rounded-md">
        {cover?.url?.startsWith("http") && <Image src={cover?.url} alt={cover?.alt || title + "_img"} fill />}
      </div>

      <div className="flex-1 py-0.5 flex flex-col justify-between shrink-0">
        <div className="flex gap-2.5 items-start justify-between">
          <div className="text-v2-content-primary text-xs font-medium shrink-0 max-w-[80%]">{title}</div>

          <div className="shrink-0">
            <Icon
              icon="solar:menu-dots-bold"
              className="rotate-90 shrink-0 size-4 text-v2-content-primary cursor-pointer"
              onClick={() => onClickMenu?.(productId)}
            />
          </div>
        </div>
        <div>
          {/* -------------------------------- supplier -------------------------------- */}
          {supplier?.name && supplier?.id && (
            <div className="text-gray-500 text-[13px] font-normal truncate max-w-36">
              <Link href={supplier?.id ? `${makePath(routes.supplierProducts(supplier.id))}` : ""}>
                <div className="font-normal text-v2-content-tertiary text-[10px] leading-4">{supplier?.name}</div>
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default RetailerImportedProductCard;
