import { TFunction } from "i18next";

export const headerItems = ({ t }: { t: TFunction<"translation", undefined> }) => [
  {
    id: 1,
    hasCheckBox: true,
    title: t("product.tableHeaderItems.product"),
    size: "300px"
  },
  {
    id: 2,
    hasCheckBox: false,
    title: t("product.tableHeaderItems.category"),
    size: 115
  },
  {
    id: 3,
    hasCheckBox: false,
    title: t("product.tableHeaderItems.price"),
    size: 110
  },
  {
    id: 4,
    hasCheckBox: false,
    title: t("product.tableHeaderItems.status"),
    size: 100
  },
  {
    id: 5,
    hasCheckBox: false,
    title: t("product.tableHeaderItems.inventory"),
    size: 67
  },
  {
    id: 6,
    hasCheckBox: false,
    title: t("product.tableHeaderItems.action"),
    size: 160
  }
];

export const BCrumb = ({ t }: { t: TFunction<"translation", undefined> }) => [
  {
    to: "/",
    title: t("product.breadCrumbs.home")
  },
  {
    title: t("product.breadCrumbs.product")
  }
];

export const productStatusVariants = {
  InReview: {
    bgColor: "rgb(var(--color-warning-50))",
    color: "rgb(var(--color-warning-500))"
  },
  Inactive: {
    bgColor: "rgb(var(--color-gray-20))",
    color: "rgb(var(--color-gray-400))"
  },
  Active: {
    bgColor: "rgb(var(--color-success-50))",
    color: "rgb(var(--color-success-500))"
  },
  Rejected: {
    bgColor: "rgb(var(--color-error-50))",
    color: "rgb(var(--color-error-500))"
  }
};
