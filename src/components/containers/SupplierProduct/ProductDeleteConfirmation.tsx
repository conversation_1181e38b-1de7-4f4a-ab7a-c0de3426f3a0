import Button from "@/components/ui/Button";
import { useDeleteProductMutation } from "@/store/apps/product";
import useModal from "@/utils/hooks/useModal";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import { useTranslation } from "react-i18next";

interface IProductDeleteConfirmationProps {
  ids: string[];
  onSuccess?: () => void;
}

function ProductDeleteConfirmation({ ids, onSuccess }: IProductDeleteConfirmationProps) {
  const { t } = useTranslation();
  const { hideModal } = useModal();
  const [deleteProduct, { isLoading }] = useDeleteProductMutation();

  const handleDelete = async () => {
    try {
      await deleteProduct({ ids }).then(res => {
        // const error = (res as any)?.error?.data;

        if ((res as any)?.error) {
          clientDefaultErrorHandler({ error: (res as any)?.error });
          return;
        }

        hideModal();
        onSuccess?.();
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  return (
    <div className="flex flex-col gap-5">
      <Icon icon="solar:trash-bin-trash-bold" width={48} height={48} className="text-v2-content-on-error-2" />
      <div className="flex flex-col gap-2">
        <div className="text-base font-bold text-v2-content-primary">{t("product.actionModal.deleteTitle")}</div>
        <div className="text-[13px] font-medium text-v2-content-tertiary">
          {t("product.actionModal.deleteSubtitle")}
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Button className="w-full" variant="secondaryGray" onClick={hideModal}>
          {t("product.actionModal.cancel")}
        </Button>
        <Button className="w-full" variant="destructivePrimary" disabled={isLoading} onClick={handleDelete}>
          {isLoading ? <CircularProgress color="info" size={20} /> : t("product.actionModal.delete")}
        </Button>
      </div>
    </div>
  );
}

export default ProductDeleteConfirmation;
