import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import CustomFormLabel from "@/components/ui/CustomFormLabel/CustomFormLabel";
import CustomNumberField from "@/components/ui/CustomNumberField/CustomNumberField";
import { useBulkUpdatePriceMutation } from "@/store/apps/product";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

function ProductBulkChangePrice({ ids, onSuccess }: { ids: string[]; onSuccess: VoidFunction }) {
  const { t } = useTranslation();
  const { hideModal } = useModal();
  const [bulkUpdate, { isLoading, isError }] = useBulkUpdatePriceMutation();

  const options = [
    {
      id: "22165707-98da-42bd-b8fe-d7c06d09ee8f",
      title: t("product.bulk.price.notFixed_incremental"),
      is_fixed: false,
      is_incremental: true,
      description: t("product.bulk.price.notFixed_incremental_desc"),
      symbol: t("product.bulk.price.percentage")
    },
    {
      id: "32f16bdf-2899-4c31-8fda-1a21837d2083",
      title: t("product.bulk.price.notFixed_notIncremental"),
      is_fixed: false,
      is_incremental: false,
      description: t("product.bulk.price.notFixed_notIncremental_desc"),
      symbol: t("product.bulk.price.percentage")
    },
    {
      id: "41478752-4269-454e-8cd4-41313d54a33b",
      title: t("product.bulk.price.fixed_incremental"),
      is_fixed: true,
      is_incremental: true,
      description: t("product.bulk.price.fixed_incremental_desc"),
      symbol: t("product.bulk.price.rial")
    },
    {
      id: "a504efc0-0996-462e-8418-f8b45d2e69e2",
      title: t("product.bulk.price.fixed_notIncremental"),
      is_fixed: true,
      is_incremental: false,
      description: t("product.bulk.price.fixed_notIncremental_desc"),
      symbol: t("product.bulk.price.rial")
    }
  ];

  const [priceType, setPriceType] = useState(options[0]);
  const [amount, setAmount] = useState<number | undefined | null>(null);

  const isValid = !!priceType && !!amount;

  const handleSubmit = () => {
    if (!isValid || !ids?.length) return;

    bulkUpdate({
      ids,
      is_fixed: priceType?.is_fixed,
      is_incremental: priceType?.is_incremental,
      value: Number(amount)
    }).then(res => {
      if (!(res as any)?.error) {
        hideModal();
      }
    });
  };

  return (
    <div className="flex flex-col gap-4">
      {/* ---------------------------------- body ---------------------------------- */}

      <div className="flex flex-col gap-6">
        {
          /* --------------------------------- header --------------------------------- */
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Icon icon="solar:dollar-minimalistic-bold" width={24} height={24} className="text-purple-500" />
              <div className="text-gray-999 text-[13px] font-medium">{t("product.bulk.changePrice")}</div>
            </div>

            <Icon
              icon="mdi:close"
              width={20}
              height={20}
              className="text-gray-500 cursor-pointer"
              onClick={hideModal}
            />
          </div>
        }

        {isError && (
          <div className="rounded-md bg-[#fde3e3] text-gray-999 text-xs p-2 flex items-center justify-start gap-2">
            <Icon icon="solar:info-circle-outline" width={20} height={20} />
            {t("product.bulk.errorMsg")}
          </div>
        )}

        <div>
          <CustomAutocomplete
            value={{ id: priceType?.id, label: priceType?.title }}
            options={options?.map(item => ({
              id: item?.id,
              label: item?.title
            }))}
            labelCLassName="!mt-0"
            size="medium"
            label={t("product.bulk.priceChangeType")}
            placeholder={t("product.bulk.priceChangeType")}
            onChange={(e, value) => {
              if (value?.id === undefined) return;
              const foundOption = options?.find(a => a.id === value?.id);

              if (!foundOption) return;
              setPriceType(foundOption);
            }}
          />
        </div>
        <div>
          <CustomFormLabel htmlFor="amount" className="!mt-0">
            {t("product.bulk.priceAmount")}
          </CustomFormLabel>
          <CustomNumberField
            fullWidth
            id="amount"
            name="amount"
            type="number"
            placeholder={t("product.bulk.priceAmount")}
            value={amount as any}
            hasComma
            returnType="number"
            isParsFloat
            onTextChange={v => setAmount(v as number)}
            endAdornment={<div className="text-gray-400 text-[13px] font-medium">{priceType?.symbol}</div>}
          />
        </div>

        <div className="rounded-md bg-[#E3EAFD] text-gray-999 text-xs p-2 flex items-center justify-start gap-2">
          <Icon icon="solar:info-circle-outline" width={20} height={20} />
          {priceType?.description}
        </div>
      </div>

      {/* --------------------------------- footer --------------------------------- */}
      <div className="flex items-center justify-between gap-4">
        <CustomButton color="secondary" fullWidth className="flex items-center gap-1.5" onClick={hideModal}>
          {t("product.bulk.cancel")}
        </CustomButton>
        <CustomButton color="primary" fullWidth onClick={handleSubmit} disabled={isLoading || !isValid}>
          {isLoading ? <CircularProgress color="info" size={20} /> : t("product.bulk.applyChanges")}
        </CustomButton>
      </div>
    </div>
  );
}

export default ProductBulkChangePrice;
