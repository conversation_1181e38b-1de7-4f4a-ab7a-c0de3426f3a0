import { Box, Stack, Typography } from "@mui/material";
import "./ProductStatus.css";
import { Icon } from "@iconify/react";
import { productStatusVariants } from "./utils";

export interface IProductStatusItems {
  title: string;
  id: keyof typeof productStatusVariants;
}

interface IProductStatusVariant {
  color: string;
  bgColor: string;
}

interface IProductStatusProps extends IProductStatusItems {
  hasDot?: boolean;
}

function ProductStatus({ title, id, hasDot = false }: IProductStatusProps) {
  const variant = productStatusVariants[id] as IProductStatusVariant;

  return (
    <Box className="product-status-container">
      <Stack
        direction="row"
        alignItems="center"
        gap={0.5}
        className="product-status-wrapper"
        sx={{
          background: variant?.["bgColor"] ?? ""
        }}
      >
        {!!hasDot && <Icon icon="icon-park-outline:dot" width={8} height={8} color={variant?.["color"] ?? ""} />}
        <Typography
          className="product-status-text"
          whiteSpace="nowrap"
          sx={{
            color: variant?.["color"] ?? ""
          }}
        >
          {title}
        </Typography>
      </Stack>
    </Box>
  );
}

export default ProductStatus;
