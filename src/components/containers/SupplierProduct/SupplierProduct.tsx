"use client";

import ProductsFilter from "@/components/forms/product-form/ProductFilters/ProductFilters";
import { useProductFilters } from "@/components/forms/product-form/ProductFilters/useProductFilters";
import CustomTablePagination from "@/components/ui/CustomTablePagination/CustomTablePagination";
import { routes } from "@/constants/routes";
import { useGetProductCategoryMapperCountQuery, useGetProductListQuery } from "@/store/apps/product";
import { omitEmptyValues } from "@/utils/helpers";
import useCurrency from "@/utils/hooks/useCurrency";
import useModal from "@/utils/hooks/useModal";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import {
  generateBackendFilters,
  generateBackendSorts,
  TGenerateBackendFiltersProps
} from "@/utils/services/transformers";
import { Icon } from "@iconify/react";
import {
  Avatar,
  Box,
  CircularProgress,
  SelectChangeEvent,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Theme,
  Typography,
  useMediaQuery
} from "@mui/material";
import Link from "next/link";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import CustomCardContent from "../../ui/CustomCard/CustomCard";
import CustomCheckbox from "../../ui/CustomCheckbox/CustomCheckbox";
import ProductBulkChangePrice from "./ProductBulkChangePrice";
import ProductDeleteConfirmation from "./ProductDeleteConfirmation";
import ProductEmptyList from "./ProductEmptyList";
import ProductListMobile from "./ProductListMobile/ProductListMobile";
import ProductStatus from "./ProductStatus";
import { headerItems } from "./utils";
import { useRouter } from "next/navigation";

const SupplierProduct = () => {
  const { t } = useTranslation();
  const [curr] = useCurrency();
  const router = useRouter();
  const { render: renderPrice } = curr ?? { render: v => v };
  const makePath = useRoleBasePath();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const [checkedIds, setCheckedIds] = useState<string[]>([]);
  const { showModal } = useModal();

  const { filters, pagination, setFilters } = useProductFilters();
  const { page, pageSize } = pagination || {};

  const nonEmptyFilters = useMemo(() => omitEmptyValues(filters), [filters]);
  const finalFilters = generateBackendFilters(nonEmptyFilters as TGenerateBackendFiltersProps);
  const sorts = generateBackendSorts();

  const queryString = [
    pageSize ? `page_size=${pageSize}` : "",
    page ? `page=${page}` : "",
    finalFilters ? `filters=${finalFilters}` : "",
    sorts ? `sorts=${sorts}` : ""
  ]
    ?.filter(part => part !== "")
    ?.join("&");

  const {
    data: productData,
    isLoading: isProductLoading,
    isFetching: isProductFetching,
    isError: isProductError
  } = useGetProductListQuery(queryString, { skip: !page, refetchOnMountOrArgChange: true });

  const { data: categoryMapperCount } = useGetProductCategoryMapperCountQuery();

  const totalCount = productData?.pagination?.total ?? 0;

  const handleChangePage = (event: unknown, newPage: number) => {
    setFilters({ page: newPage }, { history: "push" });
  };

  const handleChangeRowsPerPage = (event: SelectChangeEvent<number>) => {
    setFilters({ page: 1, pageSize: event.target.value as number }, { history: "push" });
  };

  const shouldRenderFilters = Boolean(
    productData?.data?.length || (!productData?.data?.length && Object.keys(nonEmptyFilters).length) || page > 1
  );
  const cells = headerItems({ t });

  const onDeleteProduct = (ids: string[]) => {
    const cloneIds = [...checkedIds];

    showModal({
      body: (
        <ProductDeleteConfirmation
          ids={ids}
          onSuccess={() => {
            if (cloneIds?.length) setCheckedIds(prev => prev.filter(item => !cloneIds?.find(v => v === item)));
          }}
        />
      ),
      width: 428
    });
  };

  const onClickBulkChangePrice = (ids: string[]) => {
    const cloneIds = [...checkedIds];

    showModal({
      width: 450,
      modalProps: {
        showCloseIcon: false
      },
      body: (
        <ProductBulkChangePrice
          ids={ids}
          onSuccess={() => {
            if (cloneIds?.length) setCheckedIds(prev => prev.filter(item => !cloneIds?.find(v => v === item)));
          }}
        />
      )
    });
  };

  const handleCheckAllItems = (checked: boolean) => {
    const ids = productData?.data
      // ?.filter(item => !checkedIds?.find(v => v === item?.id))
      ?.map(item => item.id) as string[];

    setCheckedIds(checked ? ids : []);
  };

  const handleCheckItem = (id: string) => {
    const existId = checkedIds?.find(item => item === id);

    if (existId) {
      setCheckedIds(prev => prev?.filter(item => item !== id));
    } else {
      setCheckedIds(prev => [...prev, id]);
    }
  };

  if (isMobile) {
    return (
      <ProductListMobile
        page={page}
        setPage={v => setFilters({ page: v }, { history: "push" })}
        pageSize={pageSize}
        totalCount={totalCount}
        isLoading={isProductLoading}
        productsData={productData?.data}
        isSupplierOrderError={isProductError}
        shouldRenderFilters={!!finalFilters}
      />
    );
  }

  if (isProductLoading) {
    return (
      <div className="flex items-center justify-center h-fullMinesHeader bg-cards rounded-[10px]">
        <CircularProgress />
      </div>
    );
  }

  return (
    <CustomCardContent className="h-full flex flex-col">
      <>
        {shouldRenderFilters ? (
          <ProductsFilter
            {...{ filters, setFilters }}
            filtersEndAdornment={
              !!categoryMapperCount?.data?.count &&
              categoryMapperCount?.data?.count > 0 && (
                <Link href={`${makePath(routes.productCategoryMapper)}`}>
                  <div className="text-[13px] font-medium text-[#00359E] w-fit mr-auto cursor-pointer flex items-center justify-center mt-4">
                    {t("product.categorymapper.syncCategories", { count: categoryMapperCount?.data?.count })}{" "}
                    <Icon icon="solar:alt-arrow-left-outline" className="size-4" />
                  </div>
                </Link>
              )
            }
          />
        ) : (
          <></>
        )}
        <Box mt={3} className="flex-1 ">
          {isProductFetching ? (
            <div className="flex items-center justify-center h-fullMinesHeader">
              <CircularProgress />
            </div>
          ) : !productData?.data?.length && !isProductFetching ? (
            <ProductEmptyList shouldRenderFilters={!!finalFilters} />
          ) : (
            <Box className="flex flex-col h-full">
              <TableContainer className="flex-1">
                <Table aria-label="product">
                  <TableHead className="sticky top-0 z-10">
                    {" "}
                    <TableRow>
                      {checkedIds?.length ? (
                        <>
                          <TableCell className="py-1">
                            <Stack flexDirection="row" alignItems="center" gap={1}>
                              <CustomCheckbox
                                className="p-0"
                                indeterminate={!!checkedIds?.length && checkedIds?.length !== productData?.data?.length}
                                onChange={(e, checked) => handleCheckAllItems(checked)}
                                checked={!!checkedIds?.length && checkedIds?.length === productData?.data?.length}
                              />

                              {/* <div
                                className="cursor-pointer rounded-lg border-[1.5px] border-v2-border-primary py-1 px-3.5 bg-v2-surface-primary flex items-center justify-center gap-2"
                                onClick={() => onDeleteProduct(checkedIds)}
                              >
                                <Icon
                                  icon="solar:trash-bin-trash-outline"
                                  color="rgb(var(--color-gray-400))"
                                  width={16}
                                  height={16}
                                />
                                <div className="text-v2-content-secondary text-[13px] font-medium">
                                  {t("removeFromList")}
                                </div>
                              </div> */}

                              <div
                                className="cursor-pointer rounded-lg border-[1.5px] border-v2-border-primary py-1 px-3.5 bg-v2-surface-primary flex items-center justify-center gap-2"
                                onClick={() => onClickBulkChangePrice(checkedIds)}
                              >
                                <Icon
                                  icon="solar:dollar-linear"
                                  color="rgb(var(--color-gray-400))"
                                  width={16}
                                  height={16}
                                />
                                <div className="text-v2-content-secondary text-[13px] font-medium">
                                  {t("product.bulk.changePrice")}
                                </div>
                              </div>
                            </Stack>
                          </TableCell>
                          {[...Array.from({ length: cells?.length - 1 })].map((_, index) => (
                            <TableCell key={index} className="py-1" />
                          ))}
                        </>
                      ) : (
                        cells?.map(item => (
                          <TableCell key={item.id} width={item.size} className="py-1">
                            <Stack flexDirection="row" alignItems="center" gap={1}>
                              {item?.hasCheckBox && (
                                <CustomCheckbox
                                  className="p-0"
                                  onChange={(e, checked) => handleCheckAllItems(checked)}
                                  checked={!!checkedIds?.length && checkedIds?.length === productData?.data?.length}
                                />
                              )}
                              <div>{item.title}</div>
                            </Stack>
                          </TableCell>
                        ))
                      )}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {productData?.data?.map(item => (
                      <TableRow key={item.id}>
                        <TableCell width={cells[0].size}>
                          <Stack flexDirection="row" alignItems="center">
                            <CustomCheckbox
                              onChange={() => handleCheckItem(item.id)}
                              checked={!!checkedIds?.length && checkedIds?.some(v => v === item?.id)}
                            />

                            <Avatar
                              src={item.cover.url}
                              alt={item.cover.alt || item.title}
                              className="rounded-md w-[46px] h-[46px]"
                            />
                            <Typography className="text-sm ms-5 text-gray-999">{item.title}</Typography>
                          </Stack>
                        </TableCell>
                        <TableCell width={cells[1].size}>
                          <Typography className="text-gray-999 text-sm ">
                            {item?.category?.name ? (
                              item?.category?.name
                            ) : (
                              <div className="text-v2-content-on-error-2 flex items-center gap-0.5">
                                <Icon icon="solar:danger-triangle-linear" className="size-4" />
                                {t("product.unknownCategory")}
                              </div>
                            )}
                          </Typography>
                        </TableCell>
                        <TableCell width={cells[2].size}>
                          <Typography className="text-gray-999 text-sm ">
                            {item?.cheapestPrice && renderPrice(item?.cheapestPrice)}
                          </Typography>
                        </TableCell>
                        <TableCell width={cells[3].size}>
                          <ProductStatus title={t(`product.statusItems.${item.status}`)} id={item.status} />
                        </TableCell>
                        <TableCell width={cells[4].size}>
                          <Typography className="text-gray-999 text-sm ">
                            {item?.variants?.reduce((intry, variant) => intry + variant.inventory, 0) ?? "0"}
                          </Typography>
                        </TableCell>
                        <TableCell width={cells[5].size}>
                          <Stack flexDirection="row" alignItems="center" gap={1}>
                            <Link href={`${makePath(routes.product)}/edit/${item.id}`}>
                              <Box className="border border-solid rounded-full p-2 cursor-pointer border-gray-50">
                                <Icon
                                  icon="solar:pen-2-outline"
                                  width={20}
                                  height={20}
                                  color="rgb(var(--color-gray-400))"
                                />
                              </Box>
                            </Link>
                            {/* <Box
                              className="border border-solid rounded-full p-2 cursor-pointer border-gray-50"
                              onClick={() => onDeleteProduct([item.id])}
                            >
                              <Icon
                                icon="solar:trash-bin-trash-outline"
                                width={20}
                                height={20}
                                color="rgb(var(--color-gray-400))"
                              />
                            </Box> */}
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Stack pt={3} mt={"auto"}>
                {productData?.data && (
                  <CustomTablePagination
                    rowsPerPageOptions={[10, 50, 100, 200]}
                    count={totalCount}
                    rowsPerPage={pageSize}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage={t("product.rowPerPage")}
                  />
                )}
              </Stack>
            </Box>
          )}
        </Box>
      </>
    </CustomCardContent>
  );
};

export default SupplierProduct;
