import Input from "@/components/ui/inputs/Input";
import { Icon } from "@iconify/react";
import React from "react";
import { useTranslation } from "react-i18next";

function Header({
  title,
  titlePostfix,
  searchValue,
  onSearch,
  onClickMenu
}: {
  title: string;
  titlePostfix?: string;
  searchValue?: string;
  onSearch?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  onClickMenu?: VoidFunction;
}) {
  const { t } = useTranslation();
  return (
    <>
      <div className="h-[90px]" />
      <div className="py-3 px-4 bg-v2-surface-primary border-b border-v2-border-primary flex flex-col gap-2 fixed top-0 left-0 right-0 z-50">
        {/* ---------------------------------- title --------------------------------- */}
        <div className="text-[15px] text-v2-content-primary font-medium flex items-center gap-1">
          {title}{" "}
          {titlePostfix && <span className="text-[15px] font-medium text-v2-content-tertiary">{titlePostfix}</span>}
        </div>

        {/* ------------------------------- search bar ------------------------------- */}
        <div className="flex items-center gap-2 w-full">
          <Input
            placeholder={t("product.searchWithDots")}
            inputSize="sm"
            variant="filled"
            rootClassName="flex-1"
            onChange={onSearch}
            value={searchValue}
          />
          <Icon
            icon="solar:menu-dots-bold"
            className="rotate-90 shrink-0 size-4 text-v2-content-primary"
            onClick={onClickMenu}
          />
        </div>
      </div>
    </>
  );
}

export default Header;
