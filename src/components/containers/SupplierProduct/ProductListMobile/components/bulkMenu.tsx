import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import React from "react";
import { useTranslation } from "react-i18next";

function BulkMenu({
  onClickBulkDelete,
  onClickBulkPrice
}: {
  onClickBulkDelete?: VoidFunction;
  onClickBulkPrice?: VoidFunction;
}) {
  const { t } = useTranslation();
  const { hideModal } = useModal();

  const menuItems = [
    // {
    //   title: t("product.delete"),
    //   icon: <Icon icon="solar:trash-bin-trash-outline" className="size-4" />,
    //   onClick: onClickBulkDelete
    // },
    {
      title: t("product.bulk.changePrice"),
      icon: <Icon icon="solar:dollar-linear" className="size-4" />,
      onClick: onClickBulkPrice
    }
  ];

  return (
    <div className="flex flex-col gap-2">
      <div className="flex justify-between items-center mt-2">
        <div className="text-xs font-medium text-v2-content-primary">{t("product.bulk.bulkActions")}</div>
        <Icon icon="mdi:close" className="flex-shrink-0 size-5 text-v2-content-subtle" onClick={hideModal} />
      </div>

      <div className="flex flex-col divide-y divide-v2-border-primary">
        {menuItems?.map((menu, index) => (
          <div key={index} className="py-4 flex items-center gap-2 cursor-pointer" onClick={menu?.onClick}>
            <div className="size-4 shrink-0 overflow-hidden">{menu?.icon}</div>
            <div className="flex-1 text-sm font-medium text-v2-content-primary">{menu?.title}</div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default BulkMenu;
