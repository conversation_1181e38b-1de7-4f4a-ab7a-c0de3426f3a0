import CustomButton from "@/components/ui/CustomButton/CustomButton";
import React from "react";
import { useTranslation } from "react-i18next";

function StickyActionsFooter({ onClickBulkActions }: { onClickBulkActions?: VoidFunction }) {
  const { t } = useTranslation();

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 py-6 px-4 z-50 bg-v2-surface-primary">
        <CustomButton onClick={onClickBulkActions} fullWidth>
          {t("product.bulk.doBulkActions")}
        </CustomButton>
      </div>
      <div className="h-[90px]" />
    </>
  );
}

export default StickyActionsFooter;
