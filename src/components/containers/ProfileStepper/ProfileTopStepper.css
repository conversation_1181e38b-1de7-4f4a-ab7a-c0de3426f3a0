#sx-profilestepper-4628 {
  white-space: nowrap;
}

#sx-profilestepper-4644 {
  border-radius: 12px;
}

.profilestepper-wrapper {
  cursor: pointer;
  padding-inline: 16px;
}

.profile__content-container {
  /* border-top: 1px solid #d4d4d4; */
  /* margin: 24px 24px 24px 24px; */
}

/* .profile__content-wrapper {
  margin-top: 16px;
} */

.profilestepper-step-wrapper {
  cursor: pointer;
}

.profilestepper-container {
  background-color: rgb(var(--color-cards));
  border-radius: 10px;
  /* padding: 1px 0; */
}

@media (min-width: 768px) {
  .profilestepper-wrapper .MuiStepConnector-horizontal {
    opacity: 0;
  }

  .profilestepper-container {
    padding-inline: 24px;
  }

  .profile__content-container {
    /* margin: 16px; */
  }

  .profilestepper-wrapper {
    border-bottom: 1px solid rgb(var(--color-gray-50));
    justify-content: flex-start;
    gap: 36px;
    padding-inline: 0px;
  }

  .profilestepper-step-wrapper-active {
    padding-bottom: 10px;
    padding-inline: 10px;
    border-bottom: 2px solid #6051dc;
  }

  .profilestepper-step-wrapper {
    padding-bottom: 10px;
    padding-inline: 10px;
    flex: unset;
    cursor: pointer;
  }

  .profilestepper-step-wrapper > .MuiStepLabel-root {
    flex-direction: row;
    align-items: flex-end;
    gap: 8px;
  }

  .profilestepper-wrapper {
    cursor: pointer;
  }

  .profile__content-container {
    border-top: unset;
    margin: unset;
    padding-block-start: 4px;
    padding-block-end: 4px;
    height: 100%;
  }

  .profile__content-wrapper {
    margin-top: 0;
    height: 100%;
  }
}

/* #sx-profilestepper-4628 .MuiStepLabel-label {
  font-size: 14px;
  font-weight: 700;
  color: rgb(var(--color-gray-400));
} */

.profilestepper-item {
  font-size: 14px;
  font-weight: 700;
  color: rgb(var(--color-gray-400));
}

.profilestepper-item .MuiStepLabel-label {
  margin-top: 12px;
  font-size: 14px;
  font-weight: 700;
  color: rgb(var(--color-gray-400));
}

@media (max-width: 920px) {
  .profilestepper-wrapper {
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .profilestepper-item .MuiStepLabel-label {
    font-size: 11px;
    font-weight: 700;
    /* color: var(--mui-palette-common-black); */
  }

  .profile__content-container {
    padding: 16px;
  }
}

.profilestepper-item-active,
.profilestepper-item-active .MuiStepLabel-label {
  color: rgb(var(--color-purple-500));
}
