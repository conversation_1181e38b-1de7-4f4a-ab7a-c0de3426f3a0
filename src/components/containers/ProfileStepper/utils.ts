import { TFunction } from "i18next";
import { ReactNode } from "react";

type TSelectItem = {
  id: string;
  label: ReactNode;
};

export const supplierSteps = ({
  t,
  isMobile,
  hasSupplierShipping,
  hasSupplierData
}: {
  t: TFunction<"translation", undefined>;
  isMobile: boolean;
  hasSupplierData: boolean;
  hasSupplierShipping: boolean;
}) => [
  {
    id: 1,
    isDisabled: false,
    title: t("supplier.profile.step.supplierInfo"),
    icon: "solar:user-id-outline",
    activeIcon: "solar:user-id-bold"
  },
  {
    id: 2,
    isDisabled: false,
    title: t("supplier.profile.step.shopInfo"),
    icon: "solar:shop-outline",
    activeIcon: "solar:shop-bold"
  },
  {
    id: 3,
    isDisabled: false,
    title: t("supplier.profile.step.store&warhouseAddressInfo"),
    icon: "solar:map-point-outline",
    activeIcon: "solar:map-point-bold"
  },
  {
    id: 4,
    isDisabled: false,
    title: t("supplier.profile.step.bankInfo"),
    icon: "solar:card-2-outline",
    activeIcon: "solar:card-2-bold"
  },
  {
    id: 5,
    isDisabled: !hasSupplierData,
    title: t("supplier.profile.step.shipping"),
    icon: "mage:delivery-truck",
    activeIcon: "mage:delivery-truck-fill"
  },
  {
    id: 6,
    isDisabled: !hasSupplierData || !hasSupplierShipping,
    title: t("supplier.profile.step.policy"),
    icon: "solar:undo-right-round-square-outline",
    activeIcon: "solar:undo-right-round-square-bold"
  }
];

export const retailerSteps = ({
  t,
  hasRetailerData
}: {
  t: TFunction<"translation", undefined>;
  hasRetailerData: boolean;
}) => [
  {
    id: 1,
    isDisabled: false,
    title: t("retailer.profile.step.personalInfo"),
    icon: "solar:user-id-outline",
    activeIcon: "solar:user-id-bold"
  },
  {
    id: 2,
    isDisabled: false,
    title: t("retailer.profile.step.addressInfo"),
    icon: "solar:map-point-outline",
    activeIcon: "solar:map-point-bold"
  }
];

export function extractMinMax(id: string) {
  const [min, max] = id.split("-").map(Number);
  return { min, max };
}

export function findItemByMinMax(items: TSelectItem[], min?: number, max?: number) {
  if (min === undefined && max === undefined) return;

  return items?.find(item => {
    const { min: itemMin, max: itemMax } = extractMinMax(item.id);
    return itemMin === min && itemMax === max;
  });
}
