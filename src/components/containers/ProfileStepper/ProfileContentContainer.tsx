import { Theme, useMediaQuery } from "@mui/material";
import { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

interface IProfileContentContainerProps {
  children: ReactNode;
  containerClassName?: string;
  wrapperClassName?: string;
  topWrapperClassName?: string;
}

function ProfileContentContainer({
  children,
  containerClassName,
  wrapperClassName,
  topWrapperClassName
}: IProfileContentContainerProps) {
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  if (isMobile) {
    return (
      <div className={twMerge("px-4", topWrapperClassName)}>
        <div className={twMerge("bg-cards rounded-lg mt-[70px]", wrapperClassName)}>
          <div className={twMerge("px-4 py-6", containerClassName)}>{children}</div>
        </div>
      </div>
    );
  }

  return <div className="h-full">{children}</div>;
}

export default ProfileContentContainer;
