import React, { ReactNode, useEffect, useState } from "react";
import { Theme, useMediaQuery } from "@mui/material";
import { useTranslation } from "react-i18next";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { USER_TYPES } from "@/constants/userTypes";
import { retailerSteps, supplierSteps } from "./utils";
import { TSupplierProfileData } from "@/store/apps/supplier/types";
import { accountTypeValue } from "@/app/supplier/(dashboard)/accountInfo/utils";
import ProfileSidebar from "./ProfileSidebar";
import Header from "../header/Header";
import ChangePassword from "@/components/forms/changePassword/ChangePassword";
import Currency from "@/components/forms/currency/currency";
import Language from "../header/Language";
import useRole from "@/utils/hooks/useRole";
import StoreList from "@/app/retailer/(dashboard)/store/storeList/StoreList";
import StoreInvoiceManager from "@/app/retailer/(dashboard)/store/StoreInvoiceManager";
import { twMerge } from "tailwind-merge";
import SupplierStoreSetting from "@/components/containers/SupplierStoreSetting/SupplierStoreSetting";

interface ProfileStepperProps {
  title?: string;
  isLegal?: boolean;
  defaultActiveStep?: number;
  hasData: boolean;
  onChangeStep: (val?: number) => void;
  userType: USER_TYPES;
  RenderContent: () => ReactNode;
  hasSupplierShipping?: boolean;
  name?: string;
  contactNumber?: string;
  profileStatus?: TSupplierProfileData["status"];
}

const SETTINGS = (hasData: boolean) => [
  {
    id: 1,
    type: "password",
    render: <ChangePassword />,
    showInSidebar: true,
    titleKey: "password&security",
    icon: "solar:shield-keyhole-minimalistic-outline",
    activeIcon: "solar:shield-keyhole-minimalistic-outline",
    path: "password",
    access: ["supplier", "retailer"]
  },
  {
    id: 2,
    type: "language",
    render: <Language />,
    showInSidebar: true,
    titleKey: "language",
    icon: "solar:earth-outline",
    path: "language",
    access: ["supplier", "retailer"]
  },
  {
    id: 3,
    type: "currency",
    render: <Currency />,
    showInSidebar: true,
    titleKey: "currency",
    icon: "solar:dollar-minimalistic-outline",
    activeIcon: "solar:dollar-minimalistic-outline",
    path: "currency",
    access: ["supplier", "retailer"]
  },
  {
    id: 4,
    type: "store-configureStore",
    showInSidebar: true,
    render: <SupplierStoreSetting />,
    isExtra: true,
    titleKey: "connectStoreInfo",
    icon: "solar:server-path-outline",
    activeIcon: "solar:server-path-bold-duotone",
    access: ["supplier"],
    isDisabled: !hasData,
    path: "store"
  },
  {
    id: 5,
    type: "store-configureStore",
    showInSidebar: true,
    render: <StoreList />,
    isExtra: true,
    titleKey: "storeInfo",
    icon: "solar:shop-outline",
    activeIcon: "solar:shop-bold",
    access: ["retailer"],
    isDisabled: !hasData,
    path: "store"
  },
  {
    id: 6,
    type: "configureStore",
    render: <StoreInvoiceManager />,
    showInSidebar: false,
    titleKey: "configureStore",
    icon: "solar:shop-outline",
    activeIcon: "solar:shop-bold",
    access: ["retailer"],
    isDisabled: !hasData,
    path: "configureStore"
  }
];

function ProfileStepper({
  defaultActiveStep,
  title,
  hasData,
  userType,
  RenderContent,
  onChangeStep,
  isLegal = false,
  hasSupplierShipping = false,
  name,
  contactNumber,
  profileStatus
}: ProfileStepperProps) {
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState<undefined | number>(0);
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const pathname = usePathname();
  const router = useRouter();
  const role = useRole();
  const searchParams = useSearchParams();
  const settingType = searchParams?.get("settingType") as
    | "password"
    | "currency"
    | "language"
    | "store"
    | "configureStore";

  useEffect(() => {
    const newActiveStep = isMobile
      ? defaultActiveStep !== undefined && defaultActiveStep >= 0
        ? defaultActiveStep
        : undefined
      : defaultActiveStep;

    setActiveStep(newActiveStep);
  }, [defaultActiveStep, isMobile]);

  const steps =
    userType === USER_TYPES.SUPPLIER
      ? supplierSteps({ t, hasSupplierData: hasData, isMobile, hasSupplierShipping })
      : retailerSteps({ t, hasRetailerData: hasData });

  const handleStep = (step: number | undefined, isDisabled?: boolean) => {
    if (isDisabled) return;
    setActiveStep(step);
    onChangeStep(step);
  };

  const settings = SETTINGS(hasData)
    ?.filter(item => role && item?.access.includes(role?.toLowerCase() as any))
    .map(setting => ({
      ...setting,
      title: t(setting.titleKey),
      onclick: () => router.push(`${pathname}?settingType=${setting.path}`)
    }));

  const currentSetting = settingType ? settings.find(item => item.path === settingType) : undefined;

  const sidebarProps = {
    steps,
    title,
    hasData,
    isLegal,
    activeStep,
    handleStep,
    settings,
    profileStatus,
    contactNumber,
    accountTypeValue
  };

  if (isMobile && ((activeStep !== undefined && activeStep >= 0) || currentSetting?.id)) {
    return currentSetting?.render || <RenderContent />;
  }

  if (isMobile) {
    return (
      <div>
        <Header title={t("accountInfo")} isMobile isSticky />
        <div className="px-4 mt-3">
          <ProfileSidebar {...sidebarProps} />
        </div>
      </div>
    );
  }

  // Desktop view
  return (
    <div className="bg-cards rounded-[10px] h-fit flex p-6 gap-6">
      <ProfileSidebar {...sidebarProps} />
      <div className="flex-1">
        <div className={twMerge("profile__content-container", "xmd:h-full")}>
          <div className={twMerge("profile__content-wrapper", "xmd:h-full")}>
            {currentSetting?.render || <RenderContent />}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfileStepper;
