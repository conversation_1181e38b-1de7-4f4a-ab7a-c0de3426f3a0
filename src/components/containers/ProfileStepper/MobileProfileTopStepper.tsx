import React from "react";
import { Box, Tooltip } from "@mui/material";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import { twMerge } from "tailwind-merge";
import { Swiper, SwiperSlide } from "swiper/react";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";

import "swiper/swiper-bundle.css";
import "./ProfileTopStepper.css";

interface IStepItems {
  id: number;
  isDisabled: boolean;
  title: string;
  icon: string;
  activeIcon: string;
}

interface IProfileStepperProps {
  handleStep: (step: number, isDisabled: boolean) => void;
  steps: IStepItems[];
  RenderContent: () => React.JSX.Element;
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
}

function MobileProfileStepper({ RenderContent, steps, handleStep, activeStep }: IProfileStepperProps) {
  const { t } = useTranslation();

  return (
    <>
      <div className="mr-4 xmd:mr-0 mt-4 mb-3">
        <Swiper
          dir="rtl"
          slidesPerView={"auto"}
          keyboard
          grabCursor
          spaceBetween={8}
          modules={[Navigation, Keyboard, Mousewheel]}
        >
          {steps?.map((item, index) => (
            <SwiperSlide key={item?.id} className="max-w-fit">
              <Tooltip
                placement="top-start"
                title={t("supplier.profile.stepperTooltip")}
                PopperProps={{
                  style: {
                    display: !item.isDisabled ? "none" : undefined
                  },
                  modifiers: [
                    {
                      name: "offset",
                      options: {
                        offset: [5, -5]
                      }
                    }
                  ]
                }}
              >
                <div
                  className={twMerge(
                    "flex items-center cursor-pointer gap-1.5  px-2.5 py-[7.5px] rounded-full bg-cards text-gray-600",
                    activeStep === index && "bg-purple-500 text-[white]"
                  )}
                  onClick={() => handleStep(index, item.isDisabled)}
                >
                  <Icon
                    className="size-5"
                    icon={item?.activeIcon && activeStep === index ? item?.activeIcon : item?.icon ?? ""}
                  />
                  <span className="text-body4-medium whitespace-nowrap">{item.title}</span>
                </div>
              </Tooltip>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      <div className="xmd:px-0 px-4 ">
        <Box className={twMerge("profilestepper-container")}>
          <Box className="profile__content-container">
            <Box className="profile__content-wrapper">
              <RenderContent />
            </Box>
          </Box>
        </Box>
      </div>
    </>
  );
}

export default MobileProfileStepper;
