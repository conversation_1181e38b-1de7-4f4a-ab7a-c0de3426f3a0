import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import { useGetMeQuery } from "@/store/apps/auth";
import { TMeResponse } from "@/store/apps/auth/types";
import { useSelector } from "@/store/hooks";
import { AppState } from "@/store/store";
import { Icon } from "@iconify/react";
import { Tooltip } from "@mui/material";
import { TFunction } from "i18next";
import { isBoolean } from "lodash";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React from "react";
import { useTranslation } from "react-i18next";
import { shallowEqual } from "react-redux";
import { twMerge } from "tailwind-merge";
import ProfileSideBarItem from "./ProfileSideBarItem";

export interface ISettings {
  id: number;
  icon: string;
  activeIcon?: string;
  title: string;
  onclick: () => void;
  path?: string;
  isExtra?: boolean;
  isDisabled?: boolean;
  showInSidebar?: boolean;
}

export interface ISteps {
  isDisabled?: boolean;
  icon: string;
  type?: string;
  activeIcon?: string;
  title: string;
  isExtra?: boolean;
}

export type accountTypeItem = {
  color: string;
  icon: string;
  title: string;
};

interface IProfileSidebarProps {
  title?: string;
  hasData: boolean;
  isLegal: boolean;
  // name?: string;
  contactNumber?: string;
  profileStatus?: string;
  activeStep?: number;
  steps?: Array<ISteps>;
  settings?: Array<ISettings>;
  handleStep: (step: number | undefined, isDisabled?: boolean) => void;
  accountTypeValue: (params: { t: TFunction<"translation", undefined> }) => Record<string, accountTypeItem>;
}

const ProfileSidebar: React.FC<IProfileSidebarProps> = ({
  title,
  hasData,
  isLegal,
  // name,
  contactNumber,
  profileStatus,
  steps,
  activeStep,
  handleStep,
  settings,
  accountTypeValue
}) => {
  const { t } = useTranslation();
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const settingType = searchParams.get("settingType");

  const extraItem = settings?.find(item => item?.isExtra);

  const me = useSelector(state => state?.Auth?.queries[`getMe(undefined)`]?.data, shallowEqual) as TMeResponse;
  const name = `${me?.data?.firstName ?? ""}  ${me?.data?.lastName ?? ""}`;

  return (
    <div className="flex flex-col h-full min-w-[270px] xmd:pl-4 border-l border-l-v2-border-primary bg-cards xmd:rounded-none rounded-lg xmd:p-0 p-6">
      {title && (
        <div className="pb-6 border-b border-b-v2-border-primary mb-6">
          <h4 className="text-subtitle-bold text-v2-content-primary">{title}</h4>
        </div>
      )}

      {hasData && (
        <>
          <div className="flex items-center justify-between ">
            <div className="flex items-center gap-4">
              <div className="size-12 rounded-full bg-v2-surface-action-light flex items-center justify-center">
                <Icon
                  icon={isLegal ? "solar:buildings-outline" : "solar:user-circle-outline"}
                  className="size-6 text-v2-content-on-action-2"
                />
              </div>
              <div className="flex flex-col">
                <span className="text-body2-medium text-v2-content-primary">{name}</span>
                <span className="text-caption-medium text-v2-content-tertiary">{contactNumber}</span>
              </div>
            </div>

            <div
              className={twMerge(
                "px-2 !text-caption-regular rounded-full border border-solid text-center",
                isLegal
                  ? "border-v2-border-active bg-v2-surface-action-disable"
                  : "border-gray-40 bg-warning-75 text-warning-800"
              )}
            >
              {isLegal ? t("legal") : t("real")}
            </div>
          </div>
          {profileStatus && (
            <div className="mt-6 p-2 bg-v2-surface-thertiary rounded-md flex flex-col">
              <span className="text-v2-content-tertiary text-caption-regular">{t("profileStatus")}</span>
              <div className="flex items-center gap-0.5">
                <span className="text-body3-medium text-v2-content-primary">
                  {accountTypeValue({ t })[profileStatus]?.title}
                </span>
                {/* {profileStatus === "Active" && ( */}
                <Icon
                  icon={accountTypeValue({ t })[profileStatus]?.icon}
                  style={{
                    color: accountTypeValue({ t })[profileStatus]?.color
                  }}
                  className="size-4 text-v2-content-on-success-2"
                />
                {/* )} */}
              </div>
            </div>
          )}
        </>
      )}

      <div className="mt-6 flex flex-col gap-2 pb-2">
        {steps?.map((item, index) => (
          <ProfileSideBarItem
            key={index}
            activeStep={activeStep}
            handleStep={handleStep}
            index={index}
            item={item}
            settingType={settingType}
          />
        ))}
      </div>

      {!!extraItem?.id && (
        <ProfileSideBarItem
          activeStep={activeStep}
          handleStep={handleStep}
          item={extraItem}
          settingType={settingType}
          onClick={() => {
            handleStep(undefined);
            extraItem?.onclick();
          }}
        />
      )}

      <div className="pt-4 border-t border-t-v2-border-primary">
        <div className="bg-v2-surface-secondary rounded-lg">
          {settings
            ?.filter(item => item?.showInSidebar && (typeof item?.isExtra === "undefined" || !item?.isExtra))
            ?.map(item => (
              <div
                key={item?.id}
                className="px-4 py-3.5 flex items-center gap-2 border-b border-b-v2-border-secondary last-of-type:border-b-transparent cursor-pointer"
                onClick={() => {
                  handleStep(undefined);
                  item?.onclick();
                }}
              >
                <Icon
                  icon={item?.icon}
                  className={twMerge(
                    "size-4 ",
                    settingType === item?.path ? "text-v2-content-on-action-2" : "text-v2-content-primary"
                  )}
                />
                <span
                  className={twMerge(
                    "!text-body4-medium",
                    settingType === item?.path ? "text-v2-content-on-action-2" : "text-v2-content-primary"
                  )}
                >
                  {item?.title}
                </span>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default WithBottomBar(ProfileSidebar);
