import React from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { twMerge } from "tailwind-merge";
import { Theme, Tooltip, useMediaQuery } from "@mui/material";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";
import { ISteps } from "./ProfileSidebar";

interface TooltipNavItemProps {
  item: ISteps;
  index?: number;
  activeStep?: number;
  settingType: string | null;
  onClick?: () => void;
  handleStep?: (step?: number, isDisabled?: boolean) => void;
}

const ProfileSideBarItem: React.FC<TooltipNavItemProps> = ({
  item,
  index,
  activeStep,
  settingType,
  handleStep,
  onClick
}) => {
  const { t } = useTranslation();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const handleClick = () => {
    if (onClick) {
      onClick();
      return;
    }

    router.push(pathname);
    handleStep?.(index, item?.isDisabled);
  };

  const isActiveSettingType = (type: string) => settingType === type;

  return (
    <Tooltip
      key={index}
      placement="top-start"
      title={t("supplier.profile.stepperTooltip")}
      PopperProps={{
        style: {
          display: !item.isDisabled ? "none" : undefined
        },
        modifiers: [
          {
            name: "offset",
            options: {
              offset: [5, -5]
            }
          }
        ]
      }}
    >
      <div className={twMerge(item.isExtra && "border-t border-t-v2-border-primary pt-2 pb-2")}>
        <div
          className={twMerge(
            "flex items-center justify-between p-4 cursor-pointer text-v2-content-primary rounded-lg xmd:hover:bg-v2-surface-action-light xmd:w-auto w-full",
            (settingType ? (item?.type || "").includes(settingType) : activeStep === index) &&
              "bg-transparent xmd:bg-v2-surface-action-light"
          )}
          onClick={handleClick}
        >
          <div className="flex items-center gap-2">
            <Icon
              className="size-5"
              icon={
                !isMobile &&
                (!!item?.isExtra && settingType ? (item?.type || "").includes(settingType) : activeStep === index)
                  ? item?.activeIcon || ""
                  : item?.icon ?? ""
              }
            />
            <span className="text-body4-medium whitespace-nowrap">{item.title}</span>
          </div>

          <Icon icon="solar:alt-arrow-left-outline" className="size-4 text-v2-content-subtle" />
        </div>
      </div>
    </Tooltip>
  );
};

export default ProfileSideBarItem;
