import React from "react";
import { Box, CircularProgress, Stack, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import useOtpCountdown from "./useOtpCountdown";

interface IOTPCountdownProps {
  initialTime: number;
  onRetry: () => void;
  isLoading?: boolean;
}

const OTPCountdown = ({ initialTime, onRetry, isLoading }: IOTPCountdownProps) => {
  const { t } = useTranslation();
  const { timeLeft, isRetryVisible, handleRetry, formatTime } = useOtpCountdown({
    initialTime,
    onRetry
  });

  return (
    <Box marginBlock={3}>
      {isLoading ? (
        <Stack direction="row" alignItems="center" justifyContent="center">
          <CircularProgress color="info" size={20} />
        </Stack>
      ) : isRetryVisible ? (
        <div onClick={handleRetry} className="h-5">
          <p className="text-center cursor-pointer text-body2-medium text-v2-content-on-action-2">
            {t("otpLogin.retryCode")}
          </p>
        </div>
      ) : (
        <p className="text-center text-v2-content-tertiary text-body1-regular h-5">
          {formatTime(timeLeft)} {t("otpLogin.prefixTime")}
        </p>
      )}
    </Box>
  );
};

export default OTPCountdown;
