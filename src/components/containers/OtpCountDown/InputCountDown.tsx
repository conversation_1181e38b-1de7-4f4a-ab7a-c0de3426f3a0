import React from "react";
import { CircularProgress, Divider, IconButton } from "@mui/material";
import useOtpCountdown from "./useOtpCountdown";
import Input from "@/components/ui/inputs/Input";
import { TInput } from "@/components/ui/inputs/Input/types";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";

interface IOTPCountdownProps extends TInput {
  initialTime: number;
  onRetry: () => void;
  onRequestOtp?: () => void;
  isLoading?: boolean;
  isRequested?: boolean;
  hint?: string;
}

const InputCountdown = ({
  initialTime,
  onRetry,
  isLoading,
  onRequestOtp,
  isRequested = false,
  hint,
  ...rest
}: IOTPCountdownProps) => {
  const { t } = useTranslation();
  const { timeLeft, isRetryVisible, handleRetry, formatTime } = useOtpCountdown({
    initialTime,
    onRetry
  });

  const EndAdornment = (
    <div className="flex items-center gap-1 ">
      <Divider
        orientation="vertical"
        variant="fullWidth"
        flexItem
        className="text-foreground-neutral-tertiary ml-1.5"
      />
      <div className="h-5 flex items-center gap-1">
        {!isRequested ? (
          <p
            className="text-v2-content-on-action-2  text-caption-medium whitespace-nowrap cursor-pointer"
            onClick={onRequestOtp}
          >
            {t("changePasswordForm.sendConfirmCode")}
          </p>
        ) : (
          <>
            {isLoading ? (
              <div className="flex items-center justify-center">
                <CircularProgress color="info" size={16} />
              </div>
            ) : (
              <p className="text-v2-content-tertiary text-caption-medium w-7">{formatTime(timeLeft)}</p>
            )}
            <IconButton disabled={!isRetryVisible} onClick={handleRetry} className="otp-login-retry-code-wrapper">
              <Icon icon="solar:history-outline" className="size-4" />
            </IconButton>
          </>
        )}
      </div>
    </div>
  );

  return (
    <>
      <Input {...rest} endAdornment={EndAdornment} inputParentClassName="xmd:h-12 h-10" />
      {hint && isRequested && <span className="text-v2-content-on-action-2 text-caption-medium">{hint}</span>}
    </>
  );
};

export default InputCountdown;
