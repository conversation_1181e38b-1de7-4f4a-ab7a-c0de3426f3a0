import Button from "@/components/ui/Button";
import { Icon } from "@iconify/react";
import Image from "next/image";
import React from "react";

function SupplierCard({
  cover,
  logo,
  title,
  categoryLabel
}: {
  cover?: string;
  logo?: string;
  title?: string;
  categoryLabel?: string;
}) {
  return (
    <div className="px-4 py-[15px] flex flex-col gap-2">
      {/* ---------------------------------- Image --------------------------------- */}
      <div className="relative">
        <div className="w-full relative items-center size-[218px] justify-center overflow-hidden flex bg-gray-20 rounded">
          {cover && <Image src={cover} alt={"_img"} fill className="object-cover" />}
        </div>

        {logo && (
          <Image
            src={logo}
            alt={"_img"}
            width={55}
            height={55}
            className="object-cover rounded-full absolute -bottom-7 left-3 border-4 border-[#ffffff]"
          />
        )}
      </div>

      <div className="flex flex-col gap-1">
        <div className="text-sm font-semibold text-v2-content-primary">{title}</div>
        <div className="text-[10px] font-medium text-v2-content-tertiary">{categoryLabel}</div>
      </div>

      <div className="border-t border-v2-border-primary pt-3">
        <Button
          fullWidth
          size="md"
          variant="secondaryGray"
          className="text-v2-content-on-info"
          startAdornment={<Icon icon="ph:plus" width={20} height={20} />}
        >
          دنبال کردن
        </Button>
      </div>
    </div>
  );
}

export default SupplierCard;
