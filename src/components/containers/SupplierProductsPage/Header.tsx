import Button from "@/components/ui/Button";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { useConversationStart } from "@/utils/hooks/useConversationStart";
import { Icon } from "@iconify/react";
import { Avatar, CircularProgress } from "@mui/material";
import Image from "next/image";
import React from "react";
import { useTranslation } from "react-i18next";

export type THeader = {
  id?: string;
  cover?: string;
  logo?: string;
  name?: string;
  bio?: string;
  productName?: string;
  isSupplierView?: boolean;
};

function Header({ id, cover, logo, name, productName, isSupplierView, bio }: THeader) {
  const { t } = useTranslation();
  const { onChatStart, isConversationLoading } = useConversationStart();

  const bindPerformanceScore = {
    0: t("retailerProduct.performanceScore.mid")
  };

  return (
    <div className="flex flex-col gap-4">
      {/* -------------------------------- top cover ------------------------------- */}
      <div className="relative w-full h-[350px] bg-[#F4F6FB] rounded-[10px] overflow-hidden">
        {cover ? (
          <Image fill src={cover} alt="cover" className="object-cover object-center" />
        ) : (
          <Image src="/images/placeholders/supplier-cover.svg" fill alt="cover" className="py-8" />
        )}
      </div>

      {/* ---------------------------------- row 1 --------------------------------- */}
      <div className="flex items-center gap-4">
        {/* --------------------------------- avatar --------------------------------- */}
        <Avatar src={logo} className="size-[75px] rounded-full shrink-0" />
        <div className="py-[7.5px] flex-1 flex flex-col md:flex-row items-center gap-2">
          <div className="flex-1 flex items-center gap-6">
            {/* ----------------------------- name & subtitle ---------------------------- */}
            <div className="flex flex-col items-start gap-1.5">
              <div className="flex flex-col gap-1">
                <div className="flex items-center">
                  <div className="text-gray-999 font-medium text-lg">{name}</div>
                  {isSupplierView && <Icon icon="bitcoin-icons:verify-filled" className="text-purple-500 size-6" />}
                </div>
                {/* {isSupplierView && <span className="text-body4-regular text-v2-content-tertiary">@{id}</span>} */}
              </div>

              {!isSupplierView && (
                <div
                  className="flex items-center gap-1.5 text-gray-400 font-medium text-sm cursor-pointer"
                  onClick={() => onChatStart({ content: t("chats.product", { name: productName }), partnerId: id })}
                >
                  {isConversationLoading ? (
                    <CircularProgress size={16} />
                  ) : (
                    <Icon icon="solar:chat-line-linear" width={16} height={16} />
                  )}

                  <div>{t("retailerProduct.connectWithSupplier")}</div>
                </div>
              )}
            </div>
            {/* ------------------------------- follow btn ------------------------------- */}
            {!isSupplierView && (
              <CustomButton size="small" variant="outlined" className="flex gap-1.5">
                <Icon icon="mage:bookmark-plus" width={16} height={16} />
                <div>{t("retailerProduct.followSupplier")}</div>
              </CustomButton>
            )}
          </div>
          <div className="py-1.5 flex gap-4 items-center shrink-0">
            {/* ---------------------------------- box 1 --------------------------------- */}
            <div className="flex items-center gap-2 flex-col justify-between">
              <div className="text-purple-500 font-bold text-lg flex items-center gap-0.5 mb-1.5">
                <Icon icon="solar:star-bold" className="text-purple-500 size-4" />

                <div className="">۰</div>
              </div>
              <div className="text-v2-content-tertiary text-[13px] font-medium">{t("retailerProduct.shopScore")}</div>
            </div>

            <div className="h-6 w-px shrink-0 bg-gray-50" />

            {/* ---------------------------------- box 2 --------------------------------- */}
            <div className="flex items-center gap-2 flex-col justify-between">
              <div className="text-gray-999 font-bold text-lg">۰</div>
              <div className="text-v2-content-tertiary text-[13px] font-medium">{t("retailerProduct.followers")}</div>
            </div>

            <div className="h-6 w-px shrink-0 bg-gray-50" />

            {/* ---------------------------------- box 3 --------------------------------- */}
            <div className="flex items-center gap-2 flex-col justify-between">
              <div className="text-gray-999 font-bold text-lg">-</div>
              <div className="text-v2-content-tertiary text-[13px] font-medium">{t("retailerProduct.shopPerformanceScore")}</div>
            </div>
          </div>
        </div>
      </div>

      {/* ---------------------------------- row 2 --------------------------------- */}
      <div className="flex items-center justify-between">
        <span className="text-body3-medium">{bio}</span>

        <Button
          variant="destructiveSecondaryGray"
          className="text-v2-content-secondary"
          startAdornment={<Icon icon="solar:undo-right-round-square-linear" className="size-5" />}
        >
          {t("retailerProduct.supplierReturnPolicy")}
        </Button>
      </div>

      <hr className="border-gray-50" />
    </div>
  );
}

export default Header;
