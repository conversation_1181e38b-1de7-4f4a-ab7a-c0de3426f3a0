import React, { useRef, useCallback, useEffect } from "react";
import { useSelector, useDispatch } from "@/store/hooks";
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import InputBase from "@mui/material/InputBase";
import Popover from "@mui/material/Popover";
import EmojiPicker, { EmojiStyle, EmojiClickData, Emoji } from "emoji-picker-react";
import { IconMoodSmile, IconSend } from "@tabler/icons-react";
import { sendMsg } from "@/store/apps/chat/ChatSlice";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";

interface IChatMsgSentProps {
  onSend: (v: { text: string }) => Promise<boolean>;
  isPostConversationError: boolean;
  isPostConversationLoading: boolean;
}

const ChatMsgSent = ({ onSend, isPostConversationError, isPostConversationLoading }: IChatMsgSentProps) => {
  const [msg, setMsg] = React.useState("");
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);
  const [chosenEmoji, setChosenEmoji] = React.useState("");
  const { t } = useTranslation();
  const inputRef = useRef<HTMLInputElement>(null);

  const onEmojiClick = useCallback((emojiData: EmojiClickData) => {
    setChosenEmoji(emojiData.unified);
    setMsg(emojiData.emoji);
  }, []);

  const handleClick = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  }, []);

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const id = useSelector(state => state.chatReducer.chatContent);

  const handleChatMsgChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setMsg(e.target.value);
  }, []);

  const newMsg = { id, msg };

  const onChatMsgSubmit = useCallback(
    (e: any) => {
      e.preventDefault();
      e.stopPropagation();
      dispatch(sendMsg(newMsg));
      setMsg("");
      setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    },
    [dispatch, newMsg]
  );

  const handleSendClick = useCallback(() => {
    onSend({ text: newMsg.msg }).then(v => {
      if (v) {
        setMsg("");
        setTimeout(() => {
          inputRef.current?.focus();
        }, 200);
      }
    });
  }, [onSend, newMsg]);

  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  return (
    <Box className="bg-cards xmd:static xmd:p-4 p-[15px] xmd:border-none border-t border-transparent border-t-[#111C2D1A] border-solid  fixed bottom-0 left-0 right-0 z-[99999]  ">
      <form onSubmit={onChatMsgSubmit} style={{ display: "flex", gap: "10px", alignItems: "center" }}>
        <IconButton
          aria-label="more"
          id="long-button"
          aria-controls="long-menu"
          aria-expanded="true"
          aria-haspopup="true"
          onClick={handleClick}
        >
          <IconMoodSmile width={24} height={24} />
        </IconButton>
        <Popover
          id="long-menu"
          className="z-[999999]"
          anchorEl={anchorEl}
          keepMounted
          open={Boolean(anchorEl)}
          onClose={handleClose}
          anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
          transformOrigin={{ horizontal: "right", vertical: "bottom" }}
        >
          <EmojiPicker onEmojiClick={onEmojiClick} />
          <Box p={2}>
            Selected: {chosenEmoji ? <Emoji unified={chosenEmoji} emojiStyle={EmojiStyle.APPLE} size={22} /> : ""}
          </Box>
        </Popover>
        <InputBase
          inputRef={inputRef}
          id="msg-sent"
          fullWidth
          value={msg}
          className="bg-[#FCFAFA] rounded-md p-[9px] text-sm"
          placeholder={`${t("chats.typeNewMessage")}...`}
          size="small"
          type="text"
          inputProps={{ "aria-label": t("chats.typeNewMessage") }}
          onChange={handleChatMsgChange}
          onKeyDown={event => {
            if (event.code === "Enter") {
              onSend({ text: newMsg.msg }).then(v => {
                if (v) {
                  setMsg("");
                  setTimeout(() => {
                    inputRef.current?.focus();
                  }, 200);
                }
              });
            }
          }}
        />

        <IconButton
          aria-label="delete"
          tabIndex={-1}
          onClick={handleSendClick}
          disabled={!msg}
          className="text-[#111C2D99]"
        >
          {isPostConversationLoading ? (
            <CircularProgress size={20} />
          ) : isPostConversationError && !!msg ? (
            <Icon icon="solar:refresh-outline" />
          ) : (
            <IconSend stroke={1.5} size="20" />
          )}
        </IconButton>
      </form>
    </Box>
  );
};

export default React.memo(ChatMsgSent);
