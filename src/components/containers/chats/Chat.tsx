import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import "./chat.css";

import { Avatar, Grid, ListItemAvatar, ListItemButton, ListItemText, Skeleton, Typography } from "@mui/material";
import { MouseEvent } from "react";
import useLanguage from "@/utils/hooks/useLanguage";
import { useTranslation } from "react-i18next";
import { Message, Partner } from "@/store/apps/conversation/types";

interface IChatComponentProps {
  onClick?: (e: MouseEvent) => void;
  selected?: boolean;
  loading?: boolean;
  chat?: snakeCaseToCamelCase<Partner>;
  lastChat?: snakeCaseToCamelCase<Message>;
  date?: string;
}

const ChatComponent = ({ onClick, selected, lastChat, loading, date, chat }: IChatComponentProps) => {
  const { t } = useTranslation();
  const [{ timeDistance }] = useLanguage();

  return loading ? (
    <Grid p={1} container direction="row" gap={1}>
      <Skeleton variant="circular" width={40} height={40} />
      <Grid item width="initial" flexGrow={1} container direction="column" gap={1}>
        <Skeleton variant="text" id="sx-chat-207" />
        <Skeleton variant="text" id="sx-chat-208" />
      </Grid>
    </Grid>
  ) : (
    <div className="border-b border-solid border-b-gray-50 last:border-transparent">
      <ListItemButton
        onClick={onClick}
        id="sx-chat-213"
        className=" xmd:!px-3 rounded-lg !mb-0"
        classes={{
          selected: "!bg-gray-20"
        }}
        selected={selected}
      >
        <ListItemAvatar>
          <Avatar alt={chat?.name} src={chat?.avatar} id="sx-chat-222" />
        </ListItemAvatar>
        <ListItemText
          primary={
            <div className="flex flex-col">
              <Typography className="text-[15px] font-medium" mb={0.25}>
                {chat?.name}
              </Typography>
              {!!lastChat?.content && (
                <Typography className="truncate max-w-[120px] text-caption-regular text-gray-500">
                  {lastChat?.ours ? t("you") + ":" : lastChat?.user?.firstName + ":"} {lastChat?.content}
                </Typography>
              )}
            </div>
          }
          id="sx-chat-231"
        />
        {!!lastChat?.sendAt && <Typography className="text-gray-400 text-xs text-end"> {timeDistance(new Date(lastChat?.sendAt)) + " "}</Typography>}
      </ListItemButton>
    </div>
  );
};

export default ChatComponent;
