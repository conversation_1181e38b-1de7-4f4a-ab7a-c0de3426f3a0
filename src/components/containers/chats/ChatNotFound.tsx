import { Box, Stack, Typography } from "@mui/material";
import Image from "next/image";
import { useTranslation } from "react-i18next";

function ChatNotFound() {
  const { t } = useTranslation();
  return (
    <Box className="flex items-center justify-center w-full h-full">
      <Stack alignItems="center">
        <Image src="/images/chat/no-message.png" alt="not-found" width={190} height={190} />
        <span className="text-h5-bold text-v2-content-primary mt-6 mb-2 text-center">{t("chatNotfound")}</span>
        <span className="text-body3-medium text-v2-content-tertiary text-center">{t("startChat")}</span>
      </Stack>
    </Box>
  );
}

export default ChatNotFound;
