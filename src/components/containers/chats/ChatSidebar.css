#sx-chatsidebar-577 {
  flex-shrink: 0;
  max-height: 100%;
  /* padding-top: 8px; */
}
.sx-chatsidebar-598 {
  flex-grow: 1;
}
#sx-chatsidebar-577 .MuiDrawer-paper {
  position: relative;
  border: 1px solid rgb(var(--color-gray-50));
  border-radius: 8px;
}
#sx-chatsidebar-586 {
  padding-inline-end: 16px;
  padding-inline-start: 16px;
  padding-top: 0;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  border: 1px solid rgb(var(--color-gray-50));
  border-radius: 8px;
}

.chat-sidebar-input {
  padding-top: 8px;
}

@media (max-width: 768px) {
  #sx-chatsidebar-586 {
    border: none;
    padding-inline-end: 0;
    padding-inline-start: 0;
  }
  .chat-sidebar-input {
    padding: 0 8px;
  }
}
