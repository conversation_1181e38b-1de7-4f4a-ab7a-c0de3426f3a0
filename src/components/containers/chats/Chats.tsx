"use client";
import "./Chats.css";
import React, { useRef } from "react";
import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";
import ChatSidebar from "@/components/containers/chats/ChatSidebar";
import ChatContent, { IChatContentRef, TSelectedChat } from "@/components/containers/chats/ChatContent";
import ChatMsgSent from "@/components/containers/chats/ChatMsgSent";

import { CircularProgress, Stack, Theme, Typography, useMediaQuery } from "@mui/material";
import { TRetailerChatData } from "@/store/apps/retailer/types";
import CustomCardContent from "../../ui/CustomCard/CustomCard";
import { useSearchParams } from "next/navigation";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import { usePostConversationMessageMutation } from "@/store/apps/conversation";
import { ConversationPayloadResponse } from "@/store/apps/conversation/types";

interface IChatsProps {
  chats?: snakeCaseToCamelCase<ConversationPayloadResponse>;
  isChatsLoading?: boolean;
  selected?: TSelectedChat;
  // chat?: snakeCaseToCamelCase<ConversationMessageResponse>;
  // isChatLoading?: boolean;
}

export const Chats = ({ chats, isChatsLoading, selected }: IChatsProps) => {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const [postMessage, { isError: isPostConversationError, isLoading: isPostConversationLoading }] =
    usePostConversationMessageMutation();

  const chatId = searchParams?.get("chatId");

  // const selected = chats?.data?.find(item => item?.id === Number(chatId));

  const contentsRef = useRef<IChatContentRef>({
    addNewMessage: () => {}
  });
  const onSend = async ({ text }: { text: string }) => {
    if ((selected?.supplier || selected?.chat) && text) {
      const reciverId = selected.chat?.partnerId;
      if (!reciverId) return false;

      let chat: TRetailerChatData | null = null;
      postMessage({
        body: {
          content: text,
          contentType: "Text",
          partnerId: reciverId as string
        },
        id: chatId as string
      });

      if (chat) {
        contentsRef.current.addNewMessage(chat);
      }
      return false;
    }
    return false;
  };

  const RenderChatContent = () => {
    // if (isChatLoading) {
    //   return (
    //     <Box display="flex" height="100%" justifyContent="center" alignItems="center">
    //       <CircularProgress size={30} />
    //     </Box>
    //   );
    // }
    return (
      <Stack className="h-full ">
        <ChatContent ref={contentsRef} selected={selected} />
        {Boolean(selected) && <Divider className="xmd:block hidden" />}
        {Boolean(selected) && (
          <ChatMsgSent
            onSend={onSend}
            isPostConversationError={isPostConversationError}
            isPostConversationLoading={isPostConversationLoading}
          />
        )}
      </Stack>
    );
  };

  if (isChatsLoading) {
    return (
      <CustomCardContent className=" flex h-[75dvh] xmd:h-[84dvh] items-center justify-center">
        <CircularProgress />
      </CustomCardContent>
    );
  }

  if (!chats?.data?.length && !chatId && !isChatsLoading) {
    return (
      <div className="xmd:p-0 px-4">
        <CustomCardContent className=" flex h-dvh xmd:h-[84dvh] items-center justify-center">
          <Stack className="items-center">
            <Image src="/images/chat/no-conversation.png" alt="startChat" width={190} height={199} />
            <span className="text-v2-content-primary text-h5-bold  mt-6 mb-2 text-center">{t("noChat.title")}</span>
            <span className="text-v2-content-tertiary text-body3-medium text-center ">{t("noChat.subtitle")}</span>
          </Stack>
        </CustomCardContent>
      </div>
    );
  }

  if (isMobile) {
    return (
      <Box
        sx={{
          height: "calc(100dvh - 65px)"
        }}
      >
        {chatId ? (
          <div className="px-4 bg-cards ">
            <RenderChatContent />
          </div>
        ) : (
          <div className="px-4 py-2">
            <CustomCardContent>
              <ChatSidebar selected={selected} isChatsLoading={isChatsLoading} chats={chats} />
            </CustomCardContent>
          </div>
        )}
      </Box>
    );
  }

  return (
    <>
      <CustomCardContent className="h-[84dvh]">
        <Stack flexDirection="row" height="100%">
          <ChatSidebar selected={selected} isChatsLoading={isChatsLoading} chats={chats} />
          <Box flex={1} flexDirection="column" display="flex" height="100%">
            {!!chatId ? (
              <RenderChatContent />
            ) : (
              <Box className="flex items-center justify-center h-full">
                <Stack className="items-center">
                  <Image src="/images/chat/no-message.png" alt="startChat" width={190} height={190} />
                  <span className="text-h5-bold text-v2-content-primary mt-6 mb-2 text-center">
                    {t("initialAlertChat")}
                  </span>
                  <span className="text-v2-content-tertiary text-body3-medium text-center">
                    {t("initialAlertChatSubtitle")}
                  </span>
                </Stack>
              </Box>
            )}
          </Box>
        </Stack>
      </CustomCardContent>
    </>
  );
};
