import "./ChatSidebar.css";

import React, { useState } from "react";
import { Theme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import Box from "@mui/material/Box";
import List from "@mui/material/List";
import Scrollbar from "../../ui/custom-scroll/Scrollbar";
import ChatComponent from "./Chat";
import { useTranslation } from "react-i18next";
import { TSelectedChat } from "./ChatContent";
import EmptyList from "../EmptyList/EmptyList";
import { CircularProgress, InputAdornment } from "@mui/material";
import { IconSearch } from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import { ConversationPayloadResponse } from "@/store/apps/conversation/types";
import Input from "@/components/ui/inputs/Input";
import { Icon } from "@iconify/react";

interface chatType {
  selected?: TSelectedChat;
  onSelect?: (t: TSelectedChat) => void;
  chats?: snakeCaseToCamelCase<ConversationPayloadResponse>;
  isChatsLoading?: boolean;
}

const ChatSidebar = ({ selected, chats, isChatsLoading }: chatType) => {
  const router = useRouter();
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const drawerWidth = isMobile ? "100%" : "320px";

  const [query, setQuery] = useState("");

  return (
    <Box width={drawerWidth}>
      <List id="sx-chatsidebar-586">
        <Box className="py-2">
          <Input
            inputParentClassName="!max-h-10"
            placeholder={t("chats.searchQuery")}
            value={query}
            endAdornment={
              <InputAdornment position="end">
                <Icon icon="solar:magnifer-outline" className="size-4 text-v2-content-tertiary" />
              </InputAdornment>
            }
            onChange={e => setQuery(e.target.value)}
          />
        </Box>

        <Scrollbar className="sx-chatsidebar-598">
          {isChatsLoading && new Array(3).fill(0).map((_, index) => <ChatComponent key={index} loading />)}
          {chats?.data?.length ? (
            chats?.data
              ?.filter(item => item.partner?.name.includes(query))
              ?.map(chat => (
                <ChatComponent
                  key={chat.id}
                  date={chat?.createdAt}
                  chat={chat.partner}
                  lastChat={chat.lastMessage}
                  onClick={() => {
                    router.push(makePath(routes.chat) + `?chatId=${chat?.id}`);
                  }}
                  selected={chat.id === selected?.chat?.id}
                />
              ))
          ) : (
            <div className="m-4 flex items-center justify-center">{!isChatsLoading && <CircularProgress />}</div>
          )}
        </Scrollbar>
      </List>
    </Box>
  );
};

export default ChatSidebar;
