#sx-index-8092:hover .MuiListItemIcon-root {
  color: white;
}

#sx-index-8092 a {
  text-decoration: none;
}
#sx-index-8092 {
  white-space: nowrap;
  margin-block-end: 8px;
  /* padding: 5px 10px 5px 0; */
  border-radius: 6px;
}

#sx-index-8092:before {
  content: "";
  position: absolute;
  inset-block-start: 0;
  inset-block-end: 0;
  inset-inline-end: 0;
  height: 100%;
  z-index: -1;
  border-radius: 6px;
  transition: all 0.3s ease-in-out;
  width: 0;
}
#sx-index-8092 > .MuiListItemText-root {
  font-size: 16px !important;
}
#sx-index-8092:hover::before {
  width: calc(100%);
}
#sx-index-8092 > .MuiListItemIcon-root {
  width: 45px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  /* margin-right: 8px; */
  transition: all 0.3s ease-in-out;
}
#sx-index-8092:hover {
  background-color: transparent !important;
}
#sx-index-8092.Mui-selected {
  background-color: transparent !important;
}

/* #sx-index-8092:hover:before {
  background-color: rgb(var(--color-purple-50));
} */

#sx-index-8092:before {
  width: 100%;
}

#sx-index-8092:hover {
  color: rgb(var(--color-purple-500));
}

#sx-index-8092 {
  color: var(--mui-palette-grey-999);
}

#sx-index-8092:hover .MuiListItemIcon-root {
  color: rgb(var(--color-purple-500));
}
#sx-index-8092 .MuiListItemIcon-root {
  color: var(--mui-palette-grey-900);
}

#sx-index-8092:hover::before {
  background-color: rgb(var(--color-cards));
}
#sx-index-8092.Mui-selected {
  color: rgb(var(--color-purple-500));
}
#sx-index-8092.Mui-selected .MuiListItemIcon-root {
  color: rgb(var(--color-purple-500));
}
#sx-index-8092.Mui-selected:hover {
  color: var(--mui-palette-grey-700);
}
#sx-index-8092.Mui-selected:hover span {
  color: rgb(var(--color-purple-500));
}

/* #sx-index-8092.Mui-selected:hover:before {
  color: rgb(var(--color-purple-500));
} */

#sx-index-8092.Mui-selected:before,
#sx-index-8092:hover:before {
  background-color: rgb(var(--color-purple-50));
}

#sx-index-8092.Mui-selected:before,
#sx-index-8092:hover svg {
  color: rgb(var(--color-purple-500)) !important;
}

.nav-icon {
  min-width: 36px;
  padding: 3px 0;
}
#sx-index-8137 {
  width: 6px;
  height: 6px;
}

.sidebar-nav-item-subtitle {
  font-size: 14px;
  font-weight: 500;
}

#sx-index-8149 .MuiTypography-root {
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
}

.sidebar-item-link {
  text-decoration: none;
}

.child-items {
  padding: 0;
  border-inline-start: 1px solid var(--mui-palette-grey-200);
  margin-inline-start: var(--mui-theme-spacing-2);
}
.accordion {
  box-shadow: none;
  color: var(--mui-palette-grey-900);
}
.accordion.active .nav-icon,
.accordion.active .sidebar-accordion-title {
  color: rgb(var(--color-purple-500));
}

.accordion.active {
  background-color: var(--mui-palette-grey-50);
}

.accordion-header {
  position: relative;
}

.accordion-header-count .MuiAccordionSummary-expandIconWrapper {
  width: 24px;
  margin-inline-end: 30px;
}

.accordion-header .MuiAccordionSummary-content {
  display: flex;
  align-items: center;
}

.accordion:not(.active) .accordion-header:hover {
  background-color: rgb(var(--color-cards));
}

.accordion .MuiAccordionDetails-root {
  padding-inline-start: 24px;
  margin-inline-start: 24px;
  border-color: rgb(var(--color-gray-50));
}

.sidebar-accordion-list,
.sidebar-accordion-list-active {
  margin-bottom: 8px;
}

.sidebar-accordion-list-active .MuiPaper-root {
  background: rgb(var(--color-purple-40));
  border-radius: 8px;
}

.accordion .sidebar-item-link #sx-index-8092 {
  padding-block: 0 !important;
}

.child-items .sidebar-item-link #sx-index-8092 {
  margin-bottom: 0;
}

.accordion #sx-index-8092 .MuiTypography-root {
  padding-inline: 8px;
  padding-block: 8px;
  border-radius: 5px;
}

.accordion .Mui-selected .MuiTypography-root {
  background-color: rgb(var(--color-purple-50));
  /* padding-inline: 8px;
  padding-block: 8px;
  border-radius: 5px; */
}

.accordion #sx-index-8092:hover .MuiTypography-root {
  background-color: rgb(var(--color-purple-50));
}

.accordion.active .MuiTypography-root {
  color: rgb(var(--color-purple-500));
  padding-inline: 8px;
}

.accordion .Mui-selected .sidebar-item-link {
  color: rgb(var(--color-purple-500));
}
