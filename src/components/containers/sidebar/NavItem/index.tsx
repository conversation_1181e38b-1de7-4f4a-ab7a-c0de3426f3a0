import "./index.css";

import React from "react";
import Link from "next/link";
import { Icon } from "@iconify/react";

// mui imports
import { Accordion, AccordionDetails, AccordionSummary, Box } from "@mui/material";
import { Chip } from "@mui/material";
import { List } from "@mui/material";
import { ListItemButton } from "@mui/material";
import { ListItemIcon } from "@mui/material";
import { ListItemText } from "@mui/material";
import { Theme } from "@mui/material/styles";
import { Typography } from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import { styled } from "@mui/material/styles";
import { useTranslation } from "react-i18next";
import { KeyboardArrowDown } from "@mui/icons-material";
import { usePathname, useSearchParams } from "next/navigation";
import clsx from "clsx";
import { twMerge } from "tailwind-merge";

type NavGroup = {
  [x: string]: any;
  id?: string;
  navlabel?: boolean;
  subheader?: string;
  title?: string;
  icon?: any;
  href?: any;
  children?: NavGroup[];
  chip?: string;
  chipColor?: any;
  variant?: string | any;
  external?: boolean;
  level?: number;
  count?: number;
  onClick?: React.MouseEvent<HTMLButtonElement, MouseEvent>;
};

interface ItemType {
  item: NavGroup;
  onClick: (event: React.MouseEvent<HTMLElement>) => void;
  hideMenu?: any;
  level?: number | any;
  pathDirect: string;
  isActive?: boolean;
}

export default function NavItem({ item, level, pathDirect, hideMenu, onClick, isActive }: ItemType) {
  const lgDown = useMediaQuery((theme: Theme) => theme.breakpoints.down("lg"));
  //const Icon = item?.icon;
  const { t } = useTranslation();
  const selected = isActive;

  const path = usePathname();
  const searchParams = useSearchParams();
  const pathname = `${path}${searchParams.toString() ? `?${searchParams.toString()}` : ""}`;

  //const itemIcon = level > 1 ? <Icon size={24} /> : <Icon size="1.5rem" />;

  const ListItemStyled = styled(ListItemButton)(() => ({
    paddingLeft: hideMenu ? "0" : level > 2 ? `${level * 15}px` : level > 1 ? "10px" : "0",
    backgroundColor: level > 1 ? "transparent !important" : "inherit",
    fontWeight: level > 1 && selected ? "600 !important" : "400"
  }));
  111;
  const mountedIcon = (
    <>
      {item?.icon ? (
        <ListItemIcon className="nav-icon">
          <Icon
            icon={isActive ? item?.selectedIcon : item?.icon}
            className={twMerge(isActive ? "text-v2-content-on-action-2" : "text-v2-content-primary")}
            width="24"
            height="24"
          />
        </ListItemIcon>
      ) : (
        <Box className="opacity-0 " />
      )}
    </>
  );

  const listItem = (
    <ListItemStyled
      // {...listItemProps}
      disabled={item?.disabled}
      selected={selected}
      onClick={lgDown ? onClick : undefined}
      id="sx-index-8092"
      sx={{
        "&.Mui-selected": {
          "& .MuiTypography-root": {
            fontWeight: level > 1 ? "600 !important" : 400
          }
        }
      }}
    >
      {mountedIcon}
      <ListItemText id="sx-index-8149">
        {hideMenu ? "" : <>{t(`${item?.title}`)}</>}
        <br />
        {item?.subtitle ? (
          <Typography variant="caption" className="sidebar-nav-item-subtitle">
            {hideMenu ? "" : item?.subtitle}
          </Typography>
        ) : (
          ""
        )}
      </ListItemText>

      {!item?.chip || hideMenu ? null : (
        <Chip
          color={item?.chipColor}
          variant={item?.variant ? item?.variant : "filled"}
          size="small"
          label={item?.chip}
        />
      )}
    </ListItemStyled>
  );

  return (
    <List
      component="li"
      disablePadding
      key={item?.id && item.title}
      className={selected ? "sidebar-accordion-list-active" : "sidebar-accordion-list"}
    >
      {item.children && item.children.length ? (
        <Accordion className={"accordion" + (selected ? " active" : "")} defaultExpanded={selected}>
          <AccordionSummary
            expandIcon={
              <div className="sidebar-expanded-icon">
                <div className="w-3" />
                <KeyboardArrowDown className={selected ? "text-purple-500" : undefined} />
              </div>
            }
            aria-controls="panel1-content"
            className={clsx("accordion-header", !!item.count && "accordion-header-count")}
            sx={{ paddingLeft: "12px" }}
          >
            {!!item?.count && (
              <div className="absolute left-4 w-6 h-6 rounded-full bg-purple-50 text-purple-500 text-body4-regular flex items-center justify-center ">
                {item?.count}
              </div>
            )}
            {mountedIcon}
            <Typography className="text-gray-999 text-body3-medium">{item.title}</Typography>
          </AccordionSummary>
          <AccordionDetails className="child-items">
            {item.children.map(item => (
              <NavItem
                key={item?.id}
                isActive={item?.href === path}
                item={item}
                hideMenu={hideMenu}
                pathDirect={pathDirect}
                onClick={() => {}}
              />
            ))}
          </AccordionDetails>
        </Accordion>
      ) : (
        <Link href={item.href} className="sidebar-item-link">
          {listItem}
        </Link>
      )}
    </List>
  );
}
