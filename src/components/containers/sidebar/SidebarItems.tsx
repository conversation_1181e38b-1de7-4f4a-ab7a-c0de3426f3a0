import "./SidebarItems.css";

import { usePathname } from "next/navigation";
import { Box, Typography, useTheme } from "@mui/material";
import { List } from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import NavItem from "./NavItem";
import { useTranslation } from "react-i18next";
import { MenuitemsType } from "./MenuItems";
import { Dispatch, SetStateAction } from "react";

interface ISidebarItemsProps {
  setIsMobileSidebar?: Dispatch<SetStateAction<boolean>>;
  isMobileSidebar?: boolean;
  isHover: boolean;
  isOpen: boolean;
  setIsOpen?: (val: boolean) => void;
  items: MenuitemsType[];
  isActive: (item: MenuitemsType) => boolean;
}

const SidebarItems = ({ isOpen, isHover, items, isActive, setIsOpen, setIsMobileSidebar }: ISidebarItemsProps) => {
  const pathname = usePathname();
  const pathDirect = pathname;
  const lgUp = useMediaQuery((theme: any) => theme.breakpoints.up("lg"));
  const hideMenu: any = lgUp ? isOpen && !isHover : "";
  const { t } = useTranslation();
  const theme = useTheme();

  const Menuitems = items;

  return (
    <Box className="sx-sidebaritems-7677">
      <Typography variant="caption" color={theme.palette.grey["400"]} className="sidebar-extra-items-header">
        {t("mainMenu")}
      </Typography>
      <List id="sx-sidebaritems-7678" className="sidebarNav">
        {Menuitems.filter(item => item.isMobile === undefined || item.isMobile === false).map(item => {
          return (
            <NavItem
              isActive={isActive(item)}
              item={item}
              key={item.id}
              pathDirect={pathDirect}
              hideMenu={hideMenu}
              onClick={() => {
                setIsMobileSidebar?.(prev => !prev);
                setIsOpen?.(false);
              }}
            />
          );
        })}
      </List>
    </Box>
  );
};
export default SidebarItems;
