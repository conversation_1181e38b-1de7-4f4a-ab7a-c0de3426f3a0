import CustomButton from "@/components/ui/CustomButton/CustomButton";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";

function ContactInfo() {
  const { t } = useTranslation();

  return (
    <div className="bg-v2-surface-secondary p-4 rounded-lg ">
      <Icon className="size-5 text-v2-content-tertiary" icon="solar:help-outline" />
      <p className="text-v2-content-primary mt-2 text-body4-medium">{t("contactPhoneTitle")}</p>
      <p className="text-caption-medium text-v2-content-tertiary my-2 ">{t("contactPhoneSubtitle")}</p>
      <div className="rounded-lg border border-spacing-0.5 border-v2-border-secondary py-2 px-3 text-body4-medium text-v2-content-primary bg-v2-surface-primary w-fit flex flex-col gap-1">
        <span>021-26711705</span>
        <span>021-26711707</span>
      </div>
    </div>
  );
}

export default ContactInfo;
