import AclWrapper, { Role } from "@/components/containers/AclWrapper";
import { ADMIN_USER_TYPE, RETAILER_USER_TYPE, SUPPLIER_USER_TYPE } from "@/constants";
import ListSubheader from "@mui/material/ListSubheader";
import { Theme } from "@mui/material/styles";
import { styled } from "@mui/material/styles";
import { IconDots } from "@tabler/icons-react";
import React from "react";

type NavGroup = {
  navlabel?: boolean;
  subheader?: string;
  for?: Array<Role> | Role;
};

interface ItemType {
  item: NavGroup;
  hideMenu: string | boolean;
}

const NavGroup = ({ item, hideMenu }: ItemType) => {
  const ListSubheaderStyle = styled((props: Theme | any) => <ListSubheader disableSticky {...props} />)(
    ({ theme }) => ({
      ...theme.typography.overline,
      fontWeight: "700",
      marginTop: theme.spacing(3),
      marginBottom: theme.spacing(1),
      color: theme.palette.text.Primary,
      opacity: "0.50",
      lineHeight: "26px",
      padding: "3px 20px",
      marginLeft: hideMenu ? "" : "-10px"
    })
  );

  return (
    <AclWrapper for={item.for || ([SUPPLIER_USER_TYPE, ADMIN_USER_TYPE, RETAILER_USER_TYPE] as Array<Role>)}>
      <ListSubheaderStyle>{hideMenu ? <IconDots size="14" /> : item?.subheader}</ListSubheaderStyle>
    </AclWrapper>
  );
};

export default NavGroup;
