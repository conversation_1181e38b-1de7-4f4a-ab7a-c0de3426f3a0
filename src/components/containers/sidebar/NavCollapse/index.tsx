import "./index.css";

import React from "react";
import { Icon } from "@iconify/react";
import { useState } from "react";
import { usePathname } from "next/navigation";

// mui imports
import { Box } from "@mui/material";
import { Collapse } from "@mui/material";
import { ListItemButton } from "@mui/material";
import { ListItemIcon } from "@mui/material";
import { ListItemText } from "@mui/material";
import { Theme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import { styled, useTheme } from "@mui/material/styles";

// custom imports
import NavItem from "../NavItem";
import { isNull } from "lodash";

// plugins
import { useTranslation } from "react-i18next";
import AclWrapper, { Role } from "@/components/containers/AclWrapper";
import { ADMIN_USER_TYPE, RETAILER_USER_TYPE, SUPPLIER_USER_TYPE } from "@/constants";

type NavGroupProps = {
  [x: string]: any;
  navlabel?: boolean;
  subheader?: string;
  title?: string;
  icon?: any;
  href?: any;
};

interface NavCollapseProps {
  menu: NavGroupProps;
  level: number;
  pathWithoutLastPart: any;
  pathDirect: any;
  hideMenu: any;
  onClick: (event: React.MouseEvent<HTMLElement>) => void;
}

type NavItem = {
  children?: Array<NavItem>;
  for: Role | Array<Role>;
  id: string | number;
};

// FC Component For Dropdown Menu
export default function NavCollapse({
  menu,
  level,
  pathWithoutLastPart,
  pathDirect,
  hideMenu,
  onClick
}: NavCollapseProps) {
  const lgDown = useMediaQuery((theme: Theme) => theme.breakpoints.down("lg"));

  // const Icon = menu?.icon;
  const theme = useTheme();
  const pathname = usePathname();
  const { t } = useTranslation();
  const [open] = useState(true);

  const ListItemStyled = styled(ListItemButton)(() => ({
    marginBottom: "2px",
    padding: "5px 10px 5px 0",
    paddingLeft: hideMenu ? "0" : level > 2 ? `${level * 15}px` : level > 1 ? "10px" : "0",
    whiteSpace: "nowrap",
    "&:before": {
      content: '""',
      position: "absolute",
      top: 0,
      bottom: 0,
      left: "-20px",
      height: "100%",
      zIndex: "-1",
      borderRadius: " 0 24px 24px 0",
      transition: "all .3s ease-in-out",
      width: open && level < 2 ? "calc(100% + 20px)" : "0",
      backgroundColor: open && level < 2 ? theme.palette.primary.light : ""
    },
    "&:hover::before": {
      width: "calc(100% + 20px)",
      backgroundColor: theme.palette.primary.light
    },
    ".MuiListItemIcon-root": {
      width: 45,
      height: 40,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      borderRadius: "8px",
      marginRight: "8px",
      transition: "all .3s ease-in-out"
      // color: theme.palette.primary.main,
      // backgroundColor: theme.palette.primary.light,
    },
    "&:hover": {
      backgroundColor: pathname.includes(menu.href) || open ? theme.palette.primary.light : "transparent"
    },

    // color:
    //   open && level < 2
    //     ? theme.palette.text.primary
    //     : `inherit` && level > 1 && open
    //     ? theme.palette.primary.main
    //     : theme.palette.text.secondary,
    borderRadius: " 0 24px 24px 0"
  }));

  // If Menu has Children
  const submenus = menu.children?.map((item: any) => {
    if (item.children) {
      return (
        <NavCollapse
          key={item?.id}
          menu={item}
          level={level + 1}
          pathWithoutLastPart={pathWithoutLastPart}
          pathDirect={pathDirect}
          hideMenu={hideMenu}
          onClick={onClick}
        />
      );
    } else {
      return (
        <AclWrapper for={item.for || [SUPPLIER_USER_TYPE, ADMIN_USER_TYPE, RETAILER_USER_TYPE]} key={item.id}>
          <NavItem
            item={item}
            level={level + 1}
            pathDirect={pathDirect}
            hideMenu={hideMenu}
            onClick={lgDown ? onClick : isNull}
          />
        </AclWrapper>
      );
    }
  });

  return (
    <>
      <ListItemStyled
        selected={pathWithoutLastPart === menu.href}
        key={menu?.id}
        id="sx-index-7889"
        sx={{
          "&:hover::before": {
            backgroundColor: menu.bgcolor + ".light"
          },

          "&:before": {
            backgroundColor: open && level < 2 ? menu.bgcolor + ".light" : ""
          },
          background: open ? "rgb(99, 66, 255)" + "!important" : undefined
        }}
      >
        <ListItemIcon id="sx-index-7916">
          {level < 2 ? (
            <Icon icon={menu.icon} width="24" height="24" />
          ) : (
            <Box
              id="sx-index-7926"
              sx={{
                opacity: level > 1 && pathWithoutLastPart === menu.href ? 1 : "0.3",
                backgroundColor:
                  level > 1 && pathWithoutLastPart === menu.href
                    ? `${theme.palette.primary.main}!important`
                    : theme.palette.text.secondary
              }}
            />
          )}
        </ListItemIcon>
        <ListItemText color="inherit">{hideMenu ? "" : <>{t(`${menu.title}`)}</>}</ListItemText>
        {/* {!open ? <IconChevronDown size="1rem" /> : <IconChevronUp size="1rem" />} */}
      </ListItemStyled>
      <Collapse in={open} timeout="auto" id="sx-index-7944">
        {submenus}
      </Collapse>
    </>
  );
}
