import { RETAILER_USER_TYPE, SUPPLIER_USER_TYPE } from "@/constants";
import { routes } from "@/constants/routes";
import i18n from "@/utils/i18n";
import { uniqueId } from "lodash";
export interface MenuitemsType {
  [x: string]: any;
  id?: string;
  name?: string;
  navlabel?: boolean;
  subheader?: string;
  title?: string;
  icon?: string;
  selectedIcon?: string;
  href: string;
  extraIsActives?: string[];
  children?: MenuitemsType[];
  bgcolor?: any;
  chip?: string;
  isMobile?: boolean;
  chipColor?: string;
  variant?: string;
  external?: boolean;
}

// import { useTheme } from '@mui/material';
// const theme = useTheme();
type TMenuItemsInput = { makePath: (href: string) => string; count?: number };
export const fixedMenuItems = ({ makePath }: TMenuItemsInput) => {
  return [
    {
      id: uniqueId(),
      name: "notifAndChat",
      title: "settings.support",
      icon: "solar:chat-round-line-outline",
      selectedIcon: "solar:chat-round-line-bold-duotone",
      bgcolor: "primary",
      href: makePath(routes.chat),
      for: [RETAILER_USER_TYPE, SUPPLIER_USER_TYPE]
    },
    {
      id: uniqueId(),
      name: "accountInfo",
      title: "settings.profile",
      icon: "solar:user-circle-outline",
      selectedIcon: "solar:user-circle-bold-duotone",
      bgcolor: "primary",
      href: `${makePath(routes.profile)}?step=0`,
      for: [RETAILER_USER_TYPE, SUPPLIER_USER_TYPE]
    }
  ];
};

const supplierMenuItems = ({ makePath }: TMenuItemsInput): MenuitemsType[] => [
  {
    id: uniqueId(),
    name: "home",
    title: i18n.t("Dashboard"),
    icon: "solar:home-angle-outline",
    selectedIcon: "solar:home-angle-bold-duotone",
    bgcolor: "primary",
    for: [SUPPLIER_USER_TYPE],
    href: makePath(routes.home),
    isMobile: false
  },

  {
    id: uniqueId(),
    name: "home",
    title: i18n.t("home"),
    icon: "solar:home-angle-outline",
    selectedIcon: "solar:home-angle-bold-duotone",
    bgcolor: "primary",
    for: [SUPPLIER_USER_TYPE],
    href: makePath(routes.home),
    isMobile: true
  },

  {
    id: uniqueId(),
    name: "products",
    title: i18n.t("products"),
    icon: "solar:box-outline",
    selectedIcon: "solar:box-bold-duotone",
    bgcolor: "primary",
    href: makePath(routes.product),
    for: [SUPPLIER_USER_TYPE]
  },

  {
    id: uniqueId(),
    name: "orders",
    title: i18n.t("orders"),
    icon: "solar:bag-5-outline",
    selectedIcon: "solar:bag-5-bold-duotone",
    bgcolor: "primary",
    for: [SUPPLIER_USER_TYPE],
    href: makePath(routes.order)
  },
  {
    id: uniqueId(),
    name: "notifAndChat",
    isMobile: true,
    title: i18n.t("settings.messages"),
    icon: "solar:chat-round-line-outline",
    selectedIcon: "solar:chat-round-line-bold-duotone",
    bgcolor: "primary",
    for: [SUPPLIER_USER_TYPE],
    href: makePath(routes.chat)
  },
  {
    id: uniqueId(),
    name: "profile",
    title: i18n.t("profile"),
    icon: "solar:user-circle-outline",
    selectedIcon: "solar:user-circle-bold-duotone",
    bgcolor: "primary",
    href: `${makePath(routes.profile)}`,
    isMobile: true,
    for: [SUPPLIER_USER_TYPE]
  }
];

const retailerMenuItems = ({ makePath, count }: TMenuItemsInput): MenuitemsType[] => [
  {
    id: uniqueId(),
    name: "home",
    title: i18n.t("home"),
    icon: "solar:home-angle-outline",
    selectedIcon: "solar:home-angle-bold-duotone",
    bgcolor: "primary",
    for: [RETAILER_USER_TYPE],
    href: makePath(routes.home),
    isMobile: true
  },
  {
    id: uniqueId(),
    name: "marketplace",
    title: i18n.t("marketplaceMobile"),
    icon: "solar:shop-outline",
    selectedIcon: "solar:shop-bold-duotone",
    bgcolor: "primary",
    href: makePath(routes.product),
    for: [RETAILER_USER_TYPE],
    isMobile: true
  },
  {
    id: uniqueId(),
    name: "home",
    title: i18n.t("Dashboard"),
    icon: "solar:home-angle-outline",
    selectedIcon: "solar:home-angle-bold-duotone",
    bgcolor: "primary",
    for: [RETAILER_USER_TYPE],
    href: makePath(routes.home),
    isMobile: false
  },
  {
    id: uniqueId(),
    name: "products",
    title: i18n.t("products"),
    icon: "solar:box-outline",
    selectedIcon: "solar:box-bold-duotone",
    bgcolor: "primary",
    href: `${makePath(routes.retailerProductsDrafts)}`,
    extraIsActives: [`${makePath(routes.retailerProductsDrafts)}`, `${makePath(routes.retailerProductsImports)}`],
    for: [RETAILER_USER_TYPE],
    isMobile: true
  },
  {
    id: uniqueId(),
    name: "marketplace",
    title: i18n.t("marketplace"),
    icon: "solar:shop-outline",
    selectedIcon: "solar:shop-bold-duotone",
    bgcolor: "primary",
    href: makePath(routes.product),
    for: [RETAILER_USER_TYPE],
    isMobile: false
  },

  {
    id: uniqueId(),
    name: "products",
    title: i18n.t("products"),
    icon: "solar:box-outline",
    selectedIcon: "solar:box-bold-duotone",
    bgcolor: "primary",
    href: "",
    for: [RETAILER_USER_TYPE],
    isMobile: false,
    count,
    children: [
      {
        id: uniqueId(),
        name: "drafts",
        title: i18n.t("product.drafts"),
        href: `${makePath(routes.retailerProductsDrafts)}`,
        for: [RETAILER_USER_TYPE]
      },
      {
        id: uniqueId(),
        name: "importeds",
        title: i18n.t("product.importeds"),
        href: `${makePath(routes.retailerProductsImports)}`,
        for: [RETAILER_USER_TYPE]
      }
    ]
  },
  {
    id: uniqueId(),
    name: "orders",
    title: i18n.t("orders"),
    icon: "solar:bag-5-outline",
    selectedIcon: "solar:bag-5-bold-duotone",
    bgcolor: "primary",
    for: [RETAILER_USER_TYPE],
    href: makePath(routes.order)
  },
  // {
  //   id: uniqueId(),
  //   name: "chats",
  //   isMobile: false,
  //   title: i18n.t("chats.chats"),
  //   icon: "solar:chat-round-line-bold-duotone",
  //   selectedIcon: "solar:chat-round-line-bold",
  //   bgcolor: "primary",
  //   for: [RETAILER_USER_TYPE],
  //   href: makePath(routes.chat),

  // },

  // {
  //   id: uniqueId(),
  //   name: "subscription",
  //   title: i18n.t("subscription.selectPlan"),
  //   icon: "ion:pricetags-bold-duotone",
  //   selectedIcon: "ion:pricetags-bold-duotone",
  //   bgcolor: "primary",
  //   for: [RETAILER_USER_TYPE],
  //   href: makePath(routes.subscription),
  //   isMobile: false
  // },

  {
    id: uniqueId(),
    name: "accountInfo",
    title: i18n.t("profile"),
    icon: "solar:user-circle-outline",
    selectedIcon: "solar:user-circle-bold-duotone",
    bgcolor: "primary",
    for: [RETAILER_USER_TYPE],
    href: `${makePath(routes.profile)}`,
    isMobile: true
  }
];

export const menuitems = ({ makePath, count }: TMenuItemsInput): MenuitemsType[] => [
  ...supplierMenuItems({ makePath, count }),
  ...retailerMenuItems({ makePath, count })
];
