#sx-sidebar-7500 {
  z-index: 2000;
  flex-shrink: 0;
  border-radius: 13px;
}

#sx-sidebar-7544 {
  margin-inline-end: var(--mui-theme-spacing-1);
  position: relative;
  text-align: center;
}

#sx-sidebar-7554 {
  border-radius: 0 !important;
}

.MuiChip-root {
  margin-block: var(--mui-theme-spacing-2);
}

#sx-sidebar-7610 {
  border-radius: 0 !important;
  border: none;
  margin: 0;
  text-align: center;
}

.sidebar-header-title-text {
  font-size: 16px;
  font-weight: 900;
}

.sidebar-header-title-text-open {
  font-size: 30px;
  font-weight: 900;
}

.sidebar-header-title {
  color: rgb(var(--color-purple-500));
  text-decoration: none;
  text-align: inherit;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sidebar-header-title-chip {
  margin-top: 26px;
  margin-bottom: 50px;
  background-color: #f3fdff;
  color: #00add7;
}

.sidebar-paper {
  width: var(--drop-hub-sidebar-size);
  border: 0 !important;
  box-shadow: unset;
  background: rgb(var(--color-cards));
  color: var(--mui-palette-gray-700);
}

.lg-sidebar-paper {
  transition: width 100ms;
  overflow: hidden !important;
  border-right: 0;
  box-sizing: border-box;
  inset-block-start: calc(var(--mui-theme-spacing) * 2.5);
  inset-inline-start: calc(var(--mui-theme-spacing) * 2.5);
  inset-block-end: calc(var(--mui-theme-spacing) * 2.5);
  border-radius: 8px;
  height: calc(100% - 48px);
  background: rgb(var(--color-cards));
  color: var(--mui-palette-gray-700);
}

/* @media screen and (min-height: 900px) {
  .lg-sidebar-paper {
    height: 850px;
  }
}

@media screen and (min-height: 950px) {
  .lg-sidebar-paper {
    height: 900px;
  }
} */

@media screen and (min-height: 1000px) {
  .sidebar-container {
    /* height: 950px; */
    min-height: auto !important;
  }
}

@media screen and (max-width: 768px) {
  #sx-customizer-6749 {
    inset-block-end: 70px !important;
  }
}
