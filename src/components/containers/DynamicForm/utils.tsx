/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { FormElementData, InputType } from "@/store/apps/meta/types";
import * as yup from "yup";
import i18next from "i18next";
import Cookies from "js-cookie";
import { STORE_ID } from "@/constants/cookies";
import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import CircleWithButtonImageUploader from "@/components/containers/ImageUploader/withUi/CircleWithButtonImageUploader";
import { Divider } from "@mui/material";
import Input from "@/components/ui/inputs/Input";
import NumberInput from "@/components/ui/inputs/NumberInput";
import PasswordInput from "@/components/ui/inputs/PasswordInput";

export function sortConfigFormByPosition(configForm: { [key: string]: FormElementData }): {
  [key: string]: FormElementData;
} {
  const sortedEntries = Object.entries(configForm).sort(([, a], [, b]) => (a.position ?? 0) - (b.position ?? 0));

  return Object.fromEntries(sortedEntries);
}

export const TextFieldComponent = ({ field, form, setFieldValue, ...props }: any) => (
  <Input
    {...field}
    {...props}
    autoComplete="off"
    aria-autocomplete="off"
    dir={props?.isLtr && "ltr"}
    inputParentClassName="relative"
    startAdornment={
      props?.append ? (
        <div dir="ltr" className=" w-[100px] truncate  pl-2 ml-3 ">
          <span className=" text-body4-medium w-full">{props?.append}</span>
          <Divider orientation="vertical" className="absolute top-0 right-[90px] " />
        </div>
      ) : null
    }
    endAdornment={
      props?.prepend ? (
        <div dir="ltr" className=" w-36 truncate pr-1 mr-3 ">
          <span className=" text-body4-medium w-full ">{props?.prepend}</span>
          <Divider orientation="vertical" className="absolute top-0 left-[160px] " />
        </div>
      ) : props?.addonType === "icon" ? (
        <div className="mr-2" dangerouslySetInnerHTML={{ __html: props?.addon }} />
      ) : props?.addon ? (
        <span className="text-body4-bold mr-2">{props?.addon}</span>
      ) : undefined
    }
  />
);

export const NumberFieldComponent = ({ field, form, onChange, ...props }: any) => (
  <NumberInput
    {...field}
    {...props}
    autoComplete="off"
    inputParentClassName="relative"
    onChange={({ target: { value } }) => props?.setFieldValue(field.name, value)}
    startAdornment={
      props?.append ? (
        <div dir="ltr" className=" w-[100px] truncate  pl-2 ml-3 ">
          <span className=" text-body4-medium w-full">{props?.append}</span>
          <Divider orientation="vertical" className="absolute top-0 right-[90px] " />
        </div>
      ) : null
    }
    endAdornment={
      props?.prepend ? (
        <div dir="ltr" className=" w-36 truncate pr-1 mr-3 ">
          <span className=" text-body4-medium w-full">{props?.prepend}</span>
          <Divider orientation="vertical" className="absolute top-0 left-[148px] " />
        </div>
      ) : null
    }
    dir={props?.isLtr && "ltr"}
  />
);

export const PasswordFieldComponent = ({ field, form, ...props }: any) => (
  <PasswordInput dir={props?.isLtr && "ltr"} {...field} {...props} />
);

export const RadioFieldComponent = ({ field, form, ...props }: any) => <CustomRadio {...field} {...props} />;

export const CheckBoxFieldComponent = ({ field, form, ...props }: any) => <CustomCheckbox {...field} {...props} />;

export const FileFieldComponent = ({ field, form, setFieldValue, ...props }: any) => (
  <>
    <CircleWithButtonImageUploader
      serverFileKind="public"
      value={field.value}
      onUploaded={v => {
        setFieldValue("logo", v.url);
        form.setFieldError("logo", "");
      }}
      onError={msg => {
        form.setFieldTouched("logo", true);

        setTimeout(() => {
          form.setFieldError("logo", i18next.t(`${msg}`));
        }, 0);
      }}
      onRemove={() => {
        setFieldValue("logo", "");
        setTimeout(() => {
          form.setFieldTouched("logo", true);
        }, 0);
      }}
      wrapperClassName="rounded-md"
      containerClassName="xmd:flex-row flex-col"
      cropperProps={{ aspect: 1 }}
      withCompressorMaxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
      maxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
      buttonLabel={
        field.value ? i18next.t("store.storeInfoForm.changePhoto") : i18next.t("store.storeInfoForm.uploadProfile")
      }
      className="w-fit xmd:mx-0 mx-auto"
    />

    {!!props?.error && props?.helperText && <InputHelper> {props?.helperText}</InputHelper>}
  </>
);

export const getValidationSchema = (configForm: { [key: string]: FormElementData }, isSupplier?: boolean) => {
  const schema: { [key: string]: any } = {};

  for (const key in configForm) {
    const field = configForm[key];
    let validation = yup.string();

    if (field.type === InputType.Email) {
      validation = yup.string().email(i18next.t("validations.email"));
    }

    if (field.required) {
      validation = validation.required(i18next.t("validations.requiredField"));
    }

    if (field.requiredIfNotPresent && !schema[field.requiredIfNotPresent]) {
      validation = validation.when(field.requiredIfNotPresent, {
        is: (value: any) => !value,
        then: yup.string().required(i18next.t("validations.requiredField"))
      });
    }

    if (isSupplier && (field as any).type === "percent") {
      validation = validation.test("percent-range", i18next.t("validations.percentRange"), value => {
        return !!value && +value >= 0 && +value <= 100;
      });
    }

    if (field.max) {
      validation = validation.max(field.max, i18next.t("validations.max", { number: field.max }));
    }

    if (field.min) {
      validation = validation.min(field.min, i18next.t("validations.min", { number: field.min }));
    }

    if (field?.validationRegex) {
      validation = validation.matches(new RegExp(field.validationRegex), i18next.t("validations.invalidFormat"));
    }

    schema[key] = validation;
  }

  return yup.object().shape(schema);
};

export const storeId = () => {
  const id = Cookies.get(STORE_ID);

  if (id) return id;
  return "";
};
