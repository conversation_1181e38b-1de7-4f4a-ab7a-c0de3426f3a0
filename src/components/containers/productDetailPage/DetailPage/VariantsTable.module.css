.table th {
  background-color: rgb(var(--color-purple-50));
  padding: 0 10.5px;
  font-size: 13px;
  font-weight: 500;
  color: rgb(var(--color-gray-999));
  height: 40px;
}

.table td:first-child {
  width: 40px;
}

.table td:not(:first-child) > div {
  width: 10vw;
  min-width: 150px;
}

.table th:first-child {
  border: 1px solid rgb(var(--color-purple-50));
  border-top-right-radius: 8px;
  border-inline-end: none;
}

.table th:last-child {
  border: 1px solid rgb(var(--color-purple-50));
  border-top-left-radius: 8px;
  border-inline-start: none;
}

.table th:not(:last-child):not(:first-child) {
  border-block: 1px solid rgb(var(--color-purple-50));
}

.table td {
  vertical-align: top;
  background-color: rgb(var(--color-gray-20));
  padding: 8px;
}

.table td:first-child {
  border-right: 1px solid rgb(var(--color-gray-20));
  vertical-align: middle;
}

.table td:last-child {
  border-left: 1px solid rgb(var(--color-gray-20));
}
