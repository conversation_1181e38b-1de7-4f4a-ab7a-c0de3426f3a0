import { IProductPayLoad } from "@/store/apps/product/types";
import useModal from "@/utils/hooks/useModal";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Theme, useMediaQuery } from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import styles from "./VariantsTable.module.css";
import { Icon } from "@iconify/react";
import useCurrency from "@/utils/hooks/useCurrency";

function VariantsTable({ hasVariant, variants }: { hasVariant?: boolean; variants?: IProductPayLoad["variants"] }) {
  const { t } = useTranslation();
  const { hideModal } = useModal();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };

  if (isMobile) {
    return (
      <div>
        <div className="flex items-center justify-between mb-4">
          <div className="text-sm font-medium text-gray-600">{t("retailerProduct.variants")}</div>
          <Icon icon="iconamoon:close" width={20} height={20} onClick={hideModal} className="cursor-pointer" />
        </div>

        <div className="flex flex-col divide-y divide-gray-50 gap-2">
          {variants?.map(variant => {
            return (
              <div key={variant.id} className="pt-2 first:pt-0">
                <div>
                  {variant?.options
                    ? Object.entries(variant.options)
                        .map(item => `${item?.[0]}: ${item?.[1]}`)
                        .join(" | ")
                    : "-"}
                </div>
                <div className="flex items-center justify-between">
                  <div className="text-xs font-medium flex items-center">
                    <div className="text-gray-500">{t("product.inventory")}: </div>
                    <div className="text-gray-999">{variant?.inventory !== undefined ? variant?.inventory : "-"}</div>
                  </div>
                  <div className="text-xs font-medium flex items-center">
                    <div className="text-gray-500">{t("product.price")}: </div>
                    <div className="text-gray-999">
                      {variant?.retailPrice ? renderPrice(variant?.retailPrice) : "-"}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <div className="text-sm font-medium text-gray-600">{t("retailerProduct.variants")}</div>
        <Icon icon="iconamoon:close" width={20} height={20} onClick={hideModal} className="cursor-pointer" />
      </div>

      <TableContainer>
        <Table className={styles.table} stickyHeader aria-label="sticky table">
          <TableHead>
            <TableRow>
              {hasVariant && (
                <TableCell width={500} className="!text-purple-500 text-[13px]">
                  {t("title")}
                </TableCell>
              )}
              <TableCell className="!text-purple-500 text-[13px]">{t("inventory")}</TableCell>
              <TableCell className="!text-purple-500 text-[13px]">{t("product.price")}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {variants?.map((variant, index) => {
              return (
                <TableRow className="" key={"--" + index}>
                  {hasVariant && (
                    <TableCell width={500} className="text-[13px]">
                      {variant?.options
                        ? Object.entries(variant.options)
                            .map(item => `${item?.[0]}: ${item?.[1]}`)
                            .join(" | ")
                        : "-"}
                    </TableCell>
                  )}
                  <TableCell width={150} className="text-[13px]">
                    {variant?.inventory !== undefined ? variant?.inventory : "-"}
                  </TableCell>
                  <TableCell width={150} className="text-[13px]">
                    {variant?.retailPrice ? renderPrice(variant?.retailPrice) : "-"}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
}

export default VariantsTable;
