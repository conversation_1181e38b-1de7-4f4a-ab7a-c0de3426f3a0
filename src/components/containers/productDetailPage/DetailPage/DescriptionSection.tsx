import CustomTabsFilled from "@/components/ui/CustomTabsFilled/CustomTabsFilled";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

export type TDescriptionSection = {
  description?: string;
  returnPolicy?: string;
};

function DescriptionSection({ description, returnPolicy }: TDescriptionSection) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("products");

  const tabsItems = [
    {
      id: 1,
      title: t("retailerProduct.productDetailTab"),
      value: "products"
    },
    {
      id: 2,
      title: t("retailerProduct.returnPolicy"),
      value: "return"
    }
  ];

  return (
    <div className="flex gap-4 flex-col">
      <CustomTabsFilled items={tabsItems} value={activeTab} onChange={v => setActiveTab(v)} />

      <div className="flex-1 w-full">
        {activeTab === "products" && description && <div dangerouslySetInnerHTML={{ __html: description }} />}
        {activeTab === "return" && returnPolicy && <div dangerouslySetInnerHTML={{ __html: returnPolicy }} />}
      </div>
    </div>
  );
}

export default DescriptionSection;
