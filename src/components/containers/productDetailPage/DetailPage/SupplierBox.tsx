import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { useTranslation } from "react-i18next";

function SupplierBox({ id, name, cover }: { id?: string; name?: string; cover?: string }) {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();

  return (
    <div className="flex flex-col gap-4 justify-center self-stretch border-2 border-v2-border-primary rounded-lg p-4">
      {/* --------------------------------- top row -------------------------------- */}
      <div className="flex gap-4 justify-between">
        <div className="flex gap-2 items-start self-stretch flex-1">
          <div className="relative size-[42px] rounded-lg overflow-hidden">
            {cover ? (
              <Image fill src={cover} alt="cover" className="object-cover object-center" />
            ) : (
              <Image src="/images/placeholders/supplier-avatar.svg" fill alt="cover" className="" />
            )}
          </div>
          <div className="flex flex-col gap-0.5">
            <div className="flex items-center gap-0.5">
              <div className="text-sm font-semibold text-v2-content-primary">{name}</div>
              <div className="text-v2-content-on-info-2">
                <Icon icon="solar:verified-check-bold" width={16} height={16} />
              </div>
            </div>
            <div className="text-v2-content-on-success-2 font-normal text-xs">
              {t("retailerProduct.supplierActive")}
            </div>
          </div>
        </div>
        <div className="flex gap-2 items-center shrink-0">
          <CustomButton
            component={Link}
            href={id ? `${makePath(routes.supplierProducts(id))}` : ""}
            color="secondary"
            size="small"
            className="!py-2 !px-3.5"
          >
            {t("retailerProduct.visitSupplierShop")}
          </CustomButton>
          <CustomButton color="primary" size="small" className="!py-2 !px-3.5">
            {t("retailerProduct.followSupplier")}
          </CustomButton>
        </div>
      </div>

      {/* ------------------------------- bottom row ------------------------------- */}
      <div className="flex gap-4 justify-between items-center">
        <div className="flex items-center gap-1 text-xs font-normal text-v2-content-tertiary">
          <Icon icon="solar:star-outline" width={14} height={14} />
          <div>
            {t("retailerProduct.shopScore")}: <span className="text-v2-content-primary font-medium">۰</span>
          </div>
        </div>
        <div className="flex items-center gap-1 text-xs font-normal text-v2-content-tertiary">
          {/* <Icon icon="solar:star-outline" width={14} height={14} /> */}
          <div>
            {t("retailerProduct.followers")}: <span className="text-v2-content-primary font-medium">۰</span>
          </div>
        </div>
        <div className="flex items-center gap-1 text-xs font-normal text-v2-content-tertiary">
          {/* <Icon icon="solar:star-outline" width={14} height={14} /> */}
          <div>
            {t("retailerProduct.shopPerformanceScore")}: <span className="text-v2-content-primary font-medium">۰</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SupplierBox;
