import { TSupplierShippingData } from "@/store/apps/supplier/types";
import useCurrency from "@/utils/hooks/useCurrency";
import useLocations from "@/utils/hooks/useLocations";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import ShippingTableContent from "./ShippingTableContent";
import ImportListButton from "@/components/containers/ImportListButton/ImportListButton";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { useConversationStart } from "@/utils/hooks/useConversationStart";
import { IProductPayLoad } from "@/store/apps/product/types";
import { makeOptionsBasedOnVariants } from "@/components/forms/product-form/OptionVariantsFrom/utils";
import Image from "next/image";
import Button from "@/components/ui/Button";

export type TInfoSectionProps = {
  productId?: string;
  inventory?: number;
  title?: string;
  supplier?: {
    id?: string;
    name?: string;
  };
  category?: {
    name?: string;
  };
  startPrice?: number | string;
  shippingPolicies?: Pick<
    TSupplierShippingData,
    "shippingTime" | "shippingTo" | "rate" | "shippingCarrier" | "rateType"
  >[];
  hasVariant?: boolean;
  variants?: IProductPayLoad["variants"];
  isSupplierView?: boolean;
  imported: boolean;
  retailerProfitAmount?: number;
  retailerProfitPercent?: number;
};

function InfoSection(props: TInfoSectionProps) {
  const { t } = useTranslation();
  const [{ render: renderPrice, symbol }] = useCurrency();
  const { getLocation: findLocation } = useLocations();
  const { onChatStart, isConversationLoading } = useConversationStart();

  const {
    productId,
    title,
    supplier,
    category,
    startPrice,
    shippingPolicies,
    variants,
    hasVariant,
    inventory,
    imported,
    isSupplierView,
    retailerProfitAmount,
    retailerProfitPercent
  } = props || {};

  const variantOptions = useMemo(
    () => (!!variants?.length ? makeOptionsBasedOnVariants(variants as any) : undefined),
    [JSON.stringify(variants)]
  );

  return (
    <div className="w-full h-full flex flex-col gap-2">
      {/* ----------------------------- title, category ---------------------------- */}
      <div className="flex justify-between self-stretch gap-2 pb-4 border-b border-b-v2-border-secondary">
        <div className="flex flex-col gap-2 flex-1">
          <div className="text-lg font-semibold text-v2-content-primary">{title}</div>

          <div className="flex items-center gap-3">
            <div className="text-v2-content-tertiary font-normal text-[13px]">
              {t("retailerProduct.category")}:{" "}
              <span className="font-medium text-v2-content-primary">{category?.name}</span>
            </div>
            {/* <div className="text-v2-content-tertiary font-normal text-[13px]">
              {t("retailerProduct.inventory")}: <span className="font-medium text-v2-content-primary">{inventory}</span>
            </div> */}
          </div>
        </div>

        <div className="flex flex-col gap-3 border-b border-b-v2-border-secondary">
          <div className="flex gap-1 items-center cursor-pointer">
            <Icon icon="solar:heart-linear" className="size-6 text-v2-content-secondary" />
            <div className="text-v2-content-secondary text-xs font-medium">{t("retailerProduct.favorite")}</div>
          </div>
          {!!retailerProfitPercent && (
            <div className="flex items-stretch justify-start">
              <div className="bg-v2-content-on-action-2 py-1 px-2 text-v2-content-on-action-1 flex flex-col items-center justify-center border-l border-v2-content-on-action-disable rounded-r-[3px]">
                <div className="text-sm font-bold leading-[10px]">{retailerProfitPercent}</div>
                <div className="text-[8px] leading-[10px] font-medium">{t("retailerProduct.profitPercent")}</div>
              </div>
              <div className="bg-v2-surface-action-light rounded-l-[3px] px-1.5 py-1 flex items-start flex-col justify-center">
                <div className="text-[8px] leading-3 font-semibold text-v2-content-on-action-2">
                  {t("retailerProduct.saleCommision1")} <br />
                  {t("retailerProduct.saleCommision2")}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* --------------------------------- prices --------------------------------- */}
      <div className="pt-2 pb-2 flex flex-col gap-2 border-b border-b-v2-border-secondary">
        <div className="flex flex-col gap-1">
          <div className="text-v2-content-tertiary text-xs font-normal">{t("retailerProduct.price")}</div>
          <div className="text-v2-content-primary text-2xl font-semibold">
            {renderPrice(startPrice, { showSymbol: false })}{" "}
            <span className="text-v2-content-tertiary text-xs font-medium">{symbol}</span>
          </div>
        </div>
        {!!retailerProfitAmount && (
          <div className="flex flex-col gap-1">
            <div className="text-v2-content-tertiary text-xs font-normal">
              {t("retailerProduct.retailerProfit_commision")}
            </div>
            <div className="text-v2-content-primary text-2xl font-semibold bg-v2-surface-success-2 w-fit">
              {renderPrice(retailerProfitAmount, { showSymbol: false })}{" "}
              <span className="text-v2-content-tertiary text-xs font-medium">{symbol}</span>
            </div>
          </div>
        )}
      </div>

      {/* -------------------------------- variants -------------------------------- */}
      {!!variantOptions?.length && (
        <div className="pt-4 pb-4 border-b border-b-v2-border-secondary flex flex-col gap-4">
          {variantOptions?.map(option => (
            <div key={option?.name} className="flex items-center gap-2">
              <div className="w-24 text-v2-content-primary text-[13px] leading-6">{option?.name}</div>

              {option?.values?.map((item, index) => (
                <div
                  key={index}
                  className="px-3 py-1 rounded bg-v2-surface-thertiary text-v2-content-primary text-[13px] leading-5 font-normal"
                >
                  {item}
                </div>
              ))}
            </div>
          ))}
        </div>
      )}

      {/* ----------------------------- shipping table ----------------------------- */}
      <div className="pt-4 flex flex-col gap-4 mb-2  ">
        <div className="font-medium text-sm text-v2-content-primary flex items-center gap-1">
          <Image src="/images/svgs/truck.svg" alt="" height={16} width={16} /> {t("retailerProduct.shippingTitle")}
        </div>
        <div className="w-full">
          <ShippingTableContent shippingPolicies={shippingPolicies} findLocation={findLocation} />
        </div>
      </div>

      {/* --------------------------------- buttons -------------------------------- */}
      {!isSupplierView && (
        <div className="flex items-center gap-2.5 ">
          {productId && (
            <ImportListButton
              variant="contained"
              color="primary"
              imported={imported}
              renderImported={t("productItem.addedToWhishlist")}
              productId={productId}
              size="small"
              className="shrink-0 flex-1 !py-[11px] !px-4"
            >
              {t("productItem.addToDraftlist")}
            </ImportListButton>
          )}
          <Button variant="secondaryGray" size="lg" className="shrink-0 flex-1">
            {t("retailerProduct.buySample")}
          </Button>
          {supplier?.id && (
            <Button
              variant="secondaryGray"
              size="lg"
              className="shrink-0 flex-1"
              onClick={() => onChatStart({ content: t("chats.product", { name: title }), partnerId: supplier?.id })}
              disabled={isConversationLoading}
            >
              {isConversationLoading ? <CircularProgress size={16} /> : t("retailerProduct.contactWithSupplier")}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

export default InfoSection;
