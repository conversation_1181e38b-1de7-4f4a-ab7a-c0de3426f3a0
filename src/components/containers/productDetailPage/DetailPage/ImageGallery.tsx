import { IProductPayLoad } from "@/store/apps/product/types";
import Image from "next/image";
import React, { useState } from "react";
import { twMerge } from "tailwind-merge";
import PerfectScrollbar from "react-perfect-scrollbar";

export type TImageGalleryProps = {
  images?: IProductPayLoad["cover"][];
};

function ImageGallery({ images }: TImageGalleryProps) {
  const [selectedImg, setSelectedImg] = useState(images?.find(a => a.markedAsCover) || images?.[0]);

  return (
    <div className="w-full h-full flex gap-4">
      {/* -------------------------------------------------------------------------- */
      /*                                 thumbnails                                 */
      /* -------------------------------------------------------------------------- */}
      <PerfectScrollbar>
        <div className="w-[50px] flex flex-col gap-2 h-full">
          {images?.map(img => {
            const finalImageUrl = img?.sizes?.thumbnail || img?.url;
            const isSelected = selectedImg?.url === img.url;

            return (
              <div
                key={finalImageUrl}
                className={twMerge(
                  "relative h-[50px] w-[50px] shrink-0 border border-transparent cursor-pointer rounded-md",
                  isSelected ? "border-2 border-v2-border-active" : ""
                )}
              >
                <Image
                  src={finalImageUrl}
                  alt={img.alt || `${finalImageUrl}_img`}
                  onClick={() => setSelectedImg(img)}
                  fill
                  className="object-cover rounded-md overflow-hidden"
                />
              </div>
            );
          })}
        </div>
      </PerfectScrollbar>

      {/* -------------------------------------------------------------------------- */
      /*                                  main IMG                                  */
      /* -------------------------------------------------------------------------- */}
      <div className="flex-1 h-full">
        {selectedImg && (
          <div
            key={selectedImg?.sizes?.medium || selectedImg?.url}
            className="relative w-full h-full rounded-xl overflow-hidden"
          >
            <Image
              src={selectedImg?.sizes?.medium || selectedImg?.url}
              alt={selectedImg.alt || `${selectedImg?.sizes?.medium || selectedImg?.url}_img`}
              fill
              className="object-contain"
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default ImageGallery;
