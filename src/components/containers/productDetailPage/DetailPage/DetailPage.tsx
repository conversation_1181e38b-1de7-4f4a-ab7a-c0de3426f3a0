"use client";

import { useGetProductQuery } from "@/store/apps/product";
import { CircularProgress, Theme } from "@mui/material";
import { Box, useMediaQuery } from "@mui/system";
import { useParams } from "next/navigation";
import { useTranslation } from "react-i18next";
import EmptyPage from "@/components/containers/RetailerProduct/EmptyPage";
import ImageGallery from "./ImageGallery";
import InfoSection from "./InfoSection";
import DescriptionSection from "./DescriptionSection";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import DetailPageMobile from "../DetailPageMobile.tsx/DetailPageMobile";
import VariantsTable from "./VariantsTable";
import useModal from "@/utils/hooks/useModal";
import CustomBreadcrumb, { CustomBreadCrumbItemsType } from "@/components/ui/CustomBreadcrumb/CustomBreadcrumb";
import { TMetaCategoriesData } from "@/store/apps/meta/types";
import { calcProfitAmount } from "../utils";
import SupplierBox from "./SupplierBox";

export default function DetailPage({ isSupplierView = false }: { isSupplierView?: boolean }) {
  const { t } = useTranslation();
  const params = useParams();
  const productId = params.id as string;
  const makePath = useRoleBasePath();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const { showModal } = useModal();

  const {
    data: product,
    isLoading: isProductLoading,
    error
  } = useGetProductQuery({ id: productId }, { skip: !productId });

  const {
    images,
    title,
    supplier,
    category,
    categoryTree,
    shippingPolicies,
    description,
    returnPolicy,
    variants,
    hasVariant,
    imported,
    cheapestVariant
  } = product?.data || {};

  const retailerProfitAmount = calcProfitAmount({
    commission: cheapestVariant?.commission || 0,
    retailPrice: cheapestVariant?.retailPrice || 0
  });
  const retailerProfitPercent = cheapestVariant?.commission || 0;

  const generateBreadcrumbs = (category?: TMetaCategoriesData): CustomBreadCrumbItemsType[] => {
    const currentBreadcrumb: CustomBreadCrumbItemsType = {
      to: `${makePath(routes.product)}?category=${category?.id}`,
      title: category?.name || ""
    };

    if (category?.subCategories && category?.subCategories.length > 0) {
      return category?.subCategories.reduce(
        (acc: CustomBreadCrumbItemsType[], subCategory) => {
          return [...acc, ...generateBreadcrumbs(subCategory)];
        },
        [currentBreadcrumb]
      );
    }

    return [currentBreadcrumb];
  };

  const BCrumb = [
    {
      to: `${makePath(routes.product)}`,
      title: t("retailerProduct.marketplace")
    },
    ...(generateBreadcrumbs(categoryTree) || []),
    {
      title
    }
  ].filter(Boolean) as CustomBreadCrumbItemsType[];

  const handleOpenVariantsModal = () => {
    showModal({
      body: <VariantsTable variants={variants} hasVariant={hasVariant} />,
      width: 466,
      modalProps: {
        showCloseIcon: false
      }
    });
  };

  if (isMobile) {
    return (
      <DetailPageMobile
        isLoading={isProductLoading}
        productData={product?.data}
        handleOpenVariantsModal={handleOpenVariantsModal}
        isSupplierView={isSupplierView}
        BCrumbItems={BCrumb}
        retailerProfitAmount={retailerProfitAmount}
        retailerProfitPercent={retailerProfitPercent}
        variants={variants}
        // inventory={inventory}
      />
    );
  }

  if (isProductLoading) {
    return (
      <div className="h-full w-full flex flex-col items-center justify-center">
        <CircularProgress />
      </div>
    );
  }

  if (error?.status === 404) {
    return <EmptyPage />;
  }

  return (
    <div className="h-full flex flex-col gap-4">
      <div className="flex flex-col gap-4 bg-v2-surface-primary rounded-md px-8 py-6">
        {!isSupplierView && <CustomBreadcrumb items={BCrumb} className="!p-0 !m-0" />}

        <div className="flex flex-col sm:flex-row gap-4">
          <div className="max-w-[477px] w-full md:w-[477px] flex flex-col gap-6">
            <div className="h-[411px] w-full">
              <ImageGallery images={images} />
            </div>
            <SupplierBox id={supplier?.id} name={supplier?.name} cover={supplier?.cover} />
          </div>

          <div className="flex-1">
            <InfoSection
              productId={productId}
              title={title}
              imported={!!imported}
              supplier={supplier}
              category={category}
              startPrice={cheapestVariant?.retailPrice}
              retailerProfitAmount={retailerProfitAmount}
              retailerProfitPercent={retailerProfitPercent}
              shippingPolicies={shippingPolicies}
              hasVariant={hasVariant}
              variants={variants}
              isSupplierView={isSupplierView}
            />
          </div>
        </div>
      </div>

      <div className="bg-v2-surface-primary rounded-md px-8 py-6">
        <DescriptionSection description={description} returnPolicy={returnPolicy?.description} />
      </div>
    </div>
  );
}
