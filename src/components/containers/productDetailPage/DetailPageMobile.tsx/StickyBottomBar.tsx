import ImportListButton from "@/components/containers/ImportListButton/ImportListButton";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { useConversationStart } from "@/utils/hooks/useConversationStart";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";

interface IStickyBottomBarProps {
  productId?: string;
  supplierId?: string;
  title?: string;
  imported: boolean;
}

function StickyBottomBar({ productId, supplierId, title, imported }: IStickyBottomBarProps) {
  const { t } = useTranslation();

  const { onChatStart, isConversationLoading } = useConversationStart();

  return (
    <div className="fixed bottom-0 shadow-lg bg-cards w-full py-4 px-4 z-50">
      {/* --------------------------------- buttons -------------------------------- */}
      <div className="flex items-center gap-2.5 justify-around w-full">
        <CustomButton
          color="primary"
          size="medium"
          variant="outlined"
          startIcon={<Icon icon="solar:chat-round-call-linear" width={18} height={18} />}
          className="shrink-0 w-[calc(33%_-_6px)]"
        >
          <span className="truncate">{t("retailerProduct.buySample")}</span>
        </CustomButton>

        <CustomButton
          color="primary"
          size="medium"
          variant="outlined"
          disabled={isConversationLoading}
          onClick={() => onChatStart({ content: t("chats.product", { name: title }), partnerId: supplierId })}
          startIcon={<Icon icon="solar:chat-round-call-linear" width={18} height={18} />}
          className="shrink-0 w-[calc(33%_-_6px)]"
        >
          {isConversationLoading ? (
            <CircularProgress size={16} />
          ) : (
            <span className="truncate">{t("retailerProduct.contact")}</span>
          )}
        </CustomButton>

        {productId && (
          <ImportListButton
            variant="contained"
            color="primary"
            imported={imported}
            renderImported={t("productItem.addedToWhishlist")}
            productId={productId}
            size="medium"
            className="shrink-0 w-[calc(33%_-_6px)]"
          >
            <span className="truncate">{t("productItem.addToDraftlist")}</span>
          </ImportListButton>
        )}
      </div>
    </div>
  );
}

export default StickyBottomBar;
