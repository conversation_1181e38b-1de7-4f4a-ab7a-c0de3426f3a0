import { IProductPayLoad } from "@/store/apps/product/types";
import Image from "next/image";
import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";
import styles from "./ImageGallery.module.css";
import { twMerge } from "tailwind-merge";

export type TImageGalleryProps = {
  images?: IProductPayLoad["cover"][];
};

function ImageGallery({ images }: TImageGalleryProps) {
  return (
    <Swiper pagination={true} modules={[Pagination]} className={twMerge("h-[367px] w-full", styles.swiper)}>
      {images?.map(img => {
        return (
          <SwiperSlide key={img.url} className="max-w-fit">
            <div className="relative w-full h-full rounded-lg overflow-hidden">
              <Image src={img.url} alt={img.alt || `${img.url}_img`} fill className="object-contain" />
            </div>
          </SwiperSlide>
        );
      })}
    </Swiper>
  );
}

export default ImageGallery;
