import { CircularProgress } from "@mui/material";
import React, { useMemo, useState } from "react";
import ImageGallery from "./ImageGallery";
import { IProductPayLoad, TProductResponse } from "@/store/apps/product/types";
import { useTranslation } from "react-i18next";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import Link from "next/link";
import { routes } from "@/constants/routes";
import useCurrency from "@/utils/hooks/useCurrency";
import useLocations from "@/utils/hooks/useLocations";
import ShippingTableContent from "./ShippingTableContent";
import StickyBottomBar from "./StickyBottomBar";
import ExpandableContent from "@/components/ui/ExpandableContent/ExpandableContent";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import CustomTabsFilled from "@/components/ui/CustomTabsFilled/CustomTabsFilled";
import Image from "next/image";
import CustomBreadcrumb, { CustomBreadCrumbItemsType } from "@/components/ui/CustomBreadcrumb/CustomBreadcrumb";
import { makeOptionsBasedOnVariants } from "@/components/forms/product-form/OptionVariantsFrom/utils";
import SupplierBoxMobile from "../DetailPage/SupplierBoxMobile";

type TDetailPageMobileProps = {
  isLoading?: boolean;
  productData?: TProductResponse["data"];
  handleOpenVariantsModal: () => void;
  isSupplierView?: boolean;
  BCrumbItems?: CustomBreadCrumbItemsType[];
  startPrice?: number | string;
  retailerProfitAmount?: number;
  inventory?: number;
  retailerProfitPercent?: number;
  variants?: IProductPayLoad["variants"];
};

function DetailPageMobile(props: TDetailPageMobileProps) {
  const {
    isLoading,
    productData,
    handleOpenVariantsModal,
    isSupplierView,
    BCrumbItems,
    startPrice,
    retailerProfitAmount,
    retailerProfitPercent,
    variants,
    inventory
  } = props;
  const {
    images,
    title,
    supplier,
    category,
    cheapestPrice,
    shippingPolicies,
    description,
    returnPolicy,

    hasVariant,
    supplierId,
    id: productId,
    imported
  } = productData || {};

  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { getLocation: findLocation } = useLocations();
  const [selectedTab, setselectedTab] = useState("description");
  const [{ render: renderPrice, symbol }] = useCurrency();

  const variantOptions = useMemo(
    () => (!!variants?.length ? makeOptionsBasedOnVariants(variants as any) : undefined),
    [JSON.stringify(variants)]
  );

  if (isLoading) {
    return (
      <div className="h-full flex w-full  items-center justify-center">
        <CircularProgress />
      </div>
    );
  }

  return (
    <>
      <MobileAppBar title={t("retailerProduct.productDetails")} hasBack className="border-b border-gray-50" />
      {!isSupplierView && (
        <StickyBottomBar productId={productId} supplierId={supplierId} title={title} imported={!!imported} />
      )}

      <div className="flex flex-col gap-2 pb-20 bg-v2-background-secondary pt-2">
        {!isSupplierView && (
          <div className="overflow-x-auto w-screen px-4">
            <CustomBreadcrumb items={BCrumbItems} className="!p-0 !m-0 !bg-transparent overflow-x-auto w-max" />
          </div>
        )}

        <div className="p-4 bg-v2-background-primary flex flex-col gap-4">
          <ImageGallery images={images} />

          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-14">
              <div className="text-v2-content-tertiary font-medium text-[10px] flex items-center gap-1">
                {t("retailerProduct.supplierFrom")}:{" "}
                <Link href={supplier?.id ? `${makePath(routes.supplierProducts(supplier.id))}` : ""}>
                  <div className="font-medium text-v2-content-on-action-hover-2">{supplier?.name}</div>
                </Link>
              </div>
              <div className="text-v2-content-tertiary font-medium text-[10px]">
                {t("retailerProduct.category")}:{" "}
                <span className="font-medium text-v2-content-primary">{category?.name}</span>
              </div>
              {/* <div className="text-v2-content-tertiary font-medium text-[10px]">
                {t("retailerProduct.inventory")}:{" "}
                <span className="font-medium text-v2-content-primary">{inventory}</span>
              </div> */}
            </div>
            <div className="text-v2-content-primary font-medium text-[15px]">{title}</div>
          </div>

          {/* --------------------------------- prices --------------------------------- */}
          <div className="flex justify-between gap-2">
            <div className="flex flex-col gap-1">
              <div className="text-v2-content-tertiary text-xs font-medium">{t("retailerProduct.price")}</div>
              <div className="text-v2-content-primary text-xl font-bold">
                {renderPrice(startPrice, { showSymbol: false })}{" "}
                <span className="text-v2-content-tertiary text-[10px] font-medium">{symbol}</span>
              </div>
            </div>
            {(!!retailerProfitAmount || !!retailerProfitPercent) && (
              <div className="flex items-center justify-start gap-2">
                {retailerProfitPercent && (
                  <div className="bg-v2-surface-info p-2.5 text-v2-content-on-info flex flex-col items-center justify-center rounded-md">
                    <div className="text-xl font-bold leading-4">{retailerProfitPercent}</div>
                    <div className="text-xs leading-[6px] font-semibold">{t("retailerProduct.profitPercent")}</div>
                  </div>
                )}
                {!!retailerProfitAmount && (
                  <div className="px-1.5 py-1 flex items-start flex-col justify-center">
                    <div className="text-xs leading-4 font-medium text-v2-content-tertiary">
                      {t("retailerProduct.yourProfitUpTo")}
                    </div>

                    <div className="">
                      <span className="text-xl font-bold text-v2-content-primary">
                        {renderPrice(retailerProfitAmount, { showSymbol: false })}
                      </span>

                      <span className="text-v2-content-tertiary text-[10px] font-medium leading-4"> {symbol}</span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="p-4 bg-v2-background-primary flex flex-col gap-4">
          {/* -------------------------------- variants -------------------------------- */}
          {!!variantOptions?.length && (
            <div className="flex flex-col gap-4">
              {variantOptions?.map(option => (
                <div key={option?.name} className="flex items-center gap-2">
                  <div className="w-24 text-v2-content-primary text-xs">{option?.name}</div>

                  {option?.values?.map((item, index) => (
                    <div
                      key={index}
                      className="px-2 py-0.5 rounded bg-v2-surface-thertiary text-v2-content-primary text-xs leading-4 font-normal"
                    >
                      {item}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}

          {/* ----------------------------- shipping table ----------------------------- */}
          <div>
            <div className="font-medium text-[13px] text-v2-content-primary mb-2 flex items-center gap-1">
              <Image src="/images/svgs/truck.svg" alt="" height={16} width={16} /> {t("retailerProduct.shippingTitle")}
            </div>
            <div className="w-full">
              <ShippingTableContent shippingPolicies={shippingPolicies} findLocation={findLocation} />
            </div>
          </div>
        </div>

        <div className="p-4 bg-v2-background-primary">
          <SupplierBoxMobile id={supplier?.id} name={supplier?.name} cover={supplier?.cover} />
        </div>

        <div className="p-4 bg-v2-background-primary">
          <CustomTabsFilled
            items={[
              {
                title: t("retailerProduct.productDetailTab"),
                value: "description"
              },
              {
                title: t("retailerProduct.returnPolicy"),
                value: "return"
              }
            ]}
            value={selectedTab}
            onChange={v => setselectedTab(v)}
          />

          <div className="mt-3 text-gray-999 text-xs">
            {selectedTab === "description" && (
              <ExpandableContent>
                {description && <div dangerouslySetInnerHTML={{ __html: description }} />}
              </ExpandableContent>
            )}
            {selectedTab === "return" && (
              <ExpandableContent>
                {returnPolicy?.description && <div dangerouslySetInnerHTML={{ __html: returnPolicy?.description }} />}
              </ExpandableContent>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default DetailPageMobile;
