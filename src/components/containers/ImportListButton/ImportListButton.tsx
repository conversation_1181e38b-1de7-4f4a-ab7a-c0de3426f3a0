import { setStoreModal } from "@/store/apps/config/ConfigSlice";
import { usePostRetailderAddToImportListMutation } from "@/store/apps/retailerProduct";
import { ButtonProps, CircularProgress } from "@mui/material";
import React, { ReactElement, ReactNode, useState } from "react";
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import CustomButton from "../../ui/CustomButton/CustomButton";
import { clientDefaultErrorHandler } from "@/utils/services/utils";

type ImportListButtonProps = {
  productId: string | number;
  imported?: boolean;
  renderImported?: ReactElement | string | boolean | null;
  children?:
    | ReactNode
    | (({
        isImported,
        isLoading,
        onClick
      }: {
        isImported: Boolean;
        isLoading: boolean;
        onClick?: (e: React.MouseEvent<HTMLElement>) => void;
      }) => ReactNode);
} & Omit<ButtonProps, "children">;

const ImportListButton = (props: ImportListButtonProps) => {
  const { productId, imported, renderImported, ...bntProps } = props;
  const [importProduct, { isLoading }] = usePostRetailderAddToImportListMutation();
  const [isImported, setIsImported] = useState<boolean>(imported ?? false);
  const storesCount = useSelector(
    (state: { Retailer: any }) => state?.Retailer?.queries?.["getRetailerStores(undefined)"]?.data?.data?.length,
    shallowEqual
  );
  const dispatch = useDispatch();

  const onClick = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    e.preventDefault();
    if (storesCount === 0) return dispatch(setStoreModal(true));
    importProduct({ productId })
      .then((res: any) => {
        if (res?.error) {
          clientDefaultErrorHandler({ error: (res as any)?.error });
        } else {
          setIsImported(true);
        }
      })
      .catch((err: any) => {
        clientDefaultErrorHandler({ error: err });
      });
  };

  if (typeof bntProps.children === "function") {
    return bntProps.children({ isImported, isLoading, onClick });
  }

  const rendered = isImported ? renderImported || bntProps.children : bntProps.children;

  return (
    <CustomButton
      {...bntProps}
      disabled={isLoading || isImported}
      color={isImported ? "success" : bntProps.color || "primary"}
      onClick={onClick}
    >
      {isLoading ? (
        <>
          <div className="me-1.5">
            <CircularProgress size={13} />
          </div>
          {rendered}
        </>
      ) : (
        rendered
      )}
    </CustomButton>
  );
};

export default ImportListButton;
