import {
  useDeleteRetailerDocumentsMutation,
  useGetRetailerDocumentsQuery,
  usePostRetailerDocumentsMutation
} from "@/store/apps/retailer";
import { TRetailerDocumentsBodyDeleteData, TRetailerDocumentsPostBodyData } from "@/store/apps/retailer/types";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import RectangleImageUploader from "../ImageUploader/withUi/RectangleImageUploader";
import { useEffect, useState } from "react";
import { TDocuments } from "@/app/retailer/(dashboard)/profile/RetailerInfo/types";
import SquareImageUploader from "../ImageUploader/withUi/SquareImageUploader";
import { CircularProgress } from "@mui/material";
import InputLabel from "@/components/ui/inputs/Input/InputLabel";

interface IAttachMultipleUploadedFileToRetailerProfileProps {
  isEdit?: boolean;
  title: string;
  value?: string;
  tag: string;
  onChange: (mediaId: string, tag: string, file?: File) => void;
  onError: (tag: string, errorMessage?: string) => void;
  onRemove: (tag: string, index: number) => void;
  documents?: TDocuments;
  labelTooltipTitle?: string;
  labelTooltipDescription?: string;
  onUpload?: () => void;
  maxCount?: number;
}
type ValueOf<T> = T[keyof T];

function AttachMultipleUploadedFileToRetailerProfile({
  isEdit,
  title,
  tag,
  onChange,
  onError,
  onRemove,
  onUpload,
  documents,
  labelTooltipDescription,
  labelTooltipTitle,
  maxCount
}: IAttachMultipleUploadedFileToRetailerProfileProps) {
  const filteredDocuments: ValueOf<TDocuments> | undefined = documents ? documents?.[tag] : undefined;

  const {
    data: documentsData,
    isLoading: isLoadingGetDocs,
    refetch
  } = useGetRetailerDocumentsQuery(undefined, { skip: !isEdit });

  const images = documentsData?.data;
  const existDocumentsInServer = images?.filter(item => item.tag.startsWith(tag));

  const [postDocMutation, { isLoading: isPostDocLoading }] = usePostRetailerDocumentsMutation();
  const [deleteDocMutation, { isLoading: isDeleteDocLoading }] = useDeleteRetailerDocumentsMutation();
  const [showAddNew, setShowAddNew] = useState(true);
  const maxCountReached = maxCount !== undefined && filteredDocuments && filteredDocuments?.length >= maxCount;

  const isLoading = isLoadingGetDocs || isPostDocLoading || isDeleteDocLoading;

  const uploadDoc = async (mediaId: string, tag: string) => {
    if (!mediaId) return;
    const body = [
      {
        mediaId,
        tag
      }
    ] as TRetailerDocumentsPostBodyData[];

    try {
      await postDocMutation({
        body
      }).then(res => {
        if ((res as any)?.error) {
          clientDefaultErrorHandler({ error: (res as any)?.error });
        }
      });
    } catch (err: any) {
      if (err) {
        clientDefaultErrorHandler({ error: err });
      }
    }
  };

  const deleteDoc = async (mediaId: string) => {
    if (!mediaId) return;

    const body = {
      documentIds: [mediaId]
    } as TRetailerDocumentsBodyDeleteData;

    try {
      await deleteDocMutation({
        body
      }).then(res => {
        if ((res as any)?.error) {
          clientDefaultErrorHandler({ error: (res as any)?.error });
        }

        refetch();
      });
    } catch (err: any) {
      if (err) {
        clientDefaultErrorHandler({ error: err });
      }
    }
  };

  const onUploaded = (v: any, tag: string, oldMediaId?: string, file?: File) => {
    if (isEdit) {
      if (oldMediaId) {
        deleteDoc(oldMediaId).then(() => {
          uploadDoc(v.id, tag);
        });
      } else {
        uploadDoc(v.id, tag);
      }
    } else {
      // only if new profile, call onChange
      // because on edit, we have separate api for updating documents
      onChange(v?.id, tag, file);
    }
    onUpload?.();
  };

  const handleOnRemove = (tag: string, index: number, oldMediaId?: string) => {
    if (oldMediaId) deleteDoc(oldMediaId);
    onRemove(tag, index);
  };

  return (
    <div className="flex flex-col gap-2">
      {/* <div className="text-gray-999 text-sm font-semibold">{title}</div> */}

      <InputLabel
        containerClassName="justify-start gap-2"
        labelTooltipTitle={labelTooltipTitle}
        labelTooltipDescription={labelTooltipDescription}
      >
        {title}
      </InputLabel>

      {isLoading && (
        <div className="h-[55px] flex items-center justify-start">
          <CircularProgress size={24} className="" />
        </div>
      )}

      {!isLoading && (
        <div className="flex flex-wrap gap-3">
          {isEdit && existDocumentsInServer
            ? existDocumentsInServer.map((item, index) => {
                return (
                  <div
                    key={tag}
                    onClick={e => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <SquareImageUploader
                      serverFileKind="document"
                      value={item?.media?.url}
                      className="w-full"
                      onUploaded={(v, { file }) => onUploaded(v, tag, item?.mediaId, file)}
                      onError={onError}
                      onRemove={() => handleOnRemove(tag, index, item?.mediaId)}
                      isLoading={isLoading}
                      withCompressorMaxFileSizeMB={Number(process.env.PROFILES_MAX_UPLOAD_SIZE_MB || 1)}
                      maxFileSizeMB={Number(process.env.PROFILES_MAX_UPLOAD_SIZE_MB || 1)}
                    />
                  </div>
                );
              })
            : !!filteredDocuments?.length
              ? filteredDocuments?.map((item, index) => {
                  return (
                    <div
                      key={tag}
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                    >
                      <SquareImageUploader
                        serverFileKind="document"
                        value={item?.url}
                        className="w-fit"
                        onUploaded={(v, { file }) => onUploaded(v, tag, undefined, file)}
                        onError={onError}
                        onRemove={() => handleOnRemove(tag, index)}
                        isLoading={isLoading}
                        withCompressorMaxFileSizeMB={Number(process.env.PROFILES_MAX_UPLOAD_SIZE_MB || 1)}
                        maxFileSizeMB={Number(process.env.PROFILES_MAX_UPLOAD_SIZE_MB || 1)}
                      />
                    </div>
                  );
                })
              : null}

          {showAddNew && !maxCountReached && (
            <SquareImageUploader
              serverFileKind="document"
              className="w-fit"
              value=""
              onUploaded={(v, { file }) => {
                onUploaded(v, `${tag}`, undefined, file);

                setTimeout(() => {
                  setShowAddNew(false);

                  setTimeout(() => {
                    setShowAddNew(true);
                  }, 0);
                }, 0);
              }}
              hasCropper={false}
              onError={onError}
              withCompressorMaxFileSizeMB={Number(process.env.PROFILES_MAX_UPLOAD_SIZE_MB || 1)}
              maxFileSizeMB={Number(process.env.PROFILES_MAX_UPLOAD_SIZE_MB || 1)}
            />
          )}
        </div>
      )}
    </div>
  );
}
export default AttachMultipleUploadedFileToRetailerProfile;
