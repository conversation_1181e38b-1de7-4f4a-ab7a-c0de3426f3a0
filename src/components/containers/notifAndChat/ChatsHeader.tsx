import { useTranslation } from "react-i18next";

interface IChatsHeaderProps {
  chatsCount?: number;
}

function ChatsHeader({ chatsCount }: IChatsHeaderProps) {
  const { t } = useTranslation();

  return (
    <div className="flex items-center gap-1">
      <span className="text-body2-medium text-v2-content-primary">{t("supplier.chat.step.chat")}</span>
      {chatsCount && <span className="text-v2-content-tertiary text-body2-medium">({chatsCount})</span>}
    </div>
  );
}

export default ChatsHeader;
