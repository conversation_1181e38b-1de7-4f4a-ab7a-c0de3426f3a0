import { useRouter, useSearchParams } from "next/navigation";
import { Chats } from "@/components/containers/chats/Chats";
import { useEffect, useMemo, useState } from "react";
import { useGetConversationQuery } from "@/store/apps/conversation";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import { useMediaQuery } from "@mui/system";
import { Theme } from "@mui/material";
import { useSelector } from "@/store/hooks";
import { AppState } from "@/store/store";
import { ConversationPayloadResponse } from "@/store/apps/conversation/types";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";

function Chat() {
  const searchParams = useSearchParams();
  const makePath = useRoleBasePath();
  const router = useRouter();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const chatId = searchParams?.get("chatId");

  const conversationQuery = useSelector(
    (state: AppState) => state?.Conversation?.queries["getConversation(undefined)"] || {}
  ) as any;
  const conversation = conversationQuery?.data as snakeCaseToCamelCase<ConversationPayloadResponse>;
  const isConversationLoading = !conversationQuery?.status || conversationQuery?.status === "Pending";
  const [loading, setLoading] = useState(isConversationLoading);

  /* ------------- Sort conversations based on lastMessage sendAt ------------- */
  const sortedConversationData = useMemo(() => {
    if (!conversation?.data?.length) return [];
    return [...conversation.data].sort((a, b) => {
      const aSendAt = new Date(a.lastMessage?.sendAt || 0).getTime();
      const bSendAt = new Date(b.lastMessage?.sendAt || 0).getTime();
      return aSendAt - bSendAt;
    });
  }, [conversation?.data]);

  const sortedConversation = useMemo(() => {
    return { status: conversation?.status || "", data: sortedConversationData };
  }, [conversation?.status, sortedConversationData]);
  /* ---------------- Find the selected conversation by chatId ---------------- */
  const selected = useMemo(
    () => ({
      chat: sortedConversation?.data?.find(item => item.id === chatId),
      isNew: false
    }),
    [sortedConversation?.data, chatId]
  );

  /* ------------------ Handle redirection and loading state ------------------ */
  useEffect(() => {
    if (isConversationLoading) return;

    const handleRedirection = () => {
      if (isMobile) {
        setTimeout(() => setLoading(false), 1000);
        return;
      }

      const lastConversationId = sortedConversationData[sortedConversationData.length - 1]?.id;
      const path = `${makePath(routes.chat)}?chatId=${lastConversationId}`;
      router.push(path);

      setTimeout(() => setLoading(false), 1000);
    };

    handleRedirection();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isMobile, sortedConversationData, isConversationLoading]);

  return <Chats selected={selected} chats={sortedConversation} isChatsLoading={loading} />;
}

export default Chat;
