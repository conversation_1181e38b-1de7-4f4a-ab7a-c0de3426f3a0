import { <PERSON><PERSON>, Stack, Typography } from "@mui/material";
import Link from "next/link";

import { useSearchParams } from "next/navigation";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";

import { routes } from "@/constants/routes";
import { useGetConversationQuery } from "@/store/apps/conversation";
import useLanguage from "@/utils/hooks/useLanguage";

function ChatHeader() {
  const makePath = useRoleBasePath();
  const searchParams = useSearchParams();
  const [{ timeDistance }] = useLanguage();
  const chatId = searchParams?.get("chatId");

  const { data: conversation } = useGetConversationQuery();

  const selected = { chat: conversation?.data?.find(item => item.id === chatId), isNew: false };

  return (
    <>
      <div className="flex items-center gap-2 ">
        <Link href={makePath(routes.chat)} className="flex items-center justify-center">
          <Icon icon="lets-icons:arrow-right-light" width={24} height={24} />
        </Link>
        <Avatar className="w-10 h-10" alt={selected?.chat?.partner?.name} src={selected?.chat?.partner?.avatar} />

        <div className="flex flex-col gap-1">
          <Typography className="text-sm text-gray-999 leading-5">{selected?.chat?.partner?.name}</Typography>
          {!!selected?.chat?.createdAt && (
            <Typography className="text-gray-999 text-[11px] leading-none ">
              {timeDistance(new Date(selected?.chat.createdAt)) + " "}
            </Typography>
          )}
        </div>
      </div>
    </>
  );
}

export default ChatHeader;
