import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import React from "react";
import WithBottomBar from "@/components/layouts/WithBottomBar/WithBottomBar";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";

const Notifications: React.FC = () => {
  const { t } = useTranslation();

  return (
    <CustomCardContent>
      <Box className="flex items-center justify-center">
        <Typography>{t("notifications.notFound")}</Typography>
      </Box>
    </CustomCardContent>
  );
};

export default WithBottomBar(Notifications);
