import { routes } from "@/constants/routes";
import { TFunction } from "i18next";

export const notifAndChatSteps = ({
  t,
  makePath
}: {
  t: TFunction<"translation", undefined>;
  makePath: (href: string) => string;
}) => [
  {
    id: 1,
    path: makePath(routes.chat),
    title: t("supplier.chat.step.chat")
  },
  {
    id: 2,
    path: makePath(routes.notifications),
    title: t("supplier.chat.step.notifications")
  }
];
