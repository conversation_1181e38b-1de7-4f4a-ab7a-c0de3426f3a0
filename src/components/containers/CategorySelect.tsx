import { useGetMetaCategoriesQuery } from "@/store/apps/meta";
import { CircularProgress, InputProps } from "@mui/material";
import React from "react";
import { TMetaCategoriesData } from "@/store/apps/meta/types";

import NestedSelect from "../ui/NestedSelect/NestedSelect";

interface TCategorySelectProps {
  value: (number | string)[];
  multiple?: boolean;
  label?: string;
  onChange: (v: (number | string)[]) => void;
  helperText?: string;
  placeholder?: string;
  error?: boolean;
  hideSelecteds?: boolean;
  onBlur?: InputProps["onBlur"];
}

const CategorySelect = (props: TCategorySelectProps) => {
  const { value, onChange, multiple, error, helperText, placeholder, onBlur } = props;
  const { data, isLoading } = useGetMetaCategoriesQuery();

  if (isLoading) return <CircularProgress size={20} />;
  if (!data?.data) return null;

  return (
    <NestedSelect<TMetaCategoriesData>
      value={value}
      multiple={multiple}
      onChange={onChange}
      getKey={cat => cat.id}
      getChildren={cat => cat.subCategories}
      getLabel={cat => cat.name}
      getSelectable={cat => cat.isActive}
      data={data?.data ?? []}
      InputProps={{ error, helperText, placeholder, onBlur }}
    />
  );
};

export default React.memo(
  CategorySelect,
  (prev, next) => prev.value.join(",") === next.value.join(",") && prev.error === next.error
);
