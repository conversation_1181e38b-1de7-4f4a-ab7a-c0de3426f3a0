import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Stack from "@mui/material/Stack";
import { Icon } from "@iconify/react";
import Profile from "./Profile";
import "./Header.css";
import { Theme, Typography, useMediaQuery } from "@mui/material";
import clsx from "clsx";
import React, { ComponentType, useEffect, useState } from "react";
import throttle from "lodash/throttle";
import { debounce } from "lodash";

interface IHeaderProps {
  title?: string;
  onToggleSidebar?: () => void;
  isMobile?: boolean;
  isSticky?: boolean;
  RenderStartSuffix?: ComponentType<any>;
  RenderEndSuffix?: ComponentType<any>;
}

const Header = ({
  onToggleSidebar,
  title,
  isSticky,
  isMobile = false,
  RenderStartSuffix,
  RenderEndSuffix
}: IHeaderProps) => {
  const isTablet = useMediaQuery((theme: Theme) => theme.breakpoints.down(1200));

  const [isScrolled, setIsScrolled] = useState(false);

  const MARGIN_TOP_OFFSET = 16; // Offset for the margin-top

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;

      if (scrollY > 30 && !isScrolled) {
        setIsScrolled(true); // Set to true when scrolling down past 30px
      } else if (scrollY <= 30 && isScrolled) {
        setIsScrolled(false); // Set to false when back at the top (<= 30px)
      }
    };

    const throttledHandleScroll = throttle(handleScroll, 200);

    window.addEventListener("scroll", throttledHandleScroll);

    return () => {
      window.removeEventListener("scroll", throttledHandleScroll);
      throttledHandleScroll.cancel();
    };
  }, [isScrolled]);

  return (
    <div
      className={clsx(
        "appbar-styled",
        "transition-all duration-300",
        isMobile && isSticky ? (isScrolled ? "m-0 mt-4 !shadow-header !rounded-t-none" : "mx-4 mt-4 !mb-0") : "",
        isMobile ? "header-mobile" : ""
      )}
    >
      <div className="toolbar-styled">
        <Stack spacing={1} direction="row" alignItems="center">
          {/* ------------------------------------------- */}
          {/* Toggle Button Sidebar */}
          {/* ------------------------------------------- */}
          {isTablet && (
            <Button
              color="inherit"
              aria-label="menu"
              size="small"
              onClick={onToggleSidebar}
              className="header-hamburger-icon "
            >
              <Icon icon="solar:list-bold-duotone" width="24" height="24" />
            </Button>
          )}

          {!!RenderStartSuffix && !isMobile && <RenderStartSuffix />}

          {/* ------------------------------------------- */}
          {/* Search Dropdown */}
          {/* ------------------------------------------- */}
        </Stack>
        <Box flexGrow={1} />
        <Stack spacing={2} direction="row" alignItems="center" className="header-stack-wrapper">
          {/* {smUp ? <Search /> : ""} */}
          {/* {!IS_IRAN_SERVED && <Language />} */}
          {/* ------------------------------------------- */}
          {/* Ecommerce Dropdown */}
          {/* ------------------------------------------- */}
          {/* <Cart /> */}
          {/* ------------------------------------------- */}
          {/* End Ecommerce Dropdown */}
          {/* ------------------------------------------- */}
          {/* <Notifications /> */}

          {!!RenderEndSuffix && !isMobile && <RenderEndSuffix />}
          <Profile />
          {!!title && <Typography className="main-header-title">{title}</Typography>}
          {/* Todo: it's a temporary tag to fix alignment, after ThemeSwitcher was uncommented it will be removed */}
          <div />
          {/* <ThemeSwitcher /> */}
        </Stack>
      </div>
    </div>
  );
};

export default Header;
