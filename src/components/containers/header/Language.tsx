import React from "react";
import { useRouter } from "next/navigation";
import { LANGUAGES, LanguageType } from "@/constants/localization";
import useLanguage from "@/utils/hooks/useLanguage";
import MobileAppBar from "../mobileAppBar/MobileAppBar";
import { useTranslation } from "react-i18next";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import { Controller, useForm } from "react-hook-form";
import ProfileContentContainer from "../ProfileStepper/ProfileContentContainer";
import GraphicalRadioButton from "@/components/ui/GraphicalRadioButton/GraphicalRadioButton";
import Image from "next/image";
import CustomButton from "@/components/ui/CustomButton/CustomButton";

const Language = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const [currentLang, getPathLang, pathname] = useLanguage();

  const { control, handleSubmit } = useForm({
    // resolver: yupResolver(validationSchema),
    defaultValues: {
      language: currentLang?.value
    }
  });

  const changeLang = (lang: LanguageType["value"]) => {
    const pathLocale = getPathLang();
    let toPath = pathname;
    if (lang === pathLocale) return;
    if (pathLocale) {
      toPath = toPath.replace("/" + pathLocale, "/" + lang);
    } else {
      toPath = "/" + lang + pathname;
    }
    router.replace(toPath);
  };

  const onSubmit = (v: { language?: LanguageType["value"] }) => {
    if (v?.language) {
      changeLang(v?.language);
    }
  };

  return (
    <>
      <MobileAppBar title={t("profiles.language")} hasBack />

      <ProfileContentContainer>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <div className="text-v2-content-primary font-medium text-[15px]">{t("profiles.language")}</div>
              <div className="text-xs font-medium text-v2-content-tertiary">{t("profiles.languageDesc")}</div>
            </div>

            <div className="xmd:max-w-96 flex flex-col gap-2">
              {LANGUAGES?.map(item => {
                return (
                  <Controller
                    key={item?.value}
                    name="language"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <GraphicalRadioButton
                        color="primary"
                        icon={<Image src={item?.icon} alt="" className="size-8" width={32} height={32} />}
                        value={item?.value}
                        title={item?.flagname}
                        subtitle={item?.subTitle || ""}
                        checked={item?.value === field?.value}
                        onChange={v => {
                          field?.onChange(v);
                        }}
                        disabled={item?.disabled}
                      />
                    )}
                  />
                );
              })}
            </div>
          </div>
          <div>
            <BottomAction
              saveButtonText={t("saveChanges")}
              saveButtonProps={{
                type: "submit"
              }}
              cancelButtonText={t("supplier.profile.cancel")}
              cancelButtonProps={{
                onClick: () => router.back()
              }}
            />

            <div className=" justify-end  hidden xmd:flex">
              <CustomButton type="submit" disabled={false}>
                {t("saveChanges")}
              </CustomButton>
            </div>
          </div>
        </form>
      </ProfileContentContainer>
    </>
  );
};

export default Language;
