import { CircularProgress } from "@mui/material";
import Image from "next/image";
import { useTranslation } from "react-i18next";

function LogoutModal() {
  const { t } = useTranslation();
  return (
    <div>
      <div className="flex flex-col items-center pt-10">
        <CircularProgress size={48} className="text-v2-content-on-error-2" />
        <span className="text-body1-medium text-v2-content-on-error-2 mt-5 font-semibold">{t("logouting")}</span>
      </div>
      <div className="border border-solid border-t-gray-50 border-transparent py-4 mt-6 flex items-center justify-center">
        <Image src="/images/svgs/paymentRedirection.svg" width={82} height={20} alt="payment" />
      </div>
    </div>
  );
}

export default LogoutModal;
