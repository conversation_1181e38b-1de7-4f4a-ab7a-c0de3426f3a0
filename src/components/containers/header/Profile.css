.sx-profile-7298 {
  display: flex;
  padding: 8px !important;
  border-radius: 8px;
}
#sx-profile-7310 {
  width: 38px;
  height: 38px;
}

@media screen and (min-width: 900px) {
  #sx-profile-7310 {
    margin-inline-end: var(--mui-theme-spacing-1);
  }
}

#msgs-menu .MuiMenu-paper {
  width: 220px;
  padding-inline-end: 0;
  padding-inline-start: 0;
  top: 85px;
  /* left: 60px !important; */
  box-shadow: 0px 0px 8px 0px #00000014;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .sx-profile-7298 {
    display: flex;
    justify-content: start;
    padding-inline: 0;
  }

  .sx-profile-7298 .MuiTouchRipple-root {
    display: none;
  }

  #msgs-menu .MuiMenu-paper {
    top: 60px;
  }
}

#msgs-menu {
  z-index: 99999999;
  padding: 0;
}

.msgs-menu-item-title {
  font-size: 13px;
  font-weight: 500;
}

.msgs-menu-item-arrow-icon {
  font-size: 12px;
}

.msgs-menu-item-title-logout {
  font-size: 13px;
  font-weight: 500;
  color: #eb4937;
}

#msgs-menu .MuiList-root {
  padding-block: 0;
}

.msgs-menu-item {
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-decoration: none;
  color: var(--mui-palette-grey-900);
}

.msgs-menu-item:hover {
  background-color: rgb(var(--color-gray-20));
}

.msgs-menu-item-wrapper {
  padding: 4px 8px;
  cursor: pointer;
}

#sx-profile-7353 {
  width: 60px;
  height: 60px;
}
#sx-profile-7373 {
  padding-top: var(--mui-theme-spacing-2);
  padding-bottom: var(--mui-theme-spacing-2);
  padding-inline-end: 0px;
  padding-inline-start: 0px;
  cursor: pointer;
}
#sx-profile-7394 {
  width: 240px;
}
#sx-profile-7403 {
  width: 240px;
}
