import "./Profile.css";

import React, { useEffect, useState } from "react";
import { Avatar, CircularProgress, Theme } from "@mui/material";
import { Box } from "@mui/material";
import { Button } from "@mui/material";
import { Divider } from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import { Menu } from "@mui/material";
import { Typography } from "@mui/material";
import { Icon } from "@iconify/react";

import { Stack } from "@mui/material";
import { routes } from "@/constants/routes";
import { useTranslation } from "react-i18next";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import Link from "next/link";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import { useGetMeQuery } from "@/store/apps/auth";
import { logOut } from "@/utils/helpers";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import PaymentRedirecting from "@/app/retailer/(dashboard)/order/[id]/orderDetail/PaymentRedirecting";
import useModal from "@/utils/hooks/useModal";
import LogoutModal from "./logoutModal";

const path = process.env.NODE_ENV === "development" ? routes.supplierLogin : routes.login;
const Profile = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { showModal } = useModal();
  const router = useRouter();
  const lgUp = useMediaQuery((theme: Theme) => theme.breakpoints.up("lg"));
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const { data: me, isLoading } = useGetMeQuery();
  const name = `${me?.data?.firstName ?? ""}  ${me?.data?.lastName ?? ""}`;
  const phoneNumber = me?.data?.phoneNumber || me?.data?.email;

  const [anchorEl, setAnchorEl] = useState(null);
  const handleClick2 = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Button
        size="large"
        aria-label="show 11 new notifications"
        color="inherit"
        aria-haspopup="true"
        className="sx-profile-7298"
        id="msgs-menu"
        sx={{
          ...(typeof anchorEl === "object" && {
            color: "primary.main"
          })
        }}
        onClick={handleClick2}
      >
        <Icon icon="solar:user-circle-outline" className="size-6 text-v2-content-tertiary me-2 xmd:block hidden " />

        <Avatar
          // src={"/images/profile/user5.jpg"}
          alt={"ProfileImg"}
          id="sx-profile-7310"
          sizes="38px"
          className="xmd:hidden flex"
        >
          {name ? name?.[0]?.toLocaleUpperCase() : "U"}
        </Avatar>

        {lgUp ? (
          <div className="flex items-center text-left gap-4">
            {isLoading ? (
              <CircularProgress color="info" size={16} />
            ) : (
              <div className="flex flex-col items-start">
                <p className="text-v2-content-primary text-caption-medium max-w-32 truncate" dir="ltr">
                  {" "}
                  {name}
                </p>

                {phoneNumber && <span className="text-v2-content-tertiary text-caption-regular"> {phoneNumber}</span>}
              </div>
            )}

            {/* <KeyboardArrowDownIcon htmlColor="#B1A4A4" /> */}
            <Icon icon="solar:round-alt-arrow-down-outline" className="size-4 text-v2-content-tertiary" />
            {/* <Typography variant="subtitle2" color="textSecondary">
              {session?.user?.user_type === SUPPLIER_USER_TYPE && t("retailerImport.supplier")}
              {session?.user?.user_type === RETAILER_USER_TYPE && t("retailer.retailer")}
            </Typography> */}
          </div>
        ) : (
          ""
        )}
      </Button>
      {/* ------------------------------------------- */}
      {/* Message Dropdown */}
      {/* ------------------------------------------- */}
      <Menu id="msgs-menu" anchorEl={anchorEl} keepMounted open={Boolean(anchorEl)} onClose={handleClose}>
        <Box className="msgs-menu-item-wrapper">
          <Link
            className="msgs-menu-item"
            href={isMobile ? `${makePath(routes.profile)}` : `${makePath(routes.profile)}?step=0`}
            onClick={handleClose}
          >
            <Stack direction="row" alignItems="center" gap={1}>
              <Icon icon="solar:user-circle-bold" width={18} height={18} />
              <Typography className="msgs-menu-item-title">{t("profileMenu.profile")}</Typography>
            </Stack>
            <Icon icon="material-symbols:arrow-left-rounded" className="size-6 text-v2-content-primary" />
            {/* <ArrowBackIosIcon fontSize="small" className="msgs-menu-item-arrow-icon" /> */}
          </Link>
        </Box>

        <Box className="msgs-menu-item-wrapper">
          <Link className="msgs-menu-item" href={`${makePath(routes.chat)}`} onClick={handleClose}>
            <Stack direction="row" alignItems="center" gap={1}>
              <Icon icon="fluent:chat-24-filled" width={18} height={18} />
              <Typography className="msgs-menu-item-title">{t("profileMenu.support")}</Typography>
            </Stack>
            <Icon icon="material-symbols:arrow-left-rounded" className="size-6 text-v2-content-primary" />
            {/* <ArrowBackIosIcon fontSize="small" className="msgs-menu-item-arrow-icon" /> */}
          </Link>
        </Box>

        {/* <Box className="msgs-menu-item-wrapper">
          <Link className="msgs-menu-item" href="">
            <Stack direction="row" alignItems="center" gap={1}>
              <Icon icon="solar:chat-round-line-bold" />
              <Typography className="msgs-menu-item-title">{t("profileMenu.support")}</Typography>
            </Stack>
            <ArrowBackIosIcon fontSize="small" className="msgs-menu-item-arrow-icon" />
          </Link>
        </Box> */}

        <Divider />

        <Box className="msgs-menu-item-wrapper">
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            className="msgs-menu-item"
            onClick={() => {
              logOut();
              showModal({
                closable: false,
                body: <LogoutModal />,
                modalProps: {
                  containerClassName: "p-0",
                  showCloseIcon: false
                }
              });
              handleClose();
            }}
          >
            <Stack direction="row" alignItems="center" gap={1}>
              <Icon icon="majesticons:logout-half-circle" width={15} height={15} color="#EB4937" />
              <Typography className="msgs-menu-item-title-logout">{t("profileMenu.logout")}</Typography>
            </Stack>
          </Stack>
        </Box>
      </Menu>
    </>
  );
};

export default Profile;
