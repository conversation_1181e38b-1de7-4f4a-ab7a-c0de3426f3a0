.appbar-styled {
  box-shadow: unset;
  background: rgb(var(--color-cards));
  justify-content: center;
  backdrop-filter: unset;
  min-height: 67px;
  max-height: 67px;
  border-radius: 8px;
  align-items: center;
  display: flex;
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .header-mobile {
    display: none;
  }

  .toolbar-styled {
    padding-inline: 0px !important;
  }
}

.toolbar-styled {
  width: 100%;
  color: var(--mui-palette-secondary-main);
  padding-inline: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.main-header-title {
  font-size: 14px;
  font-weight: 700;
  color: rgb(var(--color-gray-999));
}

@media (max-width: 768px) {
  .header-hamburger-icon {
    display: none;
  }

  .header-stack-wrapper {
    width: 100%;
    justify-content: space-between;
  }

  .main-header-title.MuiTypography-root {
    margin-inline-end: 16px;
    margin-right: 0;
  }

  .appbar-styled {
    position: sticky;
    z-index: 9999;
    top: 0;
    margin-bottom: 16px;
  }
}
