import { Icon } from "@iconify/react";
import { Grid, Typography } from "@mui/material";
import { ReactElement } from "react";

const EmptyList = ({ text }: { text: string | ReactElement }) => {
  return (
    <Grid container direction="column" justifyContent="center" alignItems="center">
      <Icon icon={"iconoir:glass-empty"} color="lightgray" fontSize={45} />
      <Typography> {text}</Typography>
    </Grid>
  );
};

export default EmptyList;
