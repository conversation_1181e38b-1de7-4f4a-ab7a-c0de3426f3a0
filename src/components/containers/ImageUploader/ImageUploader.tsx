import FilePicker from "@/components/ui/FilePicker/FilePicker";
import React, { useEffect, useRef, useState } from "react";
import { TImageUploader } from "./types";
import { apiService } from "@/utils/services";
import { compressImage } from "@/utils/imageTools";
import { mediaApiRoutes } from "@/constants/apiRoutes/apiServiceRoutes";
import { isExcelType, isImageType } from "./utils";

/**
 * Just responsible for uploading file
 * Dose NOT have UI! the ui should passed in the children
 */
function ImageUploader({
  children,
  serverFileKind = "document",
  onBeforeUpload,
  value: initialValue,
  onUploaded,
  onError,
  onCancelled,
  onChange,
  className,
  isRemovable = true,
  onRemove,
  filePickerProps = { accept: "image/*" },
  isLoading: initialIsLoading,
  withCompressorMaxFileSizeMB,
  onDragChange
}: TImageUploader) {
  const [isUploading, setIsUploading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isCanceled, setIsCanceled] = useState(false);
  const [responseData, setResponseData] = useState(undefined);
  const [progressPercent, setProgressPercent] = useState(0);
  const [value, setValue] = useState<string | undefined>(initialValue);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadedFileSize, setUploadedFileSize] = useState<number | undefined>(undefined);
  const [fileName, setFileName] = useState<string | undefined>(undefined);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  useEffect(() => {
    onDragChange?.(isDragging);
  }, [isDragging]);

  useEffect(() => {
    if (initialIsLoading !== undefined && isLoading !== initialIsLoading) {
      setIsLoading(initialIsLoading);
    }
  }, [initialIsLoading]);

  const abortControllerRef = useRef<AbortController | null>(null);

  const handleResetEverything = () => {
    // reset all states
    setIsLoading(false);
    setIsUploading(false);
    setIsError(false);
    setIsSuccess(false);
    setProgressPercent(0);
    setIsCanceled(false);
    setErrorMessage(undefined);
    setResponseData(undefined);
    setUploadedFileSize(undefined);
    setFileName(undefined);
  };

  const uploadFile = async (file: File) => {
    if (!file) {
      console.debug("Please select a file first");
      return;
    }

    handleResetEverything();
    setIsUploading(true);

    // Set file info before upload
    setUploadedFileSize(file.size);
    setFileName(file.name);

    const formData = new FormData();
    formData.append("file", file);
    formData.append("kind", serverFileKind);

    abortControllerRef.current = new AbortController();

    try {
      const res = await apiService.post(mediaApiRoutes.upload, formData, {
        headers: { "Content-Type": "multipart/form-data" },
        signal: abortControllerRef.current.signal,
        onUploadProgress: progressEvent => {
          if (progressEvent.total && progressEvent.total > 0) {
            const percentCompleted = Math.min(Math.round((progressEvent.loaded * 100) / progressEvent.total), 100);
            setProgressPercent(percentCompleted);
          }
        }
      });

      /* ------------------------------- on Success ------------------------------- */

      setIsSuccess(true);
      setResponseData(res?.data);
      onUploaded?.(res?.data, { file, uploadedFileSize: file.size, fileName: file.name });
    } catch (error: any) {
      setValue(undefined);
      setUploadedFileSize(undefined);
      setFileName(undefined);

      if (error.name === "CanceledError") {
        /* -------------------------------- on cancel ------------------------------- */
        setIsCanceled(true);
        onCancelled?.();
      } else {
        /* -------------------------------- on error -------------------------------- */
        const errorMsg =
          error?.response?.status === 400
            ? "uploadFormatError"
            : error?.response?.status === 413
              ? "uploadSizeError"
              : "uploadError";

        setIsError(true);
        setErrorMessage(errorMsg);
        onError?.(errorMsg);
      }
    } finally {
      setIsUploading(false);
      abortControllerRef.current = null;
    }
  };

  const cancelUpload = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  };

  const removeFile = async () => {
    if (onRemove && onRemove instanceof Promise) {
      try {
        // can set isLoading later if needed
        await onRemove();
        setValue(undefined);
        setUploadedFileSize(undefined);
        setFileName(undefined);
      } catch (error) {}
    } else {
      onRemove?.();
      setValue(undefined);
      setUploadedFileSize(undefined);
      setFileName(undefined);
    }
  };

  const handleIfCompressor = async (file: File) => {
    if (withCompressorMaxFileSizeMB && withCompressorMaxFileSizeMB > 0 && file.type.startsWith("image/")) {
      setIsLoading(true);

      try {
        const res = await compressImage(file, { maxFileSizeMB: 1 });
        setIsLoading(false);

        return res;
      } catch (error) {
        setIsLoading(false);
      }
    }

    return file;
  };

  const handleOnBeforeUpload = async (file: File) => {
    if (onBeforeUpload !== undefined && typeof onBeforeUpload === "function") {
      setIsLoading(true);

      try {
        const res = await onBeforeUpload(file);

        setIsLoading(false);

        return res;
      } catch (error) {
        setIsLoading(false);

        throw new Error("Canceled");
      }
    }

    return file;
  };

  const onFileChanges: React.ChangeEventHandler<HTMLInputElement> = async event => {
    onChange?.(event);

    const file = event?.target?.files?.[0];
    if (!file) return;

    const accept = filePickerProps.accept;
    const fileType = file.type.toLowerCase();

    const isAcceptedImage = isImageType(accept);
    const isAcceptedExcel = isExcelType(accept);

    // Validate file type against expected type
    if (
      accept !== undefined &&
      ((isAcceptedImage && !fileType.startsWith("image/")) ||
        (isAcceptedExcel &&
          ![
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "text/csv"
          ].includes(fileType)))
    ) {
      setValue(undefined);
      setIsError(true);
      setErrorMessage("uploadFormatError");
      onError?.("uploadFormatError");
      return;
    }

    try {
      const newFile = await handleOnBeforeUpload(file);

      if (isAcceptedImage) {
        const compressedFile = await handleIfCompressor(newFile);
        const imageUrl = URL.createObjectURL(compressedFile);
        setValue(imageUrl);
        uploadFile(compressedFile);
      } else {
        // For Excel or other types, no compression or preview needed
        setValue(file.name); // Or any placeholder
        uploadFile(newFile);
      }
    } catch (error) {
      handleResetEverything();
    }
  };

  return (
    <FilePicker
      multiple={false}
      onChange={onFileChanges}
      {...filePickerProps}
      className={className}
      onDragChange={v => setIsDragging(v)}
    >
      {children({
        isUploading,
        isError,
        errorMessage,
        isSuccess,
        isCanceled,
        progressPercent,
        cancelUpload,
        responseData,
        value,
        isRemovable,
        removeFile,
        isLoading,
        isDragging,
        uploadedFileSize,
        fileName
      })}
    </FilePicker>
  );
}

export default ImageUploader;
