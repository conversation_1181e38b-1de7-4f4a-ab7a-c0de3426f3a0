/* eslint-disable @next/next/no-img-element */
/* eslint-disable jsx-a11y/alt-text */
import Button from "@/components/ui/Button";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import Image from "next/image";
import React from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import ImageUploader from "../ImageUploader";
import ImageUploaderWithCropper from "../ImageUploaderWithCropper";
import { ImageUploaderChildrenReturnProps, TImageUploaderWithCropper } from "../types";
import { isImageType } from "../utils";

function RenderContent(
  props: ImageUploaderChildrenReturnProps & {
    title: string;
    subTitle: string;
    isAcceptImage?: boolean;
    uploadBtnLabel?: string;
    hasError?: boolean;
  }
) {
  const {
    isUploading,
    value,
    progressPercent,
    cancelUpload,
    removeFile,
    isRemovable,
    isAcceptImage,
    isLoading,
    isDragging,
    uploadBtnLabel,
    title,
    subTitle,
    hasError
  } = props;
  const { t } = useTranslation();

  const onClickCancel: React.MouseEventHandler<HTMLDivElement> = e => {
    e.preventDefault();
    e.stopPropagation();
    cancelUpload();
  };

  const onClickRemove: React.MouseEventHandler<HTMLDivElement> = e => {
    e.preventDefault();
    e.stopPropagation();
    removeFile();
  };

  const onClickRoot: React.MouseEventHandler<HTMLDivElement> = e => {
    if (isLoading || isUploading) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  return (
    <div
      className={twMerge(
        "w-full h-24 shrink-0 relative rounded-[10px] overflow-hidden group border border-dashed flex justify-between items-center p-6 gap-1 select-none",
        isDragging ? "border-purple-500" : "",
        hasError ? "border-v2-content-on-error-2" : "border-v2-content-tertiary"
      )}
      onClick={onClickRoot}
    >
      <div className="flex gap-3 items-center shrink-0">
        <div className="h-12 w-12 flex items-center justify-center">
          {/* -------------------------------- isLoading ------------------------------- */}
          {isLoading && <CircularProgress size={28} className="text-gray-400" />}

          {/* ------------------------------- isUploading ------------------------------ */}
          {isUploading && (
            <div className="relative w-full h-full rounded-lg overflow-hidden">
              <div
                className="bg-gray-999/20 backdrop-blur-sm absolute z-20 top-0 left-0 w-full h-full text-gray-20 text-2xl opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer flex items-center justify-center"
                onClick={onClickCancel}
              >
                <Icon icon="material-symbols:close-rounded" width={32} height={32} />
              </div>
              <div className="flex items-center justify-center h-full w-full absolute left-0 top-0 bg-gray-999/20 backdrop-blur-sm z-10 group-hover:opacity-50 transition-opacity ">
                <CircularProgress
                  variant={progressPercent !== undefined && progressPercent > 0 ? "determinate" : "indeterminate"}
                  size={28}
                  className="text-background"
                  value={progressPercent}
                />
              </div>
            </div>
          )}

          {/* ------------------------------- select icon ------------------------------ */}
          {!isUploading && !isLoading && (
            <div className="p-3 border border-gray-50 rounded-full flex items-center justify-center">
              <Image
                src="/images/svgs/upload-1.svg"
                alt="upload-icon"
                width={24}
                height={24}
                className="w-6 h-6 block"
              />
            </div>
          )}
        </div>
        <div className="flex flex-col gap-1">
          <div className="text-v2-content-primary text-[15px] font-medium">{title}</div>
          <div className="text-v2-content-subtle text-xs font-medium">{subTitle}</div>
        </div>
      </div>
      <div className="hidden sm:block">
        <Button type="button" disabled={isUploading || isLoading} id="doc-upload-btn" size="md" variant="secondaryGray">
          {uploadBtnLabel || t("retailer.profile.uploadPhoto")}
        </Button>
      </div>
    </div>
  );
}

type TDropHereBoxImageUploader = Omit<TImageUploaderWithCropper, "children"> & {
  title: string;
  subTitle: string;
  hasCropper?: boolean;
  uploadBtnLabel?: string;
  hasError?: boolean;
};
function DropHereBoxImageUploader({
  title,
  subTitle,
  uploadBtnLabel,
  hasCropper = true,
  hasError,
  ...restProps
}: TDropHereBoxImageUploader) {
  const isAcceptImage = !restProps?.filePickerProps?.accept || isImageType(restProps?.filePickerProps?.accept);

  if (isAcceptImage && hasCropper) {
    return (
      <ImageUploaderWithCropper {...restProps}>
        {returnProps => (
          <RenderContent {...returnProps} {...{ title, subTitle, uploadBtnLabel, isAcceptImage, hasError }} />
        )}
      </ImageUploaderWithCropper>
    );
  }

  return (
    <ImageUploader {...restProps}>
      {returnProps => (
        <RenderContent {...returnProps} {...{ title, subTitle, uploadBtnLabel, isAcceptImage, hasError }} />
      )}
    </ImageUploader>
  );
}

export default DropHereBoxImageUploader;
