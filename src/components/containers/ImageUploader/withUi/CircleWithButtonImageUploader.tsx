/* eslint-disable @next/next/no-img-element */
/* eslint-disable jsx-a11y/alt-text */
import React from "react";
import ImageUploader<PERSON>ith<PERSON>ropper from "../ImageUploaderWithCropper";
import { ImageUploaderChildrenReturnProps, TImageUploaderWithCropper } from "../types";
import { Avatar, CircularProgress } from "@mui/material";
import { twMerge } from "tailwind-merge";
import { Icon } from "@iconify/react";
import { isImageType } from "../utils";
import ImageUploader from "../ImageUploader";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import Image from "next/image";

function RenderContent(
  props: ImageUploaderChildrenReturnProps & {
    isAcceptImage?: boolean;
    buttonLabel: string;
    containerClassName?: string;
    wrapperClassName?: string;
  }
) {
  const {
    isUploading,
    value,
    progressPercent,
    cancelUpload,
    removeFile,
    isRemovable,
    isAcceptImage,
    buttonLabel,
    containerClassName,
    wrapperClassName,
    isLoading
  } = props;

  const onClickCancel: React.MouseEventHandler<HTMLDivElement> = e => {
    e.preventDefault();
    e.stopPropagation();
    cancelUpload();
  };

  const onClickRemove: React.MouseEventHandler<HTMLDivElement> = e => {
    e.preventDefault();
    e.stopPropagation();
    removeFile();
  };

  const onClickRoot: React.MouseEventHandler<HTMLDivElement> = e => {
    if (isLoading || isUploading) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  return (
    <div className={twMerge("flex items-center gap-4 w-full", containerClassName)}>
      <div
        className={twMerge("w-[65px] h-[65px] shrink-0 relative rounded-full overflow-hidden group ", wrapperClassName)}
        onClick={onClickRoot}
      >
        {/* -------------------------------- isLoading ------------------------------- */}
        {isLoading && (
          <div className="flex items-center justify-center h-full w-full absolute left-0 top-0 bg-gray-999/20 backdrop-blur-sm z-10">
            <CircularProgress size={28} className="text-background" />
          </div>
        )}

        {/* ------------------------------- isUploading ------------------------------ */}
        {isUploading && (
          <>
            <div
              className="bg-gray-999/20 backdrop-blur-sm absolute z-20 top-0 left-0 w-full h-full text-gray-20 text-2xl opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer flex items-center justify-center"
              onClick={onClickCancel}
            >
              <Icon icon="material-symbols:close-rounded" width={32} height={32} />
            </div>
            <div className="flex items-center justify-center h-full w-full absolute left-0 top-0 bg-gray-999/20 backdrop-blur-sm z-10 group-hover:opacity-50 transition-opacity ">
              <CircularProgress
                variant={progressPercent !== undefined && progressPercent > 0 ? "determinate" : "indeterminate"}
                size={28}
                className="text-background"
                value={progressPercent}
              />
            </div>
          </>
        )}

        {/* ------------------------------- remove icon ------------------------------ */}
        {isRemovable && value && !(isUploading || isLoading) && (
          <>
            <div
              className="bg-gray-999/20 backdrop-blur-sm absolute z-20 top-0 left-0 w-full h-full text-gray-20 text-2xl opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer flex items-center justify-center"
              onClick={onClickRemove}
            >
              <Icon icon="solar:trash-bin-trash-bold" width={32} height={32} />
            </div>
          </>
        )}

        {/* ------------------------- preview and select icon ------------------------ */}
        <div className="relative w-full h-full">
          {value && isAcceptImage ? (
            <Image src={value || ""} className="w-full h-full" fill alt="" />
          ) : // <img src={value} className="object-cover h-full w-full" />
          value ? (
            <Avatar variant="square" src="" className="w-full h-full" />
          ) : (
            <Avatar variant="square" src="" className="w-full h-full" />
          )}
        </div>
      </div>

      <div>
        <CustomButton color="secondary" className="!text-v2-content-secondary" onClick={() => {}}>
          {buttonLabel}
        </CustomButton>
      </div>
    </div>
  );
}

type TCircleWithButtonImageUploader = Omit<TImageUploaderWithCropper, "children"> & {
  hasCropper?: boolean;
  buttonLabel: string;
  containerClassName?: string;
  wrapperClassName?: string;
};
function CircleWithButtonImageUploader({
  hasCropper = true,
  buttonLabel,
  wrapperClassName,
  containerClassName,
  ...restProps
}: TCircleWithButtonImageUploader) {
  const isAcceptImage = !restProps?.filePickerProps?.accept || isImageType(restProps?.filePickerProps?.accept);

  if (isAcceptImage && hasCropper) {
    return (
      <ImageUploaderWithCropper {...restProps}>
        {returnProps => (
          <RenderContent {...returnProps} {...{ isAcceptImage, buttonLabel, wrapperClassName, containerClassName }} />
        )}
      </ImageUploaderWithCropper>
    );
  }

  return (
    <ImageUploader {...restProps}>
      {returnProps => (
        <RenderContent {...returnProps} {...{ isAcceptImage, buttonLabel, wrapperClassName, containerClassName }} />
      )}
    </ImageUploader>
  );
}

export default CircleWithButtonImageUploader;
