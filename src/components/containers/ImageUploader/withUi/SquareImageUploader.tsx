/* eslint-disable @next/next/no-img-element */
/* eslint-disable jsx-a11y/alt-text */
import React from "react";
import ImageUploader<PERSON>ith<PERSON>ropper from "../ImageUploaderWithCropper";
import { ImageUploaderChildrenReturnProps, TImageUploader<PERSON>ithCropper } from "../types";
import { CircularProgress, Divider, MenuItem } from "@mui/material";
import { twMerge } from "tailwind-merge";
import { Icon } from "@iconify/react";
import { isImageType } from "../utils";
import ImageUploader from "../ImageUploader";
import Image from "next/image";
import CustomMenu from "@/components/ui/CustomMenu/CustomMenu";
import { useTranslation } from "react-i18next";
import useModal from "@/utils/hooks/useModal";

function RenderContent(props: ImageUploaderChildrenReturnProps & { isAcceptImage?: boolean }) {
  const { isUploading, value, progressPercent, cancelUpload, removeFile, isRemovable, isAcceptImage, isLoading } =
    props;

  const onClickCancel: React.MouseEventHandler<HTMLDivElement> = e => {
    e.preventDefault();
    e.stopPropagation();
    cancelUpload();
  };

  const onClickRemove: React.MouseEventHandler<HTMLLIElement> = e => {
    e.preventDefault();
    e.stopPropagation();
    removeFile();
  };

  const { t } = useTranslation();
  const { showModal, hideModal } = useModal();

  const handleOpenPreviewModal = (src: string) => {
    showModal({
      body: (
        <div className="flex items-center justify-center py-6">
          <img src={src} className="w-full max-h-96 object-contain mt-2" />
        </div>
      ),
      width: 600
    });
  };

  return (
    <div
      className={twMerge(
        "w-[55px] h-[55px] shrink-0 relative rounded-md overflow-hidden group border",
        value ? "border-transparent bg-gray-200 " : "border-gray-200 border-dashed rounded-[10px]"
      )}
      onClick={e => {
        if (isLoading || isUploading) {
          e.preventDefault();
          e.stopPropagation();
        }
      }}
    >
      {/* -------------------------------- isLoading ------------------------------- */}
      {isLoading && (
        <div className="flex items-center justify-center h-full w-full absolute left-0 top-0 bg-gray-999/20 backdrop-blur-sm z-10">
          <CircularProgress size={24} className="text-background" />
        </div>
      )}

      {/* ------------------------------- isUploading ------------------------------ */}
      {isUploading && (
        <>
          <div
            className="bg-gray-999/20 backdrop-blur-sm absolute z-20 top-0 left-0 w-full h-full text-gray-20 text-2xl opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer flex items-center justify-center"
            onClick={onClickCancel}
          >
            <Icon icon="material-symbols:close-rounded" width={24} height={24} />
          </div>
          <div className="flex items-center justify-center h-full w-full absolute left-0 top-0 bg-gray-999/20 backdrop-blur-sm z-10 group-hover:opacity-50 transition-opacity ">
            <CircularProgress
              variant={progressPercent !== undefined && progressPercent > 0 ? "determinate" : "indeterminate"}
              size={24}
              className="text-background"
              value={progressPercent}
            />
          </div>
        </>
      )}

      {/* ------------------------- preview and select icon ------------------------ */}
      {value && isAcceptImage ? (
        <img src={value} className="object-cover h-full w-full" />
      ) : value ? (
        <div className="w-full h-full flex items-center justify-center">
          <Icon icon="solar:file-text-outline" width={24} height={24} className="text-gray-600 size-6" />
        </div>
      ) : (
        <div className="w-full h-full flex items-center justify-center">
          <Image src="/images/svgs/upload2.svg" alt="" width={24} height={24} className="size-6" />
        </div>
      )}

      {/* ----------------------------- three dots menu ---------------------------- */}
      {!isLoading && !isUploading && value && (
        <div
          className="absolute left-1.5 top-1.5 z-10 cursor-pointer"
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          <CustomMenu
            content={
              <>
                {value && isAcceptImage && (
                  <>
                    <MenuItem
                      onClick={() => value && handleOpenPreviewModal(value)}
                      className="pb-3.5 pt-1.5 text-[13px]"
                    >
                      <Icon icon="solar:eye-scan-outline" width={18} height={18} className="ml-2" />{" "}
                      {t("productForm.uploadImage.preview")}
                    </MenuItem>

                    <Divider className="bg-gray-50 !my-0" />
                  </>
                )}

                {isRemovable && value && !(isUploading || isLoading) && (
                  <MenuItem onClick={e => onClickRemove(e)} className="text-error-500 pt-3.5 pb-1.5 text-[13px]">
                    <Icon icon="solar:trash-bin-trash-bold" width={18} height={18} className="ml-2" />
                    {t("productForm.uploadImage.remove")}
                  </MenuItem>
                )}
              </>
            }
          >
            <img src="/images/svgs/Menu-Dots-Square-2.svg" className="cursor-pointer" />
          </CustomMenu>
        </div>
      )}
    </div>
  );
}

type TRectangleImageUploader = Omit<TImageUploaderWithCropper, "children"> & {
  hasCropper?: boolean;
};
function SquareImageUploader({ hasCropper = true, ...restProps }: TRectangleImageUploader) {
  const isAcceptImage = !restProps?.filePickerProps?.accept || isImageType(restProps?.filePickerProps?.accept);

  if (isAcceptImage && hasCropper) {
    return (
      <ImageUploaderWithCropper {...restProps}>
        {returnProps => <RenderContent {...returnProps} isAcceptImage={isAcceptImage} />}
      </ImageUploaderWithCropper>
    );
  }

  return (
    <ImageUploader {...restProps}>
      {returnProps => <RenderContent {...returnProps} isAcceptImage={isAcceptImage} />}
    </ImageUploader>
  );
}

export default SquareImageUploader;
