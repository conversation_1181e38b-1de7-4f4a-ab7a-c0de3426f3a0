/* eslint-disable @next/next/no-img-element */
/* eslint-disable jsx-a11y/alt-text */
import React from "react";
import ImageUploader<PERSON>ith<PERSON>ropper from "../ImageUploaderWithCropper";
import { ImageUploaderChildrenReturnProps, TImageUploaderWithCropper } from "../types";
import { Button, CircularProgress } from "@mui/material";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import { Icon } from "@iconify/react";
import { isImageType } from "../utils";
import ImageUploader from "../ImageUploader";

function RenderContent(
  props: ImageUploaderChildrenReturnProps & {
    title: string;
    subTitle: string;
    isAcceptImage?: boolean;
    uploadBtnLabel?: string;
  }
) {
  const {
    isUploading,
    value,
    progressPercent,
    cancelUpload,
    title,
    subTitle,
    isRemovable,
    removeFile,
    isAcceptImage,
    isLoading,
    uploadBtnLabel
  } = props;

  const { t } = useTranslation();

  const onClickCancel: React.MouseEventHandler<HTMLDivElement> = e => {
    e.preventDefault();
    e.stopPropagation();
    cancelUpload();
  };

  const onClickRemove: React.MouseEventHandler<HTMLDivElement> = e => {
    e.preventDefault();
    e.stopPropagation();
    removeFile();
  };

  const onClickRoot: React.MouseEventHandler<HTMLDivElement> = e => {
    if (isLoading || isUploading) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  return (
    <div className="flex border rounded-lg border-v2-border-primary p-4 gap-3 items-center" onClick={onClickRoot}>
      {/* ------------------------------- right side ------------------------------- */}
      <div
        className={twMerge("w-12 h-12 shrink-0 relative rounded-lg overflow-hidden group", value ? "bg-gray-200" : "")}
      >
        {/* -------------------------------- isLoading ------------------------------- */}
        {isLoading && (
          <div className="flex items-center justify-center h-full w-full absolute left-0 top-0 bg-gray-999/20 backdrop-blur-sm z-10">
            <CircularProgress size={28} className="text-background" />
          </div>
        )}

        {/* ------------------------------- isUploading ------------------------------ */}
        {isUploading && (
          <>
            <div
              className="bg-gray-999/20 backdrop-blur-sm absolute z-20 top-0 left-0 w-full h-full text-gray-20 text-2xl opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer flex items-center justify-center"
              onClick={onClickCancel}
            >
              <Icon icon="material-symbols:close-rounded" width={32} height={32} />
            </div>
            <div className="flex items-center justify-center h-full w-full absolute left-0 top-0 bg-gray-999/20 backdrop-blur-sm z-10 group-hover:opacity-50 transition-opacity ">
              <CircularProgress
                variant={progressPercent !== undefined && progressPercent > 0 ? "determinate" : "indeterminate"}
                size={28}
                className="text-background"
                value={progressPercent}
              />
            </div>
          </>
        )}

        {/* ------------------------------- remove icon ------------------------------ */}
        {isRemovable && value && !(isUploading || isLoading) && (
          <>
            <div
              className="bg-gray-999/20 backdrop-blur-sm absolute z-20 top-0 left-0 w-full h-full text-gray-20 text-2xl opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer flex items-center justify-center"
              onClick={onClickRemove}
            >
              <Icon icon="solar:trash-bin-trash-bold" width={32} height={32} />
            </div>
          </>
        )}

        {/* ------------------------- preview and select icon ------------------------ */}
        {value && isAcceptImage ? (
          <img src={value} className="object-cover h-full w-full" />
        ) : value ? (
          <div className="w-full h-full flex items-center justify-center">
            <Image
              src="/images/svgs/document-uploaded-1.svg"
              width={48}
              height={48}
              alt="upload"
              className="size-[48px] "
            />
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Image
              src="/images/svgs/document-upload-2.svg"
              width={48}
              height={48}
              alt="upload"
              className="size-[48px] "
            />
          </div>
        )}
      </div>
      {/* --------------------------------- titles --------------------------------- */}
      <div className="flex-1 flex flex-col">
        <div className="text[13px] text-gray-999 font-normal">{title}</div>
        <div className={twMerge("text-xs  font-normal text-gray-500 flex items-center  gap-1.5")}>
          {subTitle}{" "}
          {value && !(isUploading || isLoading) && (
            <div className="flex items-center gap-1.5 text-v2-content-on-success-2">
              <div className="text-v2-content-on-info -mt-1">.</div>
              <Icon icon="solar:check-circle-bold" className="size-[13px]" />
              <div>آپلود شد</div>
            </div>
          )}
        </div>
      </div>
      {/* -------------------------------- left side ------------------------------- */}
      <div className="flex items-center justify-center shrink-0">
        {!value ? (
          <button
            type="button"
            disabled={isUploading || isLoading}
            className="outline-none bg-transparent border border-v2-border-primary rounded-lg px-2.5 py-2 text-[13px] font-medium"
          >
            {uploadBtnLabel || t("retailer.profile.uploadPhoto")}
          </button>
        ) : (
          <div onClick={onClickRemove}>
            <Icon icon="solar:trash-bin-minimalistic-outline" className="size-5" />
          </div>
        )}
      </div>
    </div>
  );
}

type TRectangleImageUploader2 = Omit<TImageUploaderWithCropper, "children"> & {
  title: string;
  subTitle: string;
  hasCropper?: boolean;
  uploadBtnLabel?: string;
};
function RectangleImageUploader2({
  title,
  subTitle,
  uploadBtnLabel,
  hasCropper = true,
  ...restProps
}: TRectangleImageUploader2) {
  const isAcceptImage = !restProps?.filePickerProps?.accept || isImageType(restProps?.filePickerProps?.accept);

  if (isAcceptImage && hasCropper) {
    return (
      <ImageUploaderWithCropper {...restProps}>
        {returnProps => <RenderContent {...returnProps} {...{ title, subTitle, uploadBtnLabel, isAcceptImage }} />}
      </ImageUploaderWithCropper>
    );
  }

  return (
    <ImageUploader {...restProps}>
      {returnProps => <RenderContent {...returnProps} {...{ title, subTitle, uploadBtnLabel, isAcceptImage }} />}
    </ImageUploader>
  );
}

export default RectangleImageUploader2;
