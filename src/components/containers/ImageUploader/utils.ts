import FilePicker from "@/components/ui/FilePicker/FilePicker";

export const isImageType = (accept: FilePicker["accept"]) => {
  if (!accept) return false;
  const imageTypes = [
    "image/*",
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/bmp",
    "image/svg+xml",
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".bmp",
    ".svg"
  ];
  return imageTypes.includes(accept.toLowerCase());
};

export const isExcelType = (accept: FilePicker["accept"]) => {
  if (!accept) return false;
  const excelTypes = [
    ".xlsx",
    ".xls",
    ".csv",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "text/csv"
  ];
  return excelTypes.includes(accept.toLowerCase());
};

export const formatFileSize = (bytes: number) => {
  if (bytes < 1024) return `${bytes}B`;
  if (bytes < 1024 * 1024) return `${Math.round(bytes / 1024)}KB`;
  if (bytes < 1024 * 1024 * 1024) return `${Math.round(bytes / (1024 * 1024))}MB`;
  return `${Math.round(bytes / (1024 * 1024 * 1024))}GB`;
};
