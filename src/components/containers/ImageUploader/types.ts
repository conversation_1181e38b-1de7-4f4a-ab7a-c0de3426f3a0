import FilePicker from "@/components/ui/FilePicker/FilePicker";
import { TTImageCropperOptions } from "@/components/ui/ImageCropper/types";
import { ChangeEvent, ReactNode } from "react";

export type TImageUploader = {
  children: (props: ImageUploaderChildrenReturnProps) => ReactNode;
  serverFileKind?: "document" | "public";
  value?: string;
  className?: string;
  onBeforeUpload?: (file: File) => Promise<File>;
  onUploaded?: (
    responseData: TResponse,
    {
      file,
      uploadedFileSize,
      fileName
    }: {
      file: File;
      uploadedFileSize: number;
      fileName: string;
    }
  ) => void;
  onError?: (errorMessage: string) => void;
  onCancelled?: () => void;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  onRemove?: () => void | Promise<void>;
  filePickerProps?: Omit<FilePicker, "multiple" | "onChange">;
  isLoading?: boolean;
  withCompressorMaxFileSizeMB?: number;
  maxFileSizeMB?: number;
  /**
   * @default true
   */
  isRemovable?: boolean;
  onDragChange?: (isDragging: boolean) => void;
};

export type ImageUploaderChildrenReturnProps = {
  isUploading: boolean;
  isLoading?: boolean;
  isError: boolean;
  errorMessage?: string;
  isSuccess: boolean;
  isCanceled: boolean;
  progressPercent: number;
  cancelUpload: () => void;
  removeFile: () => void;
  responseData?: TResponse;
  value?: string;
  isRemovable: boolean;
  isDragging: boolean;
  fileName?: string;
  uploadedFileSize?: number;
};

export type TImageUploaderWithCropper = Omit<TImageUploader, "onBeforeUpload"> & {
  cropperProps?: TTImageCropperOptions;
};

type TResponse = {
  id: string;
  url?: string;
};
