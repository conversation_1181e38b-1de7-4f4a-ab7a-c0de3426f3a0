import { AppB<PERSON>, Box, Stack, Typography } from "@mui/material";
import "./MobileAppBar.css";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

interface IAppBarProps {
  title?: string;
  hasBack?: boolean;
  RenderComponent?: ReactNode | JSX.Element;
  className?: string;
  onBack?: () => void;
  backIcon?: string;
}
function MobileAppBar({ title, hasBack, RenderComponent, className, onBack, backIcon }: IAppBarProps) {
  const router = useRouter();
  const hasHistory = typeof window !== "undefined" && window.history.length > 1;

  return (
    <Box className="app-bar-wrapper">
      <AppBar className={twMerge("app-bar-container", className)}>
        {RenderComponent ? (
          RenderComponent
        ) : (
          <Stack flexDirection="row" alignItems="center">
            {hasBack && (
              <Icon
                width={20}
                height={20}
                color="black"
                className="cursor-pointer"
                onClick={() => (!!onBack ? onBack?.() : router.back())}
                icon={backIcon || "solar:arrow-right-outline"}
              />
            )}
            <Typography className="app-bar-title">{title}</Typography>
            {hasBack && hasHistory && <div className="w-6 h-6" />}
          </Stack>
        )}
      </AppBar>
    </Box>
  );
}

export default MobileAppBar;
