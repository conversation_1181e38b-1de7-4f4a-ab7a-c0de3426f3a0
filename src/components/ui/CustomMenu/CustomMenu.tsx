import React, { useState, MouseE<PERSON>, ReactNode } from "react";
import { Menu, MenuItem, Button } from "@mui/material";

// Define the type for a menu item
interface MenuItemType {
  label: string;
  value: string;
}

// Define the component's props type
interface CustomMenuProps {
  menuItems?: MenuItemType[];
  children: ReactNode;
  onSelect?: (item: MenuItemType) => void;
  content?: ReactNode;
}

const CustomMenu: React.FC<CustomMenuProps> = ({ menuItems, content, children, onSelect }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSelect = (item: MenuItemType) => {
    if (onSelect) onSelect(item);
    handleClose();
  };

  return (
    <>
      <div onClick={handleClick}>{children}</div>
      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose} onClick={handleClose}>
        {content ||
          menuItems?.map((item, index) => (
            <MenuItem key={index} onClick={() => handleSelect(item)}>
              {item.label}
            </MenuItem>
          ))}
      </Menu>
    </>
  );
};

export default CustomMenu;
