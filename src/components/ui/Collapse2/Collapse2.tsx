import { Icon } from "@iconify/react";
import { Collapse as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import { ReactNode, useState } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

function Collapse2({
  children,
  initialIsOpen = false,
  startAdornment,
  endAdornment,
  rootClassName,
  className,
  variant = "primary"
}: {
  children: ReactNode;
  initialIsOpen?: boolean;
  startAdornment?: ReactNode;
  endAdornment?: ReactNode;
  rootClassName?: string;
  className?: string;
  variant?: "primary" | "secondary";
}) {
  const [isOpen, setIsOpen] = useState(initialIsOpen);
  const { t } = useTranslation();

  if (variant === "secondary") {
    return (
      <div className={rootClassName} aria-expanded={isOpen}>
        <div
          className={twMerge("flex flex-col gap-2 px-4 py-3", isOpen ? "bg-v2-surface-secondary" : "", className)}
          aria-expanded={isOpen}
        >
          <div className="flex items-center justify-between">
            {startAdornment}
            <div
              className="rounded border border-v2-border-primary size-6 flex items-center justify-center cursor-pointer shrink-0"
              onClick={() => setIsOpen(prev => !prev)}
            >
              <Icon
                icon="solar:alt-arrow-down-outline"
                className={twMerge("size-4 text-v2-content-primary  transition-transform", isOpen ? "rotate-180" : "")}
              />
            </div>
          </div>

          {endAdornment}
          <MuiCollapse in={isOpen} timeout="auto">
            <div className="py-4">{children}</div>
          </MuiCollapse>
        </div>
      </div>
    );
  }

  return (
    <div className={rootClassName} aria-expanded={isOpen}>
      <div
        className={twMerge("flex flex-col gap-2 px-4 py-3", isOpen ? "bg-v2-surface-secondary" : "", className)}
        aria-expanded={isOpen}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center justify-start flex-1 gap-2">
            <div className="flex items-center gap-2 cursor-pointer " onClick={() => setIsOpen(prev => !prev)}>
              <div className="rounded border border-v2-border-primary size-6 flex items-center justify-center">
                <Icon
                  icon="solar:alt-arrow-down-outline"
                  className={twMerge("size-4 text-v2-content-primary transition-transform", isOpen ? "rotate-180" : "")}
                />
              </div>
            </div>
            {startAdornment}
          </div>
          <div className="shrink-0 flex items-center gap-3">{endAdornment}</div>
        </div>
      </div>

      <MuiCollapse in={isOpen} timeout="auto">
        <div className="px-5 py-4">{children}</div>
      </MuiCollapse>
    </div>
  );
}

export default Collapse2;
