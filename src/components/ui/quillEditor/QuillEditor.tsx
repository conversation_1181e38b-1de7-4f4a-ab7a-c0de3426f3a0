import "./QuillEditor.css";
import "react-quill/dist/quill.snow.css";
import "./Quill.css";

/* eslint-disable react/display-name */
import { Box } from "@mui/material";
import { Paper } from "@mui/material";
import dynamic from "next/dynamic";
import { ReactQuillProps } from "react-quill";
import { memo } from "react";
import CustomFormLabel from "../CustomFormLabel/CustomFormLabel";
import { twMerge } from "tailwind-merge";
import InputHelper from "../CustomFormHelperText/InputHelper";

const ReactQuill: any = dynamic(
  async () => {
    const { default: RQ } = await import("react-quill");
    // eslint-disable-next-line react/display-name
    return ({ ...props }) => <RQ {...props} />;
  },
  {
    ssr: false
  }
);

export interface IQuillEditorProps extends ReactQuillProps {
  error?: boolean;
  helperText?: string;
  label?: string;
  optional?: boolean;
}

const QuillEditor = memo(({ value, error, helperText, label, optional, ...restProps }: IQuillEditorProps) => {
  return (
    <Box>
      <CustomFormLabel optional={optional}>{label}</CustomFormLabel>
      <Paper
        variant="outlined"
        id="sx-quilleditor-7079"
        className={twMerge("bg-cards mt-[5px]", error ? "border-[#FB977D]" : "")}
      >
        <div
          style={{
            position: "relative"
          }}
        >
          <ReactQuill {...restProps} value={value} />
        </div>
      </Paper>
      {!!helperText && <InputHelper error>{helperText}</InputHelper>}
    </Box>
  );
});

export default QuillEditor;
