import * as React from "react";
import Switch, { SwitchProps } from "@mui/material/Switch";
import { styled } from "@mui/material";
import "./ThemeSwitcher.css";
import Cookies from "js-cookie";
import { useLocalication } from "@/utils/Internationalization";

interface IThemeSwitcherProps extends SwitchProps {}

const MaterialUISwitch = styled(Switch)(({ theme }) => ({
  "& .MuiSwitch-switchBase": {
    "&.Mui-checked": {
      color: "#fff",
      transform: "translateX(22px)",
      "& .MuiSwitch-thumb:before": {
        backgroundImage: `url('/images/svgs/moon.svg')`
      },
      "& + .MuiSwitch-track": {
        opacity: 1,
        backgroundColor: theme.palette.mode === "dark" ? "#8796A5" : "#aab4be",
        "&::before": {
          content: "''",
          position: "absolute",
          width: "100%",
          height: "100%",
          left: "12px",
          top: 0,
          backgroundRepeat: "no-repeat",
          backgroundPosition: "center",
          backgroundImage: `url('/images/svgs/sun.svg')`
        }
      }
    }
  },
  "& .<PERSON>iSwitch-thumb": {
    backgroundColor: "#fff",
    width: 32,
    height: 32,
    "&::before": {
      content: "''",
      position: "absolute",
      width: "100%",
      height: "100%",
      left: 0,
      top: 0,
      backgroundRepeat: "no-repeat",
      backgroundPosition: "center",
      backgroundImage: `url('/images/svgs/sun.svg')`
    }
  },
  "& .MuiSwitch-track": {
    opacity: 1,
    // backgroundColor: theme.palette.mode === "dark" ? "#8796A5" : "#aab4be",
    borderRadius: 20 / 2,
    position: "relative",
    "&::before": {
      content: "''",
      position: "absolute",
      width: "100%",
      height: "100%",
      right: "12px",
      top: 0,
      backgroundRepeat: "no-repeat",
      backgroundPosition: "center",
      backgroundImage: `url('/images/svgs/moon.svg')`
    }
  }
}));

function ThemeSwitcher(props: IThemeSwitcherProps) {
  const [isChecked, setIsChecked] = React.useState(false);
  const { isDark, setLocalization } = useLocalication();

  const toggleDarkMode = (val: boolean) => {
    if (!val) {
      Cookies.remove("dark");
    } else {
      Cookies.set("dark", "1");
    }
    setLocalization({ isDark: val });
  };

  const onChangeTheme = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    toggleDarkMode(checked);
    setIsChecked(checked);
  };

  React.useEffect(() => {
    setIsChecked(isDark);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  React.useEffect(() => {
    // add `dark` className to root `html` element for tailwind to be worked with `dark:` variant.
    if (isChecked) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [isChecked]);

  return (
    <MaterialUISwitch
      className="ThemeSwitcher-Styled"
      {...props}
      size="medium"
      checked={isChecked}
      defaultChecked={props.checked}
      onChange={onChangeTheme}
    />
  );
}

export default ThemeSwitcher;
