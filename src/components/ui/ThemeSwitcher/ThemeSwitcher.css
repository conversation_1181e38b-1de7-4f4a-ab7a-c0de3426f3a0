:root {
  --theme-switcher-checked-bg-color: #e0e0e0;
  --theme-switcher-disabled-thumb-color: #e0e0e0;
  --theme-switcher-disabled-track-opacity: 0.7;
  --theme-switcher-track-bg-color: #e9e9ea;
}

body.dark-mode {
  --theme-switcher-checked-bg-color: #e0e0e0;
  --theme-switcher-disabled-thumb-color: rgb(var(--color-gray-400));
  --theme-switcher-disabled-track-opacity: 0.3;
  --theme-switcher-track-bg-color: #39393d;
}

.ThemeSwitcher-Styled {
  width: 54px;
  height: 32px;
  padding: 0;
}

.ThemeSwitcher-Styled .MuiSwitch-switchBase {
  padding: 0;
  margin: 4px 2px;
  transition-duration: 300ms;
  transform: translateX(-25px);
}

.ThemeSwitcher-Styled .MuiSwitch-switchBase.Mui-checked {
  transform: translateX(0px);
  color: rgb(var(--color-cards));
}

/* .ThemeSwitcher-Styled .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track {
  background-color: var(--theme-switcher-checked-bg-color);
  opacity: 1;
  border: 0;
}

.ThemeSwitcher-Styled .MuiSwitch-switchBase .MuiSwitch-track:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  right: 12px;
  top: 0;
  background-color: red;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url('/images/svgs/moon.svg');
} */

.ThemeSwitcher-Styled .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track:before {
  left: 12px;
  background-image: url("/images/svgs/sun.svg");
}

.ThemeSwitcher-Styled .MuiSwitch-switchBase.Mui-disabled + .MuiSwitch-track {
  opacity: 0.5;
}

.ThemeSwitcher-Styled .MuiSwitch-switchBase.Mui-focusVisible .MuiSwitch-thumb {
  color: #e0e0e0;
  border: 6px solid #fff;
}

.ThemeSwitcher-Styled .MuiSwitch-switchBase.Mui-disabled .MuiSwitch-thumb {
  color: var(--theme-switcher-disabled-thumb-color);
}

.ThemeSwitcher-Styled .MuiSwitch-switchBase.Mui-disabled + .MuiSwitch-track {
  opacity: var(--theme-switcher-disabled-track-opacity);
}

.ThemeSwitcher-Styled .MuiSwitch-thumb {
  box-sizing: border-box;
  width: 24px;
  height: 24px;
}

.ThemeSwitcher-Styled .MuiSwitch-track {
  border-radius: 46px;
  background-color: var(--theme-switcher-track-bg-color);
  opacity: 1;
  transition: background-color 500ms;
}
