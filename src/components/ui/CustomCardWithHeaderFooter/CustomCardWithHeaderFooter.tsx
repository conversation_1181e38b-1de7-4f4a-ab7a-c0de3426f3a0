import "./CustomCardWithHeaderFooter.css";

import { useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { themeCustomizer } from "@/utils/theme";

type Props = {
  title?: string;
  subtitle?: string;
  action?: JSX.Element | any;
  footer?: JSX.Element;
  cardheading?: string | JSX.Element;
  headtitle?: string | JSX.Element;
  headsubtitle?: string | JSX.Element;
  children?: JSX.Element;
  middlecontent?: string | JSX.Element;
};

const CustomCardWithHeaderFooter = ({
  title,
  subtitle,
  children,
  action,
  footer,
  cardheading,
  headtitle,
  headsubtitle,
  middlecontent
}: Props) => {
  const theme = useTheme();
  const borderColor = theme.palette.divider;

  return (
    <Card
      id="sx-customcard-3084"
      sx={{ border: !themeCustomizer.isCardShadow ? `1px solid ${borderColor}` : "none" }}
      elevation={themeCustomizer.isCardShadow ? 9 : 0}
      variant={!themeCustomizer.isCardShadow ? "outlined" : undefined}
    >
      {cardheading ? (
        <CardContent>
          <Typography variant="h5">{headtitle}</Typography>
          <Typography variant="subtitle2" color="textSecondary">
            {headsubtitle}
          </Typography>
        </CardContent>
      ) : (
        <CardContent id="sx-customcard-3096">
          {title ? (
            <Stack
              direction="row"
              flexWrap="wrap"
              spacing={2}
              justifyContent="space-between"
              alignItems={"center"}
              mb={3}
            >
              <Box>
                {title ? <Typography variant="h5">{title}</Typography> : ""}

                {subtitle ? (
                  <Typography variant="subtitle2" color="textSecondary">
                    {subtitle}
                  </Typography>
                ) : (
                  ""
                )}
              </Box>
              {action}
            </Stack>
          ) : null}

          {children}
        </CardContent>
      )}

      {middlecontent}
      {footer}
    </Card>
  );
};

export default CustomCardWithHeaderFooter;
