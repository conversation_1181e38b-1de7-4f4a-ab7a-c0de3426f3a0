import React from "react";

import Typography from "@mui/material/Typography";
import withClassname from "../../../utils/withClassName";
import "./CustomFormLabel.css";
import { useTranslation } from "react-i18next";
import { TypographyProps } from "@mui/system";
import { Tooltip } from "@mui/material";
import { Icon } from "@iconify/react";
import HoverPopover from "../popover/HoverPopover";

interface ICustomFormLabelProps extends TypographyProps {
  optional?: boolean;
  children: React.ReactNode;
  htmlFor?: string;
  labelTooltipTitle?: string;
  labelTooltipDescription?: string;
}

const CustomFormLabel = withClassname((props: ICustomFormLabelProps) => {
  const { t } = useTranslation();
  const { labelTooltipTitle, labelTooltipDescription, children, optional } = props || {};

  return (
    <div className="flex items-center gap-0.5">
      <Typography variant="subtitle1" fontWeight={600} {...props} component="label" htmlFor={props.htmlFor}>
        {children}
        {!!optional && (
          <Typography variant="caption" className="custom-form-label-mandatory" mx={0.25}>
            ({t("optional")})
          </Typography>
        )}
      </Typography>
      {(labelTooltipTitle || labelTooltipDescription) && (
        <HoverPopover
          content={
            <div className="flex flex-col gap-1 max-w-64">
              {labelTooltipTitle && <div className="text-gray-999 font-medium text-sm">{labelTooltipTitle}</div>}
              {labelTooltipDescription && (
                <div className="text-gray-500 font-normal text-xs">{labelTooltipDescription}</div>
              )}
            </div>
          }
          popOverProps={{
            anchorOrigin: { vertical: "top", horizontal: "left" },
            transformOrigin: { vertical: "bottom", horizontal: "right" }
          }}
        >
          <Icon icon="solar:info-circle-bold" className="text-purple-500 size-4 cursor-pointer" />
        </HoverPopover>
      )}
    </div>
  );
}, "styled-custom-form-label");

export default CustomFormLabel;
