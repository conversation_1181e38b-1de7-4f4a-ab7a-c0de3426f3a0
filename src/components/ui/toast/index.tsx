import { Icon } from "@iconify/react";
import { HTMLAttributes, ReactNode } from "react";
import { toast as ReactToastify, ToastContent, ToastContentProps, ToastOptions } from "react-toastify";
import { twMerge } from "tailwind-merge";

export const toast = <T extends ReactNode>(data: T, options?: ToastOptions<T>) => {
  ReactToastify<T>(customRender, {
    customProgressBar: true,
    data,
    rtl: true,
    icon: false,
    ...options
  });
};

const bindProgressBarStrokeColor = {
  default: "#2970FF",
  error: "#F04438",
  info: "#2970FF",
  success: "#18B466",
  warning: "#FC8415"
};

const bindProgressBarBgColor = {
  default: "bg-v2-surface-info",
  error: "bg-v2-surface-error",
  info: "bg-v2-surface-info",
  success: "bg-v2-surface-success",
  warning: "bg-v2-surface-warining-1"
};

function customRender<T extends ReactNode>({ closeToast, isPaused, toastProps, data }: ToastContentProps<T>) {
  const strokeDash = 565.48;
  const attributes: HTMLAttributes<SVGCircleElement> = {};
  const type = toastProps?.type;

  // handle controlled progress bar
  // controlled progress bar uses a transition
  if (typeof toastProps.progress === "number") {
    attributes.style = {
      transition: "all .1s linear",
      strokeDashoffset: `${strokeDash - strokeDash * toastProps.progress}px`
    };

    if (toastProps.progress >= 1) {
      attributes.onTransitionEnd = () => {
        closeToast();
      };
    }
  } else if (!!toastProps?.autoClose) {
    // normal autoclose uses an animation
    // animation inside index.css
    attributes.className = "animate";
    attributes.style = {
      animationDuration: `${toastProps.autoClose}ms`,
      animationPlayState: isPaused ? "paused" : "running"
    };

    attributes.onAnimationEnd = () => {
      closeToast();
    };
  }

  return (
    <div className="flex justify-between items-center w-full gap-2.5">
      <div className={twMerge("shrink-0 relative size-9 rounded-full", bindProgressBarBgColor[type])}>
        {type === "error" && (
          <Icon
            icon="solar:danger-triangle-outline"
            className="absolute size-5 top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 text-v2-content-on-error-2"
          />
        )}
        {type === "warning" && (
          <Icon
            icon="solar:shield-warning-outline"
            className="absolute size-5 top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 text-v2-content-on-warning-2"
          />
        )}
        {type === "success" && (
          <Icon
            icon="solar:check-circle-outline"
            className="absolute size-5 top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 text-v2-content-on-success-2"
          />
        )}
        {(type === "info" || type === "default") && (
          <Icon
            icon="solar:info-circle-outline"
            className="absolute size-5 top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 text-v2-content-on-info-2"
          />
        )}

        {!!toastProps?.autoClose && (
          <svg
            width="44"
            height="44"
            viewBox="-25 -25 250 250"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            className="-rotate-90 absolute -top-1 -right-1"
          >
            <circle
              r="90"
              cx="100"
              cy="100"
              fill="transparent"
              stroke="transparent"
              strokeWidth="6"
              strokeDasharray={`${strokeDash}px`}
              strokeDashoffset="0"
            />
            <circle
              r="90"
              cx="100"
              cy="100"
              stroke={bindProgressBarStrokeColor[type]}
              strokeWidth="10px"
              strokeLinecap="round"
              fill="transparent"
              strokeDasharray={`${strokeDash}px`}
              {...attributes}
            />
          </svg>
        )}
      </div>

      <p className="text-v2-content-primary text-base font-normal font-iranYekan">{data}</p>
    </div>
  );
}
