import React, { ReactNode, useState } from "react";
import { Collapse as Mu<PERSON><PERSON>ollapse } from "@mui/material";
import { Icon } from "@iconify/react";
import { twMerge } from "tailwind-merge";

function Collapse({
  children,
  initialIsOpen = false,
  title,
  icon,
  subTitle
}: {
  children: ReactNode;
  initialIsOpen?: boolean;
  title: ReactNode;
  icon?: ReactNode;
  subTitle?: ReactNode;
}) {
  const [isOpen, setIsOpen] = useState(initialIsOpen);

  return (
    <div className="bg-v2-surface-primary border-2 border-v2-border-primary rounded-[9px]">
      <div className="cursor-pointer flex flex-col gap-2 p-6" onClick={() => setIsOpen(prev => !prev)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center justify-start flex-1 gap-2">
            {icon}
            <div className="font-semibold text-base text-gray-999">{title}</div>
          </div>
          <div className="shrink-0">
            <Icon
              icon="solar:alt-arrow-down-outline"
              className={twMerge("size-4 text-gray-999 transition-transform", isOpen ? "rotate-180" : "")}
            />
          </div>
        </div>
        {subTitle && <div className="text-xs font-medium text-gray-999">{subTitle}</div>}
      </div>

      <MuiCollapse in={isOpen} timeout="auto">
        <div className="px-6 pb-6">
          <div className="border-t border-gray-50 pt-4">{children}</div>
        </div>
      </MuiCollapse>
    </div>
  );
}

export default Collapse;
