import { cva, type VariantProps } from "class-variance-authority";
import clsx from "clsx";
import * as React from "react";
import { twMerge } from "tailwind-merge";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:cursor-not-allowed [&_svg]:pointer-events-none [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        primary:
          "bg-v2-surface-action text-v2-content-on-action-1 disabled:bg-v2-surface-action-disable disabled:text-v2-content-on-action-disable hover:bg-v2-surface-action-hover border border-transparent",
        secondaryGray:
          "bg-v2-surface-primary text-v2-content-secondary disabled:text-v2-content-subtle hover:bg-v2-surface-secondary border border-v2-border-primary",
        secondaryColor:
          "bg-v2-surface-info text-v2-content-on-info disabled:text-v2-content-subtle border border-transparent",
        tertiaryGray:
          "bg-transparent hover:bg-v2-surface-secondary text-v2-content-secondary disabled:text-v2-content-subtle border border-transparent",
        destructivePrimary: "bg-v2-content-on-error-2 text-v2-content-on-action-1 border border-transparent",
        destructiveSecondaryGray:
          "bg-v2-surface-primary text-v2-content-on-error-2 disabled:text-v2-content-subtle hover:bg-v2-surface-secondary border border-v2-border-primary",
        warningPrimary:
          "bg-v2-surface-warining-2 text-v2-content-on-action-1 disabled:bg-v2-surface-warining-2/20 disabled:text-v2-content-on-warning-2/50 hover:bg-v2-surface-warining-hover border border-transparent",
        accentPrimary:
          "bg-v2-content-on-error-2 text-v2-content-on-action-1 disabled:bg-v2-surface-error-disable disabled:text-v2-content-on-error-disable hover:bg-v2-surface-error-hover border border-transparent"
      },
      size: {
        xl: "px-5 h-12 rounded-lg text-[15px] font-medium [&_svg]:size-6",
        lg: "px-[18px] h-11 rounded-lg text-[15px] font-medium [&_svg]:size-5",
        md: "px-4 h-10 rounded-lg text-sm font-medium [&_svg]:size-5",
        sm: "px-3.5 h-9 rounded-lg text-[13px] leading-6 font-medium [&_svg]:size-5"
      }
    },
    defaultVariants: {
      variant: "primary",
      size: "md"
    }
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  fullWidth?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, children, startAdornment, endAdornment, fullWidth, ...props }, ref) => {
    return (
      <button
        className={twMerge(clsx(buttonVariants({ variant, size, className })), fullWidth ? "w-full " : "", "")}
        ref={ref}
        {...props}
      >
        {startAdornment}
        {children}
        {endAdornment}
      </button>
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
