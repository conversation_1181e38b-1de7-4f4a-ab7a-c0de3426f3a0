.custom-radio {
  color: rgb(var(--color-gray-500));
  padding: 6px !important;
}

.custom-radio-wrapper .MuiTypography-root {
  font-size: 14px;
  font-weight: 400;
  color: rgb(var(--color-gray-999)) !important;
  white-space: nowrap;
  /* padding: 8px; */
}

.custom-radio.Mui-checked.MuiRadio-colorSecondary {
  color: #399bfa;
}

.custom-radio.Mui-checked.MuiRadio-colorPrimary {
  color: rgb(var(--color-purple-500)) !important;
}

.styled-radio-icon {
  border-radius: 50%;
  width: 21px;
  height: 21px;
  background-color: transparent;
}

.styled-radio-icon.light {
  border: 1px solid var(--mui-palette-grey-300);
}

.Mui-focusVisible .styled-radio-icon.dark {
  outline: 0px auto var(--mui-theme-palette-grey-200);
}

.Mui-focusVisible .styled-radio-icon.light {
  outline: 0px auto var(--mui-theme-palette-grey-300);
}

.Mui-focusVisible .styled-radio-icon {
  outline-offset: 2;
}

input:disabled ~ .styled-radio-icon {
  box-shadow: none;
  background: var(--mui-theme-palette-grey-100);
}

.styled-radio-bp-checked-icon::before {
  box-shadow: none;
  width: 21px;
  height: 21px;
  display: block;
  content: " ";
}

.styled-radio-bp-checked-icon.dark::before {
  background-color: rgb(var(--color-purple-500));
  background-image: radial-gradient(rgb(var(--color-cards)), rgb(var(--color-cards)) 28%, transparent 32%);
}

.styled-radio-bp-checked-icon {
  border-radius: 50%;
  overflow: hidden;
}

.styled-radio-bp-checked-icon.light::before {
  background-color: rgb(var(--color-purple-500));
  background-image: radial-gradient(#fff, #fff 28%, transparent 32%);
}

@media (max-width: 768px) {
  .custom-radio-wrapper .MuiTypography-root {
    font-size: 12px;
    font-weight: 500;
  }
}
