import "./CustomRadio.css";

import * as React from "react";
import Radio, { RadioProps } from "@mui/material/Radio";
import { FormControlLabel } from "@mui/material";
import { twMerge } from "tailwind-merge";

interface ICustomRadioProps extends RadioProps {
  label?: string;
  labelClassName?: string;
}

function CustomRadio(props: ICustomRadioProps) {
  return (
    <FormControlLabel
      className={twMerge("custom-radio-wrapper", props?.labelClassName)}
      control={
        <Radio
          {...props}
          className={twMerge("custom-radio", props?.className)}
          disableRipple
          color={props?.color || "primary"}
          inputProps={{ "aria-label": "Checkbox demo" }}
        />
      }
      label={props?.label}
    />
  );
}

export default CustomRadio;
