.custom-autocomplete {
  width: 100%;
}

.custom-autocomplete .MuiInputBase-root {
  padding: 6px;
}

.custom-autocomplete.MuiAutocomplete-root .MuiOutlinedInput-root {
  padding: 6.5px 9px !important;
}

.custom-autocomplete .MuiInputBase-sizeSmall {
  padding: 0 6px;
  max-height: 32px;
  font-size: 12px;
  font-weight: 400;
}

.custom-autocomplete-popper-small > * {
  font-size: 12px !important;
  font-weight: 400 !important;
}

@media (max-width: 400px) {
  .custom-autocomplete.MuiAutocomplete-root .MuiOutlinedInput-root {
    padding: 2.5px 9px !important;
  }
}

@media (max-width: 500px) {
  .custom-autocomplete.MuiAutocomplete-root .MuiOutlinedInput-root {
    padding: 3.5px 9px !important;
  }
}

/* MuiAutocomplete-root .MuiOutlinedInput-root */
