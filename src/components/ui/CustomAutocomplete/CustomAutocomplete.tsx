import * as React from "react";
import { Autocomplete, AutocompleteProps } from "@mui/material";

import "./CustomAutocomplete.css";
import clsx from "clsx";
import { Icon } from "@iconify/react";
import { twMerge } from "tailwind-merge";
import Input from "../inputs/Input";
import { useTranslation } from "react-i18next";
import { isBoolean } from "lodash";

interface ICustomAutoCompleteProps<T, Multiple extends boolean | undefined = false>
  extends Omit<AutocompleteProps<T, Multiple, false, false>, "renderInput"> {
  label?: string;
  placeholder?: string;
  readOnlyInput?: boolean;
  optional?: boolean;
  error?: boolean;
  helperText?: string;
  renderInput?: AutocompleteProps<T, Multiple, false, false>["renderInput"];
  handleBlur?: React.FocusEventHandler;
  name?: string;
  labelTooltipTitle?: string;
  labelTooltipDescription?: string;
  labelCLassName?: string;
  inputParentClassName?: string;
  startAdornment?: React.ReactNode;
  requiredStar?: boolean;
}

function CustomAutocomplete<T, Multiple extends boolean | undefined = false>({
  renderInput,
  placeholder,
  error,
  helperText,
  optional,
  readOnlyInput,
  handleBlur,
  name,
  startAdornment,
  labelTooltipTitle,
  labelTooltipDescription,
  requiredStar,
  inputParentClassName,
  disablePortal = true,
  ...props
}: ICustomAutoCompleteProps<T, Multiple>) {
  const { t } = useTranslation();

  return (
    <Autocomplete
      {...props}
      disablePortal={disablePortal}
      forcePopupIcon={false}
      autoComplete={false}
      id="custom-autocomplete"
      className={clsx("custom-autocomplete", props?.className)}
      classes={{
        ...props?.classes,
        popper: props?.classes?.popper || `custom-autocomplete-popper-${props?.size}`
      }}
      noOptionsText={
        props?.noOptionsText || <span className="text-body4-medium text-v2-content-tertiary">{t("noOptions")}</span>
      }
      renderInput={
        renderInput ||
        (params => (
          <div ref={params.InputProps.ref} className="autoCompleteWrapper">
            <Input
              {...params.inputProps}
              error={error}
              helperText={helperText}
              inputParentClassName={inputParentClassName}
              optional={optional}
              label={props?.label}
              placeholder={placeholder || props?.label}
              labelTooltipTitle={labelTooltipTitle}
              labelTooltipDescription={labelTooltipDescription}
              id={params.id}
              readOnly={isBoolean(readOnlyInput) ? readOnlyInput : true}
              autoComplete="off"
              startAdornment={startAdornment}
              endAdornment={
                <Icon
                  width={20}
                  height={20}
                  icon="solar:alt-arrow-down-outline"
                  className="!pointer-events-none text-gray-400 cursor-pointer"
                />
              }
              requiredStar={requiredStar}
              className={twMerge(params?.inputProps?.className)}
            />
          </div>
        ))
      }
    />
  );
}

export default CustomAutocomplete;
