.sx-categoryselect-2939 {
  flex-wrap: wrap;
  gap: 8px;
  padding-block: 10px;
}

.nested-start-adornment-wrapper-category {
  display: flex;
}

.nested-start-adornment-wrapper-empty {
  display: none;
}

#sx-categoryselect-2943 {
  background-color: transparent;
  border: 1px solid #d4d4d4;
  margin-bottom: calc(var(--mui-theme-spacing-1) / 2);
  margin-left: calc(var(--mui-theme-spacing-1) / 2);
}

#sx-categoryselect-2943 .MuiButtonBase-root {
  border-radius: 40px;
}

.nested-select-menu .MuiPaper-root {
  height: 321px;
}

.nested-select-menu-empty .MuiPaper-root {
  height: 50px;
}

.nested-select-chip-wrapper {
  border: 1px solid #d4d4d4;
  border-radius: 40px;
  padding: 4px 10px;
  pointer-events: all;
}

.nested-select-chip-text {
  font-size: 14px;
  font-weight: 400;
}

.nested-select-chip-icon {
  cursor: pointer;
}
