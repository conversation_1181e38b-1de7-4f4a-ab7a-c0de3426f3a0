import "./NestedSelect.css";

import {
  Checkbox,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  Box,
  Grid,
  Typography,
  Menu,
  InputProps,
  Stack,
  CircularProgress,
  Radio
} from "@mui/material";
import CustomTextField from "../CustomTextField/CustomTextField";
import React, { ReactNode, useEffect, useMemo, useRef, useState } from "react";
import { TMetaCategoriesData } from "@/store/apps/meta/types";
import { ExpandLess, ExpandMore } from "@mui/icons-material";
import Tree, { Node } from "react-virtualized-tree";
import useDebounce from "@/utils/hooks/useDebounce";
import { CSSProperties } from "react";
import uniq from "lodash/uniq";
import useDirection from "@/utils/hooks/useDirection";

import { omit } from "lodash";
import { Icon } from "@iconify/react";
import clsx from "clsx";

type FlattedList = Array<{ ids: (string | number)[]; label: string; selectable: boolean }>;

function makeFlatten<T>(
  mainList: Array<T>,
  getLabel: (v: T) => string,
  getKey: (v: T) => string | number,
  getChildren: (v: T) => Array<T> | null,
  getSelectable: (v: T) => boolean
): FlattedList {
  const res: FlattedList = [];

  const flatter = (list: Array<T>, obj: FlattedList, seed: Array<string | number>): FlattedList => {
    return list.reduce((map: FlattedList, item: T) => {
      const label = getLabel(item);
      const children = getChildren(item);
      const key = getKey(item);
      const selectable = getSelectable(item);
      const newFlatted = {
        ids: seed.concat(key),
        label,
        selectable
      };
      if (children && children.length) return map.concat(newFlatted).concat(flatter(children, obj, seed.concat(key)));
      return map.concat(newFlatted);
    }, obj);
  };

  return flatter(mainList, res, []);
}

interface ItemProp {
  node: Node;
  isExpanded?: boolean;
  isSelected?: boolean;
  isIntermediate?: boolean;
  onSelect: (v: boolean) => void;
  onExpand: (v: boolean) => void;
  checked?: boolean | "intermediate";
  selectable?: boolean;
  multiple?: boolean;
}

const checkIntermediat = (node: Node, selecteds: Array<number | string>): boolean => {
  return Boolean(
    node.children?.find((child: Node) => {
      if (child?.children?.length) {
        return checkIntermediat(child, selecteds);
      }
      return selecteds.includes(child?.id);
    })
  );
};

const Item = (props: ItemProp) => {
  const { node, isSelected, isExpanded, isIntermediate, selectable = true, onExpand, onSelect, multiple } = props;

  const hasChild = Boolean(node?.children?.length);

  const handleClick = () => {
    if (Boolean(node.children?.length)) onExpand(!isExpanded);
    else onSelect(!isSelected);
  };

  return (
    <ListItemButton selected={isSelected} onClick={handleClick}>
      {hasChild && <ListItemIcon>{isExpanded ? <ExpandLess /> : <ExpandMore />}</ListItemIcon>}
      <ListItemText primary={node.name} />
      {selectable && (
        <>
          {multiple ? (
            <Checkbox
              checked={isSelected}
              indeterminate={isIntermediate}
              onChange={(e, v) => {
                onSelect(v);
              }}
            />
          ) : (
            <Radio
              checked={isSelected}
              onChange={(e, v) => {
                onSelect(v);
              }}
            />
          )}
        </>
      )}
    </ListItemButton>
  );
};

function formatNested<T>(
  data: T[],
  getLabel: (v: T) => string,
  getKey: (v: T) => string | number,
  getChildren: (v: T) => Array<T> | null
): Node[] {
  const res: Node[] = [];
  data.forEach(item => {
    const id = getKey(item);
    const name = getLabel(item);
    let children: Node[] | T[] = getChildren(item) ?? [];
    if (children && children.length) {
      children = formatNested(children as T[], getLabel, getKey, getChildren);
    }
    res.push({ children: children as Node[], id, name });
  });
  return res;
}

interface TNestedSelectProps {
  value: (number | string)[];
  isLoading?: boolean;
  notfoundText?: string;
  multiple?: boolean;
  label?: string;
  onChange: (v: (number | string)[]) => void;
  helperText?: string;
  error?: boolean;
  hideSelecteds?: boolean;
  hasEndAdornment?: boolean;
  InputProps?: {
    name?: string;
    id?: string;
    clasName?: string;
    onBlur?: InputProps["onBlur"];
    error?: boolean;
    helperText?: string | ReactNode;
    placeholder?: string;
  };
}

function setShowingExpandeds(nested: Array<Node>, expandeds: Array<number | string>): Array<Node> {
  return nested.map(node => {
    return {
      ...node,
      children: node.children?.length ? setShowingExpandeds(node.children, expandeds) : [],
      state: { expanded: expandeds.includes(node.id) }
    };
  });
}

function filterNested(data: Array<Node>, filtered: FlattedList): Array<Node> {
  return data
    .map(item => {
      if (item.id && filtered.find(v => v.ids.includes(item.id))) {
        return {
          ...item,
          children: item?.children?.length ? filterNested(item.children, filtered) : undefined
        };
      }
    })
    .filter(Boolean) as Array<Node>;
}

export type ItemSelected = {
  findCaregories: (id?: number[]) => Array<TMetaCategoriesData | undefined> | undefined;
};

function NestedSelect<T>(
  props: TNestedSelectProps & {
    data?: Array<T>;
    getLabel: (v: T) => string;
    getKey: (v: T) => number | string;
    getChildren: (v: T) => T[] | null;
    getSelectable?: (v: T) => boolean;
  }
) {
  const {
    value,
    isLoading,
    notfoundText,
    onChange,
    multiple,
    hideSelecteds = false,
    data,
    hasEndAdornment = true,
    getChildren,
    getKey,
    getLabel,
    error,
    helperText,
    InputProps,
    getSelectable = () => true
  } = props;

  const [query, setQuery] = useState("");
  const selected = useRef<Array<number | string>>(value || []);
  const dataFlattened = useRef<FlattedList | null>(null);
  const [filtered, setFiltered] = useState<FlattedList | null>(null);
  const [presentData, setPresentData] = useState<Array<Node>>([]);
  const selectableMap = useRef<{ [k: string]: boolean }>({});

  const [activeDir] = useDirection();

  const debouncedSearch = useDebounce((searchTerm: string) => {
    if (dataFlattened.current && searchTerm.length >= 2) {
      const res = dataFlattened.current.filter(({ label }) => {
        return label.includes(searchTerm) || searchTerm.split(" ").find(splited => label.indexOf(splited) > -1);
      });
      setFiltered(res);
    }
    if (searchTerm.length < 2) setFiltered(null);
  }, 750);

  useEffect(() => {
    if (data && data.length) {
      const flatData = makeFlatten(data, getLabel, getKey, getChildren, getSelectable);
      dataFlattened.current = flatData;
      setPresentData(formatNested(data, getLabel, getKey, getChildren));
      selectableMap.current = flatData.reduce(
        (all, item) => ({ ...all, [item.ids.reverse()[0]]: item.selectable }),
        {}
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  // useEffect(() => {}, [data, filtered]);

  const anchor = useRef(null);
  const [menuOpen, setIsOpen] = useState<"top" | "bottom" | null>(null);

  const handleClick = (el: HTMLElement | null) => {
    if (!el?.getClientRects()) return;
    const { y } = el?.getClientRects()[0];

    setIsOpen(y > 250 ? "top" : "bottom");
  };

  const handleChange = (nodes: Node[]) => {
    setPresentData(nodes);
  };

  const handleSelect = (checked: boolean, id: number | string) => {
    if (checked) {
      if (multiple) selected.current = selected.current.concat(id as number);
      else selected.current = [id as number];
    } else {
      selected.current = selected.current.filter((item: number | string) => item !== id);
    }

    onChange(uniq(selected.current));
  };

  const getStyle = (style: CSSProperties) => {
    return {
      ...style,
      ...(activeDir === "rtl" && style.marginLeft
        ? {
            left: "initial",
            marginLeft: "initial",
            marginRight: 20,
            right: style.left
          }
        : {})
    };
  };

  const [expandeds, setExpandeds] = useState<Array<number | string>>([]);

  const handleExpand = (toValue: boolean, id: number | string) => {
    if (!id) return;
    if (toValue) setExpandeds(prev => prev.concat(id));
    else setExpandeds(prev => prev.filter(val => val !== id));
  };

  const selectedItems: Array<{ id: number | string; label: string }> = useMemo(() => {
    if (value?.length && Array.isArray(value) && dataFlattened.current && dataFlattened.current.length)
      return value
        .map(id => {
          const item = dataFlattened.current?.find(({ ids }) => ids.includes(id));
          if (item)
            return {
              id,
              label: item.label
            };
          return null;
        })
        .filter(Boolean) as Array<{ id: number | string; label: string }>;
    return [];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, dataFlattened.current]);

  const toShow = useMemo(() => {
    // expanded should stored outside  of tree data struchture ( filterng aftermath)
    //
    if (presentData) {
      const filteredNestedData = filtered ? filterNested(presentData, filtered) : presentData;
      const filteredIds = filtered
        ? filtered.reduce((total: Array<string | number>, { ids }) => total.concat(ids as Array<string | number>), [])
        : [];
      const allExpandeds = filteredIds?.length ? expandeds.concat(filteredIds) : expandeds;

      return setShowingExpandeds(filteredNestedData, allExpandeds);
    }
    return;
  }, [presentData, expandeds, filtered]);

  const clickRef = useRef<HTMLElement | null | EventTarget>(null);

  const tree = useMemo(() => {
    if (toShow) {
      return (
        <Tree nodes={toShow} onChange={handleChange}>
          {({ style, node }: { style: CSSProperties; node: Node }) => (
            <div style={getStyle(style)}>
              <Item
                node={node}
                isSelected={value?.includes(String(node?.id))}
                isExpanded={expandeds.includes(node?.id)}
                onSelect={value => {
                  if (!selectableMap.current?.[node.id]) return;
                  handleSelect(value, node.id);
                  if (!multiple) setIsOpen(null);
                }}
                onExpand={value => handleExpand(value, node?.id)}
                isIntermediate={checkIntermediat(node, value)}
                selectable={selectableMap.current?.[node.id]}
                multiple={multiple}
              />
            </div>
          )}
        </Tree>
      );
    }
    return null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [toShow, value]);

  const containerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const offClick: EventListenerOrEventListenerObject = (e: Event) => {
      const target = e.target as HTMLElement;

      if (target.id === "menu-back-drop") {
        setIsOpen(null);
      }

      if (
        clickRef.current &&
        clickRef.current !== target &&
        ((menuTarget.current && menuTarget.current !== target) || target.id === "cat-popup")
      ) {
        setIsOpen(prev => (prev ? null : prev));
      }
    };

    document.documentElement.addEventListener("click", offClick);
    return () => {
      document.documentElement.removeEventListener("click", offClick);
    };
  }, []);

  const popupWidth = containerRef?.current?.clientWidth ? containerRef?.current?.clientWidth : 350;
  const menuTarget = useRef<EventTarget | null>(null);

  // ref is for calc of popup width
  // onclick is tohold ther track of target if click target is out side of the root element
  // will close the popu

  return (
    <>
      <Grid
        container
        direction="column"
        width="100%"
        ref={(r: HTMLDivElement | null) => {
          containerRef.current = r;
        }}
        onClick={e => {
          clickRef.current = e.target;
        }}
      >
        {multiple ? (
          <CustomTextField
            {...InputProps}
            fullWidth
            helperText={helperText}
            error={error}
            value={query}
            ref={anchor}
            onChange={e => {
              setQuery(e.target.value);
              debouncedSearch(e.target.value);
            }}
            onClick={e => {
              handleClick(e.currentTarget.parentNode as HTMLElement | null);
              (e.target as HTMLImageElement).focus();
            }}
            InputProps={{
              startAdornment: !hideSelecteds ? (
                <Box
                  className={clsx(
                    "sx-categoryselect-2939",
                    selectedItems?.length
                      ? "nested-start-adornment-wrapper-category"
                      : "nested-start-adornment-wrapper-empty"
                  )}
                >
                  {selectedItems.map(item => {
                    return (
                      <Stack
                        key={item.id}
                        flexDirection="row"
                        alignItems="center"
                        gap={0.5}
                        className="nested-select-chip-wrapper"
                      >
                        <Icon
                          icon="ic:outline-close"
                          width={18}
                          height={18}
                          color="rgb(var(--color-gray-400))"
                          className="nested-select-chip-icon"
                          onClick={e => {
                            e.stopPropagation();
                            if (item.id) handleSelect(false, item.id);
                          }}
                        />
                        <Typography className="nested-select-chip-text" whiteSpace="nowrap" key={item.id}>
                          {item.label}
                        </Typography>
                      </Stack>
                    );
                  })}
                </Box>
              ) : undefined,
              endAdornment: isLoading ? (
                <CircularProgress size={16} />
              ) : (
                hasEndAdornment && (
                  <Icon
                    width={24}
                    height={24}
                    icon="solar:alt-arrow-down-outline"
                    className="pointer-events-none text-gray-400"
                  />
                )
              )
            }}
          />
        ) : (
          <>
            <CustomTextField
              {...InputProps}
              color={error ? "error" : undefined}
              onClick={() => handleClick(containerRef.current as HTMLElement)}
              ref={anchor}
              InputProps={{
                endAdornment: isLoading ? (
                  <CircularProgress size={16} />
                ) : (
                  <Icon
                    width={24}
                    height={24}
                    icon="solar:alt-arrow-down-outline"
                    className="pointer-events-none text-gray-400"
                  />
                )
              }}
              value={selectedItems.length ? selectedItems?.[0]?.label : ""}
            />
          </>
        )}
      </Grid>

      <Menu
        open={Boolean(menuOpen)}
        onClose={() => setIsOpen(null)}
        id="cat-popup"
        anchorOrigin={menuOpen ? { vertical: menuOpen, horizontal: "right" } : undefined}
        hideBackdrop
        transformOrigin={
          menuOpen ? { vertical: menuOpen === "top" ? "bottom" : "top", horizontal: "right" } : undefined
        }
        anchorEl={containerRef.current}
        BackdropProps={{ id: "menu-back-drop" }}
        onAnimationEnd={() => {
          setTimeout(() => {
            containerRef.current?.querySelector("input")?.focus();
          }, 2000);
        }}
        className={!data?.length && !!notfoundText ? "nested-select-menu-empty" : "nested-select-menu"}
        onClick={e => {
          menuTarget.current = e.target;
        }}
      >
        {!data?.length && !!notfoundText ? (
          <Box width={popupWidth} display="flex" alignItems="center" justifyContent="center">
            <Typography>{notfoundText}</Typography>
          </Box>
        ) : (
          <Box height={250} width={popupWidth} padding={2}>
            <CustomTextField
              {...omit(InputProps, ["error", "helperText"])}
              fullWidth
              value={query}
              onChange={e => {
                setQuery(e.target.value);
                debouncedSearch(e.target.value);
              }}
            />

            {tree}
          </Box>
        )}
      </Menu>
    </>
  );
}

export default NestedSelect;
