.custom-textfield input {
  /* min-width: 100%; */
  padding: 14px 14px;
  color: rgb(var(--color-gray-999));
}

.custom-textfield input::placeholder {
  font-size: 14px;
  font-weight: 500;
}

.custom-textfield textarea {
  padding: 0;
}

.custom-textfield textarea::placeholder {
  font-size: 14px;
  font-weight: 500;
}

.custom-textfield--secondary .MuiOutlinedInput-root {
  overflow: hidden;
  /* background: rgb(var(--color-cards)) !important; */
}
.custom-textfield--secondary .MuiOutlinedInput-notchedOutline {
  border-color: rgb(var(--color-gray-40));
}

.custom-textfield .MuiOutlinedInput-root {
  overflow: hidden;
  /* background: rgb(var(--color-gray-40)); */
  border-color: rgb(var(--neutral-neutral-light)) !important;
  border: 1.5px solid;
  border-radius: 8px;
}

.style-custom-textfield .MuiOutlinedInput-input::-webkit-input-placeholder {
  color: var(--mui-palette-text-secondary);

  opacity: 0.8;
}
.style-custom-textfield .MuiOutlinedInput-input.Mui-disabled::-webkit-input-placeholder {
  color: var(--mui-palette-text-secondary);
  opacity: 1;
}
.style-custom-textfield .Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: var(--mui-palette-grey-200);
}

.custom-textfield .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: rgb(var(--color-purple-500));
}

@media (max-width: 500px) {
  .style-custom-textfield .MuiFormHelperText-root.Mui-error {
    width: 270px;
  }

  .custom-textfield input {
    padding: 11px 14px;
  }
}
.style-custom-textfield .MuiFormHelperText-root.Mui-error {
  width: 260px;
}

.custom-textfield-error-icon {
  color: #e85238;
}
