import React, { ReactNode } from "react";
import TextField, { TextFieldProps } from "@mui/material/TextField";
import { Box, InputAdornment } from "@mui/material";
import clsx from "clsx";
import { Icon } from "@iconify/react";
import { twMerge } from "tailwind-merge";

import "./CustomTextField.css";
import CustomFormLabel from "../CustomFormLabel/CustomFormLabel";
import InputHelper from "../CustomFormHelperText/InputHelper";

export type ICustomTextFieldProps = {
  labelClassName?: string;
  optional?: boolean;
  maxCharClassName?: string;
  maxCharLength?: number;
  endAdornment?: ReactNode;
  labelTooltipTitle?: string;
  labelTooltipDescription?: string;
  disableAutoComplete?: boolean;
} & TextFieldProps;

const CustomTextField = ({
  label,
  disableAutoComplete = false,
  optional,
  error,
  helperText,
  endAdornment,
  labelClassName,
  labelTooltipTitle,
  maxCharClassName,
  labelTooltipDescription,
  maxCharLength,
  ...props
}: ICustomTextFieldProps) => {
  const renderErrorIcon = () => <Icon fontSize={24} icon="solar:danger-bold" className="text-error-500" />;

  const charLengthAdornment = (
    <span className={twMerge("!text-caption-regular text-gray-400 whitespace-nowrap", maxCharClassName)}>
      {maxCharLength} / {(props?.value as string)?.length || 0}
    </span>
  );

  const getStartAdornment = () =>
    error && props?.dir === "ltr" ? (
      <InputAdornment position="start">{renderErrorIcon()}</InputAdornment>
    ) : (
      props?.InputProps?.startAdornment
    );

  const getEndAdornment = () => (
    <div className="flex items-center gap-1 pointer-events-none [&>*]:pointer-events-auto">
      {error && (!props?.dir || props?.dir === "rtl") && (
        <InputAdornment position={maxCharLength || endAdornment || props?.InputProps?.endAdornment ? "start" : "end"}>
          {renderErrorIcon()}
        </InputAdornment>
      )}
      {maxCharLength ? charLengthAdornment : endAdornment || props?.InputProps?.endAdornment}
    </div>
  );

  return (
    <Box width="100%" className="styled-custom-textfield">
      {label && (
        <CustomFormLabel
          optional={optional}
          labelTooltipTitle={labelTooltipTitle}
          labelTooltipDescription={labelTooltipDescription}
          className={labelClassName}
        >
          {label}
        </CustomFormLabel>
      )}
      <TextField
        {...props}
        fullWidth
        error={error}
        className={clsx(
          props.color === "secondary" && "custom-textfield--secondary",
          "custom-textfield",
          props.className
        )}
        InputProps={{
          ...props.InputProps,
          autoComplete: disableAutoComplete ? `off` : props.InputProps?.autoComplete || undefined,
          startAdornment: getStartAdornment(),
          endAdornment: getEndAdornment()
        }}
        autoComplete={disableAutoComplete ? `off` : props?.autoComplete || undefined}
        InputLabelProps={{
          ...props?.InputLabelProps,
          shrink: disableAutoComplete ? true : props?.InputLabelProps?.shrink || false
        }}
        inputProps={{
          ...props?.inputProps,
          autoComplete: disableAutoComplete ? `off` : props?.inputProps?.autoComplete || undefined,
          form: {
            autocomplete: disableAutoComplete ? "off" : props?.inputProps?.form?.autoComplete || undefined
          }
        }}
      />
      {helperText && error && <InputHelper>{helperText}</InputHelper>}
    </Box>
  );
};

export default CustomTextField;
