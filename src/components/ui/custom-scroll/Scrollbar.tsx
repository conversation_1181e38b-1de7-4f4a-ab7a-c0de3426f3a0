import "./Scrollbar.css";

import SimpleBar from "simplebar-react";
import "simplebar-react/dist/simplebar.min.css";
import Box from "@mui/material/Box";

import withClassname from "../../../utils/withClassName";
import { CSSProperties } from "react";

const SimpleBarStyle = withClassname(SimpleBar, "simple-bar");

interface PropsType {
  children: React.ReactElement | React.ReactNode;
  sx?: CSSProperties;
  className?: string;
}

const Scrollbar = (props: PropsType) => {
  const { children, sx, ...other } = props;

  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  if (isMobile) {
    return <Box id="sx-scrollbar-814">{children}</Box>;
  }

  return (
    <SimpleBarStyle style={sx} {...other}>
      {children}
    </SimpleBarStyle>
  );
};

export default Scrollbar;
