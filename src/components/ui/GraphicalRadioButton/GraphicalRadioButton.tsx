import { ButtonBase, FormLabel, <PERSON>rid, <PERSON>lette, PaletteColor, RadioProps, Typography, useTheme } from "@mui/material";
import { ReactNode } from "react";
import "./GraphicalRadioButton.css";
import Image from "next/image";
import { twMerge } from "tailwind-merge";

type TGraphicalRadioButton = {
  color: Omit<keyof Palette, "grey">;
  title: string;
  subtitle: string;
  checked: boolean;
  children?: ReactNode;
  icon?: ReactNode;
  onChange: (v: string) => void;
  disabled?: boolean;
} & Omit<RadioProps, "onChange">;

const GraphicalRadioButton = (props: TGraphicalRadioButton) => {
  const { color = "primary", children, checked, name, value, onChange, title, subtitle, icon, disabled } = props;
  const theme = useTheme();
  const border = checked ? `2px solid rgb(var(--brand-brand))` : `2px solid rgb(var(--neutral-neutral-light))`;

  return (
    <>
      <FormLabel
        component={ButtonBase}
        className="graphical-radio-button"
        sx={{ border, backgroundColor: "transparent", width: "100%" }}
        onClick={() => {
          if (!checked && !disabled) onChange(value as string);
        }}
        disabled={disabled}
      >
        <div className="flex justify-between items-center w-full">
          <div className="flex justify-start items-center flex-1 gap-3">
            <div className={twMerge(disabled ? "opacity-70" : "")}>{icon}</div>
            <div className="flex flex-col gap-1">
              <div className="text-v2-content-primary font-medium text-sm">{title}</div>
              <div className="text-v2-content-tertiary text-xs font-normal">{subtitle}</div>
            </div>
          </div>
          <div>
            {checked ? (
              <Image src="/images/svgs/user-checked.svg" alt="realUser" width={24} height={24} />
            ) : (
              <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={24} height={24} />
            )}
          </div>
        </div>
      </FormLabel>
      <input id={`btn-radio-${name}`} type="radio" hidden checked={checked} />
    </>
  );
};

export default GraphicalRadioButton;
