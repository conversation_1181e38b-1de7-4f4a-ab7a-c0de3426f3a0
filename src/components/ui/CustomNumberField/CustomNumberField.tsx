import useLanguage from "@/utils/hooks/useLanguage";
import { arNumber, enNumber, faNumber, toCommas } from "@/utils/helpers";
import React, { ChangeEvent, useMemo } from "react";
import CustomTextField, { ICustomTextFieldProps } from "../CustomTextField/CustomTextField";
import isNaN from "lodash/isNaN";

const transformers = {
  fa: faNumber,
  ar: arNumber,
  en: enNumber,
  fr: enNumber
};
export type NumberFieldProps = {
  isParsFloat?: boolean;
  value?: string | number;
  hasComma?: boolean;
  returnType?: "string" | "number";
  onTextChange: (v?: number | string, e?: ChangeEvent<HTMLInputElement>) => void;
} & ICustomTextFieldProps;

const CustomNumberField = (props: NumberFieldProps) => {
  const [currentLang] = useLanguage();
  const transformer = transformers[currentLang.value];
  const { onTextChange, value, hasComma, isParsFloat, returnType = "number", ...restProps } = props;

  const val = useMemo(() => {
    if (value === undefined || isNaN(value)) return "";

    if (restProps.type === "number" && hasComma) {
      const comma = value ? toCommas(Number(value)) : "";
      return comma ? transformer(comma) : comma;
    }

    return transformer(value);
  }, [value, restProps.type, hasComma, transformer]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    let vall: string = enNumber(e.target.value?.trim()?.replaceAll(",", ""));

    vall = vall.replace(/\s/g, '');

    /* ----------------------- Prepare the modified event ----------------------- */
    const modifiedEvent = (v: string | number | undefined) => ({
      ...e,
      target: {
        ...e.target,
        value: v as any
      }
    });

    if (restProps.type === "number" && (vall.match(/^[0-9]*$/) || !vall)) {
      if (returnType === "number") {
        const newVal = isParsFloat ? Number.parseFloat(vall) : vall;
        const updatedValue = Number.isNaN(newVal) ? undefined : newVal;
        onTextChange(updatedValue, modifiedEvent(updatedValue));
      } else {
        onTextChange(vall, modifiedEvent(vall));
      }

      return;
    }

    if (onTextChange) {
      if (restProps.type === "number" && isNaN(+vall)) return;

      onTextChange(vall || undefined, modifiedEvent(vall || undefined));
    }
  };

  return <CustomTextField {...restProps} type="text" value={val} onChange={handleChange} />;
};

export default CustomNumberField;
