import "./CustomCheckbox.css";

import * as React from "react";
import Checkbox, { CheckboxProps } from "@mui/material/Checkbox";
import withClassname from "../../../utils/withClassName";
import { FormControlLabel } from "@mui/material";
import { twMerge } from "tailwind-merge";

const BpIcon = withClassname("span", "styled-bp-icon");

const BpCheckedIcon = withClassname(BpIcon, "styled-bp-checked-icon") as any;
const BpIndeterminateIcon = withClassname(BpIcon, "styled-bp-indeterminate-icon") as any;

interface ICustomSwitchProps extends CheckboxProps {
  label?: string;
  checkboxClassName?: string;
}

// Inspired by blueprintjs
function CustomCheckbox(props: ICustomSwitchProps) {
  return (
    <FormControlLabel
      className={"custom-checkbox-wrapper " + props?.className || ""}
      control={
        <Checkbox
          {...props}
          disableRipple
          color={props.color ? props.color : "default"}
          checkedIcon={
            <BpCheckedIcon
              sx={{
                backgroundColor: props.color ? `${props.color}.main` : "primary.main"
              }}
            />
          }
          indeterminateIcon={
            <BpIndeterminateIcon
              sx={{
                backgroundColor: props.color ? `${props.color}.main` : "primary.main"
              }}
            />
          }
          className={twMerge("custom-checkbox", props?.checkboxClassName)}
          icon={<BpIcon />}
          inputProps={{ "aria-label": "Checkbox demo" }}
        />
      }
      label={props?.label}
    />
  );
}

export default CustomCheckbox;
