.custom-checkbox-wrapper .MuiTypography-root {
  font-size: 13px;
  font-weight: 500;
  color: rgb(var(--color-gray-999));
}

.custom-checkbox .Mui-checked {
  color: rgb(var(--color-purple-500)) !important;
}

.custom-checkbox-wrapper .MuiCheckbox-indeterminate {
  color: rgb(var(--color-purple-500));
}

.styled-bp-icon {
  border-radius: 4px;
  width: 19px;
  height: 19px;
  background-color: transparent;
}

.styled-bp-icon:not(.styled-bp-checked-icon):not(.styled-bp-indeterminate-icon) {
  border: 1.5px solid rgb(var(--neutral-neutral-light));
}

.Mui-focusVisible .styled-bp-icon.dark {
  outline: 0px auto var(--mui-palette-grey-200);
  outline-offset: 2;
}

.Mui-focusVisible.light .styled-bp-icon {
  outline: 0px auto var(--mui-palette-grey-200);
}

input:disabled ~ .styled-bp-icon {
  box-shadow: none;
  background: var(--mui-palette-grey-100);
}

.styled-bp-checked-icon {
  box-shadow: none;
  width: 19px;
  height: 19px;
  background-color: rgb(var(--color-purple-500));
}

.styled-bp-checked-icon:before {
  display: block;
  width: 19px;
  height: 19px;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12 5c-.28 0-.53.11-.71.29L7 9.59l-2.29-2.3a1.003 1.003 0 00-1.42 1.42l3 3c.***********.71.29s.53-.11.71-.29l5-5A1.003 1.003 0 0012 5z' fill='%23fff'/%3E%3C/svg%3E");
  content: "";
}

.styled-bp-indeterminate-icon {
  box-shadow: none;
  width: 19px;
  height: 19px;
  background-color: rgb(var(--color-purple-500));
}

.styled-bp-indeterminate-icon:before {
  display: block;
  width: 19px;
  height: 19px;
  content: "-";
  color: white;
  font-size: 33px;
  text-align: center;
  line-height: 22px;
}
