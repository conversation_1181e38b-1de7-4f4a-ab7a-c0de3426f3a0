import { TMetaTagsData } from "@/store/apps/meta/types";
import { InputProps } from "@mui/material";
import { FocusEventHandler } from "react";

export type TCustomTagsProps = {
  value?: string[];
  label?: string;
  name?: string;
  optional?: boolean;
  requiredStar?: boolean;
  helperText?: React.ReactNode;
  error?: boolean;
  handleBlur?: FocusEventHandler;
  placeholder?: string;
  onChange: ({ values, tagValues }: { values?: TMetaTagsData[] | null; tagValues?: string[] }) => void;
  size?: InputProps["size"];
};
