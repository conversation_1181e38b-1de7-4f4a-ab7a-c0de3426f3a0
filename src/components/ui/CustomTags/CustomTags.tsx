import "./CustomTags.css";

import { Stack, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useRef, useState } from "react";
import { TMetaTagsData } from "@/store/apps/meta/types";
import ErrorIcon from "@mui/icons-material/Error";

import CustomTextField from "../CustomTextField/CustomTextField";
import { TCustomTagsProps } from "./types";
import CustomAutocomplete from "../CustomAutocomplete/CustomAutocomplete";

function CustomTags({
  name,
  error,
  size,
  value,
  onChange,
  optional,
  requiredStar,
  handleBlur,
  helperText,
  placeholder
}: TCustomTagsProps) {
  const inputRef = useRef<React.Ref<any>>(null);
  const { t } = useTranslation();

  const [options, setOptions] = useState<TMetaTagsData[]>([...(value?.map(v => ({ id: v, tag: v })) || [])]);

  const items = options?.length ? options : [];
  const defaultTagsValue = items?.filter(item => value?.find(v => item.tag === v)) ?? [];

  const onAddNewTag = () => {
    const ref = inputRef.current as any;
    if (!ref.value || ref.value.length < 3) return;
    const newItem = {
      id: ref.value as string,
      tag: ref.value as string
    };

    setOptions(items ? [...items, newItem] : [newItem]);
    onChange({ values: defaultTagsValue.concat(newItem), tagValues: value?.concat(newItem.tag) });
  };

  return (
    <div className="custom-tag-input-root">
      <CustomAutocomplete
        multiple
        value={!!defaultTagsValue?.length ? defaultTagsValue : []}
        loadingText="loading..."
        className="custom-tag-input"
        options={items ?? []}
        // onChange={(event, newInputValue) => {
        //   const tagValues = newInputValue?.map(item => item.tag);
        //   onChange({ values: newInputValue, tagValues });
        // }}
        onChange={(event, newInputValue) => {
          const tagValues = newInputValue.map(item => item.tag);

          // Ensure options only contain selected tags
          setOptions(newInputValue);

          onChange({ values: newInputValue, tagValues });
        }}
        optional={optional}
        requiredStar={requiredStar}
        onBlur={handleBlur}
        size={size}
        getOptionKey={option => option.id || option.tag}
        getOptionLabel={option => option.tag ?? ""}
        noOptionsText={<Typography className="text-xs">{t("noTags")}</Typography>}
        autoComplete={false}
        id="sx-customtags-2390"
        componentsProps={{
          paper: { sx: { display: "none" } } // Hide dropdown
        }}
        renderInput={params => (
          <CustomTextField
            {...params}
            inputRef={inputRef}
            error={error}
            name={name}
            id="sx-customtags-2401"
            onKeyDown={e => {
              if (e.key === "Enter" || e.keyCode === 13) {
                /* -------------------- This prevents the form submission ------------------- */
                e.preventDefault();
                onAddNewTag();
              }
            }}
            autoComplete="product-tags"
            helperText={helperText}
            InputProps={{
              ...params.InputProps,
              autoComplete: "off"
            }}
            inputProps={{
              ...params.inputProps,
              autoComplete: "off",
              placeholder,
              enterKeyHint: "go"
            }}
          />
        )}
      />
      {!error && !helperText && (
        <Stack direction="row" alignItems="center" gap={0.5}>
          <ErrorIcon className="text-[#F5CC00] w-5 h-5" />
          <Typography className="text-[#757575] text-xs">{t("tagsWarning")}</Typography>
        </Stack>
      )}
    </div>
  );
}

export default CustomTags;
