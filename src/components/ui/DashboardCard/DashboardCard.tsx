import "./DashboardCard.css";

import { useTheme } from "@mui/material/styles";
import { Box } from "@mui/material";
import { Card } from "@mui/material";
import { CardContent } from "@mui/material";
import { Stack } from "@mui/material";
import { Typography } from "@mui/material";
import { themeCustomizer } from "@/utils/theme";

type Props = {
  title?: string;
  subtitle?: string;
  action?: JSX.Element | any;
  footer?: JSX.Element;
  cardheading?: string | JSX.Element;
  headtitle?: string | JSX.Element;
  headsubtitle?: string | JSX.Element;
  children?: JSX.Element;
  padding?: number | string;
  middlecontent?: string | JSX.Element;
};

const DashboardCard = ({
  title,
  subtitle,
  children,
  action,
  footer,
  cardheading,
  headtitle,
  headsubtitle,
  padding,
  middlecontent
}: Props) => {
  const theme = useTheme();
  const borderColor = theme.palette.divider;

  return (
    <Card
      id="sx-dashboardcard-3263"
      sx={{ border: !themeCustomizer.isCardShadow ? `1px solid ${borderColor}` : "none" }}
      elevation={0}
      variant={!themeCustomizer.isCardShadow ? "outlined" : undefined}
    >
      {cardheading ? (
        <CardContent>
          <Typography variant="h5">{headtitle}</Typography>
          <Typography variant="subtitle2" color="textSecondary">
            {headsubtitle}
          </Typography>
        </CardContent>
      ) : (
        <CardContent id="sx-dashboardcard-3277" sx={{ p: padding ?? "30px" }}>
          {title ? (
            <Stack
              direction="row"
              flexWrap="wrap"
              spacing={2}
              justifyContent="space-between"
              alignItems={"center"}
              mb={3}
            >
              <Box>
                {title ? <Typography variant="h5">{title}</Typography> : ""}

                {subtitle ? (
                  <Typography variant="subtitle2" color="textSecondary">
                    {subtitle}
                  </Typography>
                ) : (
                  ""
                )}
              </Box>
              {action}
            </Stack>
          ) : null}

          {children}
        </CardContent>
      )}

      {middlecontent}
      {footer}
    </Card>
  );
};

export default DashboardCard;
