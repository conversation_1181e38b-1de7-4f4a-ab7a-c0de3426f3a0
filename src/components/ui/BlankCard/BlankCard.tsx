import "./BlankCard.css";

import Card, { CardTypeMap } from "@mui/material/Card";
import { useTheme } from "@mui/material/styles";
import { themeCustomizer } from "@/utils/theme";

type Props = {
  className?: string;
  children: JSX.Element | JSX.Element[];
  sx?: any;
} & CardTypeMap["props"];

const BlankCard = ({ children, className, sx, ...restProps }: Props) => {
  const theme = useTheme();
  const borderColor = theme.palette.divider;

  return (
    <Card
      {...restProps}
      id="sx-blankcard-2544"
      sx={{
        p: 0,
        border: !themeCustomizer.isCardShadow ? `1px solid ${borderColor}` : "none",
        position: "relative",
        sx
      }}
      className={className}
      elevation={restProps?.elevation || themeCustomizer.isCardShadow ? 9 : 0}
      variant={restProps?.variant || !themeCustomizer.isCardShadow ? "outlined" : undefined}
    >
      {children}
    </Card>
  );
};

export default BlankCard;
