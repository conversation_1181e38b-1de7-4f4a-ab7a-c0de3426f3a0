import CustomTextField from "../CustomTextField/CustomTextField";
import { TIranCities, TLocationSelectProps } from "./types";
import { useTranslation } from "react-i18next";
import { useRef } from "react";
import useLocations from "@/utils/hooks/useLocations";
import CustomAutocomplete from "../CustomAutocomplete/CustomAutocomplete";
import { IRN_ISO } from "@/constants/localization";
import { twMerge } from "tailwind-merge";
import { Icon } from "@iconify/react";

function MultipleLocationsSelect<T extends boolean>({
  getType = "id",
  multiple,
  name,
  error,
  optional,
  label,
  placeholder,
  inputValue,
  value,
  onChange,
  handleBlur,
  helperText,
  className,
  inputProps,
  excludeIds,
  renderTags,
  hasAllCities = false
}: TLocationSelectProps<T>) {
  const inputRef = useRef<React.Ref<any>>(null);
  const { t } = useTranslation();
  const { countries, isLoading } = useLocations();

  const countriesMap = new Map(countries?.data?.map(item => [item.iso3, item]));

  const iranData = countriesMap.get(IRN_ISO);

  const iranCities: TIranCities[] =
    iranData?.subLocations?.reduce((acc: TIranCities[], item) => {
      const seen = new Set(); // Track already seen name-parentKey combinations

      const processItem = (childItem: TIranCities) => {
        const key = `${childItem.name}-${item.name}`;
        if (!seen.has(key)) {
          seen.add(key);
          acc.push({
            ...childItem,
            parentKey: item.name
          });
        }
      };

      if (item.subLocations) {
        item.subLocations.forEach(processItem);
      } else {
        processItem(item);
      }

      return acc;
    }, []) || [];

  const staticOption = {
    id: "00000000-0000-0000-0000-000000000000",
    name: t("supplier.profile.allCities"),
    parentKey: "",
    type: "Static"
  };

  const finalExcludeIds = excludeIds?.length ? excludeIds?.filter(item => item !== value && item !== inputValue) : [];

  const options = hasAllCities
    ? [staticOption, ...iranCities].filter(city => !finalExcludeIds?.includes(city?.id as string))
    : iranCities.filter(city => !finalExcludeIds?.includes(city?.id as string));

  const defaultIranCityValue =
    multiple && value instanceof Array
      ? options.filter(item => (value as string[])?.find(v => v === item[getType]))
      : options.find(item => item[getType] === value);

  const defaultValue = multiple
    ? (value as string[] | number[])?.length
      ? defaultIranCityValue
      : []
    : (defaultIranCityValue as TIranCities)?.name
      ? defaultIranCityValue
      : null;

  return (
    <CustomAutocomplete
      multiple={multiple}
      value={defaultValue as any}
      classes={{
        groupLabel: "font-bold"
      }}
      renderTags={renderTags}
      className={className}
      autoComplete={false}
      loadingText="loading..."
      loading={isLoading}
      options={options}
      onChange={(_, newInputValue) => {
        const newValue = multiple
          ? (newInputValue as TIranCities[])?.map(item => item?.id)
          : (newInputValue as TIranCities)?.id;
        onChange(
          newValue as T extends true ? string[] : string,
          newInputValue as T extends true ? TIranCities[] : TIranCities
        );
      }}
      disablePortal={false}
      onBlur={handleBlur}
      groupBy={(option: TIranCities) => option.parentKey ?? ""}
      getOptionLabel={option => option.name ?? ""}
      noOptionsText={t("notfoundCity")}
      renderInput={params => (
        <CustomTextField
          {...params}
          {...inputProps}
          inputRef={inputRef}
          error={error}
          name={name}
          label={label}
          optional={optional}
          placeholder={placeholder || label}
          disableAutoComplete
          helperText={helperText}
          inputProps={{
            ...params.inputProps,
            ...inputProps?.inputProps,
            className: twMerge(params.inputProps?.className, inputProps?.inputProps?.className, "-ml-7 h-full")
          }}
          InputProps={{
            ...params?.InputProps,
            endAdornment: inputProps?.InputProps?.endAdornment || (
              <Icon
                width={24}
                height={24}
                icon="solar:alt-arrow-down-outline"
                className="!pointer-events-none text-gray-400 "
              />
            )
          }}
        />
      )}
    />
  );
}

export default MultipleLocationsSelect;
