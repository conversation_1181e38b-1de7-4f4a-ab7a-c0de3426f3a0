import { TMetaLocationsData } from "@/store/apps/meta/types";
import { AutocompleteOwnerState, AutocompleteRenderGetTagProps, InputProps, TextFieldProps } from "@mui/material";
import { FocusEventHandler } from "react";

export type TIranCities = {
  id: number | string;
  name: string;
  iso3?: string;
  iso2?: string;
  currency?: string;
  latitude?: number;
  longitude?: number;
  parentKey?: string;
  subGroup?: string;
};

export type TLocationSelectProps<T extends boolean> = {
  multiple?: T;
  inputValue?: string;
  hasEndAdornment?: boolean;
  size?: InputProps["size"];
  placeholder?: string;
  label?: string;
  optional?: boolean;
  getType?: "name" | "id";
  value?: T extends true ? string[] | number[] : string | number;
  name?: string;
  helperText?: React.ReactNode;
  error?: boolean;
  handleBlur?: FocusEventHandler;
  onChange: (
    value: T extends true ? string[] | number[] : string | number,
    valueObj: T extends true ? TIranCities[] : TIranCities
  ) => void;
  select?: TMetaLocationsData["type"];
  inputProps?: TextFieldProps;
  className?: string;
  excludeIds?: string[];
  hasAllCities?: boolean;
  renderTags?: (
    value: any,
    getTagProps: AutocompleteRenderGetTagProps,
    ownerState: AutocompleteOwnerState<any, T, any, any, any>
  ) => React.ReactNode;
  requiredStar?: boolean;

  /**
   * @default true
   */
  disablePortal?: boolean;
};
