import { useTranslation } from "react-i18next";
import { useRef, useState } from "react";
import useLocations from "@/utils/hooks/useLocations";
import CustomAutocomplete from "../CustomAutocomplete/CustomAutocomplete";
import { IRN_ISO } from "@/constants/localization";
import { twMerge } from "tailwind-merge";
import { Icon } from "@iconify/react";
import { useClickAway } from "react-use";

import Input from "../inputs/Input";
import { TIranCities, TLocationSelectProps } from "./types";

function LocationsSelect<T extends boolean>({
  getType = "id",
  multiple,
  name,
  error,
  optional,
  label,
  placeholder,
  inputValue,
  value,
  onChange,
  handleBlur,
  helperText,
  className,
  inputProps,
  excludeIds,
  renderTags,
  hasAllCities = false,
  disablePortal = true,
  requiredStar
}: TLocationSelectProps<T>) {
  const inputRef = useRef<React.Ref<any>>(null);
  const containerRef = useRef(null);
  const { t } = useTranslation();
  const { countries, isLoading } = useLocations();
  const [searchText, setSearchText] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  useClickAway(containerRef, () => {
    if (isOpen) {
      setIsOpen(false);
    }
  });

  const countriesMap = new Map(countries?.data?.map(item => [item.iso3, item]));
  const iranData = countriesMap.get(IRN_ISO);

  const iranCities: TIranCities[] =
    iranData?.subLocations?.reduce((acc: TIranCities[], item) => {
      const seen = new Set();

      const processItem = (childItem: TIranCities) => {
        const key = `${childItem.name}-${item.name}`;
        if (!seen.has(key)) {
          seen.add(key);
          acc.push({
            ...childItem,
            parentKey: item.name
          });
        }
      };

      if (item.subLocations) {
        item.subLocations.forEach(processItem);
      } else {
        processItem(item);
      }

      return acc;
    }, []) || [];

  const staticOption = {
    id: "00000000-0000-0000-0000-000000000000",
    name: t("supplier.profile.allCities"),
    parentKey: "",
    type: "Static"
  };

  const getDefaultValue = () => {
    if ((defaultValue as TIranCities)?.parentKey) {
      return `${(defaultValue as TIranCities)?.parentKey} ، ${(defaultValue as TIranCities)?.name}`;
    }
    if ((defaultValue as TIranCities)?.name) {
      return `${(defaultValue as TIranCities)?.name}`;
    }
    return "";
  };

  const finalExcludeIds = excludeIds?.length ? excludeIds?.filter(item => item !== value && item !== inputValue) : [];

  const options = hasAllCities
    ? [staticOption, ...iranCities].filter(city => !finalExcludeIds?.includes(city?.id as string))
    : iranCities.filter(city => !finalExcludeIds?.includes(city?.id as string));

  const filteredOptions = options.filter(
    option =>
      option.name.toLowerCase().includes(searchText.toLowerCase()) ||
      option?.parentKey?.toLowerCase().includes(searchText.toLowerCase())
  );

  const defaultIranCityValue =
    multiple && value instanceof Array
      ? options.filter(item => (value as string[])?.find(v => v === item[getType]))
      : options.find(item => item[getType] === value);

  const defaultValue = multiple
    ? (value as string[] | number[])?.length
      ? defaultIranCityValue
      : []
    : (defaultIranCityValue as TIranCities)?.name
      ? defaultIranCityValue
      : null;

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  return (
    <div ref={containerRef}>
      <CustomAutocomplete
        multiple={multiple}
        value={defaultValue as any}
        open={isOpen}
        onOpen={() => setIsOpen(true)}
        classes={{
          groupLabel: "font-bold"
        }}
        renderTags={renderTags}
        className={className}
        autoComplete={false}
        loadingText="loading..."
        loading={isLoading}
        options={filteredOptions}
        disablePortal={disablePortal}
        onChange={(_, newInputValue) => {
          const newValue = multiple
            ? (newInputValue as TIranCities[])?.map(item => item?.id)
            : (newInputValue as TIranCities)?.id;
          onChange(
            newValue as T extends true ? string[] : string,
            newInputValue as T extends true ? TIranCities[] : TIranCities
          );
          setSearchText("");
          setIsOpen(false);
        }}
        onBlur={handleBlur}
        groupBy={(option: TIranCities) => option.parentKey ?? ""}
        getOptionLabel={option => option.name ?? ""}
        noOptionsText={t("notfoundCity")}
        ListboxProps={{
          style: { maxHeight: 250 }
        }}
        // renderOption={(props, option) => (
        //   <li {...props} key={option.id}>
        //     {option.name}
        //   </li>
        // )}
        PaperComponent={({ children }) => (
          <div className="bg-cards rounded-lg border border-v2-border-primary mt-1">
            <div className="p-3">
              <Input
                type="text"
                placeholder={`${t("search")} ...`}
                value={searchText}
                inputParentClassName="bg-v2-surface-secondary border-v2-surface-secondary"
                startAdornment={
                  <Icon
                    icon="weui:search-outlined"
                    className="!pointer-events-none size-6 text-v2-content-subtle border-v2-content-subtle ml-2.5 max-h-10"
                  />
                }
                endAdornment={
                  <Icon
                    onClick={() => setSearchText("")}
                    icon="solar:close-circle-outline"
                    className="!pointer-events-none size-6 text-v2-content-subtle border-v2-content-subtle  max-h-10 cursor-pointer"
                  />
                }
                onChange={handleSearchChange}
                autoFocus
                onClick={e => e.stopPropagation()}
                requiredStar={requiredStar}
                className={twMerge("h-full -ml-7")}
              />
            </div>
            <div
              onClick={() => {
                setSearchText("");
                setIsOpen(false);
              }}
            >
              {children}
            </div>
          </div>
        )}
        renderInput={params => (
          <div ref={params.InputProps.ref} className="locationSelectWrapper">
            <Input
              error={error}
              name={name}
              label={label}
              optional={optional}
              placeholder={placeholder || label}
              autoComplete="off"
              helperText={helperText}
              {...params?.inputProps}
              className={twMerge(params.inputProps?.className, inputProps?.inputProps?.className, "h-full -ml-7")}
              readOnly
              onClick={() => setIsOpen(true)}
              value={getDefaultValue()}
              endAdornment={
                <Icon
                  width={20}
                  height={20}
                  icon="solar:alt-arrow-down-outline"
                  className="!pointer-events-none text-gray-400"
                />
              }
              requiredStar={requiredStar}
            />
          </div>
        )}
      />
    </div>
  );
}

export default LocationsSelect;
