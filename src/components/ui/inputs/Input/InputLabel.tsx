import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import HoverPopover from "../../popover/HoverPopover";
import { TInputLabel } from "./types";

function InputLabel({
  children,
  labelTooltipTitle,
  labelTooltipDescription,
  containerClassName,
  optional = false,
  requiredStar = true,
  htmlFor,
  labelEndAdornment
}: TInputLabel) {
  const { t } = useTranslation();

  return (
    <label
      htmlFor={htmlFor}
      className={twMerge(
        "text-[13px] font-medium text-v2-content-tertiary flex justify-between items-center",
        containerClassName
      )}
    >
      <div className="flex items-center justify-center gap-1">
        <span className="text-[13px] font-medium text-v2-content-tertiary">{children}</span>{" "}
        {optional && <span className="text-[13px] font-normal text-v2-content-tertiary">{t("forms.optional")}</span>}
        {requiredStar && <span className="text-v2-content-on-error-2 leading-5 text-sm font-medium">*</span>}
      </div>
      {/* ---------------------------- tooltip hint ion ---------------------------- */}
      {(labelTooltipTitle || labelTooltipDescription) && (
        <HoverPopover
          content={
            <div className="flex flex-col gap-1 max-w-64">
              {labelTooltipTitle && <div className="text-gray-999 font-medium text-sm">{labelTooltipTitle}</div>}
              {labelTooltipDescription && (
                <div className="text-gray-500 font-normal text-xs">{labelTooltipDescription}</div>
              )}
            </div>
          }
          popOverProps={{
            anchorOrigin: { vertical: "top", horizontal: "left" },
            transformOrigin: { vertical: "bottom", horizontal: "right" }
          }}
        >
          <Icon icon="solar:info-circle-outline" className="size-4 cursor-pointer" />
        </HoverPopover>
      )}
      {labelEndAdornment}
    </label>
  );
}

export default InputLabel;
