import * as React from "react";
import { twMerge } from "tailwind-merge";
import { TInput } from "./types";
import InputWrapper from "./InputWrapper";

const sizeClassNames: { [key in Exclude<TInput["inputSize"], undefined>]: string } = {
  default: "py-3 px-3",
  sm: "py-2 px-3",
  lg: "py-[13px] px-3"
};

// eslint-disable-next-line react/display-name
const Input = React.forwardRef<HTMLInputElement, TInput>((props, ref) => {
  const { className, type, readOnly, inputSize = "default", ...restProps } = props;

  return (
    <InputWrapper {...props} id={restProps?.id} charLength={(restProps?.value as string)?.length}>
      <input
        type={type}
        className={twMerge(
          "flex w-full h-full text-sm md:text-sm font-medium outline-none bg-transparent transition-colors text-v2-content-primary placeholder:text-v2-content-subtle disabled:cursor-not-allowed disabled:text-v2-content-disable",

          inputSize && sizeClassNames[inputSize],
          className
        )}
        ref={ref}
        {...restProps}
        readOnly={readOnly}
      />
    </InputWrapper>
  );
});

export default Input;
