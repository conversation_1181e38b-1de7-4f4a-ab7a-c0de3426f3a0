import { Icon } from "@iconify/react";
import { twMerge } from "tailwind-merge";
import InputHelper from "../../CustomFormHelperText/InputHelper";
import InputLabel from "./InputLabel";
import { TInputWrapper } from "./types";

const bindVariantClassNames: { [key in Exclude<TInputWrapper["variant"], undefined>]: string } = {
  outline: "border-[1.5px]",
  filled: "bg-v2-surface-thertiary"
};

// eslint-disable-next-line react/display-name
const InputWrapper = (props: TInputWrapper) => {
  const {
    rootClassName,
    inputParentClassName,
    label,
    helperText,
    error,
    startAdornment,
    endAdornment,
    labelTooltipTitle,
    labelTooltipDescription,
    maxCharLength,
    children,
    charLength,
    id,
    optional,
    requiredStar,
    hasEmptySpace = false,
    variant = "outline",
    fullWidth,
    labelEndAdornment
  } = props;

  const charLengthAdornment = (
    <span className={twMerge("!text-caption-regular text-gray-400 whitespace-nowrap")}>
      {maxCharLength} / {charLength || 0}
    </span>
  );

  return (
    <div className={twMerge("flex flex-col gap-1 group", rootClassName)}>
      {/* ---------------------------------- label --------------------------------- */}
      {!!(label || labelTooltipTitle || labelTooltipDescription) && (
        <InputLabel
          {...{ htmlFor: id, labelTooltipTitle, labelTooltipDescription, optional, requiredStar, labelEndAdornment }}
        >
          {label}
        </InputLabel>
      )}

      {/* ---------------------------- the input itself ---------------------------- */}
      <div
        className={twMerge(
          "flex items-center rounded-lg h-full",
          error
            ? "border-v2-content-on-error-2"
            : "border-v2-border-primary group-focus-within:border-v2-border-active",
          variant && bindVariantClassNames[variant],
          fullWidth && "w-full",
          inputParentClassName
        )}
        data-invalid={!!error}
      >
        {/* ----------------------------- start adornment ---------------------------- */}
        {startAdornment && (
          <div className="py-3 pr-3 pointer-events-none [&>*]:pointer-events-auto">{startAdornment}</div>
        )}
        {/* ---------------------------------- input --------------------------------- */}
        {children}
        {/* ------------------------------ end adornment ----------------------------- */}
        {(endAdornment || maxCharLength || error) && (
          <div className="flex items-center gap-1 py-3 pl-3 pointer-events-none [&>*]:pointer-events-auto">
            {endAdornment}
            {error && <Icon icon="solar:danger-bold" className="text-error-500 size-5" />}
            {maxCharLength ? charLengthAdornment : ""}
          </div>
        )}
      </div>

      {/* --------------------------------- footer --------------------------------- */}
      {!!helperText && typeof helperText === "string" && <InputHelper {...{ error }}>{helperText}</InputHelper>}
      {!helperText && hasEmptySpace && <div className="h-5" />}
    </div>
  );
};

export default InputWrapper;
