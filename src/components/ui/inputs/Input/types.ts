import { ReactNode } from "react";

export interface TInput extends React.InputHTMLAttributes<HTMLInputElement>, Omit<TInputWrapper, "children"> {}

export interface TInputWrapper {
  /**
   * @default "outline"
   */
  variant?: "outline" | "filled";

  /**
   * @default "default"
   */
  inputSize?: "default" | "sm" | "lg";

  id?: string;
  rootClassName?: string;
  inputParentClassName?: string;
  label?: string;
  helperText?: ReactNode;
  error?: boolean;
  startAdornment?: ReactNode;
  endAdornment?: ReactNode;
  labelTooltipTitle?: string;
  labelTooltipDescription?: string;
  maxCharLength?: number;
  children: ReactNode;
  charLength?: number;

  /**
   * Print's "optional" word in prentices
   * @default false
   * default is false, means all inputs required except optional ones.
   */
  optional?: boolean;

  hasEmptySpace?: boolean;

  /**
   * Prints "*" in input label
   * @default true
   */
  requiredStar?: boolean;

  /**
   * @default false
   */
  fullWidth?: boolean;
  labelEndAdornment?: ReactNode;
}

export interface TInputLabel {
  htmlFor?: string;
  children?: string;
  labelTooltipTitle?: string;
  labelTooltipDescription?: string;
  containerClassName?: string;

  /**
   * Print's "optional" word in prentices
   * @default false
   * default is false, means all inputs required except optional ones.
   */
  optional?: boolean;

  /**
   * Prints "*" in input label
   * @default true
   */
  requiredStar?: boolean;
  labelEndAdornment?: ReactNode;
}
