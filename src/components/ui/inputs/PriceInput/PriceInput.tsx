import React, { ReactNode } from "react";
import useCurrency from "@/utils/hooks/useCurrency";
import NumberInput from "../NumberInput";
import { TNumberInput } from "../NumberInput/types";

// eslint-disable-next-line react/display-name
const PriceInput = React.forwardRef<
  HTMLInputElement,
  Omit<TNumberInput, "endAdornment"> & { showSymbol?: boolean; endAdornment?: ReactNode }
>(({ showSymbol = true, hasCommaSeparator = true, endAdornment, ...restProps }, ref) => {
  const [currency] = useCurrency();
  const { symbol } = currency ?? { symbol: "" };

  return (
    <NumberInput
      {...restProps}
      ref={ref}
      autoComplete="off"
      hasCommaSeparator={hasCommaSeparator}
      endAdornment={
        endAdornment ||
        (showSymbol ? <div className="text-v2-content-tertiary text-body4-medium pr-0.5">{symbol}</div> : undefined)
      }
    />
  );
});

export default PriceInput;
