import * as React from "react";
import { twMerge } from "tailwind-merge";
import { TTextArea } from "./types";
import InputWrapper from "../Input/InputWrapper";

// eslint-disable-next-line react/display-name
const Textarea = React.forwardRef<HTMLTextAreaElement, TTextArea>((props, ref) => {
  const {
    className,
    rootClassName,
    inputParentClassName,
    label,
    helperText,
    error,
    startAdornment,
    endAdornment,
    labelTooltipTitle,
    labelTooltipDescription,
    maxCharLength,
    ...restProps
  } = props;

  return (
    <InputWrapper {...props} id={restProps?.id} charLength={(restProps?.value as string)?.length}>
      <textarea
        className={twMerge(
          "flex w-full text-[13px] resize-none md:text-sm font-medium outline-none bg-transparent transition-colors text-v2-content-primary placeholder:text-v2-content-subtle disabled:cursor-not-allowed disabled:text-v2-content-disable py-3 px-3",
          className
        )}
        ref={ref}
        {...restProps}
      />
    </InputWrapper>
  );
});

export default Textarea;
