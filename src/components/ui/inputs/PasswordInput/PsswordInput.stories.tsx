import type { Meta, StoryObj } from "@storybook/react";

import PasswordInput from "./PasswordInput";

const Password = () => {
  return (
    <div className="bg-[white] p-10 max-w-[400px]">
      <PasswordInput hasPasswordStrength />
    </div>
  );
};

const meta: Meta<typeof PasswordInput> = {
  component: () => <Password />,
  title: "Components/ui/PasswordInput"
};

export default meta;
type Story = StoryObj<typeof PasswordInput>;

export const Default: Story = {
  args: {}
};
