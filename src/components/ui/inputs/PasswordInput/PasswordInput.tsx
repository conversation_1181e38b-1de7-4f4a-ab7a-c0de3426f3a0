import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { IconButton } from "@mui/material";
import { twMerge } from "tailwind-merge";
import Input from "../Input";
import { TInput } from "../Input/types";
import { useTranslation } from "react-i18next";

interface PasswordInputProps extends Omit<TInput, "type"> {
  hasPasswordStrength?: boolean;
}

// eslint-disable-next-line react/display-name
const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>((props, ref) => {
  const { t } = useTranslation();
  const { hasPasswordStrength, onChange, ...rest } = props;
  const [showPassword, setShowPassword] = useState(false);
  const [password, setPassword] = useState("");
  const [strength, setStrength] = useState({
    length: false,
    uppercase: false,
    special: false
  });

  const handleClickShowPassword = () => setShowPassword(prev => !prev);

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);

    if (hasPasswordStrength) {
      setStrength({
        length: value.length >= 8,
        uppercase: /[A-Z]/.test(value),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(value)
      });
    }

    // Call the original onChange handler if provided
    onChange?.(e);
  };

  return (
    <div>
      <Input
        {...rest}
        ref={ref}
        autoComplete="off"
        type={showPassword ? "text" : "password"}
        value={password}
        onChange={handlePasswordChange}
        endAdornment={
          <IconButton aria-label="toggle password visibility" className="!p-0" onClick={handleClickShowPassword}>
            {showPassword ? (
              <Icon icon="iconamoon:eye-thin" className="size-5" />
            ) : (
              <Icon icon="iconamoon:eye-off-thin" className="size-5" />
            )}
          </IconButton>
        }
      />
      {hasPasswordStrength && (
        <div className="flex items-center gap-2.5 mt-2 flex-wrap">
          <div
            className={twMerge(
              "bg-v2-surface-thertiary text-v2-content-tertiary rounded-full py-1 px-2 flex items-center gap-1 whitespace-nowrap",
              strength.length && "bg-v2-surface-success-2 text-v2-content-on-success-2"
            )}
          >
            <Icon icon="akar-icons:check" className="size-3.5" />
            <span className="text-caption-regular whitespace-nowrap">{t("min8Char")}</span>
          </div>
          <div
            className={twMerge(
              "bg-v2-surface-thertiary rounded-full p-1 flex items-center gap-1 text-v2-content-tertiary",
              strength.special && "bg-v2-surface-success-2 text-v2-content-on-success-2"
            )}
          >
            <Icon icon="akar-icons:check" className="size-3.5" />
            <span className="text-caption-regular">{t("symbolChar")}</span>
          </div>
          <div
            className={twMerge(
              "bg-v2-surface-thertiary text-v2-content-tertiary rounded-full p-1 flex items-center gap-1",
              strength.uppercase && "bg-v2-surface-success-2 text-v2-content-on-success-2"
            )}
          >
            <Icon icon="akar-icons:check" className="size-3.5" />
            <span className="text-caption-regular">{t("upperChar")}</span>
          </div>
        </div>
      )}
    </div>
  );
});

export default PasswordInput;
