# How to Use ListInfiniteScroll

This guide provides you with a quick start on using the ListInfiniteScroll [InView](https://github.com/thebuilder/react-intersection-observer) library.

## Code Example

Below is an example of how to use theListInfiniteScroll in your React TypeScript application.

```tsx
import React from "react";
import { ListInfiniteScroll } from "@/components/shared/ListInfiniteScroll";

function Test() {
  const [page, setPage] = useState(1);
  const hasNextPage = true;

  return (
    <ListInfiniteScroll hasNextPage={hasNextPage} fetchNextPage={() => setPage(page + 1)}>
      <LoadingComponent />
    </ListInfiniteScroll>
  );
}

export default Test;
```
