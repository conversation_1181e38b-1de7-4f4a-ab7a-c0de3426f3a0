import "./ListInfiniteScroll.css";

import { Box } from "@mui/material";
import { CircularProgress } from "@mui/material";
import { InView } from "react-intersection-observer";

import { IListInfiniteScrollProps } from "./types";
import { ElementType } from "react";

function ListInfiniteScroll({
  hasNextPage,
  children,
  fetchNextPage,
  LoadingComponent,
  ...restProps
}: IListInfiniteScrollProps) {
  const Loading = (LoadingComponent as ElementType<any>) ?? CircularProgress;
  return (
    <>
      {hasNextPage && (
        <InView
          {...restProps}
          as="div"
          onChange={inView => {
            if (inView) {
              fetchNextPage();
            }
          }}
        >
          <Box id="sx-listinfinitescroll-4043">{children ? children : <Loading />}</Box>
        </InView>
      )}
    </>
  );
}

export default ListInfiniteScroll;
