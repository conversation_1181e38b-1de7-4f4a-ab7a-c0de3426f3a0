import React from "react";
import withClassname from "../../../utils/withClassName";

import "./VisuallyHiddenInput.css";

interface IInputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, IInputProps>((props, ref) => <input {...props} ref={ref} />);
Input.displayName = "Input";

const VisuallyHiddenInput = withClassname(Input, "visually-hidden-input");

export default VisuallyHiddenInput;
