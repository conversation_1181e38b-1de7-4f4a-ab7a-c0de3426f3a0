:root {
  --switch-checked-bg-color: #604FDC;
  --switch-disabled-thumb-color: #e0e0e0;
  --switch-disabled-track-opacity: 0.7;
  --switch-track-bg-color: #e9e9ea;
}

body.dark-mode {
  --switch-checked-bg-color: #604FDC;
  --switch-disabled-thumb-color: rgb(var(--color-gray-400));
  --switch-disabled-track-opacity: 0.3;
  --switch-track-bg-color: #39393d;
}

.switchStyled {
  width: 36px;
  height: 20px;
  padding: 0;
}

.custom-switch-label {
  font-size: 14px;
  font-weight: 400;
}

.switchStyled .MuiSwitch-switchBase {
  padding: 0;
  margin: 1.5px;
  transition-duration: 300ms;
  transform: translateX(-16px);
}

.switchStyled .MuiSwitch-switchBase.Mui-checked {
  transform: translateX(0px);
  color: #fff;
  color: rgb(var(--color-cards));
}

.switchStyled .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track {
  background-color: var(--switch-checked-bg-color);
  opacity: 1;
  border: 0;
}

.switchStyled .MuiSwitch-switchBase.Mui-disabled + .MuiSwitch-track {
  opacity: 0.5;
}

.switchStyled .MuiSwitch-switchBase.Mui-focusVisible .MuiSwitch-thumb {
  color: #604FDC;
  border: 6px solid #fff;
}

.switchStyled .MuiSwitch-switchBase.Mui-disabled .MuiSwitch-thumb {
  color: var(--switch-disabled-thumb-color);
}

.switchStyled .MuiSwitch-switchBase.Mui-disabled + .MuiSwitch-track {
  opacity: var(--switch-disabled-track-opacity);
}

.switchStyled .MuiSwitch-thumb {
  box-sizing: border-box;
  width: 17px;
  height: 17px;
}

.switchStyled .MuiSwitch-track {
  border-radius: 13px;
  background-color: var(--switch-track-bg-color);
  opacity: 1;
  transition: background-color 500ms;
}
