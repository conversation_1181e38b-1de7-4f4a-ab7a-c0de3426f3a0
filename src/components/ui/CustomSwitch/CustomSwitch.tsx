import clsx from "clsx";
import * as React from "react";
import Switch, { SwitchProps } from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import { Typography } from "@mui/material";
import "./CustomSwitch.css";
import { twMerge } from "tailwind-merge";

interface ICustomSwitchProps extends SwitchProps {
  label?: string;
  labelClassName?: string;
  textClassName?: string;
}

function CustomSwitch(props: ICustomSwitchProps) {
  return (
    <FormControlLabel
      dir="ltr"
      control={
        <Switch
          {...props}
          className={clsx("switchStyled", props?.className)}
          // sx={{ m: 1 }}
          defaultChecked={props.checked}
          onChange={props.onChange}
        />
      }
      className={props?.labelClassName}
      label={
        <span
          className={twMerge("text-gray-400 text-caption-medium ml-1.5", props?.textClassName)}
          // sx={{
          //   color: props?.checked ? "#604FDC" : undefined
          // }}
        >
          {props?.label}
        </span>
      }
    />
  );
}

export default CustomSwitch;
