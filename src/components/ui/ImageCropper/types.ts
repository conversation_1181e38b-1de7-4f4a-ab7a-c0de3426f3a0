import { ReactCropProps } from "react-image-crop";

export type TImageCropper = {
  file?: File;
  onConfirm: (file: File) => void;
  onCancel: () => void;
  cropperProps?: TTImageCropperOptions;
  onLoaded?: () => void;
  maxFileSizeMB?: number;
  withCompressorMaxFileSizeMB?: number;
};

export type TTImageCropperOptions = Omit<ReactCropProps, "crop" | "onChange"> & {
  initialCrop?: ReactCropProps["crop"];
};
