/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @next/next/no-img-element */
import React, { useRef, useState } from "react";
import { TImageCropper } from "./types";
import "react-image-crop/dist/ReactCrop.css";
import ReactCrop, { type Crop } from "react-image-crop";
import CustomButton from "../CustomButton/CustomButton";
import { useTranslation } from "react-i18next";
import { CircularProgress, Slider } from "@mui/material";
import { Icon } from "@iconify/react";
import { compressImage } from "@/utils/imageTools";

function ImageCropper({
  file,
  onConfirm,
  onCancel,
  cropperProps,
  onLoaded,
  maxFileSizeMB = 1,
  // if file is too large, the crop function wont work on mobile devices
  // becase of limitation of canvas memory limit. Most browsers have a maximum canvas size of around 32,768 pixels in either dimension
  // so it's should have value (5) even if `withCompressorMaxFileSizeMB` dosent specified.
  withCompressorMaxFileSizeMB = 5
}: TImageCropper) {
  const { t } = useTranslation();

  const [crop, setCrop] = useState<Crop>();
  const [scale, setScale] = useState(1);
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isLoadingGetCroppedImage, setIsLoadingGetCroppedImage] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const isMobile = typeof window !== undefined && window.innerWidth < 600;

  const loadImageInImageSrc = async (file: File) => {
    if (withCompressorMaxFileSizeMB && withCompressorMaxFileSizeMB > 0 && file.type.startsWith("image/")) {
      try {
        const res = await compressImage(file, { maxFileSizeMB: 1 });

        const reader = new FileReader();
        reader.onload = event => {
          setImageSrc(event?.target?.result as string);
        };
        reader.readAsDataURL(res);

        return;
      } catch (error) {
        setErrorMessage(t("cropper.error"));
      }
    }

    const reader = new FileReader();
    reader.onload = event => {
      setImageSrc(event?.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  React.useEffect(() => {
    if (!file) return;
    loadImageInImageSrc(file);
  }, [file]);

  if (!file) {
    return <div />;
  }

  const onImageSrcLoaded = () => {
    if (crop) return;

    setTimeout(() => {
      onLoaded?.();
    }, 0);

    const imgElement = imgRef?.current;

    if (!imgElement) return;

    setTimeout(
      () => {
        const containerWidth = imgElement.parentElement?.getBoundingClientRect().width ?? 0;
        const containerHeight = imgElement.parentElement?.getBoundingClientRect().height ?? 0;
        const naturalWidth = imgElement.naturalWidth;
        const naturalHeight = imgElement.naturalHeight;

        // Adjust the initial crop to fit inside the container
        let width = naturalWidth;
        let height = naturalHeight;

        if (cropperProps?.aspect) {
          // Calculate height based on the aspect ratio
          width = Math.min(naturalWidth, containerWidth); // Ensure it fits in container
          height = width / cropperProps.aspect;

          if (height > containerHeight) {
            height = containerHeight;
            width = height * cropperProps.aspect;
          }
        } else {
          // Default to the image's dimensions but constrained to the container size
          width = Math.min(naturalWidth, containerWidth);
          height = Math.min(naturalHeight, containerHeight);
        }

        const initialCrop = {
          ...cropperProps?.initialCrop,
          unit: "px" as Crop["unit"],
          x: 0,
          y: 0,
          width,
          height
        };

        setCrop(initialCrop);
      },
      isMobile ? 800 : 0
      // because after 500 ms we call drawer?.calcFitHeight in the mobile drawer,
      // we need this runs after that
    );
  };

  const getCroppedImage = async () => {
    if (!crop || !imgRef.current) return;
    setIsLoadingGetCroppedImage(true);
    setErrorMessage(null);

    const image = imgRef.current;

    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;
    const pixelRatio = 1;
    // const pixelRatio = window.devicePixelRatio;
    // devicePixelRatio slightly increases sharpness on retina devices
    // at the expense of slightly slower render times and needing to
    // size the image back down if you want to download/upload and be
    // true to the images natural size.

    const canvas = document.createElement("canvas");
    canvas.width = Math.floor(crop.width * scaleX * pixelRatio);
    canvas.height = Math.floor(crop.height * scaleY * pixelRatio);
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      setIsLoadingGetCroppedImage(false);
      setErrorMessage(t("cropper.error"));
      throw new Error("No 2D context");
    }

    ctx.scale(pixelRatio, pixelRatio);
    ctx.imageSmoothingQuality = "high";

    const cropX = crop.x * scaleX;
    const cropY = crop.y * scaleY;

    const centerX = image.naturalWidth / 2;
    const centerY = image.naturalHeight / 2;

    ctx.save();

    // Move the crop origin to the canvas origin (0,0)
    ctx.translate(-cropX, -cropY);
    // Move the origin to the center of the original position
    ctx.translate(centerX, centerY);

    // Uncomment and implement rotation if needed
    // ctx.rotate(rotateRads);

    // Scale the image
    ctx.scale(scale, scale);

    // Move the center of the image to the origin (0,0)
    ctx.translate(-centerX, -centerY);

    // Draw the image
    ctx.drawImage(image, 0, 0, image.naturalWidth, image.naturalHeight, 0, 0, image.naturalWidth, image.naturalHeight);

    try {
      canvas.toBlob(blob => {
        if (blob) {
          const croppedFile = new File([blob], file.name, { type: file.type });
          setIsLoadingGetCroppedImage(false);
          onConfirm(croppedFile);
        } else {
          throw new Error(t("cropper.fileTooLarge"));
        }
      }, "image/png");
    } catch (error: any) {
      setIsLoadingGetCroppedImage(false);
      setErrorMessage(t("cropper.fileTooLarge"));
      console.debug(error?.message);
    } finally {
      ctx.restore();
    }
  };

  return (
    <div className="flex flex-col gap-3 pt-4">
      <div className="flex items-center justify-between">
        <div className="text-gray-600 font-medium text-sm">{t("uploadImage")}</div>
        <Icon
          icon="material-symbols:close-rounded"
          className="text-gray-600 cursor-pointer"
          width={20}
          height={20}
          onClick={onCancel}
        />
      </div>
      {!imageSrc && (
        <div className="flex items-center justify-center py-52">
          <CircularProgress size={24} />
        </div>
      )}
      {imageSrc && (
        <div className="flex items-center justify-center">
          <ReactCrop crop={crop} onChange={c => setCrop(c)} className="w-fit" {...cropperProps} keepSelection>
            <img
              onLoad={onImageSrcLoaded}
              ref={imgRef}
              src={imageSrc}
              className="!max-h-[60vh]"
              style={{ transform: `scale(${scale})` }}
            />
          </ReactCrop>
        </div>
      )}

      <div className="flex flex-col gap-1">
        <div className="flex justify-between">
          <div className="w-1/2" />

          <div className="w-1/2 flex items-center gap-4">
            <Icon icon="solar:gallery-round-outline" width={16} height={16} />
            <Slider
              aria-label="scale"
              value={scale}
              onChange={(e, v) => setScale(v as number)}
              step={0.1}
              min={1}
              max={5}
              disabled={!imageSrc}
            />
            <Icon icon="solar:gallery-round-outline" width={24} height={24} />
          </div>
        </div>
        <div className="flex items-center text-gray-500 gap-1.5 text-sm">
          <Icon icon="solar:info-circle-bold" />
          {errorMessage ? (
            <div className="text-error-500">{errorMessage}</div>
          ) : (
            t("cropperDescription", { size: maxFileSizeMB })
          )}
        </div>
      </div>
      <div className="flex items-center gap-4 w-full justify-end">
        <CustomButton size="small" onClick={getCroppedImage} disabled={!imageSrc || !crop || isLoadingGetCroppedImage}>
          {isLoadingGetCroppedImage && <CircularProgress size={24} className="ml-2" />} {t("confirmAndUpload")}
        </CustomButton>
      </div>
    </div>
  );
}

export default ImageCropper;
