import React, { useRef, useState } from "react";
import { twMerge } from "tailwind-merge";

type FilePicker = React.InputHTMLAttributes<HTMLInputElement> & {
  className?: string;
  onDragChange?: (isDragging: boolean) => void;
};

const FilePicker = (props: FilePicker) => {
  const { onChange, onDragChange, children, className, ...restProps } = props;

  const inputRef = useRef<HTMLInputElement>(null);
  // Reset the input by updating the key
  // for handling if user select same file again, we want to
  // call onChange again and again
  const [key, setKey] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  const handleClick = () => {
    inputRef?.current?.click();
  };

  const handleChange: React.ChangeEventHandler<HTMLInputElement> = event => {
    onChange?.(event);
    setKey(prevKey => prevKey + 1);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
    onDragChange?.(false);

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      // Create a synthetic event for `onChange`
      const syntheticEvent = {
        target: { files } as EventTarget & HTMLInputElement
      } as unknown as React.ChangeEvent<HTMLInputElement>;

      onChange?.(syntheticEvent);
      setKey(prevKey => prevKey + 1);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (!isDragging) {
      setIsDragging(true);
      onDragChange?.(true); // Notify parent component
    }
  };

  const handleDragLeave = () => {
    if (isDragging) {
      setIsDragging(false);
      onDragChange?.(false); // Notify parent component
    }
  };

  return (
    <div
      className={twMerge("cursor-pointer", className)}
      onClick={handleClick}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      <input ref={inputRef} key={key} type="file" className="hidden" onChange={handleChange} {...restProps} />
      {children}
    </div>
  );
};

export default FilePicker;
