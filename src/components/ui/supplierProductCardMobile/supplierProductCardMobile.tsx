import { Icon } from "@iconify/react";
import Image from "next/image";
import React from "react";

type SupplierProductCardMobileProps = {
  id: string;
  title: string;
  imageUrl?: string;
  inventory?: number | string;
  price?: number | string;
  onClick?: (id: string) => void;
  onClickMenu?: (id: string) => void;
};

function SupplierProductCardMobile({
  title,
  price,
  inventory,
  imageUrl,
  onClick,
  onClickMenu,
  id
}: SupplierProductCardMobileProps) {
  return (
    <div
      className="flex items-stretch gap-2 bg-v2-surface-primary rounded-lg overflow-hidden p-4 flex-1 shrink-0"
      onClick={() => onClick?.(id)}
    >
      <div className="size-16 rounded-md relative overflow-hidden shrink-0 bg-gray-50">
        {imageUrl && <Image src={imageUrl} alt={title} fill className="w-full h-full object-cover" />}
      </div>
      <div className="flex-1 flex flex-col py-0.5 justify-between items-end">
        <div className="flex gap-2.5 items-start self-stretch w-full justify-between">
          <div className="text-v2-content-primary font-medium text-xs shrink-0 w-[80%]">{title}</div>
          <div>
            <Icon
              icon="solar:menu-dots-bold"
              className="rotate-90 shrink-0 size-4 text-v2-content-primary cursor-pointer"
              onClick={() => onClickMenu?.(id)}
            />
          </div>
        </div>

        <div className="flex justify-between items-center w-full">
          <div className="text-v2-content-tertiary text-[10px] leading-4 font-normal">{inventory}</div>
          <div className="text-v2-content-secondary text-[10px] leading-4 font-normal">{price}</div>
        </div>
      </div>
    </div>
  );
}

export default SupplierProductCardMobile;
