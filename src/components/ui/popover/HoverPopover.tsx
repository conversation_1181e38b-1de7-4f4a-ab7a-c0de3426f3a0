import React from "react";
import { Box } from "@mui/material";
import { Popover } from "@mui/material";
import { TPopoverProps } from "./types";

const HoverPopover = ({
  children,
  rootClassName = "",
  contentRootClassName = "",
  content,
  popOverProps
}: TPopoverProps) => {
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handlePopoverOpen = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <span
        aria-owns={open ? "mouse-over-popover" : undefined}
        aria-haspopup="true"
        onMouseEnter={handlePopoverOpen}
        onMouseLeave={handlePopoverClose}
        className={rootClassName}
      >
        {typeof children === "function" ? children({ isOpen: open, handleClose: handlePopoverClose }) : children}
      </span>
      <Popover
        id="mouse-over-popover"
        open={open}
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left"
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left"
        }}
        onClose={handlePopoverClose}
        disableRestoreFocus
        className="pointer-events-none"
        {...popOverProps}
      >
        <Box p={2} className={"bg-cards " + contentRootClassName}>
          {typeof content === "function" ? content({ isOpen: open, handleClose: handlePopoverClose }) : content}
        </Box>
      </Popover>
    </>
  );
};
export default HoverPopover;
