import { PopoverProps } from "@mui/material";
import { ReactNode } from "react";

export type TPopoverProps = {
  children?: ReactNode | (({ isOpen, handleClose }: { isOpen: boolean; handleClose: () => void }) => ReactNode);
  rootClassName?: string;
  contentRootClassName?: string;
  content?: ReactNode | (({ isOpen, handleClose }: { isOpen: boolean; handleClose: () => void }) => ReactNode);
  popOverProps?: Partial<PopoverProps>;
};
