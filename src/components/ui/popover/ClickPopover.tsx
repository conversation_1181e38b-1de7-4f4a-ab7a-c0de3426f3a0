import React from "react";
import Box from "@mui/material/Box";
import Popover from "@mui/material/Popover";
import { TPopoverProps } from "./types";

const ClickPopover = ({
  children,
  rootClassName = "",
  contentRootClassName = "",
  content,
  popOverProps
}: TPopoverProps) => {
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleClick = (event: any) => {
    event?.preventDefault();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  return (
    <>
      <span className={rootClassName} aria-describedby={id} onClick={handleClick}>
        {typeof children === "function" ? children({ isOpen: open, handleClose }) : children}
      </span>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left"
        }}
        {...popOverProps}
      >
        <Box p={2} className={"bg-cards " + contentRootClassName}>
          {typeof content === "function" ? content({ isOpen: open, handleClose }) : content}
        </Box>
      </Popover>
    </>
  );
};
export default ClickPopover;
