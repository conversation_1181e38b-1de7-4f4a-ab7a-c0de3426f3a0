import React from "react";
import { twMerge } from "tailwind-merge";

export type TCustomTabsProps = {
  className?: string;
  items: { value: string; title: string }[];
  value?: string;
  onChange?: (value: string) => void;
};

function CustomTabs({ className, items, value, onChange }: TCustomTabsProps) {
  return (
    <div className={twMerge("flex items-center cursor-pointer gap-4", className)}>
      {items?.map(item => (
        <div
          key={item.value}
          className={twMerge(
            "flex flex-1 flex-col gap-1.5 justify-center items-center border-b-2 py-2.5 px-3 cursor-pointer",
            item.value === value ? "border-v2-content-on-action-2" : "border-transparent"
          )}
          onClick={() => onChange?.(item.value)}
        >
          <div className={twMerge("text-[13px] font-medium text-v2-content-primary leading-5")}>{item.title}</div>
        </div>
      ))}
    </div>
  );
}

export default CustomTabs;
