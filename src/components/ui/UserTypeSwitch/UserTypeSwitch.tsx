import "./UserTypeSwitch.css";

import { Box, Button, Typography } from "@mui/material";
import clsx from "clsx";
import Image from "next/image";

type ButtonSwitchProps = {
  checked: boolean;
  onChange: (v: boolean) => void;
  labels: { legal: string; real: string };
  subTitles: { legal: string; real: string };
};

const UserTypeSwitch = (props: ButtonSwitchProps) => {
  const { checked, onChange, labels, subTitles } = props;

  return (
    <Box className="button-switch-container">
      <Button onClick={() => onChange(false)} className={`button-switch-button ${!checked ? "inactive" : ""}`}>
          <Image
            src={!checked ? "/images/svgs/active-user-real.svg" : "/images/svgs/notactive-user-real.svg"}
            alt="realUser"
            width={36}
            height={36}
          />

          {/* <Image src="/images/svgs/notactive-user-real.svg" alt="realUser" width={36} height={36} /> */}

          {/* <Image src="/images/svgs/active-user-legal.svg" alt="realUser" width={36} height={36} />
        <Image src="/images/svgs/notactive-user-legal.svg" alt="realUser" width={36} height={36} /> */}
          <Box>
            <Typography
              variant="subtitle1"
              className={clsx(
                "button-switch-container-title text-start",
                !checked ? "button-switch-container-title--active" : ""
              )}
            >
              {labels?.real}
            </Typography>
            <Typography
              variant="subtitle1"
              className={clsx(
                "button-switch-container-subtitle text-start",
                !checked ? "button-switch-container-subtitle--active" : ""
              )}
            >
              {subTitles?.real}
            </Typography>
          </Box>
        <div className="mr-auto shrink-0">
          {!checked ? (
            <Image src="/images/svgs/user-checked.svg" alt="realUser" width={20} height={20} />
          ) : (
            <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={20} height={20} />
          )}
        </div>

        {/* <div className="border border-solid border-2 border-gray-50 rounded-full size-5 mr-16" /> */}
      </Button>
      <Button onClick={() => onChange(true)} className={`button-switch-button ${checked ? "active" : ""}`}>
        {/* <Image src="/images/svgs/user-multiple.svg" alt="legalUser" width={36} height={36} /> */}
        <Image
          src={checked ? "/images/svgs/active-user-legal.svg" : "/images/svgs/notactive-user-legal.svg"}
          alt="realUser"
          width={36}
          height={36}
        />
        <Box>
          <Typography
            variant="subtitle1"
            className={clsx(
              "button-switch-container-title text-start",
              checked ? "button-switch-container-title--active" : ""
            )}
          >
            {labels?.legal}
          </Typography>
          <Typography
            variant="subtitle1"
            className={clsx(
              "button-switch-container-subtitle text-start",
              checked ? "button-switch-container-subtitle--active" : ""
            )}
          >
            {subTitles?.legal}
          </Typography>
        </Box>
        <div className="mr-auto shrink-0">
          {checked ? (
            <Image src="/images/svgs/user-checked.svg" alt="realUser" width={20} height={20} />
          ) : (
            <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={20} height={20} />
          )}
        </div>
        {/* <div className="border border-solid border-2 border-gray-50 rounded-full size-5 mr-16" /> */}
      </Button>
    </Box>
  );
};

export default UserTypeSwitch;
