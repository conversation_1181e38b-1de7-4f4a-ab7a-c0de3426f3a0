.button-switch-container {
  display: flex;
  align-items: stretch;
  gap: 8px;
  width: 100%;
}

.button-switch-button {
  border: 2px solid rgb(var(--color-gray-50));
  border-radius: 8px;
  cursor: pointer;
  color: var(--mui-palette-grey-900);
  background-color: transparent;
  padding: 18px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

@media screen and (max-width: 768px) {
  .button-switch-button {
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;

    /* justify-content: space-between; */
    flex: 1;
  }

  .button-switch-button {
    flex: 1;
  }

  .button-switch-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }
}

.button-switch-button:hover {
  background-color: transparent;
  border: 2px solid rgb(var(--color-purple-500));
  color: rgb(var(--color-gray-999));
}

.button-switch-button.active {
  /* background-color: rgb(var(--color-purple-50)); */
  border: 2px solid rgb(var(--color-purple-500));
  color: var(--mui-palette-grey-900);
}

.button-switch-button.inactive {
  /* background-color: rgb(var(--color-purple-50)); */
  border: 2px solid rgb(var(--color-purple-500));
  color: var(--mui-palette-grey-900);
}

.button-switch-container-title--active {
  color: rgb(var(--color-gray-999));
}

.button-switch-container-subtitle--active {
  color: rgb(var(--color-gray-999));
}

.button-switch-container-title {
  font-size: 12px;
  font-weight: 700;
}

.button-switch-container-subtitle {
  font-size: 10px;
  font-weight: 400;
}
