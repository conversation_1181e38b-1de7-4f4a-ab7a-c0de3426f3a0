.bottom-action {
  display: none;

}

.bottom-action .MuiBottomNavigationAction-label {
  font-size: 11px;
  font-weight: 500;
  color: rgb(var(--color-gray-400));
  margin-top: 2px;
  text-align: center;

}

.bottom-action-active .MuiBottomNavigationAction-label {
  color: rgb(var(--color-purple-500));
}

.bottom-action-active svg {
  color: rgb(var(--color-purple-500));
}

@media (max-width: 370px) {
  .bottom-action-action {
    margin-inline: -8px;
  }
}

@media screen and (max-width: 768px) {
  .bottom-action + #sx-layout-108 {
    margin-block-end: 75px;
  }

  .bottom-action {
    display: flex;
    background-color: rgb(var(--color-cards));
    z-index: 13;
    position: fixed;
    bottom: 0;
    height: 72px !important;
    width: 100%;
    inset-inline-start: 0px;
    inset-inline-end: 0px;
  }

  .bottom-action > .bottom-navigation {
    height: 72px !important;
  }

  #sx-customizer-6749 {
    inset-block-end: 70px !important;
  }
}
