import React, { ReactNode } from "react";
import { BottomNavigation } from "@mui/material";

import CustomButton, { ICustomButtonProps } from "../CustomButton/CustomButton";

import "./BottomAction.css";
import { twMerge } from "tailwind-merge";

interface BottomActionProps {
  children?: ReactNode;
  saveButtonText?: ReactNode;
  cancelButtonText?: ReactNode;
  cancelButtonProps?: ICustomButtonProps;
  saveButtonProps?: ICustomButtonProps;
  containerClassName?: string;
}

function BottomAction({
  children,
  saveButtonText,
  saveButtonProps,
  cancelButtonText,
  cancelButtonProps,
  containerClassName
}: BottomActionProps) {
  return (
    <BottomNavigation className={twMerge("bottom-action", containerClassName)}>
      {children ? (
        children
      ) : (
        <div className="flex items-center justify-between  px-3 py-4 w-full">
          {cancelButtonText && (
            <CustomButton color="secondary" {...cancelButtonProps}>
              {cancelButtonText}
            </CustomButton>
          )}
          {saveButtonText && (
            <CustomButton {...saveButtonProps} className={twMerge("mr-auto", saveButtonProps?.className)}>
              {saveButtonText}
            </CustomButton>
          )}
        </div>
      )}
    </BottomNavigation>
  );
}

export default BottomAction;
