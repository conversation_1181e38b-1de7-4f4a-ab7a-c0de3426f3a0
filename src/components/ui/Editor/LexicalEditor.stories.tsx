import type { Meta, StoryObj } from "@storybook/react";

import LexicalEditor from "./Editor";
import { useState } from "react";

const EditorExample = () => {
  const [value, setValue] = useState("");
  console.log("value", value);

  return <LexicalEditor  initialValue={value} onChange={setValue} />;
};

const meta: Meta<typeof LexicalEditor> = {
  component: () => <EditorExample />,
  title: "Components/ui/LexicalEditor"
};

export default meta;
type Story = StoryObj<typeof LexicalEditor>;

export const Default: Story = {
  args: {}
};
