import { Icon } from "@iconify/react";
import React, { ReactNode, useState } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

function ExpandableContent({
  children,
  className,
  buttonClassName
}: {
  children: ReactNode;
  className?: string;
  buttonClassName?: string;
}) {
  const { t } = useTranslation();
  const [isExpanded, setisExpanded] = useState(false);
  const needExpand = typeof children === "string" && children?.length > 200;

  return (
    <>
      <div className={twMerge(isExpanded ? "" : needExpand ? "line-clamp-4" : "", className)}>{children}</div>

      {needExpand && (
        <div
          className={twMerge(
            "cursor-pointer flex items-center justify-center bg-gray-50 text-gray-400 w-fit mx-auto px-4 py-2 rounded-md gap-1.5 mt-3 text-[10px] font-bold",
            buttonClassName
          )}
          onClick={() => setisExpanded(prev => !prev)}
        >
          {isExpanded ? (
            <>
              <span>{t("showLess")}</span>{" "}
              <Icon icon="solar:alt-arrow-down-outline" width={18} height={18} className="rotate-180" />
            </>
          ) : (
            <>
              <span>{t("showMore")}</span> <Icon icon="solar:alt-arrow-down-outline" width={18} height={18} />
            </>
          )}
        </div>
      )}
    </>
  );
}

export default ExpandableContent;
