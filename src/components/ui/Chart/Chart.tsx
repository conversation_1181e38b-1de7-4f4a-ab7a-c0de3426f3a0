import HighchartsReact from "highcharts-react-official";
import React from "react";
import Highcharts, { Options } from "highcharts";
import { toCommas } from "@/utils/helpers";
import useLanguage from "@/utils/hooks/useLanguage";
import useCurrency from "@/utils/hooks/useCurrency";
import { useTranslation } from "react-i18next";

function Chart({ data, options }: { data?: any; options?: Options }) {
  const { t } = useTranslation();
  const [{ renderDate }] = useLanguage();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };

  const chartOptions = {
    title: {
      text: ""
    },
    chart: {
      type: "areaspline"
    },
    xAxis: {
      type: "datetime",
      lineColor: "rgb(179 184 194)",
      tickColor: "rgb(179 184 194)",
      labels: {
        style: {
          color: "rgb(136 144 159)",
          fontFamily: "Iran yekan"
        },
        formatter() {
          // eslint-disable-next-line react/no-this-in-sfc
          return renderDate(this.value as string, "YYYY/MM"); // Custom formatting for X-axis labels
        }
      }
    },
    yAxis: {
      gridLineColor: "rgba(0, 0, 26, 0.15)",
      gridLineDashStyle: "Dash",
      labels: {
        enabled: false,
        style: {
          color: "rgb(136 144 159)",
          fontFamily: "Iran yekan"
        },
        formatter() {
          return `
          <span >${renderPrice(this.value || "")}</span>
          `;
        }
      },
      title: ""
    },
    tooltip: {
      enabled: false
      // useHTML: true,
      // backgroundColor: "transparent",
      // borderWidth: 0,
      // shadow: false,
      // padding: 0,
      // formatter() {
      //   return `
      //       <div class="bg-cards font-iranYekan p-3 rounded-xl flex flex-col items-start border border-solid border-gray-50" style="box-shadow: 0px 0px 8px 0px #00000014;direction:rtl;>
      //       <span class="text-caption-bold text-gray-600 ">${t("chart.income")}</span>
      //       <p class="text-body1-bold mt-3 ">${renderPrice(this.y || "")}</p>
      //       </div>
      //   `;
      // }
    },
    plotOptions: {
      areaspline: {
        color: "rgba(137, 121, 255, 1)",
        fillColor: {
          linearGradient: { x1: 0, x2: 0, y1: 0, y2: 1 },
          stops: [
            [0, "rgba(137, 121, 255, 0.3)"],
            [1, "rgba(137, 121, 255, 0.05)"]
          ]
        },
        threshold: 0,
        marker: {
          enabled: false
          // lineWidth: 1,
          // lineColor: "",
          // fillColor: "white"
        }
      }
    },
    credits: {
      enabled: false
    },
    legend: {
      enabled: false
    },
    series: [
      {
        type: "areaspline",
        data
      }
    ],
    ...options
  } as Options;

  return <HighchartsReact highcharts={Highcharts} options={chartOptions} />;
}

export default Chart;
