.custom-button {
  border-radius: 6px;
  font-size: 12px;
  font-weight: 700;
  padding-block: 14px;
  max-height: 48px;
}

@media (max-width: 500px) {
  .custom-button {
    padding-block: 10px;
    max-height: 40px;
  }
}

.custom-button.MuiButton-containedPrimary {
  background-color: rgb(var(--color-purple-500));
  border: 1px solid rgb(var(--color-purple-500));
}

.custom-button.MuiButton-containedPrimary:disabled,
.custom-button.MuiButton-containedSecondary:disabled,
.custom-button.MuiButton-containedInfo:disabled {
  background-color: rgb(var(--color-gray-50));
  color: #adadad;
  border: 1px solidrgb(var(--color-gray-50));
}

.custom-button.MuiButton-containedPrimary:hover {
  background-color: #7263ff;
}

.custom-button.MuiButton-containedSecondary {
  background-color: transparent;
  border: 1px solid rgb(var(--color-gray-50));
  color: var(--mui-palette-grey-500);
}

.custom-button.MuiButton-containedSecondary:hover {
  background-color: var(--mui-palette-grey-100);
  border-color: var(--mui-palette-grey-300);
  color: var(--mui-palette-grey-500);
}

.custom-button.MuiButton-containedInfo {
  background-color: transparent;
  border: 1px solid transparent;
  color: var(--mui-palette-grey-500);
}

.custom-button.MuiButton-containedInfo:hover {
  background-color: transparent;
  box-shadow: unset;
  border: 1px solid var(--mui-palette-grey-300);
}

.MuiButton-startIcon > *:nth-of-type(1) {
  font-size: 13px !important;
}

.MuiButton-endIcon > *:nth-of-type(1) {
  font-size: 13px !important;
}
