import "./CustomButton.css";

import * as React from "react";
import { Button, ButtonProps } from "@mui/material";
import clsx from "clsx";

export interface ICustomButtonProps extends ButtonProps {}

function CustomButton(props: ICustomButtonProps) {
  return (
    <Button
      {...props}
      disableRipple
      size={props.size ?? "large"}
      className={clsx("custom-button", props?.className)}
      color={props.color || "primary"}
      variant={props?.variant || "contained"}
    >
      {props?.children}
    </Button>
  );
}

export default CustomButton;
