#sx-customtablepagination-3193 {
  display: flex;
  align-items: center;
}

.sx-customtablepagination-4561 {
  margin-left: 8px;
  margin-right: 16px;
  max-height: 36px;
  width: fit-content;
  /* max-width: 70px; */
  font-size: 14px;
  font-weight: 700;
  padding: 10px 6px 10px 12px;
  border: 1px solid rgb(var(--color-gray-40));
  border-radius: 8px;
}

.sx-customtablepagination-4561 .MuiInputBase-input {
  /* padding: 0 !important; */
  padding-inline-end: 8px !important;
}

/* MuiOutlinedInput-root */

.sx-pagination-button {
  margin: 0 8px;
}

.sx-pagination-page {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin: 0 4px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
}

.sx-pagination-page-active {
  background-color: #6f52ed;
  color: #ffffff;
}

.sx-customtablepagination-4561 .MuiSelect-iconOutlined {
  display: none;
}
