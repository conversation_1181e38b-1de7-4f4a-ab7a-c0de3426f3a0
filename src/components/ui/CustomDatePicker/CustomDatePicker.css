.datePicker {
  font-family: inherit;
  border: none !important;
  box-shadow: 0px 4px 11px 0px rgba(35, 41, 70, 0.11);
}

.custom-date-picker-wrapper {
  width: 100%;
}

/* .mobileDateTime {
  background-color: red !important;
}

.mobileDateTime .MuiPickersToolbar-content  {
  color: red !important;
} */

.MuiPaper-root-MuiPickersPopper-paper {
  border-radius: 8px;
}

.custom-datepicker-input input {
  direction: ltr;
  text-align: end;
}

.rmdp-time-picker input {
  font-family: Iran yekan;
}

.custom-date-picker-time.rmdp-time-picker div input {
  font-size: 12px;
}
