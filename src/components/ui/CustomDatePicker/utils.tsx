import persian from "react-date-object/calendars/persian";
import persianFa from "react-date-object/locales/persian_fa";
import gregorian from "react-date-object/calendars/gregorian";
import gregorianEn from "react-date-object/locales/gregorian_en";
import arabic from "react-date-object/calendars/arabic";
import arabicAr from "react-date-object/locales/arabic_ar";
import i18next from "i18next";
import { AdapterDateFnsJalali } from "@mui/x-date-pickers/AdapterDateFnsJalali";
import { format } from "date-fns-jalali";
import { CustomToolbarProps } from "./types";

const adapter = new AdapterDateFnsJalali();

export const weekDaysFA = ["ش", "ی", "د", "س", "چ", "پ", "ج"];
export const weekDaysAR = ["ح", "ن", "ث", "ر", "خ", "ج", "س"];
export const weekDaysEN = ["Sa", "Mo", "Tu", "We", "Th", "Fr", "sa"];

export const _calendar = {
  en: gregorian,
  fa: persian,
  ar: arabic
};

export const _locale = {
  en: gregorianEn,
  fa: persianFa,
  ar: arabicAr
};

export const _week_days = {
  en: weekDaysEN,
  fa: weekDaysFA,
  ar: weekDaysAR
};

export type TWeekDaysTitle = { [key: number]: string };

export const weekDaysTitle: TWeekDaysTitle = {
  6: i18next.t("weekDays.saturday"),
  0: i18next.t("weekDays.sunday"),
  1: i18next.t("weekDays.monday"),
  2: i18next.t("weekDays.tuesday"),
  3: i18next.t("weekDays.wednesday"),
  4: i18next.t("weekDays.thursday"),
  5: i18next.t("weekDays.friday")
};

export const maxDate = new Date(new Date().getTime() + 1 * 60 * 1000);

export function formatDate(value: number | Date, formatType: string) {
  if (!value) return "";

  const dateValue = value instanceof Date ? value : new Date(value);

  if (isNaN(dateValue.getTime())) {
    return "Invalid date";
  }

  return format(dateValue, formatType, { locale: adapter.locale });
}

export const CustomToolbar: React.FC<CustomToolbarProps> = ({ value, onViewChange }) => {
  return (
    <div
      className="p-4 pb-0"
      style={{
        gridColumn: "2"
      }}
    >
      <div className="flex  justify-between">
        <div className="flex items-center gap-0.5 text-3xl font-medium text-gray-500">
          {/* <span className="text-lg" onClick={() => onViewChange("hours")}>{format(value, "a", { locale: adapter.locale })}</span> */}
          <span onClick={() => onViewChange("minutes")}>{formatDate(value, "mm")}</span>
          <span>:</span>
          <span onClick={() => onViewChange("hours")}> {formatDate(value, "HH")}</span>
        </div>

        <div onClick={() => onViewChange("year")} className="flex flex-col items-end">
          <span className="text-gray-400 text-left">{formatDate(value, "yyyy")}</span>

          <div className="flex items-center gap-2 text-2xl">
            <span>{formatDate(value, "MMMM")}</span>
            <span>{formatDate(value, "dd")}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// const CustomCalendarHeader = (props: any) => {
//   const { currentMonth, onMonthChange } = props;

//   const handleMonthClick = () => {
//     if (currentView === "day") {
//       handleViewChange("month");
//     } else {
//       handleViewChange("day");
//     }
//   };

//   const handleYearClick = () => {
//     handleViewChange("year");
//   };

//   const handlePreviousMonth = () => {
//     onMonthChange(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
//   };

//   const handleNextMonth = () => {
//     onMonthChange(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
//   };

//   return (
//     <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", padding: "8px" }}>
//       <button onClick={handlePreviousMonth}>&lt;</button>
//       <div>
//         <button onClick={handleMonthClick}>{format(currentMonth, "MMMM", { locale: adapter.locale })}</button>
//         <button onClick={handleYearClick}>{format(currentMonth, "yyyy", { locale: adapter.locale })}</button>
//       </div>
//       <button onClick={handleNextMonth}>&gt;</button>
//     </div>
//   );
// };

// const RenderInput = () => {
//   return (
//     <CustomTextField
//       {...inputProps}
//       onFocus={handleOpen}
//       fullWidth
//       autoComplete="off"
//       inputProps={{
//         ...inputProps?.inputProps,
//         autoComplete: "off",
//         readOnly: true,
//         form: {
//           autocomplete: "off"
//         }
//       }}
//       InputProps={{
//         ...inputProps?.InputProps,
//         autoComplete: "off",
//         name,
//         endAdornment: <Icon icon="solar:calendar-outline" className="size-6" />
//       }}
//       optional={optional}
//       name={name}
//       error={error}
//       helperText={helperText}
//       type="text"
//       label={label}
//       placeholder={placeholder}
//       onBlur={onBlur}
//     />
//   );
// };
