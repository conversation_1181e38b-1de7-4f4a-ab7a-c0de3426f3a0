import { InputBaseProps, InputProps, TextFieldProps } from "@mui/material";
import {
  DatePickerProps,
  DateTimePickerToolbarProps,
  StaticDatePickerSlots,
  StaticDateTimePickerProps,
  StaticDateTimePickerSlots
} from "@mui/x-date-pickers";
import { Dayjs } from "dayjs";
import { ReactNode } from "react";
import { ReactElement } from "react";
import { CalendarProps } from "react-big-calendar";
import DateObject from "react-date-object";
import { TInput } from "../inputs/Input/types";

export type TCalendarLocales = "en" | "fa" | "ar";
export type TCalendarValue = string | Date | null;
export type TCalendarPassedArgs = { value: Date | Date[]; range: boolean };

export type TDatePickerInput = {
  value: TCalendarValue;
  onFocus?: InputBaseProps["onFocus"];
};

export type TDatePickerProps = DatePickerProps<any> & StaticDateTimePickerProps<any>;
// StaticDateTimePickerProps

export type ICalendarProps = {
  label?: string;
  placeholder?: string;
  name?: string;
  optional?: boolean;
  error?: boolean;
  helperText?: string;
  inputProps?: TInput;
  wrapperStyle?: React.CSSProperties;
  onBlur?: InputBaseProps["onBlur"];

  maxDate?: Date;

  onChange?(selectedDates: TCalendarValue): void;

  hasTime?: boolean;

  value?: TCalendarValue;
} & TDatePickerProps;

export interface IRenderCalendarHeader {
  /**
   * calendar header props
   */

  /**
   * date picker value
   */
  finalValue: Date | Date[];

  /**
   * check is range picker or single picker
   * @default false
   */
  range?: boolean;
}

export interface IRenderCalendarButton {
  /**
   * check direction of arrow keys
   */
  direction: "left" | "right";

  /**
   * fire click on arrow keys
   */
  onClick: () => void;
}

export interface CustomToolbarProps extends Omit<DateTimePickerToolbarProps<any>, "value"> {
  value: number | Date;
}
