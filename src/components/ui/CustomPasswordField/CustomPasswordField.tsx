import { useState } from "react";
import { Icon } from "@iconify/react";
import Input from "../inputs/Input";
import { IconButton, TextFieldProps } from "@mui/material";

import CustomTextField from "../CustomTextField/CustomTextField";
import "./CustomPasswordField.css";
import { TInput } from "../inputs/Input/types";

interface ICustomPasswordFieldProps extends TInput {}

const CustomPasswordField = (props: ICustomPasswordFieldProps) => {
  const [showPassword, setShowPassword] = useState(false);

  const handleClickShowPassword = () => setShowPassword(prev => !prev);

  return (
    <Input
      {...props}
      autoComplete="off"
      // className="custom-password-field"
      type={showPassword ? "text" : "password"}
      endAdornment={
        <IconButton aria-label="toggle password visibility" onClick={handleClickShowPassword}>
          {showPassword ? (
            <Icon fontSize={20} icon="solar:eye-outline" />
          ) : (
            <Icon fontSize={20} icon="solar:eye-closed-outline" />
          )}
        </IconButton>
      }
    />
  );
};

export default CustomPasswordField;
