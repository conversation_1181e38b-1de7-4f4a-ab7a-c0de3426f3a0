import React, { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

interface ICustomFormHelperTextProps {
  children: ReactNode;
  error?: boolean;
  className?: string;
}

function InputHelper({ children, className, error = true }: ICustomFormHelperTextProps): ReactNode {
  return (
    <div
      className={twMerge(
        "text-xs font-normal text-v2-content-tertiary",
        error ? "text-v2-content-on-error-2" : "",
        className
      )}
    >
      {children}
    </div>
  );
}

export default InputHelper;
