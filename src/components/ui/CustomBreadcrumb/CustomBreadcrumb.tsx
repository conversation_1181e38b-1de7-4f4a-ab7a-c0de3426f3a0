import React from "react";
import { Breadcrumbs } from "@mui/material";
import { Grid, Typography, Link } from "@mui/material";
import NextLink from "next/link";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";

import "./CustomBreadcrumb.css";
import { twMerge } from "tailwind-merge";

export type CustomBreadCrumbItemsType = { title: string; to?: string; onClick?: () => void; className?: string };
export interface CustomBreadCrumbType {
  subtitle?: string;
  items?: CustomBreadCrumbItemsType[];
  className?: string;
}

const CustomBreadcrumb = ({ subtitle, items, className }: CustomBreadCrumbType) => {
  return (
    <Grid container alignItems="center" id="sx-breadcrumb-6619" style={{ padding: "10px 0" }} className={className}>
      <Grid item xs={12}>
        <Breadcrumbs
          classes={{
            separator: "custom-breadcrumb-separator"
          }}
          separator={<KeyboardArrowLeftIcon htmlColor="#D4D4D4" fontSize="small" />}
          id="sx-breadcrumb-6640"
          aria-label="breadcrumb"
        >
          {items &&
            items.map(item => (
              <div key={item.title}>
                {item.to ? (
                  <NextLink href={item.to} passHref onClick={item?.onClick}>
                    <Link
                      color="textSecondary"
                      underline="hover"
                      className={twMerge("breadcrumbs-items-text", item?.className)}
                    >
                      {item.title}
                    </Link>
                  </NextLink>
                ) : (
                  <Typography
                    className={twMerge(
                      "breadcrumbs-items-text",
                      item?.onClick ? "cursor-pointer" : "",
                      item?.className
                    )}
                    color="textPrimary"
                    onClick={item?.onClick}
                  >
                    {item.title}
                  </Typography>
                )}
              </div>
            ))}
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <Typography color="textSecondary" variant="h6" fontWeight={400} mb={0}>
          {subtitle}
        </Typography>
      </Grid>
    </Grid>
  );
};

export default CustomBreadcrumb;
