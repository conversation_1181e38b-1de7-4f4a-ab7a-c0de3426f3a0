import { Box, Divider, Typography } from "@mui/material";
import { BoxOwnProps } from "@mui/system";
import React from "react";
import { twMerge } from "tailwind-merge";
import "./sectionInfo.css";

interface ISectionInfoProps {
  title: string;
  boxProps?: BoxOwnProps;
  className?: string;
}

const SectionInfo = ({ title, boxProps, className }: ISectionInfoProps) => {
  return (
    <Box {...boxProps} className={twMerge("sectionInfo-wrapper", className)}>
      <Box className="sectionInfo-line" />
      <span className="text-gray-600">{title}</span>
    </Box>
  );
};

export default SectionInfo;
