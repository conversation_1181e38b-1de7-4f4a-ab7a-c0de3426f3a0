"use client";

import useModal from "@/utils/hooks/useModal";
import { Box, Grid, IconButton, Modal, Typography } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import Close from "@mui/icons-material/Close";
import Image from "next/image";
import clsx from "clsx";
import { CupertinoPane } from "cupertino-pane";
import "./styles.css";
import { twMerge } from "tailwind-merge";
import useModalStore from "@/store/zustand/modalStore";
import Button from "@/components/ui/Button";

const mobileBreakpoint = 600;

function ModalProvider() {
  const { isOpen, modalProps, actions, body, title, subTitle, icon, width = 428, closable = true } = useModalStore();
  const { className, containerClassName, showCloseIcon = true } = modalProps || { showCloseIcon: true };
  const isMobile = typeof window !== undefined && window.innerWidth < mobileBreakpoint;

  const { hideModal } = useModal();
  const [drawer, setDrawer] = useState<CupertinoPane | null>(null);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const drawerId = useMemo(() => `drawer-${(Math.random() * 10000).toFixed()}`, [isOpen]);

  /* -------------------------------------------------------------------------- */
  /*                             mobile action sheet                            */
  /* -------------------------------------------------------------------------- */
  useEffect(() => {
    if (isOpen) {
      /* ------------------------------- open drawer ------------------------------ */
      if (!isMobile) return;

      setDrawer(null);

      setTimeout(() => {
        const newDrawer = new CupertinoPane(
          "." + drawerId, // Pane container selector
          {
            fitHeight: true,
            parentElement: "body", // Parent container
            breaks: {
              middle: { enabled: true, height: 300, bounce: true },
              bottom: { enabled: true, height: 80 }
            },
            initialBreak: "middle",
            backdrop: true,
            backdropBlur: true,
            events: {
              onBackdropTap: () => {
                if (closable) hideModal();
              },
              onDidDismiss: () => {
                if (closable) hideModal();
              }
            }
            // bottomClose: showCloseIcon && closable,
            // buttonDestroy: showCloseIcon && closable
          }
        );
        setDrawer(newDrawer);
        setTimeout(() => newDrawer.present({ animate: true }), 500);
      }, 0);
    } else {
      /* ------------------------------ close drawer ------------------------------ */
      if (drawer) {
        setTimeout(() => drawer?.destroy({ animate: true }));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  useEffect(() => {
    if (drawer !== null) {
      (drawer as CupertinoPane)?.calcFitHeight();
    }
  }, [body]);

  if (isMobile) {
    return (
      <div className={`${drawerId} ${!isOpen ? "!hidden" : ""}`}>
        {/* {closable && showCloseIcon && (
          <IconButton onClick={closable ? hideModal : undefined} className="ms-auto me-6 block p-0 size-5 mt-px">
            <Close className="size-5" />
          </IconButton>
        )} */}
        <div className={twMerge("px-6 pb-6 w-full h-full", containerClassName)}>
          {closable && showCloseIcon && (
            <>
              {icon ? (
                <>
                  {typeof icon === "string" ? (
                    <Image src={icon} alt="modal-icon" width={48} height={48} className="size-12 block mx-auto mt-4" />
                  ) : (
                    icon
                  )}
                </>
              ) : (
                <IconButton onClick={hideModal} className="ms-auto block p-0 size-5">
                  <Close className="size-5" />
                </IconButton>
              )}
            </>
          )}
          {title && <h5 className="text-base font-bold text-center text-v2-content-primary mt-5">{title}</h5>}
          {subTitle && <h6 className="text-xs font-medium text-center text-v2-content-tertiary mt-2">{subTitle}</h6>}
          {body && typeof body === "function" ? body({ drawer }) : body}
          {actions?.length && (
            <Grid container spacing={1} className="mt-4">
              {actions?.reverse()?.map(({ label, onClick, ...restProps }, index) => (
                <Grid key={index} item xs={12} md={actions?.length > 1 ? 6 : 12}>
                  <Button
                    onClick={onClick}
                    {...restProps}
                    size={restProps?.size || "xl"}
                    className={twMerge("w-full", restProps?.className)}
                  >
                    {label}
                  </Button>
                </Grid>
              ))}
            </Grid>
          )}
        </div>
      </div>
    );
  }

  if (!isOpen) return null;

  return (
    <Modal open={isOpen} onClose={closable ? hideModal : undefined} className={clsx(className, "z-[999999999]")}>
      <Box
        width={width}
        className={twMerge(
          "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-lg p-6 bg-cards border-none outline-none",
          containerClassName
        )}
      >
        {closable && showCloseIcon && (
          <IconButton onClick={closable ? hideModal : undefined} className="ms-auto block p-0 size-5">
            <Close className="size-5" />
          </IconButton>
        )}
        {icon && (
          <>
            {typeof icon === "string" ? (
              <Image src={icon} alt="modal-icon" width={48} height={48} className="size-12 block me-auto" />
            ) : (
              icon
            )}
          </>
        )}

        {title && <h5 className="text-base font-bold text-v2-content-primary text-start mt-5">{title}</h5>}
        {subTitle && <h6 className="text-start text-v2-content-tertiary text-[13px] font-medium mt-1.5">{subTitle}</h6>}
        {body && typeof body === "function" ? body({ drawer }) : body}

        {actions?.length && (
          <div className="mt-4 flex gap-2">
            {actions?.map(({ label, onClick, ...restProps }, index) => (
              <div key={index} className={twMerge(actions?.length > 1 ? "w-1/2" : "w-full")}>
                <Button
                  onClick={onClick}
                  {...restProps}
                  size={restProps?.size || "xl"}
                  className={twMerge("w-full", restProps?.className)}
                >
                  {label}
                </Button>
              </div>
            ))}
          </div>
        )}
      </Box>
    </Modal>
  );
}

export default ModalProvider;
