import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import ProfileContentContainer from "@/components/containers/ProfileStepper/ProfileContentContainer";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import GraphicalRadioButton from "@/components/ui/GraphicalRadioButton/GraphicalRadioButton";
import useCurrency, { IUiCurrency } from "@/utils/hooks/useCurrency";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

const bindDesc = {
  irt: "واحد پول رایج ایران برای نمایش و تسویه‌حساب‌ها",
  irr: "واحد رسمی پول ایران برای کاربردهای رسمی و بانکی",
  usd: "واحد پول بین‌المللی برای تسویه‌حساب‌های ارزی",
  eur: "واحد پول مشترک اتحادیه اروپا برای معاملات جهانی"
} as any;

const bindSymbolImg = {
  irt: "/images/flag/icon-flag-fa.svg",
  irr: "/images/flag/icon-flag-fa.svg",
  usd: "/images/flag/icon-flag-en.svg",
  eur: "/images/flag/european-union.svg"
} as any;

function Currency() {
  const { t } = useTranslation();
  const [selected, selectCurrency, currencyOptions] = useCurrency();
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    // resolver: yupResolver(validationSchema),
    defaultValues: {
      currency: selected as IUiCurrency
    }
  });

  useEffect(() => {
    reset({ currency: selected });
  }, [selected]);

  const onSubmit = (v: { currency: IUiCurrency }) => {
    if (v?.currency?.id) {
      selectCurrency(v?.currency?.id);
    }
  };

  return (
    <>
      <MobileAppBar title={t("profiles.currency")} hasBack />
      <ProfileContentContainer>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <div className="text-v2-content-primary font-medium text-[15px]">{t("profiles.currency")}</div>
              <div className="text-xs font-medium text-v2-content-tertiary">{t("profiles.currencyDesc")}</div>
            </div>

            <div className="xmd:max-w-96 flex flex-col gap-2">
              {currencyOptions?.map(item => {
                return (
                  <Controller
                    key={item?.id}
                    name="currency"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <GraphicalRadioButton
                        color="primary"
                        icon={
                          field?.value && bindSymbolImg[field?.value?.iso?.toLowerCase()] ? (
                            <Image
                              src={bindSymbolImg[field.value?.iso?.toLowerCase()]}
                              alt=""
                              className="size-8"
                              width={32}
                              height={32}
                            />
                          ) : (
                            <div className="size-8 rounded-md bg-v2-surface-thertiary flex items-center justify-center">
                              {item?.symbol}
                            </div>
                          )
                        }
                        value={field?.value}
                        title={item?.name}
                        subtitle={field.value?.iso ? bindDesc?.[field?.value?.iso?.toLowerCase() as any] : ""}
                        checked={field?.value?.id === selected?.id}
                        onChange={v => {
                          field?.onChange(v);
                        }}
                      />
                    )}
                  />
                );
              })}
            </div>
          </div>
          <div>
            <BottomAction
              saveButtonText={t("saveChanges")}
              saveButtonProps={{
                type: "submit"
              }}
              cancelButtonText={t("supplier.profile.cancel")}
              cancelButtonProps={{
                onClick: () => router.back()
              }}
            />

            <div className=" justify-end  hidden xmd:flex">
              <CustomButton type="submit" disabled={false}>
                {t("saveChanges")}
              </CustomButton>
            </div>
          </div>
        </form>
      </ProfileContentContainer>
    </>
  );
}

export default Currency;
