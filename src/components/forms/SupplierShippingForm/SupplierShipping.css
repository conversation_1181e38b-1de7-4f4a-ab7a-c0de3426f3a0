#sx-suppliershipping-18088 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50dvh;
}

.sx-suppliershipping-18120 {
  /* border-block-end: 1px solid #b0b0b0; */
  padding-block-end: 8px;
  margin-block-end: 8px;
  border-radius: 0;
}

#sx-suppliershipping-18264 {
  margin-block: 8px;
}

.supplier-address-accordion-expand-icon {
  color: #a3a3a3;
}

.supplier-shipping-wrapper-title {
  font-size: 12px;
  font-weight: 400;
  color: rgb(var(--color-gray-999));
  margin-bottom: 8px;
}

.supplier-address-accordion-wrapper {
  background-color: transparent;
  box-shadow: unset;
  border: unset;
  border-bottom: 1px solid rgb(var(--color-gray-50));
}

/* .supplier-address-accordion-wrapper-error{
  border-bottom: 1px solid red;

} */

.supplier-address-accordion-wrapper.MuiAccordion-root.Mui-expanded {
  background-color: rgb(var(--color-gray-20));
  border-radius: 8px;
  border-bottom: 1px solid transparent;
}

.supplier-address-accordion-header-trash {
  margin-inline-start: auto;
  margin-inline-end: 16px;
  margin-top: 8px;
}

.supplier-address-accordion-text,
.supplier-address-accordion-detail-text {
  font-size: 14px;
  font-weight: 400;
}

.supplier-address-accordion-stack-wrapper {
  /* gap: 250px; */
  justify-content: space-between;
  flex: 1;
  margin-inline-end: 10px;
}

@media (max-width: 900px) {
  .supplier-address-accordion-stack-wrapper {
    /* gap: 120px; */
  }
}

@media (max-width: 768px) {
  .supplier-address-accordion-detail-text {
    display: none;
  }
}

.supplier-address-accordion-subtext {
  font-size: 12px;
  font-weight: 400;
  color: var(--mui-palette-grey-A500);
}

.supplier-address-accordion-add-button {
  background-color: transparent;
  font-size: 12px;
  font-weight: 700;
  margin-bottom: 12px;
}

.supplier-address-accordion-remove-button {
  background-color: transparent;
  color: #e61b06;
}

.supplier-address-accordion-save-button.custom-button.MuiButton-containedSecondary {
  color: rgb(var(--color-purple-500));
  background-color: rgb(var(--color-purple-50));
  border: 1px solid rgb(var(--color-purple-500));
}

.supplier-address-accordion-add-button-wrapper {
  border-bottom: 1px solid rgb(var(--color-gray-50));
  padding-bottom: 4px;
}

.supplier-address-accordion-exclusive-wrapper {
  margin-bottom: 16px;
}
