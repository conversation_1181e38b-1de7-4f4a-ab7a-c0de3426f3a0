import { TFunction } from "i18next";

export const getShippingTimeItems = ({ t }: { t: TFunction<"translation", undefined> }) => [
  {
    id: "1-2",
    label: t("supplier.profile.shippingTimeItems.1_2_DAYS")
  },
  {
    id: "3-4",
    label: t("supplier.profile.shippingTimeItems.3_4_DAYS")
  },
  {
    id: "5-7",
    label: t("supplier.profile.shippingTimeItems.5_7_DAYS")
  },
  {
    id: "8-10",
    label: t("supplier.profile.shippingTimeItems.8_10_DAYS")
  },
  {
    id: "11-14",
    label: t("supplier.profile.shippingTimeItems.11_14_DAYS")
  },
  {
    id: "15-20",
    label: t("supplier.profile.shippingTimeItems.15_20_DAYS")
  },
  {
    id: "21-30",
    label: t("supplier.profile.shippingTimeItems.21_30_DAYS")
  }
];

export const getShippingRateTypeItems = ({ t }: { t: TFunction<"translation", undefined> }) => [
  {
    id: "PerProduct",
    label: t("supplier.profile.rateTypeItems.PerProduct")
  },
  {
    id: "PerCart",
    label: t("supplier.profile.rateTypeItems.PerBasket")
  }
];

export const getShippingTypeItems = ({ t }: { t: TFunction<"translation", undefined> }) => [
  {
    id: true,
    label: t("supplier.profile.shippingTypeItems.prePaidLong"),
    shortLabel: t("supplier.profile.shippingTypeItems.prePaid")
  },
  {
    id: false,
    label: t("supplier.profile.shippingTypeItems.nextPaidLong"),
    shortLabel: t("supplier.profile.shippingTypeItems.nextPaid")
  }
];

export const getShippingCarrierItems = () => [
  {
    id: "پست",
    label: "پست"
  },
  {
    id: "تیپاکس",
    label: "تیپاکس"
  },
  {
    id: "باربری",
    label: "باربری"
  },
  {
    id: "پیک",
    label: "پیک"
  },
  {
    id: "سایر",
    label: "سایر"
  }
];
