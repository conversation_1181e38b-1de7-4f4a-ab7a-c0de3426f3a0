.supplier-shipping-exclusive-wrapper {
  border-radius: 12px;
  margin-top: 24px;
  /* background-color: rgb(var(--color-gray-20)); */
  /* padding: 24px; */
}

.supplier-shipping-exclusive-icon-box {
  padding: 14px;
  border-radius: 50%;
  background-color: rgb(var(--color-purple-40));
  display: flex;
  align-items: center;
  justify-content: center;
}

.supplier-shipping-exclusive-active-box {
  background-color: #eefbf5;
  font-size: 13px;
  font-weight: 400;
  color: #08874d;
  padding: 5px 16px;
  border-radius: 16px;
}

.supplier-shipping-exclusive-active-text {
  font-size: 16px;
  font-weight: 700;
  color: var(--mui-palette-grey-A700);
}

.supplier-shipping-exclusive-wrapper .custom-textfield .MuiOutlinedInput-root {
  flex-wrap: wrap;
}

.supplier-shipping-exclusive-wrapper .custom-textfield {
  background-color: rgb(var(--color-cards));
}

@media (max-width: 768px) {
  .supplier-shipping-exclusive-active-box {
    display: none;
  }

  .supplier-shipping-exclusive-container .custom-switch-label {
    display: none;
  }

  .supplier-shipping-exclusive-container .MuiFormControlLabel-root {
    margin-left: 0 !important;
  }

  .supplier-shipping-exclusive-wrapper {
    padding: 16px;
  }

  .supplier-shipping-exclusive-active-text {
    font-size: 13px;
    font-weight: 700;
  }

  .supplier-shipping-exclusive-switch-box {
    margin-inline-end: 0;
  }

  .supplier-shipping-exclusive-icon {
    width: 16px;
    height: 16px;
  }
}
