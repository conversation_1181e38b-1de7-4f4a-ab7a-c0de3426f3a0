import "./ShippingExclusive.css";

import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import LocationsSelect from "@/components/ui/CustomCountrySelect/LocationsSelect";
import { Icon } from "@iconify/react";
import { Box, Chip, Stack, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import MultipleLocationsSelect from "@/components/ui/CustomCountrySelect/MultipleLocationSelect";

interface IShippingExclusiveProps {
  onUpdateExclusiveCities: (value: string[]) => void;
  exclusiveCities?: string[];
}

function ShippingExclusive({ onUpdateExclusiveCities, exclusiveCities }: IShippingExclusiveProps) {
  const { t } = useTranslation();
  const [value, setValue] = useState<string[]>(exclusiveCities || []);
  const [active, setActive] = useState(!!value?.length || false);

  useEffect(() => {
    setValue(exclusiveCities || []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(exclusiveCities)]);

  const handleExclusive = (cities: string[]) => {
    if (!active) {
      onUpdateExclusiveCities([]);
      return;
    }

    onUpdateExclusiveCities(cities);
  };

  const MyChip = (props: any) => {
    return (
      <Chip
        style={{
          direction: "ltr"
        }}
        classes={{
          root: "bg-gray-40  [&.MuiChip-root]:!rounded-full text-gray-999 pr-3",
          label: "pr-0"
        }}
        deleteIcon={
          <Icon icon="material-symbols-light:close" width={18} height={18} color="rgb(var(--color-gray-400))" />
        }
        {...props}
      />
    );
  };

  return (
    <Box className="supplier-shipping-exclusive-wrapper">
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        mb={3}
        gap={2}
        className="supplier-shipping-exclusive-container"
      >
        <Stack direction="row" alignItems="center" gap={2}>
          <Box className="supplier-shipping-exclusive-icon-box">
            <Icon
              // color="rgb(var(--color-purple-500))"
              width={24}
              height={24}
              icon="solar:city-outline"
              className={twMerge("supplier-shipping-exclusive-icon", "text-v2-content-primary")}
            />
          </Box>
          <Stack>
            <div className="flex flex-col gap-1">
              <span className="text-gray-999 text-body2-medium">{t("supplier.profile.cityWhitelist")}</span>
              <span className="text-gray-600 text-caption-medium">{t("supplier.profile.cityWhitelistSubtitle")}</span>
              {/* {active && (
                <Box className="supplier-shipping-exclusive-active-box">{t("supplier.profile.activeExclusive")}</Box>
              )} */}
            </div>
          </Stack>
        </Stack>

        <CustomSwitch
          className="supplier-shipping-exclusive-switch-box"
          label={t("supplier.profile.active")}
          checked={active}
          onChange={(e, checked) => {
            setActive(checked);
            if (!checked) {
              onUpdateExclusiveCities([]);
              return;
            }
            onUpdateExclusiveCities(value);
          }}
        />
      </Stack>
      {active && (
        <MultipleLocationsSelect
          multiple
          inputProps={{
            color: "secondary"
          }}
          renderTags={(tagValue, getTagProps) => {
            return tagValue.map((option: any, index: any) => (
              <MyChip {...getTagProps({ index })} key={index} label={option.name} />
            ));
          }}
          hasEndAdornment={false}
          value={value}
          placeholder={`${t("supplier.profile.shippingexclusive")}...`}
          onChange={value => {
            const cityIds = value as string[];
            setValue(cityIds);
            handleExclusive(cityIds);
          }}
        />
      )}
    </Box>
  );
}

export default ShippingExclusive;
