import { TSupplierShippingData } from "@/store/apps/supplier/types";
import { SetHookFormError } from "@/utils/services/utils";
import { CupertinoPane } from "cupertino-pane";
import { FormikConfig, FormikHelpers } from "formik";
import { UseFormSetError } from "react-hook-form";

export type TSupplierShippingFormProps = {
  onBack?: () => void;
  isRemovableInClient?: boolean;
  onRemove?: (index: number) => void;
  values?: TSupplierShippingData[];
  isLoading?: boolean;
  isDisabled?: boolean;
  hasBottomAction?: boolean;
  hasAlert?: boolean;
  handleSubmit: (values: { policies: TSupplierShippingData[] }, setError?: SetHookFormError) => Promise<any>;

  onIsValidChange?: (isValid: boolean) => void;
};
export interface ISupplierShippingModalProps {
  onBack?: () => void;
  isEdit?: boolean;
  hasBottomAction?: boolean;
  drawer?: CupertinoPane | null;
  isDisabled?: boolean;
  initialValue?: TSupplierShippingData;
  initialValues?: TSupplierShippingData[];
  handleSubmit: (
    values: {
      policies: TSupplierShippingData[];
    },
    setError: SetHookFormError
  ) => Promise<any>;
}
