import "./SupplierShipping.css";
import { Box, CircularProgress, useMediaQuery } from "@mui/material";
import React, { memo, useState } from "react";
import { TSupplierShippingFormProps } from "./types";
import useCurrency from "@/utils/hooks/useCurrency";
import ShippingExclusive from "./ShippingExclusive";
import Image from "next/image";
import useModal from "@/utils/hooks/useModal";
import SupplierShippingFormModal from "./SupplierShippingModal";
import { Icon } from "@iconify/react";
import useLocations from "@/utils/hooks/useLocations";
import { useTranslation } from "react-i18next";
import { useGetMetaCarrierQuery } from "@/store/apps/meta";
import { findItemByMinMax } from "@/components/containers/ProfileStepper/utils";
import { getShippingRateTypeItems, getShippingTimeItems, getShippingTypeItems } from "./utils";
import { TSupplierShippingData } from "@/store/apps/supplier/types";
import { twMerge } from "tailwind-merge";
import { Theme } from "@mui/system";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import Button from "@/components/ui/Button";

const SupplierShippingForm = ({
  values: initialValues,
  handleSubmit,
  onRemove,
  isRemovableInClient,
  isLoading,
  isDisabled,
  hasBottomAction,
  onIsValidChange,
  onBack
}: TSupplierShippingFormProps) => {
  const { t } = useTranslation();
  const [currency] = useCurrency();
  const { render: renderPrice } = currency ?? { render: v => v };
  const { getLocation } = useLocations();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const { showModal, hideModal } = useModal();
  const [buttonLoading, setButtonLoading] = useState(false);
  const [editedValue, setEditedValue] = useState<TSupplierShippingData | undefined>(undefined);
  const [isCreate, setISCreate] = useState(false);

  const shippingTimeItems = getShippingTimeItems({ t });
  const shippingRateTypeItems = getShippingRateTypeItems({ t });
  const shippingTypeItems = getShippingTypeItems({ t });

  const { data: carriers } = useGetMetaCarrierQuery();
  const shippingCarrierItems = carriers?.data?.map(item => ({ id: item?.id, label: item?.name }));

  const processingTimeValue = (policy: TSupplierShippingData) =>
    findItemByMinMax(shippingTimeItems, policy?.shippingTime?.min, policy?.shippingTime?.max);
  const shippingRateTypeValue = (policy: TSupplierShippingData) =>
    shippingRateTypeItems?.find(item => item.id === policy?.rateType);
  const shippingTypeValue = (policy: TSupplierShippingData) =>
    shippingTypeItems?.find(item => item.id === policy?.prepaid);
  const shippingCarrierValue = (policy: TSupplierShippingData) =>
    shippingCarrierItems?.find(item => item.id === policy?.shippingCarrierId);
  // const excludeIds = values?.policies?.map(val => val?.shippingTo) as string[];

  const policiesData = initialValues;

  const finalInitialValues = {
    policies: policiesData?.length
      ? policiesData
      : [
          {
            description: undefined,
            notAllowed: false,
            rate: undefined,
            prepaid: undefined,
            rateType: undefined,
            shippingFrom: undefined,
            shippingTime: undefined,
            shippingTo: undefined,
            excluded: false,
            extraItemRate: undefined,
            shippingCarrierId: undefined
          }
        ]
  };

  /* ---------------------------- exclude shipping ---------------------------- */
  const handleUpdateExclusiveCities = (cities: string[], policies: typeof finalInitialValues.policies) => {
    if (!cities?.length) {
      // means user removed all exclusive cities

      return policiesData?.filter(policy => !policy.excluded);
    }

    const updatedExclusive = policiesData
      ?.filter(policy => {
        // if policy is excluded=true but not exist in the cities list, remove it
        // because that means user deleted it from excluded
        if (policy.excluded && !cities.includes(policy.shippingTo!)) {
          return false;
        }

        return true;
      })
      ?.map(policy => {
        const cityAlreadyExist = !!policy?.shippingTo && !!cities.includes(policy?.shippingTo);

        if (cityAlreadyExist) {
          // if city already exist, then set excluded to true
          return {
            shippingTo: policy?.shippingTo,
            excluded: true
          };
        }
        return policy;
      })
      // add new cities that not exist and is new
      ?.concat(
        ...(cities
          ?.filter(city => policies?.findIndex(p => p.shippingTo === city) <= -1)
          ?.map(c => ({
            shippingTo: c,
            excluded: true
          })) || [])
      );

    return updatedExclusive;
  };

  /* ----------------------------- remove shipping ---------------------------- */
  const handleRemove = (policy: TSupplierShippingData, index: number) => {
    const filteredPolicies = initialValues?.filter(item => item?.id !== policy?.id) || [];

    if (isRemovableInClient) {
      onRemove?.(index);
      hideModal();
      return;
    }

    setButtonLoading(true);
    handleSubmit({ policies: filteredPolicies })
      .then(() => hideModal())
      .finally(() => setButtonLoading(false));
  };

  const handleShowRemoveModal = (policy: TSupplierShippingData, index: number) => {
    showModal({
      icon: "/images/svgs/removeShipping.svg",
      title: t("supplier.profile.removeShippingModalTitle"),
      subTitle: t("supplier.profile.removeShippingModalSubtitle"),
      body: (
        <div className="flex flex-col-reverse xmd:flex-row gap-2 mt-5">
          <Button onClick={() => hideModal()} variant="secondaryGray" className="w-full" size="lg">
            {t("supplier.profile.cancel")}
          </Button>
          <Button
            className="bg-v2-content-on-error-2 text-[#fff] hover:bg-error-500 w-full"
            disabled={buttonLoading}
            onClick={() => handleRemove(policy, index)}
            variant="destructivePrimary"
            size="lg"
          >
            {buttonLoading ? <CircularProgress color="info" size={20} /> : t("supplier.profile.removeShippingButton")}
          </Button>
        </div>
      ),
      width: isMobile ? undefined : 428
    });
  };

  if (isLoading) {
    return (
      <Box id="sx-suppliershipping-18088">
        <CircularProgress />
      </Box>
    );
  }

  if (isMobile && editedValue?.id) {
    return (
      <>
        <MobileAppBar
          hasBack
          onBack={() => {
            onBack?.();
            setEditedValue(undefined);
          }}
          backIcon="ic:baseline-close"
          title={t("editShipping")}
        />
        <div className="-mt-14">
          <SupplierShippingFormModal
            isEdit
            hasBottomAction={hasBottomAction}
            isDisabled={isDisabled}
            initialValues={initialValues}
            initialValue={editedValue}
            handleSubmit={(value, setError) =>
              handleSubmit(value, setError).then(() => {
                hideModal();
                setEditedValue(undefined);
              })
            }
            onBack={() => {
              onBack?.();
              setEditedValue(undefined);
            }}
          />
        </div>
      </>
    );
  }

  if (isMobile && isCreate) {
    return (
      <>
        <MobileAppBar
          hasBack
          onBack={() => {
            onBack?.();
            setISCreate(false);
          }}
          backIcon="ic:baseline-close"
          title={t("addShipping")}
        />
        <div className="-mt-14">
          <SupplierShippingFormModal
            isDisabled={isDisabled}
            hasBottomAction={hasBottomAction}
            initialValues={initialValues}
            handleSubmit={(value, setError) =>
              handleSubmit(value, setError).then(() => {
                hideModal();
                setISCreate(false);
              })
            }
            onBack={() => {
              onBack?.();
              setISCreate(false);
            }}
          />
        </div>
      </>
    );
  }

  return (
    <>
      <div className="flex flex-col mb-4 ">
        <div className="flex xmd:items-center items-start justify-between gap-4">
          <span className="text-v2-content-primary xmd:text-body2-medium text-body2-bold ">
            {t("supplier.profile.shippingTitle")}
          </span>
          <Button
            variant="secondaryGray"
            size="md"
            startAdornment={<Icon icon="iconoir:plus" className="xmd:size-5 size-[14px] shrink-0" />}
            className="!px-3"
            type="button"
            onClick={() => {
              if (isMobile) {
                setISCreate(true);
              } else {
                showModal({
                  body: ({ drawer }) => (
                    <SupplierShippingFormModal
                      drawer={drawer}
                      isDisabled={isDisabled}
                      hasBottomAction={hasBottomAction}
                      initialValues={initialValues}
                      handleSubmit={(value, setError) => handleSubmit(value, setError).then(() => hideModal())}
                    />
                  ),
                  modalProps: {
                    containerClassName: "xmd:w-[630px]"
                  }
                });
              }
            }}
          >
            <span className="text-purple-500 text-body3-medium">{t("add")}</span>
          </Button>
        </div>
        <span className="text-v2-content-tertiary text-caption-medium xmd:mt-0 mt-3">
          {t("supplier.profile.shippingSubtitle")}
        </span>
      </div>
      {!initialValues?.filter(item => !item?.excluded)?.length ? (
        <div className="flex flex-col justify-center items-center mx-auto border border-solid border-spacing-[2px] border-transparent border-b-gray-40 pb-20 pt-16">
          <Image src="/images/svgs/no-shipping.svg" width={118} height={114} alt="no-shipping" />
          <div className="flex flex-col gap-1.5 items-center mt-3">
            <span className="text-subtitle-bold text-gray-999">{t("supplier.profile.noShippingTitle")}</span>
            <span className="text-caption-medium text-gray-600">{t("supplier.profile.noShippingSubtitle")}</span>
          </div>
        </div>
      ) : (
        <div className="flex flex-col gap-2">
          {initialValues
            ?.filter(item => !item?.excluded)
            ?.map((policy, index) => {
              if (isMobile) {
                return (
                  <div
                    className="flex flex-col border border-solid border-gray-40 rounded-[4px] px-4 py-3"
                    key={policy?.shippingCarrierId}
                  >
                    <div className="flex items-center justify-between pb-3 border border-solid border-transparent border-b-gray-40">
                      <div className="flex items-center gap-2">
                        <div className="size-6 flex items-center justify-center rounded-[4px] bg-gray-40">
                          <Icon icon="solar:map-point-wave-outline" className="size-4 text-gray-500" />
                        </div>

                        <div className="flex flex-col">
                          <span className="text-gray-999 text-body2-medium">
                            {policy?.shippingTo
                              ? getLocation(policy?.shippingTo)?.name
                              : t("supplier.profile.allCities")}
                          </span>
                          {processingTimeValue(policy)?.label && (
                            <span className="text-gray-500 text-caption-regular">
                              {processingTimeValue(policy)?.label} {t("working")}
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-3  ">
                        <div className="cursor-pointer" onClick={() => setEditedValue(policy)}>
                          <Icon icon="solar:pen-new-square-outline" className="size-4" />
                        </div>
                        {initialValues?.filter(item => !item?.excluded)?.length > 1 && (
                          <div
                            className="cursor-pointer"
                            onClick={() => {
                              // if (isDisabled) return;
                              handleShowRemoveModal(policy, index);
                            }}
                          >
                            <Icon icon="solar:trash-bin-trash-outline" className="size-4" />
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between gap-3 pt-3">
                      <div className="flex flex-col flex-1">
                        <span className="text-caption-regular text-gray-600">{t("supplier.profile.shippingType")}</span>
                        <div className="flex items-center gap-1 text-body2-medium text-gray-999">
                          <span className="text-body4-medium">{shippingCarrierValue(policy)?.label}</span>
                          <div
                            className={twMerge(
                              "px-2 py-0.5 rounded-full !text-caption-regular",
                              policy?.prepaid ? "bg-warning-50 text-warning-800" : "bg-cyan-50 text-cyan-500"
                            )}
                          >
                            {shippingTypeValue(policy)?.shortLabel}
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col flex-1 ">
                        <span className="text-caption-regular text-gray-600">{t("supplier.profile.shippingCost")}</span>
                        <div className="flex items-center gap-1">
                          {!!policy?.rate && +policy.rate > 0 ? (
                            <span className="text-gray-999 text-body2-medium">
                              {/* {policy?.rate} {symbol} */}
                              {renderPrice(policy?.rate)}
                            </span>
                          ) : (
                            t(policy?.prepaid ? "free" : "payByDelivery")
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              }

              return (
                <div
                  className="flex items-center justify-between border border-solid border-gray-40 rounded-[4px] px-4 py-3"
                  key={policy?.shippingCarrierId}
                >
                  <div className="flex items-center gap-4 flex-1">
                    <div className="size-6 flex items-center justify-center rounded-[4px] bg-gray-40">
                      <Icon icon="solar:map-point-wave-outline" className="size-4 text-gray-500" />
                    </div>

                    <div className="flex flex-col gap-1.5">
                      <span className="text-gray-999 text-body2-medium">
                        {policy?.shippingTo ? getLocation(policy?.shippingTo)?.name : t("supplier.profile.allCities")}
                      </span>
                      {processingTimeValue(policy)?.label && (
                        <span className="text-gray-500 text-caption-regular">
                          {processingTimeValue(policy)?.label} {t("working")}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col gap-1.5 flex-1">
                    <span className="text-caption-regular text-gray-600">{t("supplier.profile.shippingType")}</span>
                    <div className="flex items-center gap-1 text-body2-medium text-gray-999">
                      <span className="text-body2-medium text-gray-999">{shippingCarrierValue(policy)?.label}</span>
                      <div
                        className={twMerge(
                          "px-2 py-0.5 rounded-full !text-caption-regular",
                          policy?.prepaid ? "bg-warning-50 text-warning-800" : "bg-cyan-50 text-cyan-500"
                        )}
                      >
                        {shippingTypeValue(policy)?.shortLabel}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col gap-1.5 flex-1">
                    <span className="text-caption-regular text-gray-600">{t("supplier.profile.shippingCost")}</span>
                    <div className="flex items-center gap-1">
                      {!!policy?.rate && +policy.rate > 0 ? (
                        <span className="text-gray-999 text-body2-medium">
                          {/* {policy?.rate} {symbol} */}
                          {renderPrice(policy?.rate)}
                        </span>
                      ) : (
                        t(policy?.prepaid ? "free" : "payByDelivery")
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-4 ">
                    <div
                      className="cursor-pointer"
                      onClick={() =>
                        showModal({
                          body: ({ drawer }) => (
                            <SupplierShippingFormModal
                              isEdit
                              hasBottomAction={hasBottomAction}
                              drawer={drawer}
                              isDisabled={isDisabled}
                              initialValues={initialValues}
                              initialValue={policy}
                              handleSubmit={(value, setError) => handleSubmit(value, setError).then(() => hideModal())}
                            />
                          ),
                          modalProps: {
                            containerClassName: "xmd:w-[630px]"
                          }
                        })
                      }
                    >
                      <Icon icon="solar:pen-new-square-outline" className="size-4" />
                    </div>
                    {initialValues?.filter(item => !item?.excluded)?.length > 1 && (
                      <div
                        className="cursor-pointer"
                        onClick={() => {
                          handleShowRemoveModal(policy, index);
                          // if (isDisabled) return;
                        }}
                      >
                        <Icon icon="solar:trash-bin-trash-outline" className="size-4" />
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
        </div>
      )}
      <div className="supplier-address-accordion-exclusive-wrapper">
        <ShippingExclusive
          exclusiveCities={initialValues?.filter(item => item.excluded)?.map(item => item?.shippingTo) as string[]}
          onUpdateExclusiveCities={cities => {
            const updatedExclusive = handleUpdateExclusiveCities(cities, initialValues || []) || [];

            // setFieldValue("policies", updatedExclusive);

            handleSubmit({ policies: updatedExclusive });
            // setTimeout(() => {
            // }, 0);
          }}
        />
      </div>
    </>
  );
};

export default memo(SupplierShippingForm);
