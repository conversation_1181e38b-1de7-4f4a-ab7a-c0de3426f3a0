import PasswordChangeSuccess from "@/app/auth/authForms/PasswordChangeSuccess";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import InputCountdown from "@/components/containers/OtpCountDown/InputCountDown";
import OTPCountdown from "@/components/containers/OtpCountDown/OtpCountDown";
import ProfileContentContainer from "@/components/containers/ProfileStepper/ProfileContentContainer";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import PasswordInput from "@/components/ui/inputs/PasswordInput";
import { useChangePasswordMutation, useGetMeQuery, useRequestOtpMutation } from "@/store/apps/auth";
import { TMeResponse } from "@/store/apps/auth/types";
import { useSelector } from "@/store/hooks";
import useModal from "@/utils/hooks/useModal";
import { identifyInput } from "@/utils/identifyInput";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { yupResolver } from "@hookform/resolvers/yup";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { shallowEqual } from "react-redux";
import * as yup from "yup";

function ChangePassword() {
  const { t } = useTranslation();
  const [otpTime, setOtpTime] = useState(0);
  const router = useRouter();
  const [requestOtp, { isLoading: isRequestOtpLoading }] = useRequestOtpMutation();

  const me = useSelector(state => state?.Auth?.queries[`getMe(undefined)`]?.data, shallowEqual) as TMeResponse;
  const loading = useSelector(state => state?.Auth?.queries[`getMe(undefined)`]?.status, shallowEqual) === "pending";
  const hasMeData = me?.data?.type === undefined ? false : true;

  const { data, isLoading: isMeLoading } = useGetMeQuery(undefined, { skip: hasMeData && loading });

  const isLoadingData = isMeLoading || loading;

  const userData = me?.data || data?.data || {};
  const username = userData?.phoneNumber || userData?.email;
  const userType = userData.type;

  const { showModal } = useModal();
  const [isRequested, setIsRequested] = useState(false);
  const [changePassword, { isLoading }] = useChangePasswordMutation();

  const validationSchema = yup.object({
    otp: yup
      .string()
      .min(5, t("register.validation.minCharcter", { min: 5 }))
      .required(t("supplier.profile.validations.requiredField")),
    password: yup
      .string()
      .test("is-not-farsi", t("register.validation.farsiNotAllowed"), value => {
        if (!value) return true;
        const farsiRegex = /[\u0600-\u06FF\u0750-\u077F]/;
        return !farsiRegex.test(value);
      })
      .min(8, t("register.validation.minCharcter", { min: 8 }))
      .required(t("emailLogin.validation.requiredPassword")),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password")], t("register.validation.passwordsMustMatch"))
      .required(t("emailLogin.validation.requiredPassword"))
  });

  const {
    register,
    control,
    handleSubmit,
    setError,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      password: "",
      confirmPassword: ""
    }
  });

  const onSubmit = async (values: yup.InferType<typeof validationSchema>) => {
    const usernameType = identifyInput(username);
    const usernameKey = usernameType === "phone" ? { phone_number: username } : { email: username };

    const body = {
      ...usernameKey,
      userType,
      otp: values?.otp,
      new_password: values.password
    };

    try {
      const res = await changePassword({ body });

      const error = (res as any)?.error?.data;
      const status = (res as any)?.error?.status;

      if ((res as any)?.error?.status === 401) {
        const bodyError = {
          ...error,
          error_detail: { otp: ["otp401"] }
        };

        clientDefaultErrorHandler({ error: (res as any)?.error, bodyError, setHookFormFieldError: setError as any });
      } else if ((res as any)?.error) {
        clientDefaultErrorHandler({ error: (res as any)?.error });
        return;
      }

      if ("data" in res && res.data) {
        showModal({
          icon: "/images/svgs/password-successfull.svg",
          body: <PasswordChangeSuccess />
        });
      }
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const handleRequestOtp = async (username: string) => {
    const usernameType = identifyInput(username);
    const usernameKey = usernameType === "phone" ? { phone_number: username } : { email: username };
    const requestOtpBody = { ...usernameKey, userType };
    const requestOtpRes = await requestOtp({ body: requestOtpBody });

    const status = (requestOtpRes as any)?.error?.status;

    if (status === 404) {
      //   setError?.(t("otpLogin.validation.notfound"));
    } else if ((requestOtpRes as any)?.error) {
      clientDefaultErrorHandler({ error: (requestOtpRes as any)?.error });
    }

    if ("data" in requestOtpRes && requestOtpRes.data) {
      setIsRequested(true);
      setOtpTime(requestOtpRes.data.data.expires_in);
    }
  };

  // useEffect(() => {
  //   const requestOtp = async () => {
  //     await handleRequestOtp(username);
  //   };

  //   requestOtp();
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);

  if (isLoadingData) {
    return (
      <div className="flex items-center justify-center min-h-[50vh] h-full">
        <CircularProgress />
      </div>
    );
  }

  return (
    <>
      <MobileAppBar title={t("password&security")} hasBack />
      <ProfileContentContainer>
        <form onSubmit={handleSubmit(onSubmit)} autoComplete="off">
          <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4 xmd:p-6 ">
            <div className="xmd:w-1/2 w-full">
              <h2 className="text-body2-medium text-v2-content-primary">{t("password&security")}</h2>
              <p className="text-caption-medium text-v2-content-tertiary mt-2">{t("changePasswordForm.subtitle")}</p>

              <div className="bg-v2-surface-info rounded flex items-center p-3 gap-2.5 mb-2 mt-4">
                <Icon icon="solar:info-circle-outline" className="shrink-0 size-4" />
                <span className="text-caption-medium text-v2-content-on-info">{t("changePasswordForm.alert")}</span>
              </div>

              <Controller
                name="otp"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <InputCountdown
                    {...field}
                    maxLength={5}
                    isRequested={isRequested}
                    initialTime={otpTime}
                    disabled={!isRequested}
                    onRequestOtp={() => handleRequestOtp(username)}
                    label={t("changePasswordForm.confirmCode")}
                    placeholder={t("changePasswordForm.confirmCode")}
                    // startAdornment={
                    //   <span className="text-v2-content-primary text-body3-medium whitespace-nowrap ml-1">
                    //     {t("changePasswordForm.confirmCode")}
                    //   </span>
                    // }
                    onRetry={() => {
                      handleRequestOtp(username);
                      setOtpTime(0);
                    }}
                    isLoading={isRequestOtpLoading}
                    hint={t("passwordHint", { number: username })}
                    error={Boolean(error)}
                    helperText={error?.message}
                  />
                )}
              />

              <div className="mt-4">
                <Controller
                  name="password"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <PasswordInput
                      {...field}
                      label={t("changePasswordForm.password")}
                      autoComplete="changePasswordForm"
                      aria-autocomplete="none"
                      placeholder={t("changePasswordForm.passwordPlaceholder")}
                      error={Boolean(error)}
                      helperText={error?.message}
                    />
                  )}
                />
              </div>
              <div className="mt-4">
                <Controller
                  name="confirmPassword"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <PasswordInput
                      {...field}
                      label={t("changePasswordForm.confirmPassword")}
                      autoComplete="confirmPassword"
                      aria-autocomplete="none"
                      placeholder={t("changePasswordForm.confirmPassword")}
                      error={Boolean(error)}
                      helperText={error?.message}
                    />
                  )}
                />
              </div>
            </div>
          </div>
          <div>
            <BottomAction
              saveButtonText={t("saveChanges")}
              saveButtonProps={{
                type: "submit"
              }}
              cancelButtonText={t("supplier.profile.cancel")}
              cancelButtonProps={{
                onClick: () => router.back()
              }}
            />

            <div className=" justify-end  hidden xmd:flex">
              <CustomButton type="submit" disabled={isLoading}>
                {isLoading ? <CircularProgress color="info" size={26} /> : t("saveChanges")}
              </CustomButton>
            </div>
          </div>
        </form>
      </ProfileContentContainer>
    </>
  );
}

export default ChangePassword;
