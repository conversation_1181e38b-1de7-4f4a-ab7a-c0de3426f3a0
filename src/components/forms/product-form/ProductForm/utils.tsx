import { Authenticity, Condition } from "@/store/apps/product/types";
import i18next from "i18next";

export type ProductConditionItem = {
  id: Condition;
  title: string;
};

export type ProductAuthenticityItem = {
  id: Authenticity;
  title: string;
};

export const productConditions: ProductConditionItem[] = [
  { id: "New", title: i18next.t("product.productConditions.new") },
  { id: "Used", title: i18next.t("product.productConditions.used") },
  { id: "Refurbished", title: i18next.t("product.productConditions.refurbished") }
];

export const productAuthenticity: ProductAuthenticityItem[] = [
  { id: "Original", title: i18next.t("product.productAuthenticity.original") },
  { id: "HighCopy", title: i18next.t("product.productAuthenticity.highCopy") },
  { id: "Fake", title: i18next.t("product.productAuthenticity.nonOriginal") }
];
