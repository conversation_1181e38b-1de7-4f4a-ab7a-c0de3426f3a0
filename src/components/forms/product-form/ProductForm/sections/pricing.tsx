import { Authenticity, Condition, TProductForm } from "@/store/apps/product/types";
import { Typography } from "@mui/material";
import React, { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import GraphicalRadioButton from "@/components/ui/GraphicalRadioButton/GraphicalRadioButton";
import { Icon } from "@iconify/react";
import Image from "next/image";
import OptionVariantsFrom from "../../OptionVariantsFrom/OptionVariantsFrom";
import NumberInput from "@/components/ui/inputs/NumberInput";
import { isNaN } from "lodash";
import Input from "@/components/ui/inputs/Input";
import PriceInput from "@/components/ui/inputs/PriceInput";
import { THasVarintsStaus } from "../types";
import { Controller, useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";
import { productAuthenticity, productConditions } from "../utils";
import { calculateSellingPriceFormula } from "../../OptionVariantsFrom/utils";

function Pricing({ setHasVariants, hasVariants, isEdit }: any) {
  const { t } = useTranslation();
  const {
    control,
    setValue,
    getValues,
    clearErrors,
    watch,
    formState: { errors, defaultValues }
  } = useFormContext<TProductForm>();

  // Use state for critical UI values instead of watching all fields
  const [pricingProChecked, setPricingProChecked] = useState(() => !!getValues("map") || !!getValues("sku") || false);

  // Calculate selling price based on current form values
  const calculateSellingPrice = () => {
    const retailPrice = getValues("retail_price");
    const commission = getValues("commission");

    return calculateSellingPriceFormula({ commission, retailPrice });
  };

  const [yourSellingPrice, setYourSellingPrice] = useState(() => calculateSellingPrice());

  const updateSellingPrice = () => {
    setYourSellingPrice(calculateSellingPrice());
  };

  return (
    <div className="flex flex-col gap-4">
      {/* ------------------------ mobile has variant toggle ----------------------- */}
      <div className="md:hidden flex items-center justify-between gap-4">
        <div className="text-gray-999 text-sm font-normal">{t("productForm.hasVariant")}</div>
        <Controller
          name="variants"
          control={control}
          render={({ field: { onChange } }) => (
            <CustomSwitch
              labelClassName="me-0"
              label={hasVariants === "yes" ? t("productForm.hasVariantActive") : t("productForm.hasVariantNotActive ")}
              checked={hasVariants === "yes"}
              onChange={(e, checked) => {
                if (!defaultValues?.variants?.length) {
                  onChange([]);
                }
                setHasVariants((checked ? "yes" : "no") as THasVarintsStaus);
              }}
            />
          )}
        />
      </div>

      {/* ----------------------- desktop has variant buttons ---------------------- */}
      <div className="hidden md:flex gap-4">
        <GraphicalRadioButton
          color="primary"
          icon={
            <div className="w-[34px] h-[34px] rounded-md bg-v2-surface-thertiary flex items-center justify-center">
              <Image src="/images/svgs/box.svg" alt="" className="size-[22px]" width={22} height={22} />
            </div>
          }
          value="no"
          title={t("product.hasntVariantBtn")}
          subtitle={t("product.hasntVariantBtnDesc")}
          checked={hasVariants === "no"}
          onChange={v => {
            if (!defaultValues?.variants?.length) {
              setValue("variants", []);
            }
            setHasVariants(v as THasVarintsStaus);
          }}
        />
        <GraphicalRadioButton
          color="primary"
          icon={
            <div className="w-[34px] h-[34px] rounded-md bg-v2-surface-thertiary flex items-center justify-center">
              <Icon icon="solar:box-bold" className="size-[22px] text-gray-999" />
            </div>
          }
          value="yes"
          title={t("product.hasVariantBtn")}
          subtitle={t("product.hasVariantBtnDesc")}
          checked={hasVariants === "yes"}
          onChange={v => {
            if (!defaultValues?.variants?.length) {
              setValue("variants", []);
            }
            setHasVariants(v as THasVarintsStaus);
          }}
        />
      </div>
      {hasVariants === null && <Typography variant="caption">{t("product.validations.required")}</Typography>}

      {hasVariants === "no" && (
        <div className="flex flex-col gap-4 mt-1">
          <p className="text-v2-content-tertiary text-body4-medium pt-6 border-t border-t-v2-border-secondary">
            {t("product.productConditionsTitle")}
          </p>
          <div className="flex 2xs:flex-nowrap flex-wrap items-center xmd:gap-4 gap-2 ">
            {productConditions?.map(item => (
              <div
                className={twMerge(
                  "border border-solid border-spacing-[2px] flex justify-between gap-3 h-full rounded-lg xmd:px-4 px-2 xmd:py-3 py-2 cursor-pointer w-full",
                  item?.id === watch("condition") ? "border-purple-500" : "border-gray-50"
                )}
                key={item?.id}
                onClick={() => {
                  setValue("condition", item?.id);
                  clearErrors(`condition`);
                }}
              >
                <span className="text-body4-medium text-gray-999 whitespace-nowrap">{item?.title}</span>

                {item?.id === watch("condition") ? (
                  <Image src="/images/svgs/user-checked.svg" alt="realUser" width={24} height={24} />
                ) : (
                  <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={24} height={24} />
                )}
              </div>
            ))}
          </div>
          {errors?.condition?.message && <InputHelper error>{errors?.condition?.message}</InputHelper>}

          <span className="text-v2-content-tertiary text-body4-medium">{t("product.productAuthenticityTitle")}</span>
          <div className="flex 2xs:flex-nowrap flex-wrap items-center xmd:gap-4 gap-2 ">
            {productAuthenticity?.map(item => (
              <div
                className={twMerge(
                  "border border-solid border-spacing-[2px] flex justify-between gap-3 h-full rounded-lg xmd:px-4 px-2 xmd:py-3 py-2 cursor-pointer w-full",
                  item?.id === watch("authenticity") ? "border-purple-500" : "border-gray-50"
                )}
                key={item?.id}
                onClick={() => {
                  setValue("authenticity", item?.id);
                  clearErrors(`authenticity`);
                }}
              >
                <span className="text-body4-medium text-gray-999 whitespace-nowrap">{item?.title}</span>

                {item?.id === watch("authenticity") ? (
                  <Image src="/images/svgs/user-checked.svg" alt="realUser" width={24} height={24} />
                ) : (
                  <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={24} height={24} />
                )}
              </div>
            ))}
          </div>
          {errors?.authenticity?.message && <InputHelper error>{errors?.authenticity?.message}</InputHelper>}

          <div className="flex items-center justify-between py-6 border-y border-y-v2-border-secondary my-5">
            <div className="flex flex-col">
              <span className="text-v2-content-primary text-body3-medium !font-semibold">
                {t("product.preOrderTitle")}
              </span>
              <span className="text-v2-content-tertiary text-caption-medium">{t("product.preOrderSubTitle")}</span>
            </div>

            <div className="flex items-center gap-2 ">
              <span className="text-v2-content-tertiary text-caption-medium">{t("supplier.profile.active")}</span>
              <Controller
                name="backorder"
                control={control}
                render={({ field }) => (
                  <CustomSwitch
                    checked={field.value}
                    onChange={(e, checked) => field.onChange(checked)}
                    labelClassName="!ml-0 !mr-0"
                    textClassName="hidden"
                    size="small"
                  />
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ">
            <div>
              <Controller
                name="inventory"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <NumberInput
                    {...field}
                    onChange={e => {
                      if (e?.target?.value === undefined || isNaN(e?.target?.value)) return;

                      const value = Number(e?.target?.value);
                      field?.onChange(value);
                      // Update selling price after inventory change
                      setTimeout(updateSellingPrice, 0);
                    }}
                    autoComplete="off"
                    label={t("product.inventory")}
                    placeholder={t("product.inventory")}
                    error={Boolean(error?.message)}
                    helperText={error?.message || ""}
                    endAdornment={<div className="text-v2-content-tertiary text-body4-medium pr-0.5">{t("count")}</div>}
                  />
                )}
              />
            </div>

            <div>
              <Controller
                name="retail_price"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <PriceInput
                    {...field}
                    id={field?.name}
                    autoComplete="off"
                    label={t("productForm.retailPrice")}
                    placeholder={t("productForm.retailPricePlaceholder")}
                    error={Boolean(error?.message)}
                    helperText={error?.message || ""}
                    onChange={e => {
                      field.onChange(e);
                      // Update selling price after retail price change
                      setTimeout(updateSellingPrice, 0);
                    }}
                  />
                )}
              />

              <div className="text-xs leading-4 text-v2-content-tertiary mt-1">
                {t("productForm.retailPriceTooltipDesc")}
              </div>
            </div>

            <div>
              <Controller
                name="commission"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <PriceInput
                    {...field}
                    id={field?.name}
                    autoComplete="off"
                    label={t("productForm.salesCommissionPercentage")}
                    placeholder={t("productForm.commisionPlaceholder")}
                    error={Boolean(error?.message)}
                    helperText={error?.message || ""}
                    endAdornment={
                      <div className="text-v2-content-tertiary text-body4-medium pr-0.5">{t("percent")}</div>
                    }
                    onChange={e => {
                      field.onChange(e);
                      // Update selling price after commission change
                      setTimeout(updateSellingPrice, 0);
                    }}
                  />
                )}
              />

              <div className="text-xs leading-4 text-v2-content-tertiary mt-1">{t("product.commissionHint")}</div>
            </div>

            <div>
              <PriceInput
                autoComplete="off"
                readOnly
                requiredStar={false}
                inputParentClassName="bg-v2-surface-secondary border-v2-surface-secondary"
                value={yourSellingPrice}
                label={t("productForm.yourSellingPrice")}
                placeholder="-"
              />
            </div>

            <div className="flex items-center gap-2 mt-3 mb-2 ">
              <CustomSwitch
                checked={pricingProChecked}
                onChange={(e, checked) => {
                  setPricingProChecked(checked);
                  if (!checked) {
                    setValue("map", undefined);
                    setValue("sku", undefined);
                  }
                }}
                labelClassName="!ml-0 !mr-0"
                textClassName="hidden"
              />
              <span className="text-body4-medium">{t("productForm.pricingProStatus")}</span>
            </div>
            <div />

            {pricingProChecked && (
              <>
                <div>
                  <Controller
                    name="map"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <PriceInput
                        {...field}
                        id={field?.name}
                        autoComplete="off"
                        requiredStar={false}
                        label={t("productForm.minimumSalesCommissionPercentage")}
                        placeholder={t("productForm.minCommisionPlaceholder")}
                        error={Boolean(error?.message)}
                        endAdornment={
                          <div className="text-v2-content-tertiary text-body4-medium pr-0.5">{t("percent")}</div>
                        }
                        helperText={error?.message || ""}
                      />
                    )}
                  />

                  <div className="text-xs leading-4 text-v2-content-tertiary mt-1">{t("product.minComissionHint")}</div>
                </div>

                <div>
                  <Controller
                    name="sku"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <Input
                        {...field}
                        id={field?.name}
                        optional
                        autoComplete="off"
                        label={t("productForm.skuLabel")}
                        placeholder={t("productForm.skuPlaceholder")}
                        className="!text-right"
                        dir="ltr"
                        requiredStar={false}
                        required={false}
                        error={Boolean(error?.message)}
                        helperText={error?.message || ""}
                      />
                    )}
                  />
                  <div className="text-xs leading-4 text-v2-content-tertiary mt-1">{t("product.skuHint")}</div>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {hasVariants === "yes" && (
        <>
          <OptionVariantsFrom isEdit={isEdit} canAddOrEditOptions />
          {errors.variants && (
            <InputHelper className="mb-4">
              {typeof errors.variants?.message === "string"
                ? errors.variants?.message
                : t("product.validations.variantsNotCompleted")}
            </InputHelper>
          )}
        </>
      )}
    </div>
  );
}

export default Pricing;
