import { Grid, <PERSON>Item, ListItemButton, ListItemText } from "@mui/material";

import "./ProductFormSteps.css";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import i18n from "@/utils/i18n";

const steps = [
  { title: i18n.t("product.steps.general"), id: "general" },
  { title: i18n.t("product.steps.categories"), id: "categories" },
  { title: i18n.t("product.steps.gallery"), id: "gallery" },
  { title: i18n.t("product.steps.pricing"), id: "pricing" }
];

const ProductFormSteps = () => {
  const [activeStep, setActiveStep] = useState("general");

  const { t } = useTranslation();

  useEffect(() => {
    let options = {
      rootMargin: "0px",
      threshold: 1.0
    };

    let observer = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting) setActiveStep(entries[0].target.id);
    }, options);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const observers = steps.map(step => {
      const el = document.getElementById(step.id);

      if (el) observer.observe(el);

      return el;
    });
  }, []);

  return (
    <div>
      <Grid container direction="column" className="product-from-steps" width="200px!important">
        {steps.map(step => (
          <ListItem key={step.id} className={"int-link " + (activeStep === step.id ? "active" : "")}>
            <ListItemButton
              href={"#" + step.id}
              onClick={() => {
                setActiveStep(step.id);
              }}
            >
              <ListItemText>{t("productForm.steps." + step.id)}</ListItemText>
            </ListItemButton>
          </ListItem>
        ))}
      </Grid>
    </div>
  );
};

export default ProductFormSteps;
