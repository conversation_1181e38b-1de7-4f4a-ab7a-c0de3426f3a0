import React, { useEffect, useState } from "react";
import { Theme } from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import { Menu } from "@mui/material";
import { Typography } from "@mui/material";
import { Icon } from "@iconify/react";
import { Add } from "@mui/icons-material";
import { routes } from "@/constants/routes";
import { useTranslation } from "react-i18next";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import Link from "next/link";
import useModal from "@/utils/hooks/useModal";
import Button from "@/components/ui/Button";
import ExcelUploadModal from "./ExcelUploadModal";

const AddProduct = () => {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { showModal, hideModal } = useModal();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const isTablet = useMediaQuery((theme: Theme) => theme.breakpoints.down(1024));

  const [anchorEl, setAnchorEl] = useState(null);
  const handleClick2 = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const openUploadExcel = () => {
    showModal({
      body: () => <ExcelUploadModal />,
      modalProps: {
        showCloseIcon: false
      },
      width: isTablet ? undefined : 600
    });
    handleClose();
  };

  return (
    <>
      <Button
        variant="primary"
        size="xl"
        aria-label="show 11 new notifications"
        color="inherit"
        aria-haspopup="true"
        // className="sx-profile-7298"
        id="add-product--menu"
        onClick={handleClick2}
      >
        <Add />
        {isMobile ? t("add") : t("product.createProduct")}
      </Button>

      <Menu id="add-product--menu" anchorEl={anchorEl} keepMounted open={Boolean(anchorEl)} onClose={handleClose}>
        <div className="flex flex-col py-2.5">
          <Link prefetch href={makePath(routes.createProduct)} onClick={handleClose}>
            <div className="flex items-center gap-2 px-4 py-3">
              <Icon icon="solar:inbox-linear" className="size-[18px]" />
              <span className="text-body3-medium text-v2-content-primary">{t("product.createNewProduct")}</span>
            </div>
          </Link>
          <div className="px-4 py-3 flex items-center gap-2 cursor-pointer" onClick={openUploadExcel}>
            <Icon icon="solar:file-download-linear" className="size-[18px]" />
            <span className="text-body3-medium text-v2-content-primary">{t("product.importExcelFile")}</span>
          </div>
        </div>
      </Menu>
    </>
  );
};

export default AddProduct;
