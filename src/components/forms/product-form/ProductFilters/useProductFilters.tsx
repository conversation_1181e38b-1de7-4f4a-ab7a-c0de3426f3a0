import { parseAsArrayOf, parseAsInteger, parseAsString, useQueryStates } from "nuqs";

export const useProductFilters = () => {
  const parsers = {
    title: parseAsString,
    page: parseAsInteger.withDefault(1),
    pageSize: parseAsInteger.withDefault(10),
    tags: parseAsArrayOf(parseAsString),
    sku: parseAsString,
    status: parseAsString,
    created_from: parseAsString,
    created_to: parseAsString,
    updated_from: parseAsString,
    updated_to: parseAsString
  };

  const [filters, setFilters] = useQueryStates(parsers, { history: "push", shallow: false });
  const { page, pageSize, ...restFilters } = filters || {};

  return {
    filters: restFilters,
    setFilters,
    pagination: { page, pageSize }
  };
};
