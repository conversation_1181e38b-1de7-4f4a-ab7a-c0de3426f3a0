import Input from "@/components/ui/inputs/Input";
import { routes } from "@/constants/routes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { Add } from "@mui/icons-material";
import { Theme, useMediaQuery } from "@mui/system";
import Link from "next/link";
import React, { ReactNode } from "react";
import { useTranslation } from "react-i18next";
import AddSupplierStore from "../../../containers/SupplierStoreSetting/AddSupplierStore";
import Button from "@/components/ui/Button";
import TagsFilter from "@/components/containers/Filters/TagsFilter";
import i18n from "@/utils/i18n";
import InputFilter from "@/components/containers/Filters/InputFilter";
import DropdownFilter from "@/components/containers/Filters/DropdownFilter";
import RangeDatepickerFilter from "@/components/containers/Filters/RangeDatepickerFilter";
import { useProductFilters } from "./useProductFilters";
import { handleSetFilter, omitEmptyValues } from "@/utils/helpers";
import AddProduct from "./AddProduct";

const ProductsFilter = ({
  showAddButton = true,
  filtersEndAdornment
}: {
  showAddButton?: boolean;
  filtersEndAdornment?: ReactNode;
}) => {
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const { filters, setFilters } = useProductFilters();
  const hasFilters = !!Object.values(omitEmptyValues(filters))?.filter(Boolean)?.length;

  const handleResetAll = () => {
    const keys = !!filters ? Object.keys(filters) : [];

    keys?.forEach(item => setFilters({ [item]: null }, { history: "push" }));
  };

  const onReset = (key: string | string[]) => {
    if (Array.isArray(key) && key?.length) {
      key?.forEach(item => {
        setFilters({ [item]: null }, { history: "push" });
      });
    } else setFilters({ [key as any]: null }, { history: "push" });
  };

  return (
    <div className="flex flex-col gap-4">
      {!isMobile && (
        <div className="flex items-center gap-4">
          <Input
            startAdornment={
              <Icon icon="solar:magnifer-linear" width="1.1rem" color="#ADADAD" height="1.1rem" className="ml-1.5" />
            }
            rootClassName="shrink-0 flex-1"
            inputParentClassName="bg-v2-surface-primary"
            placeholder={`${t("chats.searchQuery")} ...`}
            value={filters?.title || ""}
            onChange={e => {
              // setFilters({ title: e.target.value }, { history: "push" });
              handleSetFilter({ key: "title", value: e.target.value, setFilters });
            }}
            requiredStar={false}
          />

          {showAddButton && (
            <AddProduct />
            // <Link prefetch href={makePath(routes.createProduct)}>
            //   <Button variant="primary" size="xl">
            //     <Add />
            //     {isMobile ? t("add") : t("product.createProduct")}
            //   </Button>
            // </Link>
          )}
        </div>
      )}

      <div className="flex justify-between items-start">
        <div className="flex items-center gap-2 flex-wrap">
          <TagsFilter
            title={i18n.t("product.filterItems.tags")}
            filterKey="tags"
            initialValue={filters?.tags}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />
          <InputFilter
            title={i18n.t("product.filterItems.sku")}
            filterKey="sku"
            initialValue={filters?.sku}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />

          <DropdownFilter
            title={i18n.t("product.filterItems.status")}
            filterKey="status"
            initialValue={filters?.status}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
            options={[
              { id: "Active", label: t(`product.statusItems.Active`) },
              { id: "Inactive", label: t(`product.statusItems.Inactive`) },
              { id: "InReview", label: t(`product.statusItems.InReview`) },
              { id: "Rejected", label: t(`product.statusItems.Rejected`) }
            ]}
          />

          <RangeDatepickerFilter
            title={i18n.t("product.filterItems.createdDate")}
            filterKey={["created_from", "created_to"]}
            initialValue={[filters?.created_from || "", filters?.created_to || ""]}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />

          <RangeDatepickerFilter
            title={i18n.t("product.filterItems.updatedDate")}
            filterKey={["updated_from", "updated_to"]}
            initialValue={[filters?.updated_from || "", filters?.updated_to || ""]}
            setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
            onReset={onReset}
          />

          {hasFilters && (
            <>
              <div className="w-px h-4 bg-v2-border-primary" />

              <div className="text-v2-content-on-action-2 text-xs cursor-pointer" onClick={handleResetAll}>
                {t("removeFilters")}
              </div>
            </>
          )}
        </div>

        {filtersEndAdornment}
      </div>
    </div>
  );
};

export default ProductsFilter;
