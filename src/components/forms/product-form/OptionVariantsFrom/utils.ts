import { TProductVariantBody } from "@/store/apps/product/types";
import { VARIANT_STATUS_ACTIVE } from "@/constants/product";
import { isEqual } from "lodash";
import { TOption } from "../DefineVariantsTable/VariantsTable";
import { productAuthenticity, productConditions } from "../ProductForm/utils";

export const makeOptionsBasedOnVariants = (variants: Array<TProductVariantBody>): Array<TOption> => {
  const rawOptionValues: Array<TProductVariantBody["options"]> = variants.map(v => v.options);

  return rawOptionValues?.reduce((total: TOption[], options: TProductVariantBody["options"]) => {
    Object.entries(options).forEach(([key, valu]) => {
      const index = total.findIndex(option => option.name === key);
      if (index > -1) {
        // if variants exists, then push to it's values instead of adding whole new variant
        if (!total[index].values?.find(v => v === valu)) {
          // prevent duplicates
          total[index].values.push(valu);
        }
      } else {
        // if variants is not exist, add it
        total.push({ name: key, values: [valu] });
      }
    });
    return total;
  }, []);
};

function generateCombinations(options: TOption[]) {
  // Base case: if there are no options, return an array with an empty combination
  if (options.length === 0) {
    return [{} as TOption]; // Return an array with an empty object
  }

  // Get the first variant and the rest of the options
  const [firstOption, ...restOptions] = options;

  // Generate combinations for the rest of the options
  const restCombinations = generateCombinations(restOptions);

  // Create combinations by adding each value of the first variant to each of the combinations from the rest
  const combinations: TOption[] = [];
  firstOption.values.forEach(value => {
    restCombinations.forEach(combination => {
      combinations.push({ ...combination, [firstOption.name]: value });
    });
  });

  return combinations;
}

const emptyVariant = {
  inventory: null, // must be null because react-hook-form does not accept undefined
  retail_price: null,
  sku: null,
  status: VARIANT_STATUS_ACTIVE,
  authenticity: productAuthenticity?.[0]?.id,
  backorder: false,
  commission: null,
  map: null,
  condition: productConditions?.[0]?.id,
  is_active: undefined!
};

export const updateVariantsBasedOnOptions = (options: Array<TOption>, variants: TProductVariantBody[]) => {
  const allCombinations = [...generateCombinations(options)];

  if (!options?.length) return [];

  return allCombinations?.map(variant => {
    const found = variants?.find(a => isEqual(a.options, variant));

    if (found) {
      return {
        ...found,
        options: variant
      };
    }
    return {
      ...emptyVariant,
      options: variant
    };
  });
};

export const calculateSellingPriceFormula = ({
  retailPrice,
  commission
}: {
  retailPrice?: number;
  commission?: number;
}) => {
  if (!retailPrice || !commission) return "";
  const result = retailPrice - (retailPrice * commission) / 100;
  return Number(result.toFixed(2));
};
