import CustomButton from "@/components/ui/CustomButton/CustomButton";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import NumberInput from "@/components/ui/inputs/NumberInput";
import PriceInput from "@/components/ui/inputs/PriceInput";
import useModal from "@/utils/hooks/useModal";
import i18n from "@/utils/i18n";
import { yupResolver } from "@hookform/resolvers/yup";
import { Close } from "@mui/icons-material";
import { isNaN } from "lodash";
import Image from "next/image";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import * as yup from "yup";
import { productAuthenticity, productConditions } from "../ProductForm/utils";

const validationSchema = yup.object({
  inventory: yup.number().typeError(i18n.t("product.validations.required")),
  // .required(i18n.t("product.validations.required")),

  map: yup
    .number()
    .transform((value, originalValue) => (originalValue === "" ? undefined : value))
    .nullable()
    .test("map", i18n.t("product.validations.map"), (value, context) => {
      if (!value) return true;
      const { commission } = context.parent;
      return value <= commission;
    }),
  // map: yup
  // .number()
  // .transform((value, originalValue) => (originalValue === "" ? undefined : value))
  // .when([], schema => {
  //   if (!hasVariants) {
  //     return yup
  //       .number()
  //       .transform((value, originalValue) => (originalValue === "" ? undefined : value))
  //       .nullable()
  //       .test("map", i18n.t("product.validations.map"), function (value) {
  //         const { commission } = this.parent;
  //         if (value == null) return true;
  //         return value <= commission;
  //       });
  //   }
  //   return schema;
  // }),
  is_active: yup.boolean(),
  authenticity: yup.string().typeError(i18n.t("product.validations.required")),
  // .required(i18n.t("product.validations.required")),
  condition: yup.string().typeError(i18n.t("product.validations.required")),
  // .required(i18n.t("product.validations.required")),
  backorder: yup.boolean(),
  commission: yup
    .number()
    .typeError(i18n.t("product.validations.required"))
    // .required(i18n.t("product.validations.required"))
    .min(1, i18n.t("supplier.profile.validations.positive"))
    .max(100, i18n.t("product.validations.max100")),

  // listing_price: yup
  //   .number()
  //   .typeError(i18n.t("product.validations.required"))
  //   .required(i18n.t("product.validations.required"))
  //   .min(1, i18n.t("supplier.profile.validations.positive")),

  // minimum_retail_price: yup
  //   .number()
  //   .typeError(i18n.t("product.validations.required"))
  //   .required(i18n.t("product.validations.required"))
  //   .test("price", i18n.t("product.validations.minimumRetailPrice"), (value, context) => {
  //     if (value === undefined) return false;
  //     const { listing_price } = context.parent;
  //     return value >= listing_price;
  //   }),
  retail_price: yup
    .number()
    .typeError(i18n.t("product.validations.required"))
    // .required(i18n.t("product.validations.required"))
    .min(1, i18n.t("supplier.profile.validations.positive"))
  // .test("price", i18n.t("product.validations.retailPrice"), (value, context) => {
  //   if (value === undefined) return false;
  //   const { minimum_retail_price, listing_price } = context.parent;
  //   return value >= listing_price && value >= minimum_retail_price;
  // })
});

export type TFormData = {
  inventory: number;
  retail_price: number;
  authenticity: string;
  condition: string;
  backorder: boolean;
  commission: number;
};

function BulkVariantEdit({ onSubmit: onSubmitModal }: { onSubmit?: (d: TFormData) => void }) {
  const { hideModal } = useModal();
  const { t } = useTranslation();

  const form = useForm<TFormData>({
    resolver: yupResolver(validationSchema as any) as any
  });

  const { handleSubmit, control, watch, setValue } = form;

  const onSubmit = async (values: TFormData) => {
    onSubmitModal?.(values);
    hideModal();
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <div className="flex gap-2">
          <div>
            <Image src="/images/svgs/trash-in-3.svg" alt="" width={20} height={20} />
          </div>
          <div className="text-[13px] font-medium text-v2-content-primary">
            {t("product.variantsBulkOperation.title")}
          </div>
        </div>
        <div onClick={hideModal} className="cursor-pointer text-v2-content-tertiary">
          <Close className="size-5" />
        </div>
      </div>
      <FormProvider {...form}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <p className="text-v2-content-tertiary text-body4-medium pt-6 border-t border-t-v2-border-secondary">
            {t("product.productConditionsTitle")}
          </p>
          <div className="flex 2xs:flex-nowrap flex-wrap items-center xmd:gap-4 gap-2 mb-3 ">
            {productConditions?.map(item => (
              <div
                className={twMerge(
                  "border border-solid border-spacing-[2px] flex justify-between gap-3 h-full rounded-lg xmd:px-4 px-2 xmd:py-3 py-2 cursor-pointer w-full",
                  item?.id === watch("condition") ? "border-purple-500" : "border-gray-50"
                )}
                key={item?.id}
                onClick={() => setValue("condition", item?.id)}
              >
                <span className="text-body4-medium text-gray-999 whitespace-nowrap">{item?.title}</span>

                {item?.id === watch("condition") ? (
                  <Image src="/images/svgs/user-checked.svg" alt="realUser" width={24} height={24} />
                ) : (
                  <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={24} height={24} />
                )}
              </div>
            ))}
          </div>

          <span className="text-v2-content-tertiary text-body4-medium">{t("product.productAuthenticityTitle")}</span>
          <div className="flex 2xs:flex-nowrap flex-wrap items-center xmd:gap-4 gap-2 ">
            {productAuthenticity?.map(item => (
              <div
                className={twMerge(
                  "border border-solid border-spacing-[2px] flex justify-between gap-3 h-full rounded-lg xmd:px-4 px-2 xmd:py-3 py-2 cursor-pointer w-full",
                  item?.id === watch("authenticity") ? "border-purple-500" : "border-gray-50"
                )}
                key={item?.id}
                onClick={() => setValue("authenticity", item?.id)}
              >
                <span className="text-body4-medium text-gray-999 whitespace-nowrap">{item?.title}</span>

                {item?.id === watch("authenticity") ? (
                  <Image src="/images/svgs/user-checked.svg" alt="realUser" width={24} height={24} />
                ) : (
                  <Image src="/images/svgs/user-unchecked.svg" alt="realUser" width={24} height={24} />
                )}
              </div>
            ))}
          </div>

          <div className="flex items-center justify-between py-6 border-y border-y-v2-border-secondary my-5">
            <div className="flex flex-col">
              <span className="text-v2-content-primary text-body3-medium !font-semibold">
                {t("product.preOrderTitle")}
              </span>
              <span className="text-v2-content-tertiary text-caption-medium">{t("product.preOrderSubTitle")}</span>
            </div>

            <div className="flex items-center gap-2 ">
              <span className="text-v2-content-tertiary text-caption-medium">{t("supplier.profile.active")}</span>
              <Controller
                name="backorder"
                control={control}
                render={({ field }) => (
                  <CustomSwitch
                    checked={field.value}
                    onChange={(e, checked) => field.onChange(checked)}
                    labelClassName="!ml-0 !mr-0"
                    textClassName="hidden"
                    size="small"
                  />
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div>
              <Controller
                name="inventory"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <NumberInput
                    {...field}
                    label={t("inventory")}
                    placeholder={t("inventory")}
                    error={Boolean(error?.message)}
                    helperText={error?.message}
                    value={field?.value ?? ""}
                    onChange={e => {
                      if (e?.target?.value === undefined || isNaN(e?.target?.value)) return;

                      field?.onChange(Number(e?.target?.value));
                    }}
                    requiredStar={false}
                  />
                )}
              />
            </div>

            <div>
              <Controller
                name="retail_price"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <PriceInput
                    {...field}
                    showSymbol={false}
                    label={t("product.retailPrice")}
                    placeholder={t("product.retailPrice")}
                    error={Boolean(error?.message)}
                    helperText={error?.message}
                    requiredStar={false}
                  />
                )}
              />
            </div>

            <div>
              <Controller
                name="commission"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <PriceInput
                    {...field}
                    id={field?.name}
                    autoComplete="off"
                    requiredStar={false}
                    label={t("productForm.salesCommissionPercentage")}
                    placeholder={t("productForm.commisionPlaceholder")}
                    error={Boolean(error?.message)}
                    helperText={error?.message || ""}
                    endAdornment={
                      <div className="text-v2-content-tertiary text-body4-medium pr-0.5">{t("percent")}</div>
                    }
                    onChange={e => {
                      field.onChange(e);
                      // Update selling price after commission change
                      // setTimeout(updateSellingPrice, 0);
                    }}
                  />
                )}
              />

              <div className="text-xs leading-4 text-v2-content-tertiary mt-1">{t("product.commissionHint")}</div>
            </div>
          </div>

          <div className="flex items-center gap-4 mt-6">
            <CustomButton color="secondary" className="!py-3 flex-1" onClick={hideModal} type="button">
              {t("product.variantsBulkOperation.cancel")}
            </CustomButton>
            <CustomButton className="!py-3 flex-1" type="submit">
              {t("product.variantsBulkOperation.add")}
            </CustomButton>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}

export default BulkVariantEdit;
