import Button from "@/components/ui/Button";
import Input from "@/components/ui/inputs/Input";
import { Icon } from "@iconify/react";
import { Close } from "@mui/icons-material";
import { Chip, Grid, IconButton } from "@mui/material";
import { Formik } from "formik";
import Image from "next/image";
import { useRef } from "react";
import { useTranslation } from "react-i18next";
import { optionFormValidationSchema } from "./validation";
import { TOption } from "./VariantsTable";

interface IAddVariantModal {
  value: TOption;
  isEdit?: Boolean;
  onSubmit: (v: TOption) => void;
  onCancel: () => void;
}

function AddVariantModal(props: IAddVariantModal) {
  const { t } = useTranslation();
  const { value, isEdit, onCancel, onSubmit } = props;
  const inptRef = useRef<HTMLInputElement | null>(null);

  return (
    <Formik
      initialValues={value}
      validationSchema={optionFormValidationSchema}
      onSubmit={(values, { resetForm }) => {
        onSubmit({ ...values, values: values?.values?.filter(Boolean) });
        resetForm();
      }}
      validateOnBlur={false}
      validateOnChange={false}
    >
      {({ values, errors, handleChange, submitForm, setFieldValue, setFieldError }) => {
        const addNewValue = () => {
          const val = inptRef.current?.value.trim();
          if (inptRef.current) {
            inptRef.current.value = "";
          }

          if (!val) return;

          if (values?.values?.includes(val)) {
            setFieldError("values", t("product.validations.uniqField"));
            return;
          }

          setFieldValue("values", values.values.concat(val));
          setFieldError("values", undefined);
        };
        return (
          <>
            <div className="flex justify-between items-center mb-6 mt-2 md:mt-0">
              <div className="flex gap-2 items-center">
                <Image src="/images/svgs/3d-cube-scan.svg" alt="not-found" width={24} height={24} className="block" />
                <div className="font-medium text-[13px] text-v2-content-primary">
                  {isEdit ? t("product.editOption") : t("product.newOption")}
                </div>
              </div>
              <IconButton onClick={onCancel} className="ms-auto w-5 h-5">
                <Close />
              </IconButton>
            </div>

            <Grid item container direction="column">
              <Input
                label={t("product.optionName")}
                maxCharLength={15}
                value={values.name}
                onChange={handleChange}
                name="name"
                error={Boolean(errors.name)}
                helperText={errors.name}
                requiredStar={false}
              />
            </Grid>

            <div className="flex gap-2 mt-4">
              <Input
                ref={inptRef}
                error={Boolean(errors.values)}
                helperText={!!errors.values}
                label={t("product.optionValues")}
                placeholder={t("product.newOptionPlaceholder")}
                onKeyDown={e => {
                  if (e.key === "Enter" || e.keyCode === 13) {
                    e.preventDefault?.();
                    addNewValue();
                  }
                }}
                rootClassName="flex-1"
                endAdornment={
                  <Button
                    variant="tertiaryGray"
                    size="md"
                    onClick={addNewValue}
                    className="shrink-0 -my-2 -ml-2 !text-v2-content-on-action-2"
                    startAdornment={<Icon icon="ph:plus" className="size-4" />}
                  >
                    {t("product.addOption")}
                  </Button>
                }
              />
            </div>

            <div className="flex flex-wrap gap-1.5 mt-4 min-h-36">
              {values?.values?.map((item, index) => (
                <Chip
                  deleteIcon={<Close />}
                  label={item}
                  // {...getTagProps({ index })}
                  className="!my-0 bg-v2-surface-thertiary"
                  key={String(item)}
                  onDelete={() =>
                    setFieldValue(
                      "values",
                      values.values.filter((v, i) => index !== i)
                    )
                  }
                />
              ))}
            </div>

            <Grid container spacing={2} direction="row" justifyContent="space-between" className="pt-4">
              <Grid item xs={6} pt={0}>
                <Button onClick={onCancel} className="w-full" variant="secondaryGray">
                  {t("product.cancel")}
                </Button>
              </Grid>

              <Grid item xs={6}>
                <Button onClick={() => submitForm()} className="w-full">
                  {t("product.addOption")}
                </Button>
              </Grid>
            </Grid>
          </>
        );
      }}
    </Formik>
  );
}

export default AddVariantModal;
