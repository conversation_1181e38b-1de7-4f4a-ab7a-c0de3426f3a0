import { Icon } from "@iconify/react";
import { IconButton } from "@mui/material";
import React from "react";
import useModal from "@/utils/hooks/useModal";
import { useTranslation } from "react-i18next";

export type TOption = { name: string; values: string[] };

interface IVariantsTable {
  value: TOption;
  onClickEdit: () => void;
  onRemove?: () => void;
}

function VariantsTable(props: IVariantsTable) {
  const { t } = useTranslation();
  const { value, onRemove, onClickEdit } = props;
  const { showModal, hideModal } = useModal();

  const handleDelete = () => {
    showModal({
      title: t("product.deleteVariantTitle"),
      subTitle: t("product.deleteVariantSubTitle"),
      icon: "/images/svgs/removeShipping.svg",
      actions: [
        {
          label: t("product.cancel"),
          onClick: () => hideModal(),
          variant: "secondaryGray"
        },
        {
          label: t("product.yesDelete"),
          variant: "destructivePrimary",
          onClick: () => {
            hideModal();
            onRemove?.();
          }
        }
      ]
    });
  };

  return (
    <div className=" flex justify-between items-center gap-4">
      <div className="flex items-center gap-2 border-2 border-v2-surface-thertiary rounded-md px-4 py-3 flex-1">
        <div className="text-v2-content-primary text-[13px] font-medium">{value.name}:</div>

        <div className="flex items-center gap-2 flex-wrap">
          {value?.values?.map(optionValue => (
            <div key={optionValue} className="bg-v2-surface-thertiary rounded py-1 px-3 text-v2-content-primary">
              {optionValue}
            </div>
          ))}
        </div>
      </div>
      <div className="shrink-0 flex items-center gap-4">
        <IconButton onClick={() => onClickEdit()} className="!p-0">
          <Icon icon="solar:pen-new-square-linear" width={20} height={20} color="rgb(var(--color-gray-999))" />
        </IconButton>

        {onRemove && (
          <IconButton color="error" onClick={() => handleDelete()} className="!p-0">
            <Icon icon="solar:trash-bin-trash-outline" />
          </IconButton>
        )}
      </div>
    </div>
  );
}

export default VariantsTable;
