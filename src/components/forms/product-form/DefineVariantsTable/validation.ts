import * as yup from "yup";
import i18n from "@/utils/i18n";
import { AnyObject } from "yup/lib/types";

type NewArraySchema = {
  unique: (
    s: string
  ) => yup.ArraySchema<
    yup.StringSchema<string | undefined, AnyObject, string | undefined>,
    AnyObject,
    (string | undefined)[] | undefined,
    (string | undefined)[] | undefined
  >;
} & yup.ArraySchema<
  yup.StringSchema<string | undefined, AnyObject, string | undefined>,
  AnyObject,
  (string | undefined)[] | undefined,
  (string | undefined)[] | undefined
>;

yup.addMethod(yup.array, "unique", function (message, mapper = (a: string) => a) {
  return this.test("unique", message, (list: any[] | undefined): boolean => {
    const uniqueMappedItems = new Set(list?.map(mapper));

    const isValidList =
      Boolean(list) && Boolean(list?.filter(Boolean)?.length) && list?.length === uniqueMappedItems.size;
    return isValidList;
  });
});

export const optionFormValidationSchema = new yup.ObjectSchema({
  name: yup
    .string()
    .required(i18n.t("product.validations.required"))
    .max(15, i18n.t("product.validations.maxChar", { amount: 15 })),
  values: (yup.array().of(yup.string()) as NewArraySchema)
    .min(1, i18n.t("product.validations.required"))
    .unique(i18n.t("product.validations.uniqField"))
});
