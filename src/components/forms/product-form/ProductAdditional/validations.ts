import { SUPPLIER_SHIPPING_PAYER_CUSTOMER, SUPPLIER_SHIPPING_PAYER_SUPPLIER } from "@/constants";
import i18n from "@/utils/i18n";
import * as yup from "yup";

export const productReturnValidationSchema = new yup.ObjectSchema({
  isAllowed: yup.boolean(),
  description: yup.string().when("isAllowed", isAllowed => {
    if (isAllowed) {
      return yup
        .string()
        .required(i18n.t("supplier.profile.validations.requiredField"))
        .max(2048, i18n.t("supplier.profile.validations.max2048"));
    }
    return yup.string().max(2048, i18n.t("supplier.profile.validations.max2048"));
  }),
  shippingPayer: yup.string().when("isAllowed", isAllowed => {
    if (isAllowed) {
      return yup
        .string()
        .required(i18n.t("supplier.profile.validations.requiredField"))
        .test("shippingPayer", i18n.t("supplier.profile.validations.shippingPayer"), value => {
          return (
            Boolean(value?.length) &&
            [SUPPLIER_SHIPPING_PAYER_CUSTOMER, SUPPLIER_SHIPPING_PAYER_SUPPLIER].includes(value as string)
          );
        });
    }
    return yup.string().test("shippingPayer", i18n.t("supplier.profile.validations.shippingPayer"), value => {
      return (
        Boolean(value?.length) &&
        [SUPPLIER_SHIPPING_PAYER_CUSTOMER, SUPPLIER_SHIPPING_PAYER_SUPPLIER].includes(value as string)
      );
    });
  }),

  windowTime: yup.object().when("isAllowed", isAllowed => {
    if (isAllowed) {
      return yup.object().shape({
        min: yup.number().required(i18n.t("supplier.profile.validations.requiredField")),
        max: yup.number().required(i18n.t("supplier.profile.validations.requiredField"))
      });
    }
    return yup.object();
  })
});
