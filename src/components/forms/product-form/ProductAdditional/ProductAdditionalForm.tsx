import "./ProductAdditionalForm.css";

import { CircularProgress, Collapse } from "@mui/material";
import React, { ForwardedRef, useEffect, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import SupplierShippingForm from "@/components/forms/SupplierShippingForm/SupplierShippingForm";
import { IProductReturnValues, ProductReturn } from "./ProductReturn";
import { IProductAdditionalFormProps, IProductAdditionalFormRefProps } from "./types";
import {
  useGetSupplierReturnQuery,
  useGetSupplierShippingQuery,
  usePutSupplierReturnMutation,
  usePutSupplierShippingMutation
} from "@/store/apps/supplier";
import { TSupplierShippingData } from "@/store/apps/supplier/types";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { SnakeToCamelFieldErrorWrapper } from "@/utils/helpers";
import { isEmpty } from "lodash";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import { twMerge } from "tailwind-merge";
import { Icon } from "@iconify/react";

const ProductAdditionalForm = (
  props: IProductAdditionalFormProps,
  ref: ForwardedRef<IProductAdditionalFormRefProps>
) => {
  const { productId } = props;
  const { t } = useTranslation();
  const [hasSpecial, setHasSpecial] = useState(false);
  const [shippingToBeSubmittedLater, setShippingToBeSubmittedLater] = useState<TSupplierShippingData[] | undefined>(
    undefined
  );
  const [productReturnToBeSubmittedLater, setProductReturnToBeSubmittedLater] = useState<
    IProductReturnValues | undefined
  >(undefined);

  const [tab, onTabChange] = useState(0);

  const { data: supplierShippingData, isLoading: isLoadingSupplierShipping } = useGetSupplierShippingQuery(
    {
      productId
    },
    {
      skip: !productId
    }
  );

  const { data: supplierReturnData, isLoading: isLoadingSupplierReturn } = useGetSupplierReturnQuery(
    { productId },
    { skip: !productId }
  );

  const isLoading = isLoadingSupplierShipping || isLoadingSupplierReturn;

  const [putSupplierShipping] = usePutSupplierShippingMutation();
  const [putSupplierReturn] = usePutSupplierReturnMutation();

  useEffect(() => {
    setHasSpecial(!!(supplierReturnData?.data || supplierShippingData?.data?.length));
  }, [supplierReturnData?.data, supplierShippingData?.data?.length]);

  const handleSubmitPolicies = async (
    { policies: body, productId }: { policies: TSupplierShippingData[]; productId?: string },
    { setFieldError }: { setFieldError?: (v: string, s: string | undefined) => void }
  ) => {
    try {
      await putSupplierShipping({ body: body?.map(v => ({ ...v, productId })) }).then(res => {
        const error = (res as any)?.error?.data;

        if (error && setFieldError) {
          clientDefaultErrorHandler({
            error: (res as any)?.error,
            bodyError: error,
            setFieldError: SnakeToCamelFieldErrorWrapper(setFieldError)
          });
        }

        if ("data" in res && res?.data) {
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const onSubmitPolicies = async ({ policies: body }: { policies: TSupplierShippingData[] }, setFieldError: any) => {
    if (!productId) {
      setShippingToBeSubmittedLater(body);
    } else {
      handleSubmitPolicies({ policies: body, productId }, { setFieldError });
    }
  };

  const handleSubmitReturn = async (
    { values, productId }: { values: IProductReturnValues; productId?: string },
    { setFieldError }: { setFieldError?: (v: string, s: string | undefined) => void }
  ) => {
    const body = values as unknown as IProductReturnValues;

    try {
      await putSupplierReturn({ body: { ...body, productId } }).then(res => {
        const error = (res as any)?.error?.data;

        if (error && setFieldError) {
          clientDefaultErrorHandler({
            error: (res as any)?.error,
            bodyError: error,
            setFieldError: SnakeToCamelFieldErrorWrapper(setFieldError)
          });
        }
        if ("data" in res && res?.data) {
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  const onSubmitReturn = async (
    values: IProductReturnValues,
    { setFieldError }: { setFieldError: (v: string, s: string | undefined) => void }
  ) => {
    if (!productId) {
      setProductReturnToBeSubmittedLater(values);
    } else {
      handleSubmitReturn({ values, productId }, { setFieldError });
    }
  };

  useImperativeHandle(
    ref,
    () => ({
      handleSubmitData: productId => {
        if (shippingToBeSubmittedLater?.length) {
          handleSubmitPolicies({ policies: shippingToBeSubmittedLater, productId }, {});
        }
        if (!isEmpty(productReturnToBeSubmittedLater)) {
          handleSubmitReturn({ values: productReturnToBeSubmittedLater, productId }, {});
        }
      }
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [shippingToBeSubmittedLater, productReturnToBeSubmittedLater]
  );

  if (isLoading) {
    return (
      <div className="items-center justify-center flex w-full py-16">
        <CircularProgress />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-start">
        <div className="flex-1 flex flex-col gap-2">
          <div className="text-v2-content-primary font-semibold text-sm">{t("productForm.specialState")}</div>
          <div className="text-[13px] text-v2-content-tertiary font-medium">{t("productForm.specialStateDesc")}</div>
        </div>

        <CustomSwitch
          label={t("supplier.profile.active")}
          labelClassName="ml-0"
          checked={hasSpecial}
          onChange={(e, v) => setHasSpecial(v)}
          textClassName="text-v2-content-tertiary text-xs font-medium"
        />
      </div>

      {/* ---------------------------------- tabs ---------------------------------- */}

      <Collapse in={hasSpecial}>
        <div className="flex items-center gap-4">
          <div
            className={twMerge(
              "rounded-full p-2.5 flex items-center gap-1.5 w-fit cursor-pointer select-none",
              tab === 0
                ? "bg-v2-surface-action text-v2-surface-primary"
                : "bg-v2-surface-thertiary text-v2-content-primary"
            )}
            onClick={() => onTabChange(0)}
          >
            <Icon icon="solar:delivery-bold" className="size-4" />
            <span className="text-[13px]">{t("supplier.profile.step.shipping")}</span>
          </div>
          <div
            className={twMerge(
              "rounded-full p-2.5 flex items-center gap-1.5 w-fit cursor-pointer select-none",
              tab === 1
                ? "bg-v2-surface-action text-v2-surface-primary"
                : "bg-v2-surface-thertiary text-v2-content-primary"
            )}
            onClick={() => onTabChange(1)}
          >
            <Icon icon="solar:undo-right-round-square-outline" className="size-4" />
            <span className="text-[13px]">{t("supplier.profile.step.policy")}</span>
          </div>
        </div>

        <div className="mt-6">
          <div hidden={tab !== 0}>
            <SupplierShippingForm
              handleSubmit={onSubmitPolicies}
              values={supplierShippingData?.data || shippingToBeSubmittedLater}
              isRemovableInClient={!!shippingToBeSubmittedLater?.length}
              onRemove={index =>
                setShippingToBeSubmittedLater(prev =>
                  shippingToBeSubmittedLater?.filter((item, itemIndex) => itemIndex !== index)
                )
              }
            />
          </div>

          <div hidden={tab !== 1}>
            <ProductReturn initialValues={supplierReturnData?.data} onSubmit={onSubmitReturn} />
          </div>
        </div>
      </Collapse>
    </div>
  );
};

export default React.forwardRef(ProductAdditionalForm);
