import { getWindowTimeItems } from "@/app/supplier/(dashboard)/profile/SupplierReturn/utils";
import { FormikConfig, useFormik } from "formik";
import { useTranslation } from "react-i18next";
import { productReturnValidationSchema } from "./validations";
import { Box, FormControl, FormHelperText, Grid, MenuItem, Typography } from "@mui/material";
import React, { useEffect, useRef } from "react";
import { isEqual } from "lodash";
import useDebounce from "@/utils/hooks/useDebounce";
import SectionInfo from "@/components/ui/SectionInfo/SectionInfo";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import CustomRadio from "@/components/ui/CustomRadio/CustomRadio";
import CustomFormLabel from "@/components/ui/CustomFormLabel/CustomFormLabel";
import NumberInput from "@/components/ui/inputs/NumberInput";
import Textarea from "@/components/ui/inputs/Textarea/Textarea";
import InputLabel from "@/components/ui/inputs/Input/InputLabel";

type WindowTime = {
  min: number;
  max: number;
};
export type IProductReturnValues = {
  windowTime?: WindowTime;
  isAllowed?: boolean;
  description?: string;
  shippingPayer?: "Supplier" | "Customer";
};
interface IProductReturnProps {
  initialValues?: IProductReturnValues;
  onSubmit: FormikConfig<IProductReturnValues>["onSubmit"];
}

const formInitValuesIfEmpty = {
  isAllowed: false,
  description: "",
  windowTime: undefined,
  shippingPayer: undefined
};

export const ProductReturn = (props: IProductReturnProps) => {
  const { t } = useTranslation();
  const windowTimeItems = getWindowTimeItems({ t });

  const { initialValues = formInitValuesIfEmpty, onSubmit } = props;
  const formik = useFormik<IProductReturnValues>({
    initialValues,
    validationSchema: productReturnValidationSchema,
    onSubmit
  });
  const { values, errors, isValid, setFieldValue, handleBlur, handleChange, touched, handleSubmit } = formik;
  const debouncedSubmit = useDebounce(() => {
    handleSubmit();
  }, 900);

  const submited = useRef<IProductReturnValues>(values);

  const windowTimeValue = values.windowTime ? Object.values(values.windowTime).join("-") : "";

  useEffect(() => {
    if (isValid && !isEqual(values, submited.current)) {
      debouncedSubmit();
      submited.current = values;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isValid, values]);

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-start gap-4">
        <div className="flex flex-col gap-1">
          <div className="text-v2-content-primary text-sm font-semibold">{t("supplier.profile.returnalIsAllowed")}</div>
          <div className="text-v2-content-tertiary text-xs font-medium">
            {t("supplier.profile.returnalIsAllowedDesc")}
          </div>
        </div>

        <CustomSwitch
          checked={values.isAllowed}
          onChange={(e, v) => setFieldValue("isAllowed", v)}
          onBlur={handleBlur}
          id="isAllowed"
          name="isAllowed"
          color="primary"
          label={t("supplier.profile.active")}
          labelClassName="ml-0 mr-2"
          textClassName="text-v2-content-tertiary text-xs font-medium"
        />
      </div>

      <div className="flex flex-col gap-2">
        <div className="text-sm text-v2-content-primary font-semibold">{t("supplier.profile.whoPayer")}</div>

        <div className=" flex items-center gap-16">
          <CustomRadio
            checked={values?.shippingPayer === "Customer"}
            onChange={handleChange}
            onBlur={handleBlur}
            value="Customer"
            label={t("supplier.profile.customer")}
            name="shippingPayer"
          />
          <CustomRadio
            checked={values?.shippingPayer === "Supplier"}
            onChange={handleChange}
            onBlur={handleBlur}
            value="Supplier"
            label={t("supplier.profile.me")}
            name="shippingPayer"
          />
          {/* <CustomSelect
              fullWidth
              displayEmpty
              MenuProps={{
                style: {
                  maxHeight: "400px"
                }
              }}
              id="windowTime"
              name="windowTime"
              value={windowTimeValue}
              onChange={e => {
                setFieldValue("windowTime", extractMinMax(e.target.value));
              }}
              onBlur={handleBlur}
              error={touched?.windowTime && Boolean(errors?.windowTime)}
              renderValue={selected => {
                if (selected.length === 0) {
                  return <div className="text-[#ADADAD] font-normal text-sm">{t("supplier.profile.windowTime")}</div>;
                }

                return windowTimeItems?.find(a => a.id === selected)?.label || selected;
              }}
            >
              {windowTimeItems?.map(item => (
                <MenuItem key={item.id} value={item.id}>
                  {item.label}
                </MenuItem>
              ))}
            </CustomSelect> */}
        </div>
      </div>

      <div className="flex flex-col max-w-xs">
        <NumberInput
          name="windowTime.max"
          placeholder={t("supplier.profile.placeholders.windowTime")}
          label={t("supplier.profile.windowTime")}
          value={values?.windowTime?.max}
          error={(touched.windowTime as any) && Boolean((errors.windowTime as any)?.max)}
          helperText={(touched.windowTime as any) && (errors.windowTime as any)?.max}
          endAdornment={
            <span className="text-body3-medium text-gray-999 whitespace-nowrap">
              {t("supplier.profile.daysOfWorking")}
            </span>
          }
          onChange={e => setFieldValue("windowTime", { min: 0, max: e?.target?.value ? Number(e?.target?.value) : "" })}
          onBlur={handleBlur}
          labelTooltipTitle={t("supplier.profile.windowTimeTooltipTitle")}
          labelTooltipDescription={t("supplier.profile.windowTimeTooltipDesc")}
        />
        <p className="text-v2-content-tertiary font-normal mt-1 text-xs">{t("windowTimeHint")}</p>
      </div>
      <div className="flex flex-col gap-2">
        <Textarea
          name="description"
          value={values?.description}
          error={touched.description && Boolean(errors.description)}
          helperText={touched?.description && errors?.description}
          onChange={handleChange}
          onBlur={handleBlur}
          label={t("product.description")}
          placeholder={t("product.descriptionPlaceholder")}
          className="pb-8"
          requiredStar={!!values?.isAllowed}
          optional={!values?.isAllowed}
        />
      </div>
    </div>
  );
};
