.int-link a {
  color: rgb(var(--color-gray-400));
  padding: 0px;
  /* border-inline-start: 4px solid var(--mui-palette-grey-400); */
  padding-inline-start: calc(var(--mui-theme-spacing-1) / 2);
  font-size: 14px;
  position: relative;
}

.int-link a:before {
  inset-inline-start: -4px;
  inset-block-start: 6px;
  width: 4px;
  height: 14px;
  background-color: rgb(var(--color-gray-50));
  position: absolute;
  content: " ";
}

.int-link.active a:before {
  background-color: #67dfef !important;
}

.int-link.active a {
  font-size: 15px;
  color: var(--mui-palette-grey-900);
  /* border-inline-start: 4px solid var(--mui-palette-secondary-main); */
}

.int-link {
  width: 200px;
  padding-block: 5px;
}

.product-from-steps {
  position: sticky;
  top: 0;
}
