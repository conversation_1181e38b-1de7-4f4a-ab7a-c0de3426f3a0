import DropHereBoxImageUploader from "@/components/containers/ImageUploader/withUi/DropHereBoxImageUploader";
import CustomMenu from "@/components/ui/CustomMenu/CustomMenu";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { Divider, MenuItem, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

type TImageObject = {
  url: string;
  markedAsCover: boolean;
};
interface ProductImagesInterface {
  images: TImageObject[];
  setImages: (v: TImageObject[]) => void;
  error?: boolean | string;
  onError?: (msg?: string) => void;
}

const RenderImageBox = ({
  src,
  isCover,
  onRemove,
  onSelectForCover
}: {
  src?: string;
  isCover?: boolean;
  onRemove: () => void;
  onSelectForCover: () => void;
}) => {
  const { t } = useTranslation();
  const { showModal, hideModal } = useModal();

  const handleOpenPreviewModal = () => {
    showModal({
      body: (
        <div className="flex items-center justify-center py-6">
          <img src={src} className="w-full max-h-96 object-contain mt-2" />
        </div>
      ),
      width: 600
    });
  };

  return (
    <div className="w-[94px] h-[94px] rounded-lg overflow-hidden relative group bg-gray-50">
      {isCover && <img src="/images/svgs/startWithBg.svg" className="absolute right-1.5 top-1.5 z-10" />}

      <img src={src} className="object-cover h-full w-full" />

      <div className="absolute left-1.5 top-1.5 z-10">
        <CustomMenu
          content={
            <>
              <MenuItem onClick={() => handleOpenPreviewModal()} className="pb-3.5 pt-1.5 text-[13px]">
                <Icon icon="solar:eye-scan-outline" width={18} height={18} className="ml-2" />{" "}
                {t("productForm.uploadImage.preview")}
              </MenuItem>

              <Divider className="bg-gray-50 !my-0" />

              <MenuItem onClick={() => onSelectForCover()} className="py-3.5 text-[13px]">
                <Icon icon="solar:star-bold" width={18} height={18} className="ml-2" />{" "}
                {t("productForm.uploadImage.selectForCover")}
              </MenuItem>

              <Divider className="bg-gray-50 !my-0" />

              <MenuItem onClick={() => onRemove()} className="text-error-500 pt-3.5 pb-1.5 text-[13px]">
                <Icon icon="solar:trash-bin-trash-bold" width={18} height={18} className="ml-2" />
                {t("productForm.uploadImage.remove")}
              </MenuItem>
            </>
          }
        >
          <img src="/images/svgs/Menu-Dots-Square-2.svg" className="cursor-pointer" />
        </CustomMenu>
      </div>
    </div>
  );
};

const ProductImages = (props: ProductImagesInterface) => {
  const { images, setImages, error, onError } = props;
  const { t } = useTranslation();

  const handleOnUploaded = (v: { url?: string; id: string }) => {
    if (!v?.url) {
      return;
    }

    const cover = images?.some(a => a.markedAsCover);
    setImages([...(images || []), { url: v?.url, markedAsCover: !cover ? true : false }]);
  };

  const handleOnRemove = (value: string) => {
    const newImages = images.filter((v, i) => v.url !== value);

    if (newImages?.length === 1) {
      // if just one image left, that image will considered as cover
      setImages([{ url: value, markedAsCover: true }]);
    } else {
      setImages(newImages);
    }
  };

  const handleOnSelectForCover = (value: string) => {
    const newImages = images?.map(item => ({ url: item?.url, markedAsCover: item?.url === value ? true : false }));
    setImages(newImages);
  };

  return (
    <div>
      <DropHereBoxImageUploader
        serverFileKind="public"
        title={t("productForm.uploadImage.title")}
        subTitle={t("productForm.uploadImage.subTitle")}
        onUploaded={handleOnUploaded}
        hasCropper={false}
        withCompressorMaxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
        maxFileSizeMB={Number(process.env.PRODUCTS_IMG_MAX_SIZE_MB || 1)}
        onError={onError}
        hasError={!!error}
      />
      {!!images?.length && (
        <>
          <div className="mb-3 mt-4">
            <div className="text-sm font-medium text-v2-content-primary">{t("productForm.uploadedGallery")}</div>
          </div>
          <div className="2xs:w-[75vw] w-[73vw] xmd:w-full overflow-hidden">
            <div className="relative overflow-hidden">
              <Swiper
                dir="rtl"
                slidesPerView="auto"
                keyboard
                grabCursor
                spaceBetween={12}
                modules={[Navigation, Keyboard, Mousewheel]}
              >
                {images?.map((item, index) => {
                  return (
                    <SwiperSlide key={index} className="max-w-fit">
                      <RenderImageBox
                        key={index}
                        src={item?.url}
                        isCover={item?.markedAsCover}
                        onRemove={() => handleOnRemove(item?.url)}
                        onSelectForCover={() => handleOnSelectForCover(item?.url)}
                      />
                    </SwiperSlide>
                  );
                })}
              </Swiper>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ProductImages;
