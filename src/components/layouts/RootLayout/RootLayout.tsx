"use client";

import "./layout.css";
import { Box } from "@mui/material";
import { Container } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import React, { forwardRef, Ref, useImperativeHandle, useState } from "react";
import Sidebar from "../../containers/sidebar/Sidebar";
import withClassname from "../../../utils/withClassName";
import { themeCustomizer } from "@/utils/theme";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

const PageWrapper = withClassname("div", "styled-page-wrapper") as any;

interface Props {
  children: React.ReactNode;
}

export interface RootLayoutRef {
  onToggleSidebar: () => void;
}

// eslint-disable-next-line react/display-name
const RootLayout = forwardRef(({ children }: Props, ref: Ref<RootLayoutRef>) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isHover, setIshover] = useState(false);
  const theme = useTheme();

  useImperativeHandle(ref, () => ({
    onToggleSidebar: () => {
      setIsOpen(prev => !prev);
    }
  }));

  return (
    <Box
      id="sx-layout-76"
      sx={{
        display: "flex",
        minHeight: "100vh",
        width: "100%",
        padding: themeCustomizer.isHorizontal ? 0 : "20px"
      }}
    >
      <title>{t("companyTitle")}</title>
      {/* ------------------------------------------- */}
      {/* Main Wrapper */}
      {/* ------------------------------------------- */}
      <Box width="100%" className={twMerge("main-container", "flex ")}>
        {/* PageContent */}

        {/* ------------------------------------------- */}
        {/* Sidebar */}
        {/* ------------------------------------------- */}
        <Sidebar isOpen={isOpen} isHover={isHover} setIsOpen={setIsOpen} setIshover={setIshover} />

        <PageWrapper
          className={"page-wrapper h-full" + (isOpen ? " side-open" : "")}
          id="sx-layout-108"
          sx={{
            ...(isOpen && {
              [theme.breakpoints.up("lg")]: {
                ml: `${themeCustomizer.MiniSidebarWidth}px`
              }
            }),
            ...(!isOpen &&
              !themeCustomizer.isHorizontal && {
                [theme.breakpoints.up("lg")]: {
                  ml: `${themeCustomizer.SidebarWidth}px`
                }
              })
          }}
        >
          <Container
            id="sx-layout-124"
            sx={{
              maxWidth: themeCustomizer.isLayout === "boxed" ? "lg" : "100%!important"
            }}
            className="h-full"
          >
            {/* ------------------------------------------- */}
            {/* Header */}
            {/* ------------------------------------------- */}
            {/* {themeCustomizer.isHorizontal
                ? " "
                : !isMobile && (
                    <Header
                      onToggleSidebar={() => {
                        setIsOpen(prev => !prev);
                        // if (sideRef.current) sideRef.current.toggle();
                      }}
                    />
                  )} */}

            {/* ------------------------------------------- */}
            {/* PageContent */}
            {/* ------------------------------------------- */}

            {/* <Box id="sx-layout-147" className="h-[calc(100%_-_67px)]"> */}
            {/* <Outlet /> */}
            {children}
            {/* <Index /> */}
            {/* </Box> */}

            {/* ------------------------------------------- */}
            {/* End Page */}
            {/* ------------------------------------------- */}
          </Container>
          {/* <Customizer /> */}
        </PageWrapper>
      </Box>
    </Box>
  );
});

export default RootLayout;
