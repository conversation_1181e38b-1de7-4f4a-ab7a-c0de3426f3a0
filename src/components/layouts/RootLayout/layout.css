#sx-layout-76 {
  display: flex;
  min-height: 100vh;
  width: 100%;
  padding: 24px;
}

@media (min-width: 1550px) {
  .main-container {
    max-width: 1440px;
    margin-inline: auto;
  }
}

@media (max-width: 1200px) {
  #sx-layout-76 {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  #sx-layout-76 {
    padding: 0px;
  }

  #sx-layout-108:has(.bottom-action) {
    margin-bottom: 75px !important;
  }
}

#sx-layout-108 {
  padding-bottom: 0;
}

#sx-layout-124 {
  padding: 0;
  padding-inline-start: 14px;
}

@media (max-width: 1200px) {
  #sx-layout-124 {
    padding-inline-start: 0;
  }
}

@media (min-width: 768px) {
  #sx-layout-147 {
    /* min-height: calc(100vh - 170px); */
    /* padding-top: 16px; */
  }
}

.styled-page-wrapper {
  display: flex;
  flex-grow: 1;
  padding-bottom: 60px;
  flex-direction: column;
  z-index: 1;
  background-color: transparent;
}

/* @media screen and (min-width: 1200px) {
  .styled-page-wrapper.side-open {
    margin-inline-start: 50px;
  }

  .styled-page-wrapper:not(.side-open) {
    margin-inline-start: 270px;
  }
} */
