import React, { ComponentType } from "react";
import { menuitems, MenuitemsType } from "@/components/containers/sidebar/MenuItems";
import { USERTYPES } from "@/constants/userTypes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { BottomNavigation, BottomNavigationAction, Box } from "@mui/material";
import clsx from "clsx";
import Link from "next/link";
import { usePathname } from "next/navigation";

import "./WithBottomBar.css";
import useSessionStore from "@/store/zustand/sessionStore";

interface WithBottomBarProps {
  [x: string]: any;
}

function WithBottomBar<P extends WithBottomBarProps>(WrappedComponent: ComponentType<P>): ComponentType<P> {
  function HocComponent(props: P) {
    const { user_type } = useSessionStore();
    const pathname = usePathname();
    const makePath = useRoleBasePath();

    const Menuitems = menuitems({ makePath })?.filter(item => item?.for?.includes(user_type as USERTYPES));

    const isActive = (item: MenuitemsType) => {
      // If we're on exactly the home page and this is the home item
      if (item.name === "home" && item.href.includes(pathname)) {
        return true;
      }

      // Check if current path matches the item's href directly
      if (item.href && pathname === item.href) {
        return true;
      }

      // Check if current path includes the item's name (if name is provided and not empty)
      if (item.name && item.name !== "" && pathname.includes(item.name)) {
        return true;
      }

      // Check extraIsActives array if it exists
      if (Array.isArray(item.extraIsActives) && item.extraIsActives.length > 0) {
        return item.extraIsActives.some(path => pathname.includes(path));
      }

      return false;
    };

    if (!Menuitems?.length) return null;

    return (
      <>
        <Box className="pb-[93px] mb-8 md:pb-0 md:mb-0 h-full">
          <WrappedComponent {...props} />
        </Box>

        <BottomNavigation showLabels className="bottom-navigation" value={"Recents"}>
          {Menuitems.filter(item => item.isMobile === undefined || item.isMobile).map(item => (
            <BottomNavigationAction
              label={item?.title}
              key={item.id}
              LinkComponent={Link}
              href={item.href}
              classes={{ root: "!justify-start !pt-5" }}
              color={isActive(item) ? "" : "rgb(var(--color-gray-400))"}
              className={clsx("bottom-navigation-action", isActive(item) && "bottom-navigation-action-active")}
              icon={
                <Icon
                  icon={(isActive(item) ? item.selectedIcon : item.icon) || ""}
                  color={isActive(item) ? "rgb(var(--color-purple-500))" : "rgb(var(--color-gray-400))"}
                  width={24}
                  height={24}
                />
              }
            />
          ))}
        </BottomNavigation>
      </>
    );
  }

  return HocComponent;
}

export default WithBottomBar;
