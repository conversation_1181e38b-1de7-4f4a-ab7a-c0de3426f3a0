.bottom-navigation {
  display: none;
}

.bottom-navigation-action .MuiBottomNavigationAction-label {
  font-size: 11px;
  font-weight: 500;
  color: rgb(var(--color-gray-400));
  margin-top: 2px;
  text-align: center;
}

.bottom-navigation-action-active .MuiBottomNavigationAction-label {
  color: rgb(var(--color-purple-500));
}

.bottom-navigation-action-active svg {
  color: rgb(var(--color-purple-500));
}

@media (max-width: 370px) {
  .bottom-navigation-action {
    margin-inline: -8px;
  }
}

@media screen and (max-width: 768px) {
  .bottom-navigation + #sx-layout-108 {
    margin-block-end: 56px;
  }

  .bottom-navigation {
    display: flex;
    background-color: rgb(var(--color-cards));
    box-shadow: 0px -12px 32px 0px rgba(87, 111, 133, 0.07);
    z-index: 40;
    position: fixed;
    bottom: 0;
    height: 93px;
    width: 100%;
    inset-inline-start: 0px;
    inset-inline-end: 0px;
  }

  #sx-customizer-6749 {
    inset-block-end: 70px !important;
  }
}
