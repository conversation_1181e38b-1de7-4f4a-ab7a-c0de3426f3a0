"use client";

import Image from "next/image";
import React from "react";
import { useTranslation } from "react-i18next";

interface AuthContainerProps {
  children: React.ReactNode;
}

function AuthContainer({ children }: AuthContainerProps) {
  const { t } = useTranslation();

  return (
    <div className="flex justify-center xmd:py-16 py-7 h-dvh overflow-auto w-full">
      <title>{t("companyTitle")}</title>
      <div className="xmd:max-w-[600px] w-full h-full flex flex-col justify-between gap-2 xmd:px-0 px-4">
        <div className=" w-full h-full flex flex-col justify-center">
          <div className="bg-cards rounded-2xl w-full h-fit xmd:py-12 py-8  ">
            <div>
              <div className="max-w-40 mx-auto">
                <Image src="/images/svgs/drophub-logo.svg" alt="dophub-logo" width={140} height={45} />
              </div>

              <div className="flex-1 flex justify-center items-center w-full mt-9 px-6">
                <div className=" xmd:w-1/2 w-full">{children}</div>
              </div>
            </div>
          </div>
        </div>
        <p className="mx-auto text-body4-regular text-v2-content-tertiary">{t("auth.footer")}</p>
      </div>
    </div>
  );
}

export default AuthContainer;
